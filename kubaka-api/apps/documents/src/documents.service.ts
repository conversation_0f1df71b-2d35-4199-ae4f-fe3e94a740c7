import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as fs from 'fs';
import * as path from 'path';
import * as xlsx from 'xlsx';
import { Not, Repository } from 'typeorm';
import { DocumentUpload } from '../entities/documentUpload.entity';
// import axios from 'axios';
import { APPLICATIONS_SERVICE } from '@app/common/constants';
import { ClientProxy, EventPattern } from '@nestjs/microservices';
import { isUUID } from 'class-validator';

import axios from 'axios';
import * as FormData from 'form-data';

import config from '../config';
import { Documents, Entity_Type } from '../entities/documents.entity';

interface MulterFile {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
  fieldname: string;
}

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(DocumentUpload)
    private readonly fileRepository: Repository<DocumentUpload>,

    @InjectRepository(Documents)
    private readonly documentsRepository: Repository<Documents>,

    @Inject(APPLICATIONS_SERVICE)
    private readonly applicationService: ClientProxy,
  ) {}

  // updating documentStatus
  @EventPattern('updateDocumentStatusEvent')
  async updateDocumentStatusEvent(documentId: string) {
    const document = await this.fileRepository.findOne({
      where: { id: documentId },
    });
    if (!document) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }
    document.documentStatusId = '1';
    return await this.fileRepository.save(document);
  }
  // requesting documents names from application service
  async requestRequiredDocumentData(requiredDocumentId: string) {
    return this.applicationService
      .send<any>({ cmd: 'requiredDocumentData' }, requiredDocumentId)
      .toPromise();
  }

  // uploading documents to the server
  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', file.originalname);
  //   const fileName = file.originalname;
  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: fileName },
  //   });
  //   if (dataFromDb)
  //     throw new HttpException(
  //       'File name already exist, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName,
  //   });
  //   return await this.fileRepository.save(newFile);
  // }

  // uploading documents to the server
  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname);
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', newFileName);

  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   return await this.fileRepository.save(newFile);
  // }

  // uploading documents to the server with check the excel(BOQ) format and pdf
  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', newFileName);

  //   if (fileExtension === '.xlsx' || fileExtension === '.xls') {
  //     // Check if the Excel file contains the required columns
  //     const workbook = xlsx.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const jsonData = xlsx.utils.sheet_to_json(worksheet, {
  //       header: 1,
  //     }) as unknown as string[][];
  //     const headerRow = jsonData[0] as string[];

  //     const requiredColumns = [
  //       'Item number',
  //       'Designation',
  //       'Unit',
  //       'QTY',
  //       'Unit Price(VAT inclusive)',
  //       'Total (VAT-inclusive)',
  //     ];

  //     const hasRequiredColumns = requiredColumns.every((col) =>
  //       headerRow.includes(col),
  //     );

  //     if (!hasRequiredColumns) {
  //       throw new HttpException(
  //         'The Excel file does not contain the required columns.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } else if (fileExtension !== '.pdf') {
  //     throw new HttpException(
  //       'Unsupported file type. Only Excel and PDF files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   return await this.fileRepository.save(newFile);
  // }

  // // // // file to be uploaded on different server
  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<DocumentUpload> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;

  //   if (fileExtension === '.xlsx' || fileExtension === '.xls') {
  //     const workbook = xlsx.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const jsonData = xlsx.utils.sheet_to_json(worksheet, {
  //       header: 1,
  //     }) as unknown as string[][];
  //     const headerRow = jsonData[0] as string[];

  //     const requiredColumns = [
  //       'Item number',
  //       'Designation',
  //       'Unit',
  //       'QTY',
  //       'Unit Price(VAT inclusive)',
  //       'Total (VAT-inclusive)',
  //     ];

  //     const hasRequiredColumns = requiredColumns.every((col) =>
  //       headerRow.includes(col),
  //     );

  //     if (!hasRequiredColumns) {
  //       throw new HttpException(
  //         'The Excel file does not contain the required columns.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } else if (fileExtension !== '.pdf') {
  //     throw new HttpException(
  //       'Unsupported file type. Only Excel and PDF files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Upload the file to the remote server
  //   const form = new FormData();
  //   form.append('file', file.buffer, newFileName);

  //   const remoteUploadUrl = 'https://api-gatway.kubaka.gov.rw/attachment/upload/';
  //   // const remoteUploadUrl = 'https://api-gateway.kubaka.gov.rw:3005/docMgt';
  //   try {
  //     const response = await axios.post(remoteUploadUrl, form, {
  //       headers: {
  //         ...form.getHeaders(),
  //       },
  //     });

  //     if (response.status !== 200) {
  //       throw new HttpException(
  //         'Failed to upload file to the remote server.',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } catch (error) {
  //     console.log(error);
  //     throw new HttpException(
  //       `Error uploading file: ${error.message}`,
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }

  //   // Save file metadata in the database
  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl: `${remoteUploadUrl}/${newFileName}`,
  //     fileName: newFileName,
  //   });

  //   return await this.fileRepository.save(newFile);
  // }

  // // file to be uploaded on the same server
  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<{ message: string; data: DocumentUpload }> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', newFileName);

  //   if (fileExtension === '.xlsx' || fileExtension === '.xls') {
  //     // Check if the Excel file contains the required columns
  //     const workbook = xlsx.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const jsonData = xlsx.utils.sheet_to_json(worksheet, {
  //       header: 1,
  //     }) as unknown as string[][];
  //     const headerRow = jsonData[0] as string[];

  //     const requiredColumns = [
  //       'Item number',
  //       'Designation',
  //       'Unit',
  //       'QTY',
  //       'Unit Price(VAT inclusive)',
  //       'Total (VAT-inclusive)',
  //     ];

  //     const hasRequiredColumns = requiredColumns.every((col) =>
  //       headerRow.includes(col),
  //     );

  //     if (!hasRequiredColumns) {
  //       throw new HttpException(
  //         'The Excel file does not contain the required columns.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } else if (fileExtension !== '.pdf') {
  //     throw new HttpException(
  //       'Unsupported file type. Only Excel and PDF files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   const savedFile = await this.fileRepository.save(newFile);

  //   return {
  //     message: 'File uploaded successfully',
  //     data: savedFile,
  //   };
  // }

  // find all files by applicationId
  async findAllUploadByApplicationId(applicationId: string): Promise<any[]> {
    // Validate the applicationId
    if (!applicationId || !isUUID(applicationId)) {
      throw new HttpException(
        'Invalid applicationId. Please provide a valid UUID.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Fetch documents where applicationId matches and uploadType is not 2
    const documents = await this.fileRepository.find({
      where: { applicationId, uploadType: Not('2') },
    });

    // Fetch requiredDocumentName for each document, including null values if requiredDocumentId is invalid or null
    const documentsWithNames = await Promise.all(
      documents.map(async (document) => {
        const requiredDocumentId = document.requiredDocumentId;

        // If requiredDocumentId is invalid or null, set requiredDocumentName to null
        if (!requiredDocumentId || !isUUID(requiredDocumentId)) {
          return { ...document, requiredDocumentName: null };
        }

        const requiredDocumentName =
          await this.requestRequiredDocumentData(requiredDocumentId);

        return { ...document, requiredDocumentName };
      }),
    );

    return documentsWithNames;
  }

  // async findAllByApplicationId(applicationId: string): Promise<any[]> {
  //   const documents = await this.fileRepository.find({
  //     where: { applicationId },
  //   });

  //   // Fetch requiredDocumentName for each document
  //   const documentsWithNames = await Promise.all(
  //     documents.map(async (document) => {
  //       const requiredDocumentId = document.requiredDocumentId;
  //       const requiredDocumentName = await this.applicationService.send(
  //         'getRequiredDoc',
  //         requiredDocumentId,
  //       );
  //       return { ...document, requiredDocumentName };
  //     }),
  //   );

  //   return documentsWithNames;
  // }

  // async findAllByApplicationId(applicationId: string): Promise<any[]> {
  //   const documents = await this.fileRepository.find({
  //     where: { applicationId },
  //   });

  //   // Fetch requiredDocumentName for each document
  //   const documentsWithNames = await Promise.all(
  //     documents.map(async (document) => {
  //       const requiredDocumentId = document.requiredDocumentId;
  //       const requiredDocumentName =
  //         await this.fetchRequiredDocumentName(requiredDocumentId);
  //       return { ...document, requiredDocumentName };
  //     }),
  //   );

  //   return documentsWithNames;
  // }

  // async fetchRequiredDocumentName(requiredDocumentId: string) {
  //   const response = await axios.get(
  //     `http://localhost:3000/applications/requiredDocument/${requiredDocumentId}`,
  //   );
  //   return response.data.name;
  // }

  // find all documents
  async findAllDocuments() {
    const applications = await this.fileRepository.find({
      where: { uploadType: Not('2') },
    });

    return applications;
  }

  // // find all documents
  // async findAllDocuments() {
  //   return this.fileRepository.find({});
  // }

  // find one document by its url
  async findOneByUrl(fileUrl: string): Promise<DocumentUpload> {
    return await this.fileRepository.findOne({ where: { fileUrl } });
  }

  // async delete(id: string): Promise<void> {
  //   const fileToDelete = await this.fileRepository.findOne(id);
  //   if (fileToDelete) {
  //     fs.unlinkSync(fileToDelete.fileUrl);
  //     await this.fileRepository.delete(id);
  //   }
  // }

  // async delete(id: string): Promise<void> {
  //   // Find the document by Id
  //   const documentToDelete = await this.fileRepository.findOne({
  //     where: { id },
  //   });

  //   if (!documentToDelete) {
  //     throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
  //   }
  //   try {
  //     await this.fileRepository.remove(documentToDelete);
  //     fs.unlinkSync(documentToDelete.fileUrl);
  //   } catch (error) {
  //     throw new HttpException(
  //       'Failed to delete document',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  async delete(id: string): Promise<{ message: string }> {
    const documentToDelete = await this.fileRepository.findOne({
      where: { id },
    });

    if (!documentToDelete) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }

    try {
      await this.fileRepository.remove(documentToDelete);
      // Check if file exists before deleting
      if (fs.existsSync(documentToDelete.fileUrl)) {
        fs.unlinkSync(documentToDelete.fileUrl);
      } else {
        console.warn(`File not found: ${documentToDelete.fileUrl}`);
      }

      // Return success message
      return { message: 'Document successfully deleted' };
    } catch (error) {
      console.error('Error during document deletion:', error);
      throw new HttpException(
        'Failed to delete document',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async delete(id: string): Promise<void> {
  //   // Find the document by Id
  //   const documentToDelete = await this.fileRepository.findOne({
  //     where: { id },
  //   });

  //   if (!documentToDelete) {
  //     throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
  //   }

  //   try {
  //     await this.fileRepository.remove(documentToDelete);

  //     // Check if file exists before deleting
  //     if (fs.existsSync(documentToDelete.fileUrl)) {
  //       fs.unlinkSync(documentToDelete.fileUrl);
  //     } else {
  //       console.warn(`File not found: ${documentToDelete.fileUrl}`);
  //     }
  //   } catch (error) {
  //     console.error('Error during document deletion:', error);
  //     throw new HttpException(
  //       'Failed to delete document',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // updating documentStatus
  async updateDocumentStatus(documentId: string): Promise<DocumentUpload> {
    const document = await this.fileRepository.findOne({
      where: { id: documentId },
    });
    if (!document) {
      throw new HttpException('Document not found', HttpStatus.NOT_FOUND);
    }
    document.documentStatusId = '1';
    return await this.fileRepository.save(document);
  }

  // // save location with this service
  // async createUploadAdditional(
  //   additionalDescription: string,
  //   uploadType: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<{ message: string; data: DocumentUpload }> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', newFileName);

  //   // Allowed file extensions
  //   const allowedExtensions = [
  //     '.pdf',
  //     '.png',
  //     '.jpg',
  //     '.jpeg',
  //     '.gif',
  //     '.bmp',
  //     '.tiff',
  //   ];

  //   // Check if file extension is allowed
  //   if (!allowedExtensions.includes(fileExtension)) {
  //     throw new HttpException(
  //       'Unsupported file type. Only PDF and image files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Save the file to the server
  //   fs.writeFileSync(fileUrl, file.buffer);

  //   // Check for duplicate file names in the database
  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Create a new file record in the database
  //   const newFile = this.fileRepository.create({
  //     additionalDescription,
  //     uploadType,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   const savedFile = await this.fileRepository.save(newFile);

  //   return {
  //     message: 'File uploaded successfully',
  //     data: savedFile,
  //   };
  // }

  // additional upload on the new microservice
  // async createUploadAdditional(
  //   additionalDescription: string,
  //   uploadType: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<{ message: string; data: DocumentUpload }> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;

  //   // Allowed file extensions
  //   const allowedExtensions = [
  //     '.pdf',
  //     '.png',
  //     '.jpg',
  //     '.jpeg',
  //     '.gif',
  //     '.bmp',
  //     '.tiff',
  //   ];

  //   // Check if file extension is allowed
  //   if (!allowedExtensions.includes(fileExtension)) {
  //     throw new HttpException(
  //       'Unsupported file type. Only PDF and image files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Upload the file to the microservice
  //   const form = new FormData();
  //   form.append('file', file.buffer, newFileName);

  //   const remoteUploadUrl = 'https://api-gatway.kubaka.gov.rw/attachment/upload/';
  //   let fileUrl: string;

  //   try {
  //     const response = await axios.post(remoteUploadUrl, form, {
  //       headers: {
  //         ...form.getHeaders(),
  //       },
  //     });

  //     if (response.status !== 201) {
  //       throw new HttpException(
  //         'Failed to upload file to the remote server.',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }

  //     // Extract file URL from the response (assuming response.data contains it)
  //     fileUrl = response.data.fileUrl || `${remoteUploadUrl}${newFileName}`;
  //   } catch (error) {
  //     console.error('Error uploading file:', error.message);
  //     throw new HttpException(
  //       `Error uploading file: ${error.message}`,
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }

  //   // Check for duplicate file names in the database
  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Save metadata in the database
  //   const newFile = this.fileRepository.create({
  //     additionalDescription,
  //     uploadType,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   const savedFile = await this.fileRepository.save(newFile);

  //   return {
  //     message: 'File uploaded and metadata saved successfully.',
  //     data: savedFile,
  //   };
  // }

  async createUploadAdditional(
    additionalDescription: string,
    uploadType: string,
    applicationId: string,
    file: MulterFile,
  ): Promise<{ statusCode: number; message: string }> {
    const timestamp = Date.now();
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const fileNameWithoutExtension = path.basename(
      file.originalname,
      fileExtension,
    );
    const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;

    // Allowed file extensions
    const allowedExtensions = [
      '.pdf',
      '.png',
      '.jpg',
      '.jpeg',
      '.gif',
      '.bmp',
      '.tiff',
    ];

    // Check if file extension is allowed
    if (!allowedExtensions.includes(fileExtension)) {
      throw new HttpException(
        'Unsupported file type. Only PDF and image files are allowed.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Upload the file to the microservice
    const form = new FormData();
    form.append('file', file.buffer, newFileName);

    const remoteUploadUrl =
      // 'https://api-gatway.kubaka.gov.rw/attachment/upload/';
      `${config.documentMGT.docAPIDev}/attachment/upload/`;

    try {
      const response = await axios.post(remoteUploadUrl, form, {
        headers: {
          ...form.getHeaders(),
        },
      });

      if (response.status !== 201) {
        throw new HttpException(
          'Failed to upload file to the remote server.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (error) {
      console.error('Error uploading file:', error.message);
      throw new HttpException(
        `Error uploading file: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Check for duplicate file names in the database
    const dataFromDb = await this.fileRepository.findOne({
      where: { fileName: newFileName },
    });
    if (dataFromDb) {
      throw new HttpException(
        'File name already exists, Please change the name',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Save metadata in the database
    const fileUrl = `${remoteUploadUrl}${newFileName}`; // Use for database reference
    const newFile = this.fileRepository.create({
      additionalDescription,
      uploadType,
      applicationId,
      fileUrl,
      fileName: newFileName,
    });

    await this.fileRepository.save(newFile);

    // Return success response
    return {
      statusCode: 200,
      message: 'File uploaded successfully.',
    };
  }

  // async createUploadAdditional(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<{ message: string; data: DocumentUpload }> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;
  //   const fileUrl = path.join(__dirname, '..', 'docMgt', newFileName);

  //   if (fileExtension === '.xlsx' || fileExtension === '.xls') {
  //     // Check if the Excel file contains the required columns
  //     const workbook = xlsx.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const jsonData = xlsx.utils.sheet_to_json(worksheet, {
  //       header: 1,
  //     }) as unknown as string[][];
  //     const headerRow = jsonData[0] as string[];

  //     const requiredColumns = [
  //       'Item number',
  //       'Designation',
  //       'Unit',
  //       'QTY',
  //       'Unit Price(VAT inclusive)',
  //       'Total (VAT-inclusive)',
  //     ];

  //     const hasRequiredColumns = requiredColumns.every((col) =>
  //       headerRow.includes(col),
  //     );

  //     if (!hasRequiredColumns) {
  //       throw new HttpException(
  //         'The Excel file does not contain the required columns.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } else if (fileExtension !== '.pdf') {
  //     throw new HttpException(
  //       'Unsupported file type. Only Excel and PDF files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   fs.writeFileSync(fileUrl, file.buffer);

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   const savedFile = await this.fileRepository.save(newFile);

  //   return {
  //     message: 'File uploaded successfully',
  //     data: savedFile,
  //   };
  // }

  async findAllUploadAdditional(): Promise<DocumentUpload[]> {
    // Query the database to fetch applications with uploadType = 2
    const applications = await this.fileRepository.find({
      where: { uploadType: '2' },
    });

    return applications;
  }

  async findAllUploadAdditionalByApplicationId(
    applicationId: string,
  ): Promise<any[]> {
    const documents = await this.fileRepository.find({
      where: { applicationId, uploadType: '2' },
    });

    return documents;
  }

  // // when the upload is on different server
  // //   import axios from 'axios';
  // // import FormData from 'form-data';

  // async create(
  //   requiredDocumentId: string,
  //   applicationId: string,
  //   file: MulterFile,
  // ): Promise<{ message: string; data: DocumentUpload }> {
  //   const timestamp = Date.now();
  //   const fileExtension = path.extname(file.originalname).toLowerCase();
  //   const fileNameWithoutExtension = path.basename(
  //     file.originalname,
  //     fileExtension,
  //   );
  //   const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;

  //   if (fileExtension === '.xlsx' || fileExtension === '.xls') {
  //     // Check if the Excel file contains the required columns
  //     const workbook = xlsx.read(file.buffer, { type: 'buffer' });
  //     const sheetName = workbook.SheetNames[0];
  //     const worksheet = workbook.Sheets[sheetName];
  //     const jsonData = xlsx.utils.sheet_to_json(worksheet, {
  //       header: 1,
  //     }) as string[][];
  //     const headerRow = jsonData[0] as string[];

  //     const requiredColumns = [
  //       'Item number',
  //       'Designation',
  //       'Unit',
  //       'QTY',
  //       'Unit Price(VAT inclusive)',
  //       'Total (VAT-inclusive)',
  //     ];

  //     const hasRequiredColumns = requiredColumns.every((col) =>
  //       headerRow.includes(col),
  //     );

  //     if (!hasRequiredColumns) {
  //       throw new HttpException(
  //         'The Excel file does not contain the required columns.',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } else if (fileExtension !== '.pdf') {
  //     throw new HttpException(
  //       'Unsupported file type. Only Excel and PDF files are allowed.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Upload the file to the external server
  //   const formData = new FormData();
  //   formData.append('file', file.buffer, newFileName);

  //   const externalServerUrl = `https://api-gatway.kubaka.gov.rw/attachment/`;

  //   try {
  //     const response = await axios.post(externalServerUrl, formData, {
  //       headers: {
  //         ...formData.getHeaders(),
  //       },
  //     });

  //     if (response.status !== 200) {
  //       throw new HttpException(
  //         'Failed to upload file to the external server.',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } catch (error) {
  //     throw new HttpException(
  //       `Error uploading file to the external server: ${error.message}`,
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }

  //   const fileUrl = `https://api-gatway.kubaka.gov.rw/attachment/${newFileName}`;

  //   const dataFromDb = await this.fileRepository.findOne({
  //     where: { fileName: newFileName },
  //   });
  //   if (dataFromDb) {
  //     throw new HttpException(
  //       'File name already exists, Please change the name',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const newFile = this.fileRepository.create({
  //     requiredDocumentId,
  //     applicationId,
  //     fileUrl,
  //     fileName: newFileName,
  //   });

  //   const savedFile = await this.fileRepository.save(newFile);

  //   return {
  //     message: 'File uploaded successfully',
  //     data: savedFile,
  //   };
  // }

  // save file on new microservice
  async create(
    requiredDocumentId: string,
    applicationId: string,
    file: MulterFile,
  ) {
    const timestamp = Date.now();
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const fileNameWithoutExtension = path.basename(
      file.originalname,
      fileExtension,
    );
    const newFileName = `${fileNameWithoutExtension}_${timestamp}${fileExtension}`;

    // Validate file type and content
    if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      const workbook = xlsx.read(file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = xlsx.utils.sheet_to_json(worksheet, {
        header: 1,
      }) as unknown as string[][];
      const headerRow = jsonData[0] as string[];

      const requiredColumns = [
        'Item number',
        'Designation',
        'Unit',
        'QTY',
        'Unit Price(VAT inclusive)',
        'Total (VAT-inclusive)',
      ];

      const hasRequiredColumns = requiredColumns.every((col) =>
        headerRow.includes(col),
      );

      if (!hasRequiredColumns) {
        throw new HttpException(
          'The Excel file does not contain the required columns.',
          HttpStatus.BAD_REQUEST,
        );
      }
    } else if (fileExtension !== '.pdf') {
      throw new HttpException(
        'Unsupported file type. Only Excel and PDF files are allowed.',
        HttpStatus.BAD_REQUEST,
      );
    }
    // } else if (fileExtension !== '.pdf' && fileExtension !== '.zip') {
    //   throw new HttpException(
    //     'Unsupported file type. Only ZIP and PDF files are allowed.',
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // Upload the file to the remote server
    const form = new FormData();
    form.append('file', file.buffer, newFileName);

    const remoteUploadUrl =
      // 'https://api-gatway.kubaka.gov.rw/attachment/upload/';
      `${config.documentMGT.docAPIDev}/attachment/upload/`;

    let fileUrl: string;

    try {
      const response = await axios.post(remoteUploadUrl, form, {
        headers: {
          ...form.getHeaders(),
        },
      });

      console.log(response);

      if (response.status === 201) {
        fileUrl = response.data.url; // Adjust this according to the actual response structure
      } else {
        throw new HttpException(
          'Failed to upload file to the remote server.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (error) {
      console.error('Upload Error:', error.response?.data || error.message);
      throw new HttpException(
        `Error uploading file: ${error.response?.data || error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Save file metadata in the database
    try {
      const newFile = this.fileRepository.create({
        requiredDocumentId,
        applicationId,
        fileUrl, // Save the file URL returned from the microservice
        fileName: newFileName,
      });

      await this.fileRepository.save(newFile);
      return {
        statusCode: 200,
        message: 'File uploaded successfully.',
      };
    } catch (dbError) {
      console.error('Database Error:', dbError.message);
      throw new HttpException(
        'Error saving file metadata to the database.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // // function to save documents of the Document entity and return te saved document IDs
  // starting with chatboard file attachement

  async saveBulkDocuments(
    files: Express.Multer.File[],
    entityType: Entity_Type,
  ): Promise<string[]> {
    const savedDocumentIds: string[] = [];

    for (const file of files) {
      const docFileName = file.originalname;
      const form = new FormData();
      form.append('file', file.buffer, docFileName);

      const remoteUploadUrl = `${config.documentMGT.docAPIDev}/attachment/upload/`;

      try {
        const response = await axios.post(remoteUploadUrl, form, {
          headers: {
            ...form.getHeaders(),
          },
        });

        if (response.status !== 201) {
          throw new HttpException(
            'Failed to upload file to the remote server.',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      } catch (error) {
        console.error('Upload Error:', error.response?.data || error.message);
        throw new HttpException(
          `Error uploading file: ${error.response?.data || error.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const fileUrl = `${remoteUploadUrl}${docFileName}`;

      try {
        const document = this.documentsRepository.create({
          fileUrl,
          fileName: file.originalname,
          entityType,
          documentStatusId: '0',
          description: '',
        });

        const saved = await this.documentsRepository.save(document);
        savedDocumentIds.push(saved.id);
      } catch (dbError) {
        console.error('Database Error:', dbError.message);
        throw new HttpException(
          'Error saving file metadata to the database.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }

    return savedDocumentIds;
  }

  // find one document by its url for "Documents Entity"
  async findOneDocumentByUrl(fileUrl: string): Promise<Documents> {
    return await this.documentsRepository.findOne({ where: { fileUrl } });
  }

  async findOneDocumentById(documentId: string): Promise<Documents> {
    return await this.documentsRepository.findOne({
      where: { id: documentId },
    });
  }

  async getAttachmentsByIds(
    chatMessages: any[],
  ): Promise<any[]> {
    try {
      // loop thru each message to get attachment URLs
      const messagesWithAttachments = await Promise.all(
        chatMessages.map(async (message) => {
          if (message.documentIds && message.documentIds.length > 0) {
            const attachments = await Promise.all(
              message.documentIds.map(async (documentId: string) => {
                try {                   
                  return await this.findOneDocumentById(documentId);;
                } catch (error) {
                  console.error(
                    `Error getting URL for document ${documentId}:`,
                    error,
                  );
                }
              }),
            );
            return {
              ...message,
              documents: attachments,
            };
          } else {
            return {
              ...message,
              documents: [],
            };
          }
        }),
      );
      return messagesWithAttachments;
    } catch (error) {
      console.error('Error in getAttachmentsByIds service:', error);
      throw new Error('Failed to retrieve document URLs');
    }
  }
}
