<div class="app-gen appl-details">
    <div class="app-gen-header genvline">
        <section class="kbk-x-s kbk-aend sp-2">
            <div class="land-info">
                <div class="land-info-item">
                    <h3>Project</h3>
                    <div class="aline">
                        <div class="form-out">
                            <label>{{applicationDetails?.application?.projects.projectName}}</label>
                            <span>{{applicationDetails?.application?.projects?.upi}}</span>
                        </div>
                        <div class="form-out">
                            <label>Application</label>
                            <span>{{applicationDetails?.application?.applicationName}}</span>
                        </div>
                        <div class="form-out">
                            <label>Agency</label>
                            <span> {{applicationDetails?.application?.agencyCode}}</span>
                        </div>
                        <div class="form-out">
                            <label>Permit Type</label>
                            <span>{{applicationDetails?.application?.permitTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Category Type</label>
                            <span>{{applicationDetails?.application?.categoryTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Build Type</label>
                            <span>{{applicationDetails?.application?.buildTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Submitted On</label>
                            <span>{{applicationDetails?.application?.submittedDate | date}}</span>
                        </div>
                    </div>
                </div>
                <div class="btns" *ngIf="!this.inputData?.isBackNotAllowed">
                    <button type="button" class="kbk-btn kbk-btn-sec"
                        [routerLink]="['/account/all-applications/lists']">Back</button>
                </div>
            </div>
        </section>
    </div>
</div>
<div class="app-dash">
    <div class="app-gen-header hide">
        <div class="genline">
            <div class="hide">
                <span class="hder" aria-label="header tittle">Project:
                    {{applicationDetails?.application?.projects.projectName}}</span><br />
                <span class="hder" aria-label="header tittle">Application:
                    {{applicationDetails?.application?.applicationName}}</span>
                <span class="hder-sm" aria-label="header tittle">UPI :
                    {{applicationDetails?.application?.projects?.upi}}</span>
                <span class="hder-sm" aria-label="header tittle">Agency :
                    {{applicationDetails?.application?.agencyCode}}</span>
                <span class="hder-sm" aria-label="header tittle">Permit Type :
                    {{applicationDetails?.application?.permitTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Category Type :
                    {{applicationDetails?.application?.categoryTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Build Type :
                    {{applicationDetails?.application?.buildTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Application Submitted On :
                    {{applicationDetails?.application?.submittedDate | date}}</span>
            </div>
            <div class="btns hide" *ngIf="this.paramId">
                <button type="button" class="kbk-btn kbk-btn-sec"
                    [routerLink]="['/account/all-applications/lists']">Back</button>
            </div>
        </div>
    </div>
    <div class="dash-flex">
        <div class="app-l">
            <!-- <div class="progress-cards">
                <span class="progress-title"> Created on : {{applicationDetails[0].created_at | date}}</span>
                <div class="progr-bar">
                    <div class="progr-bar_item" role="progressbar"></div>
                </div>
            </div> -->
            <div class="progress-steps">
                <ul class="step-list">
                    <!-- <li class="step-list_item" *ngFor="let dt of applicationDetails">
                        <div class="step-list_titl">
                            <h4>Approval Level: {{dt.approvalLevels.name}}, Status: {{dt.approvalStatus.name}}</h4>
                            <h4>Date: {{dt.created_at | date}}</h4>
                        </div>
                        <div class="step-list_dscb">
                            <p>Comment: {{dt.comment}}</p>
                        </div>
                    </li> -->
                    <!--  -->
                    <span class="hder" aria-label="header tittle">Submission Logs</span>
                    <li class="step-list_item" *ngFor="let dt of applicationDetails.submissionLogs">
                        <div class="step-list_titl step-ln">
                            <!-- <h5>Project: {{applicationDetails.application?.projects?.projectName}}</h5> -->
                            <!-- <h5>Approval Level: <span>{{applicationDetails.approvalLevels?.name}}</span></h5> -->
                            <!-- <h5>Application Status: <span>{{dt.approvalStatus}}</span></h5> -->
                            <h5>Application Status: <span>{{dt.applicationStatusName}}</span></h5>
                            <h5>User: <span>{{dt.name}}</span></h5>
                            <h5>IP: <span>{{dt.ipAddress}}</span></h5>
                            <h5>OS: <span>{{dt.operatingSystem}}</span></h5>
                            <h5>Browser: <span>{{dt.browser}}</span></h5>
                            <h5>Date: <span>{{dt.createdAt | date}}</span></h5>
                        </div>
                    </li>
                    <span class="hder" aria-label="header tittle">Reviewers On Application </span>
                    <li class="step-list_item" *ngFor="let dt of applicationDetails.reviewersOnApplications">
                        <div class="step-list_titl step-ln">
                            <!-- <h5>Project: {{applicationDetails.application?.projects?.projectName}}</h5> -->
                            <!-- <h5>Approval Level: <span>{{applicationDetails.approvalLevels?.name}}</span></h5> -->
                            <!-- <h5>Assign by <span>{{dt.userId}}</span></h5> -->
                            <h5>Reviewer: <span>{{dt.name}}</span></h5>
                            <h5>IP: <span>{{dt.ipAddress}}</span></h5>
                            <h5>OS: <span>{{dt.operatingSystem}}</span></h5>
                            <h5>Browser: <span>{{dt.browser}}</span></h5>
                            <h5>Date: <span>{{dt.createdAt | date}}</span></h5>
                        </div>

                    </li>
                    <span class="hder" aria-label="header tittle">Check List</span>
                    <li class="step-list_item" *ngFor="let dt of applicationDetails.approvalCheckList">
                        <div class="step-list_titl step-ln">
                            <!-- <h5>Project: {{applicationDetails.application?.projects?.projectName}}</h5> -->
                            <h5>User: <span>{{dt.name}}</span></h5>
                            <h5>Application Status: <span>{{dt.approvalStatus?.name}}</span></h5>
                            <h5>Approval Level: <span>{{dt.approvalLevel?.name}}</span></h5>
                            <h5>IP: <span>{{dt.ipAddress}}</span></h5>
                            <h5>OS: <span>{{dt.operatingSystem}}</span></h5>
                            <h5>Browser: <span>{{dt.browser}}</span></h5>
                            <h5>Date: <span>{{dt.createdAt | date}}</span></h5>
                        </div>
                        <div class="step-list_dscb">
                            <p>Decision: {{dt.decision}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Conditions Of Approval: {{dt.conditionsOfApproval}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Structural Comment: {{dt.structuralComment}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Civil Engineering Comment: {{dt.civilEngineeringComment}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Architectural Comment: {{dt.architecturalComment}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Urban Planning Comment: {{dt.urbanPlanningComment}}</p>
                        </div>
                        <div class="step-list_dscb">
                            <p>Site Analysis Comment: {{dt.siteAnalysisComment}}</p>
                        </div>
                    </li>
                    <span class="hder" aria-label="header tittle">Approval Levels</span>
                    <li class="step-list_item" *ngFor="let dt of applicationDetails.approvals">
                        <div class="step-list_titl step-ln">
                            <!-- <h5>Project: {{applicationDetails.application?.projects?.projectName}}</h5> -->
                            <!-- <h5>Approval Level: <span>{{applicationDetails.approvalLevels?.name}}</span></h5> -->
                            <!-- <h5>Name: <span>{{dt.name}}</span></h5> -->
                            <h5>Application Status: <span>{{dt.approvalStatus?.name}}</span></h5>
                            <h5>Approval Level: <span>{{dt.approvalLevel?.name}}</span></h5>
                            <h5>IP: <span>{{dt.ipAddress}}</span></h5>
                            <h5>OS: <span>{{dt.operatingSystem}}</span></h5>
                            <h5>Browser: <span>{{dt.browser}}</span></h5>
                            <h5>Date: <span>{{dt.createdAt | date}}</span></h5>
                        </div>
                        <div class="step-list_dscb">
                            <p>Comment: {{dt.comment}}</p>
                        </div>
                    </li>
                    <span class="hder" aria-label="header tittle">Reviewers</span>
                    <li class="step-list_item" *ngFor="let dt of applicationDetails.reviewers">
                        <div class="step-list_titl step-ln">
                            <h5>Reviewer: <span>{{dt?.name}}</span></h5>
                        </div>
                        <!-- <div class="step-list_dscb">
                            <p>Comment: {{dt.comment}}</p>
                        </div> -->
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
