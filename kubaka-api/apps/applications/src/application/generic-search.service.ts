import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';


@Injectable()
export class GenericSearch<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.andWhere(whereSearch);
    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

export class GenericSearchWithMany<T> {
  async searchProjects(repository: Repository<T>, searchQuery: any) {
    let queryBuilder = repository.createQueryBuilder('project');

    if (searchQuery.upi) {
      queryBuilder = queryBuilder.andWhere('project.upi = :upi', {
        upi: searchQuery.upi,
      });
    }
    if (searchQuery.projectName) {
      queryBuilder = queryBuilder.andWhere(
        'project.projectName = :projectName',
        { projectName: searchQuery.projectName },
      );
    }
    if (searchQuery.created_at) {
      queryBuilder = queryBuilder.andWhere('project.created_at = :created_at', {
        created_at: searchQuery.created_at,
      });
    }
    const items = await queryBuilder.getMany();
    const totalCount = items.length;

    return { items, totalCount };
  }
}

@Injectable()
export class GenericSearchWithRelations<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
    relations?: string[],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.where(whereSearch);

    // Include relations if specified
    if (relations && relations.length > 0) {
      for (const relationName of relations) {
        queryBuilder.leftJoinAndSelect(`alias.${relationName}`, relationName);
      }
    }

    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

@Injectable()
export class GenericSearchWithRelations2<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
    relations?: string[],
    select?: (keyof T)[],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[String(field)] = ILike(`%${search}%`)),
    );
    queryBuilder.where(whereSearch);

    // Include relations if specified
    if (relations && relations.length > 0) {
      for (const relationName of relations) {
        queryBuilder.leftJoinAndSelect(`alias.${relationName}`, relationName);
      }
    }

    // Select specific fields if specified
    if (select && select.length > 0) {
      const selectFields = select.map((field) => `alias.${String(field)}`);
      queryBuilder.select(selectFields);
    }

    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}
