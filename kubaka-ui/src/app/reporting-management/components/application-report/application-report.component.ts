import { HttpClient } from '@angular/common/http';
import { Component } from '@angular/core';
import { AgGridAngular, ICellRendererAngularComp } from 'ag-grid-angular';
import {
  CellValueChangedEvent,
  ColDef,
  // ColumnApi,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  SelectionChangedEvent,
  ValueFormatterParams,
  CsvExportParams
} from 'ag-grid-community';
import { environment } from 'src/environments/environment';
import { ApplicationChartsComponent } from '../application-charts/application-charts.component';
import { SessionService } from 'src/app/authentication-services/session.service';
import { jwtDecode } from 'jwt-decode';
// import { GridApi } from '@ag-grid-community/core';


export class MissionResultRenderer implements ICellRendererAngularComp {
  // Init Cell Value
  public value!: string;
  agInit(params: ICellRendererParams): void {
    this.value = params.value ? 'tick-in-circle' : 'cross-in-circle';
  }

  // Return Cell Value
  refresh(params: ICellRendererParams): boolean {
    this.value = params.value;
    return true;
  }
}



export class CompanyLogoRenderer implements ICellRendererAngularComp {
  // Init Cell Value
  public value!: string;
  agInit(params: ICellRendererParams): void {
    this.value = params.value;
  }

  // Return Cell Value
  refresh(params: ICellRendererParams): boolean {
    this.value = params.value;
    return true;
  }
}

@Component({
  selector: 'app-application-report',
  templateUrl: './application-report.component.html',
  standalone: true,
  imports: [AgGridAngular, ApplicationChartsComponent],
  styleUrls: ['./application-report.component.scss']
})
export class ApplicationReportComponent {
  currentUser: any;
  private gridApi!: GridApi;
  // private gridColumnApi!: ColumnApi;

  // Return formatted date value
  dateFormatter(params: ValueFormatterParams) {
    return new Date(params.value).toLocaleDateString('en-us', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Row Data: The data to be displayed.
  rowData: any[] = [];

  // Column Definitions: Defines & controls grid columns.
  colDefs: ColDef[] = [
    {
      field: 'applicationName',
      width: 150,
      // checkboxSelection: true,
      headerName: 'Application No'
    },
    {
      field: 'projects.upi',
      width: 150,
      headerName: 'UPI'
    },

    {
      field: 'agencyCode',
      width: 150,
      // checkboxSelection: true,
      headerName: 'Agency'
    },

    {
      field: 'permitTypes.name',
      width: 150,
      headerName: 'Permit Type'
    },
    {
      field: 'categoryTypes.name',
      width: 150,
      headerName: 'Category Type'
    },
    {
      field: 'buildTypes.name',
      width: 150,
      headerName: 'Build Type'
    },
    {
      field: 'projects.projectName',
      width: 150,
      headerName: 'Project Name'
      // checkboxSelection: true,
    },
    {
      field: 'projects.ownerFullName',
      width: 150,
      headerName: 'Owner FullName'
    },
    {
      field: 'projects.ownerIdNo',
      width: 150,
      headerName: 'Owner ID'
    },
    {
      field: 'projects.selectedCategoryUse',
      width: 150,
      headerName: 'Category Use'
    },
    {
      field: 'projects.selectedUse',
      width: 150,
      headerName: 'Selected Use'
    },
    {
      field: 'projects.districtName',
      width: 150,
      headerName: 'District'
    },
    {
      field: 'projects.sectorName',
      width: 150,
      headerName: 'Sector'
    },
    {
      field: 'projects.cellName',
      width: 150,
      headerName: 'Cell'
    },
    {
      field: 'projects.villageName',
      width: 150,
      headerName: 'village'
    },
    {
      field: 'projects.plotSize',
      width: 150,
      headerName: 'Plot size '
    },
    {
      field: 'projects.isUnderMortgage',
      width: 150,
      headerName: 'Mortgage Status'
    },
    {
      field: 'projects.isUnderRestriction',
      width: 150,
      headerName: 'Restriction Status'
    },
    {
      field: 'buildUpArea',
      width: 150,
      headerName: 'Build Up Area'
    },
    {
      field: 'grossFloorArea',
      width: 150,
      headerName: 'Gross Floor Area'
    },
    {
      field: 'numberOfParkingSpace',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'numberOfDwellingUnits',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'DescriptionOfOperation',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'percentageSpaceUse',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'waterConsumption',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'electricityConsumption',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'DistanceToTheNearestLandIn',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'ProjectCostInUSD',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'ProjectCostInRwf',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'created_at',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'submittedDate',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'resubmittedDate',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'applicationStatus.name',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'isNonObjection',
      width: 150,
      // headerName: 'Gross Floor Area'
    },
    {
      field: 'isAssociatedUpi',
      width: 150,
      // headerName: 'Gross Floor Area'
    },

    // invoices
    {
      headerName: 'Invoice Numbers',
      width: 150,
      valueGetter: (params) => {
        return (params.data.invoices as { invoiceNumber: string }[])
          ?.map((inv) => inv.invoiceNumber)
          .join(', ') || '';
      }
    },
    {
      headerName: 'Amounts',
      width: 150,
      valueGetter: (params) => {
        return (params.data.invoices as { amount: string }[])?.map(inv => inv.amount).join(', ') || '';
      }
    },
    {
      headerName: 'External References',
      width: 150,
      valueGetter: (params) => {
        return (params.data.invoices as { externalReferenceNumber: string }[])?.map(inv => inv.externalReferenceNumber).join(', ') || '';
      }
    },
    {
      headerName: 'Due Dates',
      width: 150,
      valueGetter: (params) => {
        return (params.data.invoices as { dueDate: string }[])?.map(inv => new Date(inv.dueDate).toLocaleDateString()).join(', ') || '';
      }
    },
    {
      headerName: 'created_at Dates',
      width: 150,
      valueGetter: (params) => {
        return (params.data.invoices as { created_at: string }[])?.map(inv => new Date(inv.created_at).toLocaleDateString()).join(', ') || '';
      }
    },
    {
      headerName: 'Invoice Statuses',
      width: 150,
      valueGetter: (params) => {
        const invoices = params.data.invoices as {
          invoiceStatus: {
            name: string;
          };
        }[];

        return invoices?.map((inv) => inv.invoiceStatus?.name).join(', ') || '';
      }
    },

        // certificates
        {
          headerName: 'certificate Numbers',
          width: 150,
          valueGetter: (params) => {
            return (params.data.certificates as { certificateNumber: string }[])
              ?.map((cert) => cert.certificateNumber)
              .join(', ') || '';
          }
        },
        {
          headerName: 'Expired Dates',
          width: 150,
          valueGetter: (params) => {
            return (params.data.certificates as { expiredDate: string }[])?.map(cert => new Date(cert.expiredDate).toLocaleDateString()).join(', ') || '';
          }
        },
        {
          headerName: 'Permitted At',
          width: 150,
          valueGetter: (params) => {
            return (params.data.certificates as { created_at: string }[])?.map(cert => new Date(cert.created_at).toLocaleDateString()).join(', ') || '';
          }
        },
        {
          headerName: 'expiredDate At',
          width: 150,
          valueGetter: (params) => {
            return (params.data.certificates as { expiredDate: string }[])?.map(cert => new Date(cert.expiredDate).toLocaleDateString()).join(', ') || '';
          }
        },


    // {
    //   field: 'company',
    //   width: 130,
    //   cellRenderer: CompanyLogoRenderer,
    // },
    // {
    //   field: 'location',
    //   width: 225,
    // },
    // {
    //   field: 'date',
    //   valueFormatter: this.dateFormatter,
    // },
    // {
    //   field: 'price',
    //   width: 130,
    //   valueFormatter: (params) => {
    //     return '£' + params.value.toLocaleString();
    //   },
    // },
    // {
    //   field: 'successful',
    //   width: 120,
    //   cellRenderer: MissionResultRenderer,
    // },
    // { field: 'rocket' },
  ];

  // Default Column Definitions: Apply configuration across all columns
  defaultColDef: ColDef = {
    filter: true, // Enable filtering on all columns
    editable: true, // Enable editing on all columns
  };

  // Load data into grid when ready
  constructor(private http: HttpClient,
    private sessionService: SessionService
  ) {

    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    // this.onGridReady('');

    if (this.currentUser.data.user.role.code === 'ADM') {
      this.http
      .get<any[]>(
        environment.applicationUrl + 'application/application'
      )
      .subscribe((data) => (this.rowData = data));
    } else {
      this.http
      .get<any[]>(
        environment.applicationUrl + 'application/application/agency/' + this.currentUser.data.user.agency.id
      )
      .subscribe((data) => (this.rowData = data));
    }

  }
  onGridReady(params: GridReadyEvent) {
    this.gridApi = params.api;
    // this.gridColumnApi = params.columnApi;
    setTimeout(() => {
      // this.http
      //   .get<any[]>(environment.applicationUrl + 'application/application')
      //   .subscribe((data) => (this.rowData = data));
      if (this.currentUser.data.user.role.code === 'ADM') {
        this.http
        .get<any[]>(
          environment.applicationUrl + 'application/application'
        )
        .subscribe((data) => (this.rowData = data));
      } else {
        this.http
        .get<any[]>(
          environment.applicationUrl + 'application/application/agency/' + this.currentUser.data.user.agency.id
        )
        .subscribe((data) => (this.rowData = data));
      }
    }, 0);
  }

  // Handle row selection changed event
  onSelectionChanged = (event: SelectionChangedEvent) => {
  };

  // Handle cell editing event
  onCellValueChanged = (event: CellValueChangedEvent) => {
  };


  onExport() {
    const params: CsvExportParams = {
      fileName: 'applications.csv',
      columnSeparator: ',',
    };
    this.gridApi.exportDataAsCsv(params);
  }
}
