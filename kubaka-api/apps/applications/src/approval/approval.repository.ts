import { AbstractRepository } from '@app/common';
import { Injectable } from '@nestjs/common';
import {
  ApplicationApproval,
  ApplicationApprovalCheckList,
  ApprovalDocument,
  ApprovalLevel,
  ApprovalStatus,
  PermitCheckList,
} from '../entities/approval.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';

@Injectable()
export class ApprovalLevelRepository extends AbstractRepository<ApprovalLevel> {
  constructor(
    @InjectRepository(ApprovalLevel)
    approvalLevelRepository: Repository<ApprovalLevel>,
    entityManager: EntityManager,
  ) {
    super(entityManager, approvalLevelRepository);
  }
}
@Injectable()
export class ApprovalStatusRepository extends AbstractRepository<ApprovalStatus> {
  constructor(
    @InjectRepository(ApprovalStatus)
    approvalStatusRepository: Repository<ApprovalStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, approvalStatusRepository);
  }
}

@Injectable()
export class ApplicationApprovalRepository extends AbstractRepository<ApplicationApproval> {
  constructor(
    @InjectRepository(ApplicationApproval)
    applicationApprovalRepository: Repository<ApplicationApproval>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationApprovalRepository);
  }
}

@Injectable()
export class PermitCheckListRepository extends AbstractRepository<PermitCheckList> {
  constructor(
    @InjectRepository(PermitCheckList)
    permitCheckListRepository: Repository<PermitCheckList>,
    entityManager: EntityManager,
  ) {
    super(entityManager, permitCheckListRepository);
  }
}

@Injectable()
export class ApplicationApprovalCheckListRepository extends AbstractRepository<ApplicationApprovalCheckList> {
  constructor(
    @InjectRepository(ApplicationApprovalCheckList)
    applicationApprovalCheckListRepository: Repository<ApplicationApprovalCheckList>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationApprovalCheckListRepository);
  }
}

@Injectable()
export class ApprovalDocumentRepository extends AbstractRepository<ApprovalDocument> {
  constructor(
    @InjectRepository(ApprovalDocument)
    approvalDocumentRepository: Repository<ApprovalDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, approvalDocumentRepository);
  }
}
