{"name": "kubaka-web-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "dev": "ng serve --configuration=development --source-map --live-reload --port=4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@ag-grid-community/angular": "^31.2.0", "@ag-grid-community/client-side-row-model": "^31.2.0", "@ag-grid-community/core": "^31.2.0", "@ag-grid-enterprise/charts-enterprise": "^31.2.0", "@ag-grid-enterprise/menu": "^31.2.0", "@ag-grid-enterprise/row-grouping": "~31.2.0", "@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/google-maps": "^15.2.9", "@angular/material": "^16.2.12", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ng-bootstrap/ng-bootstrap": "^15.1.2", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "ag-charts-angular": "^9.3.2", "ag-charts-enterprise": "^10.1.0", "ag-grid-angular": "^31.2.0", "bootstrap": "^5.3.2", "crisp-sdk-web": "^1.0.22", "file-saver": "^2.0.5", "highcharts": "^11.4.7", "highcharts-angular": "^3.1.2", "html2pdf.js": "^0.10.1", "jwt-decode": "^4.0.0", "kubaka-web-app": "file:", "ng-qrcode": "^16.0.0", "ngx-csv": "^0.3.2", "rxjs": "~7.8.0", "sweetalert2": "^11.10.2", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.11", "@angular/cli": "^16.2.11", "@angular/compiler-cli": "^16.2.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "@types/lodash": "^4.17.15", "@types/node": "^22.10.7", "@types/xlsx": "^0.0.36", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}, "volta": {"node": "18.20.6"}}