import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

export enum Entity_Type {
  CHAT = 'CHAT',
  APPLICATION = 'APPLICATION',
  PROFILE = 'PROFILE',
  APPROVAL = 'APPROVAL',
  OTHER = 'OTHER', // other specifi entity types can be added here
}

@Entity('documents')
export class Documents {
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column()
  public fileUrl: string;

  @Column()
  public fileName: string;

  @Column({ nullable: true })
  public description?: string;

  @Column({ nullable: true })
  public documentStatusId?: string;

  @Column({ nullable: true })
  public uploadedBy?: string; // user id if available... if user logged in

  @Column({
    type: 'enum',
    enum: Entity_Type,
    default: Entity_Type.OTHER,
  })
  public entityType: Entity_Type;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public createdAt!: Date;
}
