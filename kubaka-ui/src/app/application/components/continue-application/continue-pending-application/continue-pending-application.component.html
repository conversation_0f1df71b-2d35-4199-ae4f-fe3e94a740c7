<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"continuePendingApplication.step" | translate }} <span>3</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{ upiInfo?.isFromOldSystem ? ('continuePendingApplication.oldSystemHeaderNote' | translate) : ('continuePendingApplication.developmentDetails' | translate) }}</h2>
                    <div class="form-incheckbox">
                        <label class="checkbox">
                            <input style="display: none;" type="checkbox" id="check" [checked]="isAssociated"
                                (click)="checkAssociated(associatedUpiContent)" data-bs-toggle="modal" type="button"
                                data-bs-target="#showModal" />
                            <span class="checkbox_box"></span>
                            <span class="checkbox_txt">{{"continuePendingApplication.checkToAddAssociatedUpi" | translate }}</span>
                        </label>
                    </div>
                </div>
                <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"continuePendingApplication.projectName" | translate }}</label>
                                <div>
                                    <textarea name="projectName" formControlName="projectName" id="projectName"
                                        cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.projectDescription" | translate }}</label>
                                <div>
                                    <textarea name="projectDescription" formControlName="projectDescription"
                                        id="projectDescription" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"continuePendingApplication.permitType" | translate }}</label>
                                <div>
                                    <select [class.disabled-select]="'true'" name="permitTypeId" id="permitTypeId" formControlName="permitTypeId"
                                          >
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.buildingType" | translate }}</label>
                                <div>
                                    <select [class.disabled-select]="'true'" name="buildTypeId"
                                        id="buildTypeId" formControlName="buildTypeId"
                                     required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.plotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.buildUpArea" | translate }} <span class="estrx">*</span>
                                </label>
                                <div>
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                        (keyup)="calculateGrossFlowArea()" noNegative
                                        required>
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> {{"continuePendingApplication.buildUpAreaError" | translate }}</div>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.numberOfFloor" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor"
                                    (keyup)="calculateGrossFlowArea()" name="numberOfFloor"
                                        formControlName="numberOfFloor" required>
                                </div>
                            </div>

                            <div class="form-input">
                                <label>{{"continuePendingApplication.grossFloorArea" | translate }}</label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                    noNegative
                                        formControlName="grossFloorArea"
                                        readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.numberOfParkingSpaces" | translate }}</label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace"
                                        noNegative
                                         required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.estimatedPriceOfDwellingUnitRWF" | translate }}</label>
                                <div>
                                    <input type="text" appCommaSeparator id="priceOfDwellingUnitRwf" name="priceOfDwellingUnitRwf"
                                        formControlName="priceOfDwellingUnitRwf" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.numberOfDwellingUnits" | translate }}</label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.descriptionOfOperations" | translate }}</label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.percentageSpaceUse" | translate }}</label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse"
                                         readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.estimatedWaterConsumption" | translate }}</label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption"
                                        noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.estimatedElectricityConsumption" | translate }}</label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption"
                                        noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.distanceToLandline" | translate }}</label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn"
                                        formControlName="DistanceToTheNearestLandIn"
                                        noNegative
                                        required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.estimatedProjectCostUSD" | translate }}</label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.estimatedProjectCostRWF" | translate }}</label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInRwf" name="ProjectCostInRwf"
                                        formControlName="ProjectCostInRwf" required>
                                </div>
                            </div>
                            <!-- <div class="form-input">
                                <label>Technology Survey</label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId" required>
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div> -->
                            <div class="form-input">
                                <label>{{"continuePendingApplication.capacityInformation" | translate }}</label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation"
                                        noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.selectedCategoryUse" | translate }}</label>
                                <div>
                                    <input type="text" id="selectedCategoryUse" name="selectedCategoryUse"
                                        formControlName="selectedCategoryUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"continuePendingApplication.selectedUse" | translate }}</label>
                                <div>
                                    <input type="text" id="selectedUse" name="selectedUse"
                                        formControlName="selectedUse" readonly
                                         required>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" formControlName="isFromOldSystem"
                                    [disabled]="isReadOnly" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"continuePendingApplication.isFromOldSystem" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" name="isUnderMortgage"
                                        formControlName="isUnderMortgage"
                                        [disabled]="isReadOnly"/>
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"continuePendingApplication.isUnderMortgage" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" name="isUnderRestriction"
                                        formControlName="isUnderRestriction"
                                        [disabled]="isReadOnly" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"continuePendingApplication.isUnderRestriction" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="step-panel_footer">
                        <button (click)="cancel()">{{"continuePendingApplication.cancel" | translate }}</button>
                        <button type="submit" [disabled]="userForm.invalid">{{"continuePendingApplication.update" | translate }}</button>
                        <button *ngIf="applicationDetail.id" type="button" (click)="next()">{{"continuePendingApplication.next" | translate }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>




<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"continuePendingApplication.associatedUpi" | translate }}</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
            (backToParent)="closeAssociatedPopup($event)"
            (backToParentAndSubmitCombining)="getNewCombiningAssociated($event)"></app-associated-upi-application>
    </div>
</ng-template>
