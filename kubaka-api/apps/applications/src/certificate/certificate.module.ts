import { forwardRef, Module } from '@nestjs/common';
import { CertificateService } from './certificate.service';
import { CertificateController } from './certificate.controller';
import { DatabaseModule } from '@app/common';
import {
  Certificate,
  DevelopmentStatus,
  RandomInspection,
  Signatory,
  SiteStatus,
  TransferCertificate,
} from '../entities/certificate.entity';
import { Application } from '../entities/application.entity';
import { Invoice } from '../entities/invoice.entity';
import {
  CertificateRepository,
  DevelopmentStatusRepository,
  RandomInspectionRepository,
  SignatoryRepository,
  SiteStatusRepository,
  TransferCertificateRepository,
} from './certificate.repository';
import {
  GenericSearch,
  GenericSearchWithRelations,
  GenericSearchWithRelations2,
} from './generic-search.service';
import { AUTH_SERVICE } from '@app/common/constants';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ApplicationModule } from '../application/application.module';
import { InvoiceModule } from '../invoice/invoice.module';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      // certificate
      Certificate,
      Signatory,
      TransferCertificate,
      RandomInspection,
      SiteStatus,
      DevelopmentStatus,
      Application,
      Invoice,
    ]),

    // import app
    ApplicationModule,

    forwardRef(() => InvoiceModule),

    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [CertificateController],
  providers: [
    CertificateService,
    CertificateRepository,
    SignatoryRepository,
    TransferCertificateRepository,

    RandomInspectionRepository,
    SiteStatusRepository,
    DevelopmentStatusRepository,

    GenericSearch,
    GenericSearchWithRelations,
    GenericSearchWithRelations2,
  ],
  exports: [CertificateModule, CertificateService],
})
export class CertificateModule {}
