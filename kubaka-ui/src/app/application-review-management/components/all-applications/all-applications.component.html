<!-- Applicant Applications -->
<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <div class="app-welnote">
        <div class="app-welnote_dtails">
          <span class="prim-nt">Welcome! </span>
          <h3>
            <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
          </h3>
        </div>
      </div>
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle"> Applications </span>
            <div class="tbleFilter">
              <div class="form-input_search">
                <!-- <label>Filter by date</label> -->
                <input type="text" name="searchTerm" [(ngModel)]="userService.searchTerm"
                  (ngModelChange)="onSearchTermChange($event)" placeholder="Search for something..." />
                <button type="button" class="btn">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>From date</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateFrom" [(ngModel)]="searchData.dateFrom"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>To date</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateTo" [(ngModel)]="searchData.dateTo"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>Application Status</Strong> </label>
                <div>
                  <select name="filterByApplicationStatus" id="filterByApplicationStatus"
                    [(ngModel)]="applicationDetail.filterByApplicationStatus" 
                    (change)="changeByApplicationStatus()">
                    <option value="all">All applications</option>
                    <option *ngFor="let r of applicationStatuses" [value]="r.id"> {{ r.name }} </option>
                  </select>
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>Page size</Strong> </label>
                <div>
                  <select name="pageSize" id="pageSize" [(ngModel)]="pageSize" (change)="changePageSize()">
                    <!-- <option disabled value="">All</option> -->
                    <option *ngFor="let r of utilService.tableArraySelector" [value]="r.id"> {{ r.id }} </option>
                  </select>
                </div>
              </div>
              <div class="filter-group">
                <button class="download-btn" (click)="exportApplicationsToExcel()">
                  Download Excel
                </button>
              </div>
            </div>
          </div>
          <ul class="tblist">

            <li class="tblist-item" *ngFor="let li of lists" [ngClass]="{
              'red-background': li.daysBetween > li.permitTypes.issuedDays && utilService.isPendingStatus(li.applicationStatus?.code),
              'yellow-background': li.daysBetween == li.permitTypes.issuedDays && utilService.isPendingStatus(li.applicationStatus?.code),
              'green-background': li.daysBetween < li.permitTypes.issuedDays && utilService.isPendingStatus(li.applicationStatus?.code)
            }">

              <!-- <span>
                <span class="ttl">Days between</span> {{ li.daysBetween }} </span>
              <span> <span class="ttl">Permit Types issue days</span> {{ li.permitTypes.issuedDays }} </span> -->


              <!-- <div class="tblist-item_ck">
                                <label class="form-checkbox">
                                    <input type="checkbox">
                                </label>
                            </div> -->
              <div class="tblist-item_icon">
                <img class="icon-dft" src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                <img class="icon-done" src="assets/ikons/colored/ikon-file-green.svg" alt="" />
                <img class="icon-error" src="assets/ikons/colored/ikon-file-red.svg" alt="" />
                <img class="icon-wait" src="assets/ikons/colored/ikon-file-yellow.svg" alt="" />
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">UPI</span> {{ li.projects.upi }} </span>
                <span>
                  <span class="ttl">Permit type</span> {{ li.permitTypes.name }}
                </span>
                <span>
                  <span class="ttl">Submission Status</span> {{ li.isResubmitted ? 'Resubmitted': 'Submitted' }}
                </span>
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">Application No</span> {{ li.applicationName }} </span>
                <span>
                  <span class="ttl">Project Name</span>{{ li.projects?.projectName | titlecase }}</span>
              </div>
              <!-- <div class="tblist-item_dt">
                <span class="ttl"></span> {{ li.applicationName }}
              </div> -->
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Category</span> {{ li.categoryTypes.name }} </span>
                <span>
                  <span class="ttl">Build Type</span> {{ li.buildTypes.name }} </span>
                <span>
                  <span class="ttl">Site Location</span> {{ li.projects.villageName }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Created On</span> {{ li.created_at | date:'yyyy-MM-dd HH:mm:ss' }} <span
                    class="ttl">Submitted date</span> {{ li.submittedDate | date:'yyyy-MM-dd HH:mm:ss' }} </span>
                <span [ngClass]="{
                      'red-background': li.isLocked, }">
                  <span class="ttl">Mode</span> {{ li.isLocked ? 'Locked': 'Accessible' }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">No of Reviewer</span> {{ li.count }} </span>
              </div>
              <!-- *ngIf="li.applicationStatus?.code == 'SUB'  ||
                li.applicationStatus?.code == 'RSMB'
                || li.applicationStatus?.code == 'UNRV'" -->
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Days between</span> {{ li.daysBetween }}
                </span>
                <span *ngIf="
                        li.applicationStatus.code === 'CTFD'">
                  <span class="ttl">Day It Takes</span> {{ li.numberOfDayItTakes }}
                </span>
              </div>
              <div class="tblist-item_status">
                <span class="bdg bdg-pend"> {{ li.applicationStatus?.name }}</span>
              </div>
              <div class="tblist-item_xcn">
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="edit">
                  <img src="assets/ikons/colored/ikon-edit.svg" alt="" />
                </a>
                <a class="kbk-link-btn hs-tp" data-kbk-tooltip="delete">
                  <img src="assets/ikons/colored/ikon-trash.svg" alt="" />
                </a> -->
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="view details"
                  [routerLink]="['/account/all-applications/application-detail/', li.id]">
                  <img src="assets/ikons/colored/ikon-eye.svg" alt="" />
                </a> -->
                <!-- <a class="kbk-link-btn">
                                    <img src="assets/ikons/colored/ikon-eye.svg" alt="">
                                </a> -->
                <!-- the original one -->
                <div class="kbk-table-dropdown">
                  <button class="kbk-link-btn">More</button>
                  <div class="kbk-table-dropdown_list">
                    <a *ngIf="li.applicationStatus?.code !== 'PND'" class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      data-kbk-tooltip="view details" [class.disabled]="li.isLocked && (
                      currentUser.data.user.role.code !== 'DRCT'
                      && currentUser.data.user.role.code !== 'SINSP'
                      )" data-bs-toggle="modal" id="create-btn" [routerLink]="li.isLocked && (
                        currentUser.data.user.role.code !== 'DRCT'
                        && currentUser.data.user.role.code !== 'SINSP'
                      ) ? null :
                       [
                        '/account/all-applications/application-detail/',
                        li.id
                      ]">View </a>
                    <a *ngIf="
                        (currentUser.data.user.role.code === 'DRCT' ||
                        currentUser.data.user.role.code === 'OFCMNG' ||
                        currentUser.data.user.role.code === 'TLD' ||
                        currentUser.data.user.role.code === 'SINSP' ||
                        currentUser.data.user.role.code === 'DINSP') &&
                        li.applicationStatus.code !== 'CTFD' &&
                        li.applicationStatus?.code !== 'PND' && permissions.isAllowToAssignApplication
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser.data.user.role.code !== 'DRCT'"
                      data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                      (click)="li.isLocked &&
                      currentUser.data.user.role.code !== 'DRCT' ? null : assignToUser(openToAssign, li, 'lg')">Assign</a>
                    <a *ngIf="
                        (currentUser.data.user.role.code === 'DRCT' ||
                        currentUser.data.user.role.code === 'OFCMNG' ||
                        currentUser.data.user.role.code === 'TLD' ||
                        currentUser.data.user.role.code === 'SINSP' ||
                        currentUser.data.user.role.code === 'DINSP') &&
                        li.applicationStatus?.code !== 'PND'
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser.data.user.role.code !== 'DRCT'" id="create-btn"
                      [routerLink]="li.isLocked && currentUser.data.user.role.code !== 'DRCT' ? null : ['/account/all-applications/view-approval-process/', li.id]">Process</a>
                    <a *ngIf="
                        (currentUser.data.user.role.code === 'DRCT' ||
                        currentUser.data.user.role.code === 'OFCMNG' ||
                        currentUser.data.user.role.code === 'TLD' ||
                        currentUser.data.user.role.code === 'SINSP' ||
                        currentUser.data.user.role.code === 'DINSP') &&
                        li.applicationStatus?.code !== 'PND'
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser.data.user.role.code !== 'DRCT'" id="create-btn"
                      [routerLink]="li.isLocked && currentUser.data.user.role.code !== 'DRCT' ? null : ['/account/all-applications/view-process-history/', li.id]">History</a>
                    <a *ngIf="
                        li.applicationStatus.code === 'CTFD'
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm" id="create-btn" (click)="viewCertificate2(li?.id)">
                      Permit</a>
                    <!-- <a *ngIf="
                        (currentUser.data.user.role.code === 'DRCT' ||
                        currentUser.data.user.role.code === 'ADM' ||
                        currentUser.data.user.role.code === 'OFCMNG' ||
                        currentUser.data.user.role.code === 'TLD' ||
                        currentUser.data.user.role.code === 'SINSP' ||
                        currentUser.data.user.role.code === 'DINSP') &&
                        li.applicationStatus.code === 'CTFD' &&
                        li.applicationStatus?.code !== 'PND'
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser.data?.user?.role?.code !== 'DRCT'" id="create-btn"
                      (click)="li.isLocked && currentUser?.data?.user?.role?.code !== 'DRCT' ?
                       null : viewCertificate(li.certificates[0])"> Permit</a> -->
                    <a *ngIf="
                        (currentUser.data.user?.role?.code === 'ADM' || currentUser.data.user?.role?.code === 'DRCT') &&
                        li.applicationStatus?.code === 'CTFD' &&
                        li.applicationStatus?.code !== 'PND' &&
                        (li?.invoices)
                      " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser?.data?.user?.role?.code !== 'DRCT'" id="create-btn"
                      [routerLink]="['/account/payment/invoice-detail-water-mark/',li?.invoices[0]?.id,li?.id]">Invoice</a>
                    <a *ngIf="
                    currentUser.data.user.role.code === 'DRCT' &&
                    li.applicationStatus?.code !== 'PND'
                    && permissions.isAllowToLockApplication
                    " class="kbk-btn kbk-btn-sec kbk-btn-sm"
                      [class.disabled]="li.isLocked && currentUser.data.user.role.code !== 'DRCT'" id="create-btn"
                      (click)="viewLockUnlock(lockApplicationModel, li, 'md')">{{li.isLocked ? 'Unlock' : 'Lock'}}</a>
                  </div>
                </div>
                <!-- the original one -->
              </div>
            </li>
            <li *ngIf="lists.length === 0" class="kbk-empty">
              <div class="kbk-empty_cont">
                <!-- <span class="kbk-empty_icon">No data found</span> -->
                <span class="kbk-empty_ttl">No data found</span>
              </div>
            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                <!-- <span class="ent">{{ totalRecords }}</span>
                <span class="cur">{{ startIndex }} - {{ endIndex }}</span> -->
                <span class="ent">{{ totalRecords }}</span>
                <span class="cur">{{ startIndex }} - {{ endIndex }}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <!-- <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="getPremiumData()">
              </ngb-pagination> -->
              <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="loadList()">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #openToAssign role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Assign Reviewers</h2>
    <!-- <p> Users </p> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="outputData.isAssigningData">
    <app-assign-application-to-user [inputData]="outputData"
      (backToParent)="closePopup($event)"></app-assign-application-to-user>
  </div>
</ng-template>
<ng-template #isNewPermit role="document" let-modal>
  <div class="modol-header" *ngIf="!applicationDetail.isNewPermit">
    <h2 id="exampleModalLabel">Is it new permit</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="!applicationDetail.isNewPermit">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="isNewPermitM('no')"> No </button>
      <button class="kbk-btn kbk-btn-main" type="button" (click)="isNewPermitM('yes')"> Yes </button>
    </div>
  </div>
  <div class="modol-header" *ngIf="applicationDetail.isNewPermit">
    <h2 id="exampleModalLabel">Fill certificate number</h2>
    <!-- <p>Track what is permitted, prohibited and conditional to be built in your plot</p> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="step-panel_body" *ngIf="applicationDetail.isNewPermit">
    <form>
      <div class="form-set">
        <div class="form-input">
          <label>Licence</label>
          <div>
            <input type="text" id="certificateNumber" name="certificateNumber"
              [(ngModel)]="applicationDetail.certificateNumber" required />
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modol-content" *ngIf="applicationDetail.isNewPermit">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> Cancel </button>
      <button class="kbk-btn kbk-btn-main" type="button" (click)="submitCertificateNumber()"> Submit </button>
    </div>
  </div>
</ng-template>
<ng-template #lockApplicationModel let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel">You are about to {{outputData.isLocked ? 'Unlock':'Lock'}} application ?</h2>
      <!-- <p>Deleting your user will remove all of your information from our database.</p> -->
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <!-- <div class="mt-md">
              <h4>You are about to delete a User ?</h4>
          </div> -->
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close">Close</button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="confirmLockUnlock()">Yes
          {{outputData.isLocked ? 'Unlock':'Lock'}}!</button>
      </div>
    </div>
  </div>
</ng-template>