<!-- Applicant Invoices -->
<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-welnote">
                <div class="app-welnote_dtails">
                    <span class="prim-nt">Welcome!</span>
                    <h3>
                        <span> {{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
                    </h3>
                </div>
            </div>
            <div class="app-lists">
                <div class="app-tblist">
                    <div class="app-tblist_title">
                        <span class="hder" aria-label="header tittle">Transfered Permits</span>
                        <div class="btns">
                            <div class="form-input w-aut clear-m">
                              <div class="form-input_search">
                                <input type="text" name="searchTerm" [(ngModel)]="applicationService.searchTerm"
                                (ngModelChange)="onSearchTermChange($event)" placeholder="Search for something...">
                                <button type="button" class="btn">
                                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <g data-name="Layer 2">
                                      <g data-name="search">
                                        <rect width="24" height="24" opacity="0" />
                                        <path
                                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                                      </g>
                                    </g>
                                  </svg>
                                </button>
                              </div>
                            </div>
                        </div>
                    </div>
                    <ul class="tblist">
                        <li class="tblist-item" *ngFor="let li of lists">
                            <div class="tblist-item_icon icon-sm bg-l-o">
                                <img src="assets/ikons/colored/ikon-calendar.svg" alt="" />
                            </div>
                            <div class="tblist-item_dt">
                              <span>
                                <span class="ttl">Name</span> {{ li.firstName }}  {{ li.lastName }}
                              </span>

                            </div>
                            <div class="tblist-item_dt">
                              <span>
                                <span class="ttl">Document Number</span> {{ li.documentNumber }}
                              </span>

                            </div>
                            <div class="tblist-item_dt">
                              <span>
                                <span class="ttl">Permit Number</span> {{ li.certificateNumber }}
                              </span>

                            </div>
                            <div class="tblist-item_dt">
                              <span>
                                <span class="ttl">Created On</span> {{ li.created_at | date }}
                              </span>

                            </div>
                            <div class="tblist-item_xcn">
                                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Pay"> Pay </a>
                                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Delete">
                                      <img src="assets/ikons/colored/ikon-trash.svg" alt="">
                                  </a> -->
                                <a *ngIf="li.statusId === '1'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="View Certificate" [routerLink]="[
                      '/account/certificate/certificate-detail',
                      li.id
                    ]">
                                    <img src="assets/ikons/colored/ikon-eye.svg" alt="" />
                                </a>
                                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Transfer Ownership"
                                    data-bs-toggle="modal" data-bs-target="#showModal"
                                    (click)="openModal(isTransferOwnerShip, 'lg', li)"> Transfer Ownership </a> -->
                            </div>
                        </li>
                    </ul>
                    <div class="pagnation" *ngIf="lists.length > 0">
                      <div class="pagnation-item">
                          <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                              <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} -
                                  {{endIndex}}</span>
                          </div>
                      </div>
                      <div class="pagnation-item">
                          <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                              (pageChange)="getPremiumData();">
                          </ngb-pagination>
                      </div>
                  </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- <ng-template #isTransferOwnerShip role="document" let-modal>
    <div class="modol-header">
      <h2 id="exampleModalLabel">Transfer Ownership</h2>
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="step-panel_body">
      <form [formGroup]="saveForm" (ngSubmit)="onSubmit()">
        <div class="form-set">
          <div class="form-input">
            <label>Document Type</label>
            <div>
              <select name="documentTypeId" id="documentTypeId" formControlName="documentTypeId">
                <option [value]="">Select document type</option>
                <option *ngFor="let op of utilService.documentTypes" [value]="op.id">{{op.name}}</option>
              </select>
            </div>
          </div>
          <div class="form-input">
            <label>Document Number (National ID/ Passport)</label>
            <div>
              <input type="text" id="documentNumber" name="documentNumber" formControlName="documentNumber"
                (keyup)="changeAndGetNamesByNationalId()" required>
            </div>
          </div>
          <div class="form-input">
            <label>First name</label>
            <div>
              <input type="text" id="firstName" name="firstName" formControlName="firstName" required>
            </div>
          </div>
          <div class="form-input">
            <label>Last name</label>
            <div>
              <input type="text" id="lastName" name="lastName" formControlName="lastName" required>
            </div>
          </div>
          <div class="form-input">
            <label>Phone number</label>
            <div>
              <input type="text" id="phoneNumber" name="phoneNumber" formControlName="phoneNumber" required>
            </div>
          </div>
        </div>
        <div class="modol-content">
          <div class="kbk-x-c sp-sm mt-md">
            <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> Cancel </button>
            <button class="kbk-btn kbk-btn-main" type="submit"> Submit </button>
          </div>
        </div>
      </form>
    </div>
  </ng-template> -->
