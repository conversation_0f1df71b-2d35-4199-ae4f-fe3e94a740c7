import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-service-info-page',
  templateUrl: './service-info-page.component.html',
  styleUrls: ['./service-info-page.component.scss']
})
export class ServiceInfoPageComponent implements OnInit {
  @Input() inputData: any;
  @Output() backAndClose = new EventEmitter<any>();

  details: any;

  constructor(
    private router: Router
  ) {

  }

  ngOnInit(): void {
    // console.log
    this.details = this.inputData.details;
    // this.details = landingData.find((x: any) => x.id === this.inputData.id)?.details;
  }
  close() {
    this.backAndClose.emit(false);
  }


  applyToService() {
    this.close();
    this.router.navigate(['/login']);
  }
}
