import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { User } from './entities/user-management/user.entity';
// import * as bcrypt from 'bcrypt';
import * as bcrypt from 'bcryptjs';
import { JWTService } from './jwt.service';
import { ForgottenPassword } from './entities/user-management/forgottenPassword.entity';
import config from '../config';
// import * as nodemailer from 'nodemailer';
import { InjectRepository } from '@nestjs/typeorm';
import { EmailVerification } from './entities/user-management/emailVerification.entity';
import { Repository } from 'typeorm';
import { CreateStaffDto } from './dto/user.dto';
// import { CreateStaffDto, GetUserDto } from './dto/user.dto';
import { EventPattern } from '@nestjs/microservices';
// import { MessagePattern } from '@nestjs/microservices';
// import { AgencyRepository } from './user-management/user-management.repository';
// import { MessagePattern } from '@nestjs/microservices';
import axios from 'axios';
import { UserManagementService } from './user-management/user-management.service';

const saltRounds = 10;

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JWTService,

    @InjectRepository(User)
    private userRepository: Repository<User>,

    // @InjectRepository(AgencyRepository)
    // private readonly agencyRepository: AgencyRepository,

    @InjectRepository(EmailVerification)
    private emailVerificationRepository: Repository<EmailVerification>,

    @InjectRepository(ForgottenPassword)
    private forgottenPasswordRepository: Repository<ForgottenPassword>,

    private readonly userManagementService: UserManagementService,
  ) {}

  @EventPattern('checkUser')
  // @MessagePattern('checkUser')
  async checkUser(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: { userType: true, agency: true },
    });
    return !!user;
  }

  @EventPattern('checkUserData')
  async checkUserData(userId: string) {
    return await this.userRepository.findOne({
      where: { id: userId },
      relations: { userType: true, agency: true },
    });
  }

  @EventPattern('checkAgencyDataByDistrictCode')
  async checkAgencyDataByDistrictCode(districtCode: string) {
    console.log('Received districtCode:', districtCode);

    const agency =
      await this.userManagementService.findAgencyByDistrictCode(districtCode);
    console.log('Agency Data Found:', agency);

    return agency;
  }

  @EventPattern('checkAgencyDataById')
  async checkAgencyDataById(agencyId: string) {
    console.log('Received agencyId:', agencyId);
    const agency = await this.userManagementService.findOneAgency(agencyId);
    console.log('Agency Data Found:', agency);

    return agency;
  }

  @EventPattern('getAllUserByAgency')
  async getAllUserByAgency(agencyId: string) {
    // console.log('Received event for agency ID:', agencyId);

    try {
      if (!agencyId) {
        console.error('Agency ID is required but not provided.');
        return {
          success: false,
          message: 'Agency ID is required.',
          data: [],
        };
      }

      // console.log(`Fetching users for agency ID: ${agencyId}`);

      const users = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.role', 'role')
        .leftJoinAndSelect('user.userType', 'userType')
        .where('user.agency = :agencyId', { agencyId })
        .getMany();

      if (users.length === 0) {
        // console.log(`No users found for agency ID: ${agencyId}`);
        return {
          success: true,
          message: 'No users found for the specified agency ID.',
          data: [],
        };
      }

      // console.log(
      //   `Users fetched successfully for agency ID: ${agencyId}`,
      //   users,
      // );
      return {
        success: true,
        message: 'Users fetched successfully',
        data: users,
      };
    } catch (error) {
      console.error('Error fetching users by agency ID:', error);
      return {
        success: false,
        message: 'Failed to fetch users. Please try again later.',
        error: error.message,
      };
    }
  }

  @EventPattern('getAllApplicants')
  async getAllApplicants() {
    try {
      const users = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.role', 'role')
        .leftJoinAndSelect('user.userType', 'userType')
        .where('userType.code = :code', { code: 'LO' })
        .getMany();

      if (users.length === 0) {
        return {
          success: true,
          message: 'No users found for the specified agency ID.',
          data: [],
        };
      }

      return {
        success: true,
        message: 'Users fetched successfully',
        data: users,
      };
    } catch (error) {
      console.error('Error fetching users by agency ID:', error.message);
      throw new Error('Failed to fetch users. Please try again later.');
    }
  }

  async userExists(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: { userType: true },
    });
    return !!user;
  }

  async userData(userId: string) {
    return await this.userRepository.findOne({
      where: { id: userId },
      relations: { userType: true, agency: true },
    });
  }

  async agencyExists(agencyId: string) {
    const agency = await this.userManagementService.findOneAgency(agencyId);
    return agency;
  }

  // validate with locking accounet
  // async validateLogin(email: string, password: string) {
  //   const userFromDb = await this.userRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.userType', 'userType')
  //     .leftJoinAndSelect('user.agency', 'agency')
  //     .leftJoinAndSelect('user.sector', 'sector')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .leftJoinAndSelect('role.userActivities', 'userActivity')
  //     .where('user.email = :email', { email })
  //     .getOne();

  //   // const passwordIsValid = bcrypt.compare(password, userFromDb.password);

  //   // const userFromDb = await this.userRepository.findOne({
  //   //   where: { email: email },
  //   //   relations: { role: true },
  //   // });
  //   if (!userFromDb)
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);
  //   // throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);
  //   if (userFromDb.isEmailValid === false)
  //     throw new HttpException('Email not verified', HttpStatus.FORBIDDEN);
  //   // throw new HttpException('LOGIN.EMAIL_NOT_VERIFIED', HttpStatus.FORBIDDEN);

  //   const passwordIsValid = await bcrypt.compare(password, userFromDb.password);

  //   if (passwordIsValid) {
  //     // const accessToken = await this.jwtService.createToken(userFromDb);
  //     const accessToken = await this.jwtService.createToken(
  //       email,
  //       userFromDb.id,
  //       userFromDb.firstName,
  //       userFromDb.lastName,
  //       userFromDb.licenceIdEng,
  //       userFromDb.licenceIdArch,
  //       userFromDb.nationalId,
  //       userFromDb.isPasswordExpired,
  //       userFromDb.isStaffPasswordChanged,
  //     );
  //     return { token: accessToken, user: new GetUserDto(userFromDb) };
  //   } else {
  //     throw new HttpException('Login error', HttpStatus.UNAUTHORIZED);
  //   }
  // }

  // // //validation with a locking account and after 15 minutes the user will be login as expected
  // async validateLogin(email: string, password: string) {
  //   const userFromDb = await this.userRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.userType', 'userType')
  //     .leftJoinAndSelect('user.agency', 'agency')
  //     .leftJoinAndSelect('user.sector', 'sector')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .leftJoinAndSelect('role.userActivities', 'userActivity')
  //     .where('user.email = :email', { email })
  //     .getOne();

  //   if (!userFromDb) {
  //     throw new HttpException(
  //       'Login failed, try again later',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }
  //   if (userFromDb.isEmailValid === false) {
  //     throw new HttpException('Email not verified', HttpStatus.FORBIDDEN);
  //   }

  //   // Check if account is locked and time has passed
  //   if (userFromDb.lockUntil && userFromDb.lockUntil > new Date()) {
  //     const remainingTimeInMs = userFromDb.lockUntil.getTime() - Date.now();
  //     const remainingMinutes = Math.floor(remainingTimeInMs / (1000 * 60));
  //     const remainingSeconds = Math.floor(
  //       (remainingTimeInMs % (1000 * 60)) / 1000,
  //     );

  //     throw new HttpException(
  //       `Account locked. Try again in ${remainingMinutes} minutes and ${remainingSeconds} seconds.`,
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }

  //   const passwordIsValid = await bcrypt.compare(password, userFromDb.password);

  //   if (passwordIsValid) {
  //     // Reset failed attempts and lockUntil on successful login
  //     await this.userRepository.update(userFromDb.id, {
  //       failedLoginAttempts: 0,
  //       lockUntil: null,
  //     });

  //     const accessToken = await this.jwtService.createToken(
  //       email,
  //       userFromDb.id,
  //       userFromDb.firstName,
  //       userFromDb.lastName,
  //       userFromDb.licenceIdEng,
  //       userFromDb.licenceIdArch,
  //       userFromDb.nationalId,
  //       userFromDb.phoneNumber,
  //       userFromDb.isPasswordExpired,
  //       userFromDb.isStaffPasswordChanged,
  //     );

  //     return { token: accessToken, user: new GetUserDto(userFromDb) };
  //   } else {
  //     // Increment failed attempts
  //     const failedAttempts = userFromDb.failedLoginAttempts + 1;

  //     let lockUntil = null;
  //     if (failedAttempts >= 5) {
  //       lockUntil = new Date(Date.now() + 15 * 60 * 1000); // Lock for 15 minutes
  //     }

  //     await this.userRepository.update(userFromDb.id, {
  //       failedLoginAttempts: failedAttempts,
  //       lockUntil,
  //     });

  //     if (lockUntil) {
  //       throw new HttpException(
  //         'Account locked due to too many failed login attempts. Try again later.',
  //         HttpStatus.FORBIDDEN,
  //       );
  //     } else {
  //       throw new HttpException(
  //         'Invalid email or password.',
  //         HttpStatus.UNAUTHORIZED,
  //       );
  //     }
  //   }
  // }

  // // validation with a locking account and after 15 minutes the user will be login as expected
  // // Plus otp for 2FA
  // async validateLogin(email: string, password: string) {
  //   const userFromDb = await this.userRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.userType', 'userType')
  //     .leftJoinAndSelect('user.agency', 'agency')
  //     .leftJoinAndSelect('user.sector', 'sector')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .leftJoinAndSelect('role.userActivities', 'userActivity')
  //     .where('user.email = :email', { email })
  //     .getOne();

  //   if (!userFromDb) {
  //     throw new HttpException(
  //       'Login failed, try again later',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }
  //   if (userFromDb.isEmailValid === false) {
  //     throw new HttpException('Email not verified', HttpStatus.FORBIDDEN);
  //   }

  //   if (userFromDb.lockUntil && userFromDb.lockUntil > new Date()) {
  //     const remainingTimeInMs = userFromDb.lockUntil.getTime() - Date.now();
  //     const remainingMinutes = Math.floor(remainingTimeInMs / (1000 * 60));
  //     const remainingSeconds = Math.floor(
  //       (remainingTimeInMs % (1000 * 60)) / 1000,
  //     );

  //     throw new HttpException(
  //       `Account locked. Try again in ${remainingMinutes} minutes and ${remainingSeconds} seconds.`,
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }

  //   const passwordIsValid = await bcrypt.compare(password, userFromDb.password);

  //   if (passwordIsValid) {
  //     // Reset failed attempts and lockUntil on successful login
  //     await this.userRepository.update(userFromDb.id, {
  //       failedLoginAttempts: 0,
  //       lockUntil: null,
  //     });

  //     // // Generate a 6-digit OTP
  //     // const otp2FA = Math.floor(100000 + Math.random() * 900000).toString();

  //     // // Save the OTP in the database
  //     // await this.userRepository.update(userFromDb.id, {
  //     //   otp2FA, // Save the generated OTP
  //     // });

  //     // Generate a 6-digit OTP
  //     const otp2FA = Math.floor(100000 + Math.random() * 900000).toString();

  //     // Calculate the expiration time (10 minutes from now)
  //     const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000);

  //     // Save the OTP and expiration time in the database
  //     await this.userRepository.update(userFromDb.id, {
  //       otp2FA,
  //       otpExpiresAt,
  //     });

  //     // Send OTP to user's email or phone number (example for email)
  //     await this.sendOtp(userFromDb.email, userFromDb.phoneNumber, otp2FA);

  //     return {
  //       message: 'OTP sent to your email. Please verify to continue.',
  //     };
  //   } else {
  //     const failedAttempts = userFromDb.failedLoginAttempts + 1;

  //     let lockUntil = null;
  //     if (failedAttempts >= 5) {
  //       lockUntil = new Date(Date.now() + 15 * 60 * 1000); // Lock for 15 minutes
  //     }

  //     await this.userRepository.update(userFromDb.id, {
  //       failedLoginAttempts: failedAttempts,
  //       lockUntil,
  //     });

  //     if (lockUntil) {
  //       throw new HttpException(
  //         'Account locked due to too many failed login attempts. Try again later.',
  //         HttpStatus.FORBIDDEN,
  //       );
  //     } else {
  //       throw new HttpException(
  //         'Invalid email or password.',
  //         HttpStatus.UNAUTHORIZED,
  //       );
  //     }
  //   }
  // }

  // validation with a locking account and after 15 minutes the user will be login as expected
  // Plus otp for 2FA
  // Plus check if user is active
  async validateLogin(email: string, password: string) {
    const userFromDb = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.agency', 'agency')
      .leftJoinAndSelect('user.sector', 'sector')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('role.userActivities', 'userActivity')
      .where('user.email = :email', { email })
      .getOne();

    if (!userFromDb) {
      throw new HttpException(
        'Login failed, try again later',
        HttpStatus.NOT_FOUND,
      );
    }
    if (userFromDb.isEmailValid === false) {
      throw new HttpException('Email not verified', HttpStatus.FORBIDDEN);
    }

    if (userFromDb.isActive === false) {
      throw new HttpException(
        'User account is deactivated',
        HttpStatus.FORBIDDEN,
      );
    }

    // if (userFromDb.lockUntil && userFromDb.lockUntil > new Date()) {
    //   const remainingTimeInMs = userFromDb.lockUntil.getTime() - Date.now();
    //   const remainingMinutes = Math.floor(remainingTimeInMs / (1000 * 60));
    //   const remainingSeconds = Math.floor(
    //     (remainingTimeInMs % (1000 * 60)) / 1000,
    //   );

    //   throw new HttpException(
    //     `Account locked. Try again in ${remainingMinutes} minutes and ${remainingSeconds} seconds.`,
    //     HttpStatus.FORBIDDEN,
    //   );
    // }
    if (userFromDb.lockUntil) {
      const now = new Date();
      const lockUntilTime = new Date(userFromDb.lockUntil);
      const timeDifferenceInMs = now.getTime() - lockUntilTime.getTime();
      const minutesPassed = timeDifferenceInMs / (1000 * 60);

      // If lockUntil is still in the future and less than 15 minutes have passed, deny access
      if (lockUntilTime > now && minutesPassed < 15) {
        const remainingTimeInMs = lockUntilTime.getTime() - now.getTime();
        const remainingMinutes = Math.floor(remainingTimeInMs / (1000 * 60));
        const remainingSeconds = Math.floor(
          (remainingTimeInMs % (1000 * 60)) / 1000,
        );

        throw new HttpException(
          `Account locked. Try again in ${remainingMinutes} minutes and ${remainingSeconds} seconds.`,
          HttpStatus.FORBIDDEN,
        );
      }
    }

    // Allow the user to continue if lockUntil is not set or 15 minutes have passed.

    const passwordIsValid = await bcrypt.compare(password, userFromDb.password);

    // // clean if the user has some fail login attempts ()
    if (passwordIsValid) {
      // Reset failed attempts and lockUntil on successful login
      await this.userRepository.update(userFromDb.id, {
        failedLoginAttempts: 0,
        lockUntil: null,
      });

      // // Generate a 6-digit OTP
      // const otp2FA = Math.floor(100000 + Math.random() * 900000).toString();

      // // Save the OTP in the database
      // await this.userRepository.update(userFromDb.id, {
      //   otp2FA, // Save the generated OTP
      // });

      // Generate a 6-digit OTP
      const otp2FA = Math.floor(100000 + Math.random() * 900000).toString();

      // Calculate the expiration time (10 minutes from now)
      const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000);

      // Save the OTP and expiration time in the database
      await this.userRepository.update(userFromDb.id, {
        otp2FA,
        otpExpiresAt,
      });

      // Send OTP to user's email or phone number (example for email)
      await this.sendOtp(userFromDb.email, userFromDb.phoneNumber, otp2FA);

      return {
        message: 'OTP sent to your email. Please verify to continue.',
      };
    } else {
      const failedAttempts = userFromDb.failedLoginAttempts + 1;

      let lockUntil = null;
      if (failedAttempts >= 5) {
        lockUntil = new Date(Date.now() + 15 * 60 * 1000); // Lock for 15 minutes
      }

      await this.userRepository.update(userFromDb.id, {
        failedLoginAttempts: failedAttempts,
        lockUntil,
      });

      if (lockUntil) {
        throw new HttpException(
          'Account locked due to too many failed login attempts. Try again later.',
          HttpStatus.BAD_REQUEST,
        );
      } else {
        throw new HttpException(
          'Invalid email or password.',
          HttpStatus.UNAUTHORIZED,
        );
      }
    }
  }

  // verify the OTP
  // public async verifyOtp(
  //   email: string,
  //   otp: string,
  // ): Promise<{ token: string; user: GetUserDto }> {
  //   const user = await this.userRepository.findOne({ where: { email } });

  //   if (!user) {
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);
  //   }

  //   if (user.otp2FA !== otp) {
  //     throw new HttpException('Invalid OTP', HttpStatus.BAD_REQUEST);
  //   }

  //   await this.userRepository.update(user.id, {
  //     otp2FA: null,
  //     isOtpVerified: true,
  //   });

  //   const tokenData = await this.jwtService.createToken(
  //     user.email,
  //     user.id,
  //     user.firstName,
  //     user.lastName,
  //     user.licenceIdEng,
  //     user.licenceIdArch,
  //     user.nationalId,
  //     user.phoneNumber,
  //     user.isPasswordExpired,
  //     user.isStaffPasswordChanged,
  //   );

  //   // Ensure we only return the access_token
  //   const accessToken =
  //     typeof tokenData === 'string' ? tokenData : tokenData.access_token;

  //   return {
  //     token: accessToken,
  //     user: new GetUserDto(user),
  //   };
  // }

  //verify the OTP final
  async verifyOtp(email: string, otp: string) {
    const userFromDb = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.agency', 'agency')
      .leftJoinAndSelect('user.sector', 'sector')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('role.userActivities', 'userActivity')
      .where('user.email = :email', { email })
      .getOne();

    if (!userFromDb) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    if (userFromDb.otp2FA !== otp) {
      throw new HttpException('Invalid OTP', HttpStatus.BAD_REQUEST);
    }

    // Check if OTP has expired
    if (userFromDb.isOtpVerified && userFromDb.otpExpiresAt < new Date()) {
      throw new HttpException('OTP has expired', HttpStatus.BAD_REQUEST);
    }

    // Clear OTP and expiration after successful verification
    await this.userRepository.update(userFromDb.id, {
      // otp2FA: null,
      isOtpVerified: true,
    });

    // Generate a new access token
    const accessToken = await this.jwtService.createToken(
      userFromDb.email,
      userFromDb.id,
      userFromDb.firstName,
      userFromDb.lastName,
      userFromDb.licenceIdEng,
      userFromDb.licenceIdArch,
      userFromDb.nationalId,
      userFromDb.passport,
      userFromDb.phoneNumber,
      userFromDb.isPasswordExpired,
      userFromDb.isStaffPasswordChanged,
    );

    return {
      token: accessToken,
      user: {
        firstName: userFromDb.firstName,
        lastName: userFromDb.lastName,
        email: userFromDb.email,
        phoneNumber: userFromDb.phoneNumber,
        nationalId: userFromDb.nationalId,
        passport: userFromDb.passport,
        isPasswordExpired: userFromDb.isPasswordExpired,
        isStaffPasswordChanged: userFromDb.isStaffPasswordChanged,
        approvalLevelId: userFromDb.approvalLevelId,
        role: {
          id: userFromDb.role.id,
          name: userFromDb.role.name,
          code: userFromDb.role.code,
          userActivities: userFromDb.role.userActivities,
        },
        userType: {
          id: userFromDb.userType.id,
          name: userFromDb.userType.name,
          code: userFromDb.userType.code,
        },
        agency: userFromDb.agency,
        sector: userFromDb.sector,
      },
    };
  }

  // // //verify the OTP semifinal
  // async verifyOtp(email: string, otp: string) {
  //   const userFromDb = await this.userRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.userType', 'userType')
  //     .leftJoinAndSelect('user.agency', 'agency')
  //     .leftJoinAndSelect('user.sector', 'sector')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .leftJoinAndSelect('role.userActivities', 'userActivity')
  //     .where('user.email = :email', { email })
  //     .getOne();

  //   if (!userFromDb) {
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);
  //   }

  //   if (userFromDb.otp2FA !== otp) {
  //     throw new HttpException('Invalid OTP', HttpStatus.UNAUTHORIZED);
  //   }

  //   // Check if OTP has expired
  //   if (userFromDb.isOtpVerified && userFromDb.otpExpiresAt < new Date()) {
  //     throw new HttpException('OTP has expired', HttpStatus.UNAUTHORIZED);
  //   }

  //   // Clear OTP and expiration after successful verification
  //   await this.userRepository.update(userFromDb.id, {
  //     // otp2FA: null,
  //     isOtpVerified: true,
  //   });

  //   // Generate a new access token
  //   const accessToken = await this.jwtService.createToken(
  //     userFromDb.email,
  //     userFromDb.id,
  //     userFromDb.firstName,
  //     userFromDb.lastName,
  //     userFromDb.licenceIdEng,
  //     userFromDb.licenceIdArch,
  //     userFromDb.nationalId,
  //     userFromDb.phoneNumber,
  //     userFromDb.isPasswordExpired,
  //     userFromDb.isStaffPasswordChanged,
  //   );

  //   return {
  //     success: true,
  //     message: 'OTP verified successfully',
  //     data: {
  //       token: {
  //         expires_in: 36000000, // Example expiration time in milliseconds
  //         access_token: accessToken,
  //       },
  //       user: {
  //         firstName: userFromDb.firstName,
  //         lastName: userFromDb.lastName,
  //         email: userFromDb.email,
  //         phoneNumber: userFromDb.phoneNumber,
  //         nationalId: userFromDb.nationalId,
  //         isPasswordExpired: userFromDb.isPasswordExpired,
  //         isStaffPasswordChanged: userFromDb.isStaffPasswordChanged,
  //         approvalLevelId: null, // Assuming no approval level
  //         role: {
  //           id: userFromDb.role.id,
  //           name: userFromDb.role.name,
  //           code: userFromDb.role.code,
  //           userActivities: userFromDb.role.userActivities,
  //         },
  //         userType: {
  //           id: userFromDb.userType.id,
  //           name: userFromDb.userType.name,
  //           code: userFromDb.userType.code,
  //         },
  //         agency: userFromDb.agency,
  //         sector: userFromDb.sector,
  //       },
  //     },
  //   };
  // }

  private async sendOtp(email: string, phoneNumber: string, otp: string) {
    const message = `Your Kubaka Login OTP is ${otp}. It is valid for 10 minutes.`;
    // Sending email
    await axios.post(`${config.notification.emailAPI}/email/send/`, {
      sender_name: 'KUBAKA MIS',
      sender_email: `${config.notification.senderEmail}`,
      receiver_name: 'Kubaka User',
      receiver_email: email,
      subject: 'KUBAKA MIS OTP Email',
      message: message,
    });

    // Sending SMS
    await axios.post(`${config.notification.smsAPI}/sms/send/`, {
      msisdn: phoneNumber,
      message: message,
    });
  }

  async createEmailToken(email: string) {
    console.log(email);
    const createdEmailVerification = {
      email: email,
      emailToken: (Math.floor(Math.random() * 9000000) + 1000000).toString(), //Generate 7 digits number
      timestamp: new Date(),
    };

    console.log(createdEmailVerification);

    const emailVerification = this.emailVerificationRepository.create(
      createdEmailVerification,
    );
    return await emailVerification.save();
  }

  // async createForgottenPasswordToken(
  //   email: string,
  // ): Promise<ForgottenPassword> {
  //   const forgottenPassword = await this.forgottenPasswordRepository.findOne({
  //     where: { email: email },
  //   });

  //   if (
  //     forgottenPassword &&
  //     (new Date().getTime() - forgottenPassword.timestamp.getTime()) / 60000 <
  //       15
  //   ) {
  //     throw new HttpException(
  //       'Reset password email sended recently',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   } else {
  //     console.log(forgottenPassword);
  //     const forgottenPasswordModel = {
  //       email: email,
  //       newPasswordToken: (
  //         Math.floor(Math.random() * 9000000) + 1000000
  //       ).toString(), //Generate 7 digits number,
  //       timestamp: new Date(),
  //     };

  //     console.log(forgottenPasswordModel);

  //     const forgottenPasswordSaved = this.forgottenPasswordRepository.create(
  //       forgottenPasswordModel,
  //     );
  //     return await forgottenPasswordSaved.save();
  //   }
  // }
  async createForgottenPasswordToken(
    email: string,
  ): Promise<ForgottenPassword> {
    const forgottenPassword = await this.forgottenPasswordRepository.findOne({
      where: { email: email },
    });

    if (
      forgottenPassword &&
      forgottenPassword.timestamp &&
      new Date().getTime() - new Date(forgottenPassword.timestamp).getTime() <
        15 * 60000
    ) {
      throw new HttpException(
        'Reset password email sent recently',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } else {
      console.log(forgottenPassword);
      const forgottenPasswordModel = {
        email: email,
        newPasswordToken: (
          Math.floor(Math.random() * 9000000) + 1000000
        ).toString(),
        timestamp: new Date(),
      };

      console.log(forgottenPasswordModel);

      const forgottenPasswordSaved = this.forgottenPasswordRepository.create(
        forgottenPasswordModel,
      );
      return await forgottenPasswordSaved.save();
    }
  }

  async getTokenByEmail(email: string) {
    const userToBeVerified = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });
    if (!userToBeVerified) {
      throw new NotFoundException('User not found');
    }
    return userToBeVerified;
  }

  async verifyEmail(token: string): Promise<boolean> {
    const emailToBeVerified = await this.emailVerificationRepository.findOne({
      where: { emailToken: token },
    });
    if (emailToBeVerified.email) {
      const userFromDb = await this.userRepository.findOne({
        where: { email: emailToBeVerified.email },
      });
      if (userFromDb) {
        userFromDb.isEmailValid = true;
        // const savedUser = await userFromDb.save();
        const savedUser = this.userRepository.create(userFromDb);
        await savedUser.save();
        // await emailVerif.remove();
        await this.emailVerificationRepository.remove(emailToBeVerified);
        return !!savedUser;
      }
    } else {
      throw new HttpException('Email code not valid', HttpStatus.FORBIDDEN);
    }
  }

  async getForgottenPassword(
    newPasswordToken: string,
  ): Promise<ForgottenPassword> {
    return await this.forgottenPasswordRepository.findOne({
      where: { newPasswordToken: newPasswordToken },
    });
  }

  async checkPassword(email: string, password: string): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email },
      });

      if (!userFromDb) {
        console.log(`User with email ${email} not found`);
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }

      console.log(`Provided password: ${password}`);
      console.log(`Stored password: ${userFromDb.password}`);

      const isPasswordValid = await bcrypt.compare(
        password,
        userFromDb.password.trim(),
      );

      if (isPasswordValid) {
        console.log(
          `Password validation succeeded for user with email ${email}`,
        );
      } else {
        console.log(`Password validation failed for user with email ${email}`);
      }

      return isPasswordValid;
    } catch (error) {
      console.error(
        `Error in checkPassword for email ${email}: ${error.message}`,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // testing email address
  // async sendEmailVerification(email: string): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });
  //   console.log(model);

  //   if (model && model.emailToken) {
  //     const transporter = nodemailer.createTransport({
  //       host: config.mail.host,
  //       auth: {
  //         user: config.mail.user,
  //         pass: config.mail.pass,
  //       },
  //     });

  //     const mailOptions = {
  //       from: '"KUBAKA" <' + config.mail.user + '>',
  //       to: email,
  //       subject: 'Verify Email',
  //       text: 'Verify Email',
  //       html:
  //         'Hi there, <br> Thank you for registering with us! <br><br> To activate your account, simply click the link below: <br>' +
  //         'Your OTP Code is: ' +
  //         model.emailToken +
  //         '<a href=' +
  //         config.host.url +
  //         ':' +
  //         config.host.port +
  //         '/email/verify/' +
  //         model.emailToken +
  //         '><br>Activate Account Here</a> <br><br> Best regards,   <br> KUBAKA Team ', // html body
  //     };

  //     const sent = await new Promise<boolean>(async function (resolve, reject) {
  //       return await transporter.sendMail(mailOptions, async (error, info) => {
  //         if (error) {
  //           console.log('Message sent: %s', error);
  //           return reject(false);
  //         }
  //         console.log('Message sent: %s', info.messageId);
  //         resolve(true);
  //       });
  //     });

  //     return sent;
  //   } else {
  //     throw new HttpException(
  //       'Email not sent',
  //       // 'REGISTER.USER_NOT_REGISTERED',
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }
  // }

  // live email address
  async sendEmailVerification(
    email: string,
    phoneNumber: string,
  ): Promise<boolean> {
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'KUBAKA MIS',
        sender_email: `${config.notification.senderEmail}`,
        // sender_email: '<EMAIL>',
        receiver_name: 'KUBAKA User',
        receiver_email: email,
        subject: 'Verify Email',
        message: `
        Hi there, <br>
        Thank you for registering with us! <br><br>
        To activate your account, simply click the link below: <br>
        Activate Account Here: ${config.host.url}/email/verify/ <br><br>
        <br>
        Your OTP Code is: ${model.emailToken} 
        <br>
        Best regards, <br>
        KUBAKA Team       
        `,
      };
      // if (model && model.emailToken) {
      //   const requestData = {
      //     sender_name: 'KUBAKA MIS',
      //     sender_email: '<EMAIL>',
      //     receiver_name: 'KUBAKA User',
      //     receiver_email: email,
      //     subject: 'Verify Email',
      //     message: `
      //     Hi there, <br>
      //     Thank you for registering with us! <br><br>
      //     To activate your account, simply click the link below: <br>
      //     Your OTP Code is: ${model.emailToken} <br>
      //     Activate Account Here: ${config.host.url}:${config.host.port}/email/verify/${model.emailToken} <br><br>
      //     Best regards, <br>
      //     KUBAKA Team
      //     `,
      //   };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          // sending email and sms responses
          axios.post(
            `${config.notification.emailAPI}/email/send/`,
            // 'https://notification.kubaka.gov.rw/email/send/',
            requestData,
          ),
          axios.post(
            `${config.notification.smsAPI}/sms/send/`,
            // 'https://notification.kubaka.gov.rw/sms/send',
            {
              msisdn: phoneNumber,
              message: `Thank you for registering with us!\n This is the link to active your account:\n ${config.host.url}/email/verify/ \n This is your token ${model.emailToken}\n\nBest regards\nKUBAKA Team`,
            },
          ),
        ]);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);
        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }
  // live email address
  async resendEmailVerification(email: string) {
    //Get the user details
    const userFromDb = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.agency', 'agency')
      .leftJoinAndSelect('user.sector', 'sector')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('role.userActivities', 'userActivity')
      .where('user.email = :email', { email })
      .getOne();

    if (!userFromDb) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'KUBAKA MIS',
        sender_email: `${config.notification.senderEmail}`,
        receiver_name: 'KUBAKA User',
        receiver_email: email,
        subject: 'Re-Verify Email',
        message: `
        Dear${userFromDb.firstName} ${userFromDb.lastName},, <br>
        To activate your account, simply click the link below: <br>
        Activate Account Here: ${config.host.url}/email/verify/ <br><br>
        <br>
        This is OTP code for your account: ${model.emailToken}
        <br>
        Best regards, <br>
        KUBAKA Team       
        `,
      };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          axios.post(
            `${config.notification.emailAPI}/email/send/`,
            requestData,
          ),
          axios.post(`${config.notification.smsAPI}/sms/send/`, {
            msisdn: userFromDb.phoneNumber,
            message: `Dear ${userFromDb.firstName} ${userFromDb.lastName},\nThis is the link to active your account:\n${config.host.url}/email/verify/\nThis is your token ${model.emailToken}\n\nBest regards\nKUBAKA Team`,
          }),
        ]);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);

        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Error occur', HttpStatus.FORBIDDEN);
    }
  }
  // // live email address
  // async resendEmailVerification(email: string): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });

  //   if (model && model.emailToken) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: `${config.notification.senderEmail}`,
  //       // sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: email,
  //       subject: 'Verify Email',
  //       message: `
  //       Hi there, <br>
  //       Thank you for registering with us! <br><br>
  //       To activate your account, simply click the link below: <br>
  //       Activate Account Here: ${config.host.url}/email/verify/ <br><br>
  //       Best regards, <br>
  //       KUBAKA Team
  //       `,
  //     };
  //     // if (model && model.emailToken) {
  //     //   const requestData = {
  //     //     sender_name: 'KUBAKA MIS',
  //     //     sender_email: '<EMAIL>',
  //     //     receiver_name: 'KUBAKA User',
  //     //     receiver_email: email,
  //     //     subject: 'Verify Email',
  //     //     message: `
  //     //     Hi there, <br>
  //     //     Thank you for registering with us! <br><br>
  //     //     To activate your account, simply click the link below: <br>
  //     //     Your OTP Code is: ${model.emailToken} <br>
  //     //     Activate Account Here: ${config.host.url}:${config.host.port}/email/verify/${model.emailToken} <br><br>
  //     //     Best regards, <br>
  //     //     KUBAKA Team
  //     //     `,
  //     //   };

  //     try {
  //       const [emailResponse] = await Promise.all([
  //         // sending email and sms responses
  //         axios.post(
  //           `${config.notification.emailAPI}/email/send/`,
  //           // 'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         ),
  //       ]);

  //       console.log('Email sent successfully:', emailResponse);
  //       return true;
  //     } catch (error) {
  //       console.error('Error sending email:', error.response.data);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
  //   }
  // }

  // // testing email address
  // async sendEmailVerificationForStaffWithDefaultPassword(
  //   email: string,
  // ): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });
  //   console.log(model);

  //   if (model && model.emailToken) {
  //     const transporter = nodemailer.createTransport({
  //       host: config.mail.host,
  //       auth: {
  //         user: config.mail.user,
  //         pass: config.mail.pass,
  //       },
  //     });

  //     const mailOptions = {
  //       from: '"KUBAKA" <' + config.mail.user + '>',
  //       to: email,
  //       subject: 'Verify Email',
  //       text: 'Verify Email',
  //       html:
  //         'Hi there, <br> Thank you for registering with us! <br><br> To activate your account, simply click the link below: <br>' +
  //         'Your OTP Code is: ' +
  //         model.emailToken +
  //         '<br>And this is the default password: ' +
  //         'Kubaka@123!' +
  //         ' ' +
  //         ', please change it after login' +
  //         '<br><a href=' +
  //         config.host.url +
  //         ':' +
  //         config.host.port +
  //         '/email/verify/' +
  //         model.emailToken +
  //         '><br>Activate Account Here</a> <br><br> Best regards, <br> KUBAKA Team ', // html body
  //     };

  //     const sent = await new Promise<boolean>(async function (resolve, reject) {
  //       return await transporter.sendMail(mailOptions, async (error, info) => {
  //         if (error) {
  //           console.log('Message sent: %s', error);
  //           return reject(false);
  //         }
  //         console.log('Message sent: %s', info.messageId);
  //         resolve(true);
  //       });
  //     });

  //     return sent;
  //   } else {
  //     throw new HttpException(
  //       'Email not sent',
  //       // 'REGISTER.USER_NOT_REGISTERED',
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }
  // }

  // live email address
  // async sendEmailVerificationForStaffWithDefaultPassword(
  //   email: string,
  // ): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });

  //   if (model && model.emailToken) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: email,
  //       subject: 'Verify Email',
  //       message: `Hi there, <br>
  //         Thank you for registering with us! <br><br>
  //         To activate your account, simply click the link below: <br>
  //         Your OTP Code is: ${model.emailToken} <br>
  //         And this is the default password: Kubaka@123!, please change it after login <br>
  //         Activate Account Here: ${config.host.url}:${config.host.port}/email/verify/${model.emailToken} <br><br>
  //         Best regards, <br>
  //         KUBAKA Team`,
  //     };

  //     try {
  //       const response = await axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);
  //       return true;
  //     } catch (error) {
  //       console.error('Error sending email:', error.response.data);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
  //   }
  // }
  async sendEmailVerificationForStaffWithDefaultPassword(
    email: string,
    phoneNumber: string,
  ): Promise<boolean> {
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'KUBAKA MIS',
        sender_email: `${config.notification.senderEmail}`,
        // sender_email: '<EMAIL>',
        receiver_name: 'KUBAKA User',
        receiver_email: email,
        subject: 'Verify Email',
        message: `Hi there, <br>
          Thank you for registering with us! <br><br>
          To activate your account, simply click the link below: <br>
          And this is the default password: Kubaka@123! <br>
          Please change it after login <br>
          Activate Account Here: ${config.host.url}/email/verify/ <br><br>
          Best regards, <br>
          KUBAKA Team`,
      };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          // sending email and sms responses
          axios.post(
            `${config.notification.emailAPI}/email/send/`,
            // 'https://notification.kubaka.gov.rw/email/send/',
            requestData,
          ),
          axios.post(
            `${config.notification.smsAPI}/sms/send/`,
            // 'https://notification.kubaka.gov.rw/sms/send',
            {
              msisdn: phoneNumber,
              message: `Hi there,\n This is the link to active your account:\n ${config.host.url}/email/verify/ \n This is your token ${model.emailToken}\n\nBest regards\nKUBAKA Team`,
            },
          ),
        ]);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);

        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }

  // async sendEmailVerificationForStaff(
  //   email: string,
  //   password: string,
  // ): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });
  //   console.log(model);

  //   if (model && model.emailToken) {
  //     const transporter = nodemailer.createTransport({
  //       host: config.mail.host,
  //       auth: {
  //         user: config.mail.user,
  //         pass: config.mail.pass,
  //       },
  //     });

  //     const mailOptions = {
  //       from: '"KUBAKA" <' + config.mail.user + '>',
  //       to: email,
  //       subject: 'Verify Email',
  //       text: 'Verify Email',
  //       html:
  //         'Hi there, <br> Thank you for registering with us! <br><br> To activate your account, simply click the link below: <br>' +
  //         'Your OTP Code is: ' +
  //         model.emailToken +
  //         '<br>And this is the default password to use: ' +
  //         password +
  //         '<br><a href=' +
  //         config.host.url +
  //         ':' +
  //         config.host.port +
  //         '/email/verify/' +
  //         model.emailToken +
  //         '><br>Activate Account Here</a> <br><br> Best regards,   <br> KUBAKA Team ', // html body
  //     };

  //     const sent = await new Promise<boolean>(async function (resolve, reject) {
  //       return await transporter.sendMail(mailOptions, async (error, info) => {
  //         if (error) {
  //           console.log('Message sent: %s', error);
  //           return reject(false);
  //         }
  //         console.log('Message sent: %s', info.messageId);
  //         resolve(true);
  //       });
  //     });

  //     return sent;
  //   } else {
  //     throw new HttpException(
  //       'Email not sent',
  //       // 'REGISTER.USER_NOT_REGISTERED',
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }
  // }

  // async sendEmailVerification(email: string): Promise<boolean> {
  //   const model = await this.emailVerificationRepository.findOne({
  //     where: { email: email },
  //   });
  //   console.log(model);

  //   if (model && model.emailToken) {
  //     const transporter = nodemailer.createTransport({
  //       host: config.mail.host,
  //       auth: {
  //         user: config.mail.user,
  //         pass: config.mail.pass,
  //       },
  //     });

  //     const mailOptions = {
  //       from: '"KUBAKA" <' + config.mail.user + '>',
  //       to: email,
  //       subject: 'Verify Email',
  //       text: 'Verify Email',
  //       html:
  //         'Hi! <br><br> Thanks for your registration<br><br>' +
  //         model.emailToken +
  //         '<a href=' +
  //         config.host.url +
  //         ':' +
  //         config.host.port +
  //         '/auth/email/verify/' +
  //         model.emailToken +
  //         '>Click here to activate your account</a>', // html body
  //     };

  //     const sent = await new Promise<boolean>(async function (resolve, reject) {
  //       return await transporter.sendMail(mailOptions, async (error, info) => {
  //         if (error) {
  //           console.log('Message sent: %s', error);
  //           return reject(false);
  //         }
  //         console.log('Message sent: %s', info.messageId);
  //         resolve(true);
  //       });
  //     });

  //     return sent;
  //   } else {
  //     throw new HttpException(
  //       'EMAIL.NOT_SENT',
  //       // 'REGISTER.USER_NOT_REGISTERED',
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }
  // }

  // // testing email address
  // async sendEmailForgotPassword(email: string): Promise<boolean> {
  //   const userFromDb = await this.userRepository.findOne({
  //     where: { email: email },
  //   });
  //   if (!userFromDb)
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);
  //   // throw new HttpException('LOGIN.USER_NOT_FOUND', HttpStatus.NOT_FOUND);

  //   const tokenModel = await this.createForgottenPasswordToken(email);

  //   if (tokenModel && tokenModel.newPasswordToken) {
  //     const transporter = nodemailer.createTransport({
  //       host: config.mail.host,
  //       auth: {
  //         user: config.mail.user,
  //         pass: config.mail.pass,
  //       },
  //     });

  //     const mailOptions = {
  //       from: '"KUBAKA" <' + config.mail.user + '>',
  //       to: email,
  //       subject: 'Forgot Password',
  //       text: 'Forgot Password',
  //       html:
  //         'Hi there, <br> If you have requested to reset your password, <br> please follow the link below: <br> ' +
  //         '<a href=' +
  //         config.host.url +
  //         ':' +
  //         config.host.port +
  //         '/auth/email/reset-password/' +
  //         tokenModel.newPasswordToken +
  //         '>Click here to reset password </a> <br> Best regards,<br> KUBAKA Team', // html body
  //     };

  //     const sended = await new Promise<boolean>(async function (
  //       resolve,
  //       reject,
  //     ) {
  //       return await transporter.sendMail(mailOptions, async (error, info) => {
  //         if (error) {
  //           console.log('Message sent: %s', error);
  //           return reject(false);
  //         }
  //         console.log('Message sent: %s', info.messageId);
  //         resolve(true);
  //       });
  //     });

  //     return sended;
  //   } else {
  //     throw new HttpException(
  //       'Email not sent',
  //       // 'REGISTER.USER_NOT_REGISTERED',
  //       HttpStatus.FORBIDDEN,
  //     );
  //   }
  // }

  // forgot password sent email plus the token in SMS phone
  async sendEmailForgotPassword(email: string): Promise<boolean> {
    const userFromDb = await this.userRepository.findOne({
      where: { email: email },
    });
    if (!userFromDb)
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);

    const tokenModel = await this.createForgottenPasswordToken(email);

    if (tokenModel && tokenModel.newPasswordToken) {
      const requestData = {
        sender_name: 'KUBAKA MIS',
        sender_email: `${config.notification.senderEmail}`,
        // sender_email: '<EMAIL>',
        receiver_name: 'KUBAKA User',
        receiver_email: email,
        subject: 'Forgot Password',
        message: `Dear ${userFromDb.firstName} ${userFromDb.lastName}, <br>
            If you have requested to reset your password, <br>
            please follow the link below: <br>
            <a href="${config.host.url}/reset-password/">Click here to reset password</a> <br>
            Best regards, <br>
            KUBAKA Team`,
      };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          axios.post(
            `${config.notification.emailAPI}/email/send/`,
            requestData,
          ),
          axios.post(`${config.notification.smsAPI}/sms/send/`, {
            msisdn: userFromDb.phoneNumber,
            message: `Dear ${userFromDb.firstName} ${userFromDb.lastName},\n This is the token for the forgotten password :\n ${tokenModel.newPasswordToken} \nBest regards\nKUBAKA Team`,
          }),
        ]);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);

        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }
  // // send the email for forgot password with token iside
  // async sendEmailForgotPassword(email: string): Promise<boolean> {
  //   const userFromDb = await this.userRepository.findOne({
  //     where: { email: email },
  //   });
  //   if (!userFromDb)
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);

  //   const tokenModel = await this.createForgottenPasswordToken(email);

  //   if (tokenModel && tokenModel.newPasswordToken) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: `${config.notification.senderEmail}`,
  //       // sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: email,
  //       subject: 'Forgot Password',
  //       message: `Hi there, <br>
  //           If you have requested to reset your password, <br>
  //           please follow the link below: <br>
  //           <a href="${config.host.url}/reset-password/${tokenModel.newPasswordToken}">Click here to reset password</a> <br>
  //           Best regards, <br>
  //           KUBAKA Team`,
  //     };

  //     try {
  //       const response = await axios.post(
  //         `${config.notification.emailAPI}/email/send/`,
  //         // 'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);
  //       return true;
  //     } catch (error) {
  //       console.error('Error sending email:', error.response.data);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
  //   }
  // }

  // Create user with random password
  async createNewStaffWithRandomPSW(newStaff: CreateStaffDto): Promise<User> {
    const dataFromDb = await this.userManagementService.findOneRole(
      newStaff.roleId,
    );
    if (!dataFromDb)
      throw new HttpException(
        `Role not found with id ${newStaff.roleId}`,
        HttpStatus.NOT_FOUND,
      );

    const dataFromDb2 = await this.userManagementService.findOneUserType(
      newStaff.userTypeId,
    );
    if (!dataFromDb2)
      throw new HttpException(
        `User type not found with id id ${newStaff.userTypeId}`,
        HttpStatus.NOT_FOUND,
      );

    const dataFromDb3 = await this.userManagementService.findOneAgency(
      newStaff.agencyId,
    );
    if (!dataFromDb3)
      throw new HttpException(
        `Agency not found with id ${newStaff.agencyId}`,
        HttpStatus.NOT_FOUND,
      );

    const userToBeRegistered =
      await this.userManagementService.validateToBeCreatedStaff(newStaff);
    if (userToBeRegistered) {
      throw new HttpException(
        'User already registered',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (this.userManagementService.isValidEmail(newStaff.email)) {
        const userRegistered = await this.userManagementService.findUserByEmail(
          newStaff.email,
        );

        if (!userRegistered) {
          const dataFromDb = await this.userManagementService.findOneRole(
            newStaff.roleId,
          );
          if (!dataFromDb)
            throw new HttpException('Data Not Found', HttpStatus.NOT_FOUND);

          const createdUser = new User();
          createdUser.firstName = newStaff.firstName;
          createdUser.lastName = newStaff.lastName;
          createdUser.email = newStaff.email;
          createdUser.phoneNumber = newStaff.phoneNumber;
          createdUser.gender = newStaff.gender;
          createdUser.approvalLevelId = newStaff.approvalLevelId;
          const randomPassword = this.generateRandomPassword();
          createdUser.password = await bcrypt.hash(randomPassword, saltRounds);

          createdUser.role = { id: dataFromDb.id } as any;
          createdUser.userType = { id: dataFromDb2.id } as any;
          createdUser.agency = { id: dataFromDb3.id } as any;

          // const userToBeCreated = this.userRepository.create(createdUser);
          // const savedUser = await userToBeCreated.save();

          const userToBeCreated = this.userRepository.create(createdUser);

          // Call the email sending service
          await this.sendEmailVerificationForStaffWithRandomPassword(
            newStaff.email,
            newStaff.phoneNumber,
            randomPassword,
          );

          return await userToBeCreated.save();

          // return savedUser;
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'User already registered',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'Missing mandatory parameters',
          HttpStatus.FORBIDDEN,
        );
      }
    }
  }
  // Send password to the user with new password
  async updatePasswordAndNotifyUser(userId: string): Promise<User> {
    // const user = await this.userRepository.findOne({ id: userId });
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: { userType: true },
    });
    if (!user) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    // Generate a new random password
    const newRandomPassword = this.generateRandomPassword();
    const hashedPassword = await bcrypt.hash(newRandomPassword, saltRounds);

    // Update the user's password
    user.password = hashedPassword;
    const updatedUser = await this.userRepository.save(user);

    // Send the new password via email
    await this.sendEmailVerificationForStaffWithRandomPassword(
      user.email,
      user.phoneNumber,
      newRandomPassword,
    );

    return updatedUser;
  }

  generateRandomPassword(): string {
    const length = 8;
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }
  // Send email with random password
  async sendEmailVerificationForStaffWithRandomPassword(
    email: string,
    phoneNumber: string,
    randomPassword: string,
  ): Promise<boolean> {
    const model = await this.emailVerificationRepository.findOne({
      where: { email: email },
    });

    console.log(email, phoneNumber, randomPassword);
    console.log(model);

    if (model && model.emailToken) {
      const requestData = {
        sender_name: 'KUBAKA MIS',
        sender_email: `${config.notification.senderEmail}`,
        // sender_email: '<EMAIL>',
        receiver_name: 'KUBAKA User',
        receiver_email: email,
        subject: 'Verify Email',
        message: `Hi there, <br>
          Thank you for registering with us! <br><br>
          To activate your account, simply click the link below: <br>
          And this is your default password: ${randomPassword}, please change it after login <br>
          Activate Account Here: ${config.host.url}/email/verify/ <br><br>
          Best regards, <br>
          KUBAKA Team`,
      };

      try {
        const [emailResponse, smsResponse] = await Promise.all([
          axios.post(
            `${config.notification.emailAPI}/email/send/`,
            // 'https://notification.kubaka.gov.rw/email/send/',
            requestData,
          ),
          axios.post(
            `${config.notification.smsAPI}/sms/send/`,
            // 'https://notification.kubaka.gov.rw/sms/send',
            {
              msisdn: phoneNumber,
              message: `Hi there,\n This is the link to activate your account:\n ${config.host.url}/email/verify/ \n This is your token ${model.emailToken}\n\nBest regards\nKUBAKA Team`,
            },
          ),
        ]);

        console.log(email, phoneNumber, randomPassword);
        console.log(model.emailToken);

        console.log('Email sent successfully:', emailResponse);
        console.log('SMS sent successfully:', smsResponse);

        return true;
      } catch (error) {
        console.error('Error sending email:', error.response.data);
        throw new HttpException(
          'Email not sent',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else {
      throw new HttpException('Email not sent', HttpStatus.FORBIDDEN);
    }
  }
  public async deactivateUser(email: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found.`);
    }

    user.isActive = false;
    await this.userRepository.save(user);

    return { message: 'User account has been deactivated successfully.' };
  }
  public async activateUser(email: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found.`);
    }

    user.isActive = true;
    await this.userRepository.save(user);

    return { message: 'User account has been activated successfully.' };
  }
  public async unlockUser(email: string): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found.`);
    }

    user.failedLoginAttempts = 0;
    user.lockUntil = null;
    await this.userRepository.save(user);

    return { message: 'User account has been unlocked successfully.' };
  }

  // expire password for one user
  public async expireAPasswordForASpecificUser(
    email: string,
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found.`);
    }

    user.isPasswordExpired = true;
    await this.userRepository.save(user);

    return { message: 'User password has been expired.' };
  }

  // unExpire password for one user
  public async unExpireAPasswordForASpecificUser(
    email: string,
  ): Promise<{ message: string }> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found.`);
    }

    user.isPasswordExpired = false;
    await this.userRepository.save(user);

    return { message: 'User password has been expired.' };
  }

  // expire all password of the users
  // public async expireAllStaffPasswords(): Promise<{ message: string }> {
  //   const staffUsers = await this.userRepository.find({
  //     where: {
  //       userType: { code: 'STF' },
  //     },
  //     relations: ['userType'],
  //   });

  //   if (!staffUsers.length) {
  //     return { message: 'No staff users found to update.' };
  //   }

  //   for (const user of staffUsers) {
  //     user.isPasswordExpired = true;
  //   }

  //   await this.userRepository.save(staffUsers);

  //   return {
  //     message: `${staffUsers.length} staff user(s) password(s) expired.`,
  //   };
  // }
  public async expireAllStaffPasswords(): Promise<{ message: string }> {
    const updateResult = await this.userRepository
      .createQueryBuilder()
      .update(User)
      .set({ isPasswordExpired: true })
      .where('userTypeId IN (SELECT id FROM user_type WHERE code = :code)', {
        code: 'STF',
      })
      .execute();

    return {
      message: `${updateResult.affected} staff user(s) password(s) expired.`,
    };
  }
}
