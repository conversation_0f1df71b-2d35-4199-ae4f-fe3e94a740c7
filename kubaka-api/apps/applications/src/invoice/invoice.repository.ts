import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import {
  Invoice,
  InvoiceItem,
  InvoiceStatus,
  InvoiceType,
  Price,
  Receipt,
} from '../entities/invoice.entity';
import { AbstractRepository } from '@app/common';

@Injectable()
export class InvoiceStatusRepository extends AbstractRepository<InvoiceStatus> {
  constructor(
    @InjectRepository(InvoiceStatus)
    invoiceStatusRepository: Repository<InvoiceStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, invoiceStatusRepository);
  }
}

@Injectable()
export class PriceRepository extends AbstractRepository<Price> {
  constructor(
    @InjectRepository(Price)
    priceRepository: Repository<Price>,
    entityManager: EntityManager,
  ) {
    super(entityManager, priceRepository);
  }
}

@Injectable()
export class InvoiceTypeRepository extends AbstractRepository<InvoiceType> {
  constructor(
    @InjectRepository(InvoiceType)
    invoiceTypeRepository: Repository<InvoiceType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, invoiceTypeRepository);
  }
}

@Injectable()
export class InvoiceItemRepository extends AbstractRepository<InvoiceItem> {
  constructor(
    @InjectRepository(InvoiceItem)
    invoiceItemRepository: Repository<InvoiceItem>,
    entityManager: EntityManager,
  ) {
    super(entityManager, invoiceItemRepository);
  }
}

@Injectable()
export class InvoiceRepository extends AbstractRepository<Invoice> {
  constructor(
    @InjectRepository(Invoice)
    invoiceRepository: Repository<Invoice>,
    entityManager: EntityManager,
  ) {
    super(entityManager, invoiceRepository);
  }
}

@Injectable()
export class ReceiptRepository extends AbstractRepository<Receipt> {
  constructor(
    @InjectRepository(Receipt)
    receiptRepository: Repository<Receipt>,
    entityManager: EntityManager,
  ) {
    super(entityManager, receiptRepository);
  }
}
