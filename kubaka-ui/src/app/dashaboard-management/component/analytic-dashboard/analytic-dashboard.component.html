<div class="stepper-content">
    <div class="stepper-header">
        <div class="dashboard-container">
            <div class="upper-counter">{{"analyticsDashboard.title" | translate }}</div>
            <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="form-container">
                <div class="form-set">
                    <div class="form-input">
                        <!-- <label>Year</label> -->
                        <div>
                            <select name="year" id="year" formControlName="year" required>
                                <option value="">{{"analyticsDashboard.filterbyYear" | translate }}</option>
                                <option *ngFor="let op of years" [value]="op">{{op}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-input">
                        <!-- <label>Agency</label> -->
                        <div>
                            <select name="agencyId" id="agencyId" formControlName="agencyId" required>
                                <option value="">{{"analyticsDashboard.filterbyAgency" | translate }}</option>
                                <option *ngFor="let op of agencies" [value]="op.id">{{op.name}}</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit">{{"analyticsDashboard.filter" | translate }}</button>
                </div>
            </form>
        </div>
    </div>
    <div class="stepper-body" *ngIf="isLoading">
      
        <div>
            <highcharts-chart [Highcharts]="Highcharts" [options]="chartOptions"
                style="width: 100%; height: 400px; display: block;"></highcharts-chart>
        </div>

        <div>
            <highcharts-chart [Highcharts]="Highcharts" [options]="pieChartOptions"
                style="width: 100%; height: 400px; display: block;"></highcharts-chart>
        </div>
    </div>
</div>
<!-- <ag-charts
    style="height: 100%"
    [options]="chartOptions">
   </ag-charts> -->