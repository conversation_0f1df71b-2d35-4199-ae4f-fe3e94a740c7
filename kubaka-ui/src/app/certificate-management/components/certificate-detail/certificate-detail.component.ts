import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApplicationService } from 'src/app/application/services/application.service';
import { environment } from 'src/environments/environment';
import html2pdf from 'html2pdf.js';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { jwtDecode } from 'jwt-decode';

@Component({
  selector: 'app-certificate-detail',
  templateUrl: './certificate-detail.component.html',
  styleUrls: ['./certificate-detail.component.scss']
})
export class CertificateDetailComponent {
  certificateDetail: any = {};
  cert: any = 'e';
  inspectionRight: any = {};
  developmentStatusData: any = {};
  currentUser: any;
  // project data
  projectData: any = {};
  associatedUpis: any[] = [];


  hostname = 'http://' + window.location.hostname + '/verification/certificate/';

  constructor(
    private applicationService: ApplicationService,
    private route: ActivatedRoute,
    private utilsService: UtilService,
    private router: Router,
    private sessionService: SessionService,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    route.params.subscribe((params: any) => {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'certificate/certificate/allData/' + params.id)
        .subscribe(
          data => {
            if (data[0].statusId === '1') {
              this.certificateDetail = data[0];

              this.loadProjectData(this.certificateDetail.upi);
              this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=' + this.certificateDetail.agencyCode)
                .subscribe(
                  data => {
                    this.certificateDetail.agencyName = data.items[0].name;
                  }
                )


            } else {
              this.router.navigate(['/account/certificate']);
              this.utilsService.showNotification(NOTIFICATION_COLOR.error, "The invoice needs to be paid first", "bottom", "center");
            }
          },
        )
    })


    // geting project data by UPI
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.certificateDetail.upi)
    //   .subscribe(
    //     data => {
    //       if (data.length > 0) {
    //         this.projectData = data[0];
    //         console.log("this is project data");
    //         console.log(this.projectData);

    //       } else {
    //         this.utilsService.showNotification(NOTIFICATION_COLOR.error, "Project data not found for the given UPI", "bottom", "center");
    //         console.error("this is my UPI");
    //         console.log(this.certificateDetail);
    //         this.router.navigate(['/account/certificate']);
    //       }
    //     },  error => {
    //       this.utilsService.showNotification(NOTIFICATION_COLOR.error, "Error fetching project data: " + error.message, "bottom", "center");
    //       this.router.navigate(['/account/certificate']);
    //     }

    //   )

    // colling the associated upis
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/associatedUPI/ByProject/' + this.route.snapshot.params['id'])
    //   .subscribe(
    //     data => {
    //       this.associatedUpis = data;
    //     }
    //   )
  }
loadProjectData(upi: string) {
  const encodedUpi = encodeURIComponent(upi); // Always safe
  this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + encodedUpi)
    .subscribe(
      data => {
        if (data.items?.length > 0) {
          this.projectData = data.items[0];
          // console.log("this is project data", this.projectData);
          this.loadAssociatedUpis(this.projectData.id);
        } else {
          this.utilsService.showNotification(NOTIFICATION_COLOR.error, "Project data not found for the given UPI", "bottom", "center");
          this.router.navigate(['/account/certificate']);
        }
      },
      error => {
        this.utilsService.showNotification(NOTIFICATION_COLOR.error, "Error fetching project data: " + error.message, "bottom", "center");
        this.router.navigate(['/account/certificate']);
      }
    );
}

loadAssociatedUpis(projectId: string) {
  this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'application/associatedUPI/ByProject/' + projectId
      )
    .subscribe(
      data => {
        this.associatedUpis = data;
        console.log("Associated UPIs this my:", this.associatedUpis);
      },
      error => {
        console.error("Error loading associated UPIs:", error);
      }
    );
}






  getQRdATA(data: any) {
    return JSON.stringify(data);
  }


  public generatePDF() {
    var element = document.getElementById('pdf');
    html2pdf(element, {
      margin: 1,
      filename: this.certificateDetail.permitTypes.name + '_certificate.pdf',
      // image: { type: 'jpeg', quality: 0.98 },
      image: { type: 'jpeg', quality: 1 },
      html2canvas: { dpi: 300, letterRendering: true, scale: 3 },
      // jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
      // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'landscape' },
      // copy: { enabled: true }, // Enable copying text
      link: { enabled: true }  // Enable clickable links
    });
  }




  changeCertificate(event: any) {
    this.cert = event;
  }



  inspectionForm(event: any) {
    this.inspectionRight.applicationDetail = this.certificateDetail;

    if (event === '1') {
      this.inspectionRight.isRandomInspection = true;
      this.inspectionRight.isFoundationInspection = false;
      this.inspectionRight.isOccupancyInspection = false;
      this.inspectionRight.name = 'Inspection';
    }
    if (event === '2') {
      this.inspectionRight.isFoundationInspection = true;
      this.inspectionRight.isOccupancyInspection = false;
      this.inspectionRight.isRandomInspection = false;
      this.inspectionRight.name = 'Foundation Inspection';
    }
    if (event === '3') {
      this.inspectionRight.isOccupancyInspection = true;
      this.inspectionRight.isFoundationInspection = false;
      this.inspectionRight.isRandomInspection = false;
      this.inspectionRight.name = 'Occupancy Inspection';
    }
    // Development status
    if (event === '4') {
      this.inspectionRight.name = 'Development Status';
      this.developmentStatusData.applicationDetail = this.certificateDetail;
      this.developmentStatusData.isDevelopmentStatus = true;
    }
  }


  closeInspection() {
    this.inspectionRight.isOccupancyInspection = false;
    this.inspectionRight.isFoundationInspection = false;
    this.inspectionRight.isRandomInspection = false;
  }
}

