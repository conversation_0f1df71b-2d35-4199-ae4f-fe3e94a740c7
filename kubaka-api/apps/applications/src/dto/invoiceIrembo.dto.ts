// IremboPaymentRequestInputDto.ts
export class IremboPaymentRequestInputDto {
  customer: CustomerInfoInputDto;
  description: string;
  expiryAt: Date;
  language: string;
  paymentAccountIdentifier: string;
  transactionId: string;
  paymentItems: PaymentItemInputDto[];
}

// PaymentItemInputDto.ts
export class PaymentItemInputDto {
  code: string;
  quantity: number;
  unitAmount: number;
}

// IremboResponseGetDto.ts
export class IremboResponseGetDto {
  statusCode: number;
  response: any;
  message: string;
}

// CustomerInfoInputDto.ts
export class CustomerInfoInputDto {
  email: string;
  phoneNumber: string;
}
