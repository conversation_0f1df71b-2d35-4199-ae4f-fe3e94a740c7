import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class ApprovalLevelDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field agencyId required' })
  @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
  agencyId: string;
}

export class ApprovalStatusDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field additionalCode is required' })
  @IsOptional()
  additionalCode: string;

  @ApiProperty({ description: 'The field approvalLevelId required' })
  @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
  approvalLevelId: string;
}

export class PermitCheckListDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field permitTypeId required' })
  @IsNotEmpty({ message: 'The field permitTypeId cannot be empty' })
  permitTypeId: string;
}

// with a file
// export class ApplicationApprovalDto {
//   @ApiProperty({ description: 'The field comment required' })
//   @IsNotEmpty({ message: 'The field comment cannot be empty' })
//   comment: string;

//   @ApiProperty()
//   @IsOptional()
//   applicationStatusId: string;

//   @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
//   @ApiProperty({ description: 'The field applicationId is required' })
//   applicationId: string;

//   @IsNotEmpty({ message: 'The field applicantUserId cannot be empty' })
//   @ApiProperty({ description: 'The field applicantUserId is required' })
//   applicantUserId: string;

//   @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
//   @ApiProperty({ description: 'The field approvalStatusId is required' })
//   approvalStatusId: string;

//   @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
//   @ApiProperty({ description: 'The field approvalLevelId is required' })
//   approvalLevelId: string;

//   @IsNotEmpty({ message: 'The field userId cannot be empty' })
//   @ApiProperty({ description: 'The field userId is required' })
//   userId: string;

//   @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
//   @ApiProperty({ description: 'The field agencyId is required' })
//   agencyId: string;

//   @ApiProperty({
//     type: 'string',
//     format: 'binary',
//     required: false,
//     description: 'The uploaded file',
//   })
//   @IsOptional()
//   file?: any;
// }

export class ApplicationApprovalDto {
  @ApiProperty({ description: 'The field comment required' })
  @IsNotEmpty({ message: 'The field comment cannot be empty' })
  comment: string;

  @ApiProperty()
  @IsOptional()
  applicationStatusId: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field applicantUserId cannot be empty' })
  @ApiProperty({ description: 'The field applicantUserId is required' })
  applicantUserId: string;

  @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
  @ApiProperty({ description: 'The field approvalStatusId is required' })
  approvalStatusId: string;

  @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
  @ApiProperty({ description: 'The field approvalLevelId is required' })
  approvalLevelId: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
  @ApiProperty({ description: 'The field agencyId is required' })
  agencyId: string;
}

// approval with irembo
export class ApplicationApprovalWithIremboDto {
  @ApiProperty({ description: 'The field comment required' })
  @IsNotEmpty({ message: 'The field comment cannot be empty' })
  comment: string;
  @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
  @ApiProperty({ description: 'The field approvalStatusId is required' })
  approvalStatusId: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field applicantUserId cannot be empty' })
  @ApiProperty({ description: 'The field applicantUserId is required' })
  applicantUserId: string;

  @ApiProperty()
  @IsOptional()
  applicationStatusId: string;
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
  @ApiProperty({ description: 'The field approvalLevelId is required' })
  approvalLevelId: string;

  @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
  @ApiProperty({ description: 'The field agencyId is required' })
  agencyId: string;
  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  @ApiProperty({ description: 'The field agencyCode is required' })
  agencyCode: string;
  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  @ApiProperty({ description: 'The field agencyCode is required' })
  amount: string;
  @IsNotEmpty({ message: 'The field transactionNumber cannot be empty' })
  @ApiProperty({ description: 'The field transactionNumber is required' })
  transactionNumber: string;
  @IsNotEmpty({ message: 'The field invoiceTypeId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceTypeId is required' })
  invoiceTypeId: string;
  @IsNotEmpty({ message: 'The field invoiceTypeId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceTypeId is required' })
  invoiceItemId: string;
  @IsNotEmpty({ message: 'The field invoiceStatusId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceStatusId is required' })
  invoiceStatusId: string;
  @IsNotEmpty({ message: 'The field permitTypeId cannot be empty' })
  @ApiProperty({ description: 'The field permitTypeId is required' })
  permitTypeId: string;
  @IsNotEmpty({ message: 'The field title cannot be empty' })
  @ApiProperty({ description: 'The field title is required' })
  title: string;
  @IsNotEmpty({ message: 'The field lows cannot be empty' })
  @ApiProperty({ description: 'The field lows is required' })
  lows: string;
  @IsNotEmpty({ message: 'The field expiredDate cannot be empty' })
  @ApiProperty({ description: 'The field expiredDate is required' })
  expiredDate: string;
  @IsNotEmpty({ message: 'The field backgroundUrl cannot be empty' })
  @ApiProperty({ description: 'The backgroundUrl lows is required' })
  backgroundUrl: string;
}

// with a file
export class ApplicationApprovalCheckListDto {
  @ApiProperty()
  @IsOptional()
  decision: string;

  @ApiProperty()
  @IsOptional()
  conditionsOfApproval: string;

  @ApiProperty()
  @IsOptional()
  structuralComment: string;

  @ApiProperty()
  @IsOptional()
  civilEngineeringComment: string;

  @ApiProperty()
  @IsOptional()
  architecturalComment: string;

  @ApiProperty()
  @IsOptional()
  urbanPlanningComment: string;

  @ApiProperty()
  @IsOptional()
  siteAnalysisComment: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
  @ApiProperty({ description: 'The field approvalStatusId is required' })
  approvalStatusId: string;

  @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
  @ApiProperty({ description: 'The field approvalLevelId is required' })
  approvalLevelId: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'The uploaded file',
  })
  @IsOptional()
  file?: any;
}

// export class ApplicationApprovalCheckListDto {
//   @ApiProperty()
//   @IsOptional()
//   decision: string;

//   @ApiProperty()
//   @IsOptional()
//   conditionsOfApproval: string;

//   @ApiProperty()
//   @IsOptional()
//   structuralComment: string;

//   @ApiProperty()
//   @IsOptional()
//   civilEngineeringComment: string;

//   @ApiProperty()
//   @IsOptional()
//   architecturalComment: string;

//   @ApiProperty()
//   @IsOptional()
//   urbanPlanningComment: string;

//   @ApiProperty()
//   @IsOptional()
//   siteAnalysisComment: string;

//   @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
//   @ApiProperty({ description: 'The field applicationId is required' })
//   applicationId: string;

//   @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
//   @ApiProperty({ description: 'The field approvalStatusId is required' })
//   approvalStatusId: string;

//   @IsNotEmpty({ message: 'The field approvalLevelId cannot be empty' })
//   @ApiProperty({ description: 'The field approvalLevelId is required' })
//   approvalLevelId: string;

//   @IsNotEmpty({ message: 'The field userId cannot be empty' })
//   @ApiProperty({ description: 'The field userId is required' })
//   userId: string;
// }

export class ApprovalDocumentDto {
  // @ApiProperty({ description: 'The field comment required' })
  // @IsNotEmpty({ message: 'The field comment cannot be empty' })
  // url: string;
  @ApiProperty({ description: 'The field documentId required' })
  @IsNotEmpty({ message: 'The field documentId cannot be empty' })
  documentId: string;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field user id is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field document cannot be empty' })
  @ApiProperty({ description: 'The field document id is required' })
  requiredDocumentId: string;

  @IsNotEmpty({ message: 'The field approvalStatusId cannot be empty' })
  @ApiProperty({ description: 'The field approvalStatusId is required' })
  approvalStatusId: string;
}

export class SiteStatusDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class ReviewerReportDto {
  @ApiProperty({ description: 'The field userId required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;

  @ApiProperty({ description: 'The field startDate is required' })
  @IsNotEmpty({ message: 'The field startDate cannot be empty' })
  startDate: string;

  @ApiProperty({ description: 'The field endDate is required' })
  @IsNotEmpty({ message: 'The field endDate cannot be empty' })
  endDate: string;
}
