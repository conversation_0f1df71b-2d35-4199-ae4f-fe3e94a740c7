import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApprovalService } from './approval.service';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import {
  ApplicationApprovalCheckListDto,
  ApplicationApprovalDto,
  ApprovalDocumentDto,
  ApprovalLevelDto,
  ApprovalStatusDto,
  PermitCheckListDto,
  ReviewerReportDto,
  ApplicationApprovalWithIremboDto,
} from '../dto/approval.dto';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ApprovalLevel,
  ApprovalStatus,
  PermitCheckList,
} from '../entities/approval.entity';
import { Repository } from 'typeorm';
import { GenericSearch } from './generic-search.service';
import { FileInterceptor } from '@nestjs/platform-express';

import { Request } from 'express';

@ApiTags('approval')
@Controller('approval')
export class ApprovalController {
  constructor(
    private readonly approvalService: ApprovalService,

    @InjectRepository(ApprovalLevel)
    protected readonly approvalLevelRepository: Repository<ApprovalLevel>,
    protected readonly genericSearch: GenericSearch<ApprovalLevel>,

    @InjectRepository(ApprovalStatus)
    protected readonly approvalStatusRepository: Repository<ApprovalStatus>,
    protected readonly genericSearch2: GenericSearch<ApprovalStatus>,

    @InjectRepository(PermitCheckList)
    protected readonly permitCheckListRepository: Repository<PermitCheckList>,
    protected readonly genericSearch3: GenericSearch<PermitCheckList>,
  ) {}

  // Approval Level
  @Post('approvalLevel')
  async CreateApprovalLevel(@Body() approvalLevelDto: ApprovalLevelDto) {
    return this.approvalService.createApprovalLevel(approvalLevelDto);
  }

  @Get('approvalLevel')
  async findAllApprovalLevels() {
    return this.approvalService.findAllApprovalLevels();
  }

  @Get('approvalLevel/:id')
  async findOneApprovalLevel(@Param('id') id: string) {
    return this.approvalService.findOneApprovalLevel(id);
  }

  @Patch('approvalLevel/:id')
  async updateApprovalLevel(
    @Param('id') id: string,
    @Body() approvalLevelDto: ApprovalLevelDto,
  ) {
    return this.approvalService.updateApprovalLevel(id, approvalLevelDto);
  }

  @Delete('approvalLevel/:id')
  async removeApprovalLevel(@Param('id') id: string) {
    return this.approvalService.removeApprovalLevel(id);
  }

  @Get('approvalLevel/code/search')
  async searchApprovalLevelByCode(@Query('search') search: string) {
    const searchFields: (keyof ApprovalLevel)[] = ['code'];
    return this.genericSearch.search(
      this.approvalLevelRepository,
      searchFields,
      search,
    );
  }

  // Approval Status
  @Post('approvalStatus')
  async CreateApprovalStatus(@Body() approvalStatusDto: ApprovalStatusDto) {
    return this.approvalService.createApprovalStatus(approvalStatusDto);
  }

  @Get('approvalStatus')
  async findAllApprovalStatus() {
    return this.approvalService.findAllApprovalStatus();
  }

  @Get('approvalStatus/:id')
  async findOneApprovalStatus(@Param('id') id: string) {
    return this.approvalService.findOneApprovalStatus(id);
  }

  @Patch('approvalStatus/:id')
  async updateApprovalStatus(
    @Param('id') id: string,
    @Body() approvalStatusDto: ApprovalStatusDto,
  ) {
    return this.approvalService.updateApprovalStatus(id, approvalStatusDto);
  }

  @Delete('approvalStatus/:id')
  async removeApprovalStatus(@Param('id') id: string) {
    return this.approvalService.removeApprovalStatus(id);
  }

  @Get('approvalStatus/code/search')
  async searchApprovalStatusByCode(@Query('search') search: string) {
    const searchFields: (keyof ApprovalStatus)[] = ['code'];
    return this.genericSearch2.search(
      this.approvalStatusRepository,
      searchFields,
      search,
    );
  }

  @Get('approvalStatus/approvalLevel/:approvalLevelId')
  async findApprovalStatusByApprovalLevelId(
    @Param('approvalLevelId') approvalLevelId: string,
  ) {
    return this.approvalService.findApprovalStatusByApprovalLevelId(
      approvalLevelId,
    );
  }

  // // Application Approval with file
  // @Post('applicationApproval')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiConsumes('multipart/form-data')
  // @ApiBody({
  //   description: 'Create Application Approval with file upload',
  //   type: ApplicationApprovalDto,
  // })
  // async createApplicationApproval(
  //   @Body() applicationApprovalDto: ApplicationApprovalDto,
  //   @UploadedFile() file: Express.Multer.File,
  // ) {
  //   return this.approvalService.createApplicationApproval(
  //     applicationApprovalDto,
  //     file,
  //   );
  // }

  // Application Approval with IP address and browser
  @Post('applicationApproval')
  async createApplicationApproval(
    @Body() applicationApprovalDto: ApplicationApprovalDto,
    @Req() request: Request,
  ) {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.approvalService.createApplicationApproval(
      applicationApprovalDto,
      clientIp,
      userAgent,
    );
  }
  // application approval with irembo
  @Post('applicationApprovalWithIrembo')
  async createApplicationApprovalWithIrembo(
    @Body() applicationApprovalDto: ApplicationApprovalWithIremboDto,
    @Req() request: Request,
  ) {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.approvalService.createApplicationApprovalWithIrembo(
      applicationApprovalDto,
      clientIp,
      userAgent,
    );
  }

  // @Post('applicationApproval')
  // async createApplicationApproval(
  //   @Body() applicationApprovalDto: ApplicationApprovalDto,
  //   @Req() request: Request,
  // ) {
  //   const clientIp = this.getClientIp(request);
  //   const userAgent = request.headers['user-agent'];

  //   return this.approvalService.createApplicationApproval(
  //     applicationApprovalDto,
  //     clientIp,
  //     userAgent,
  //   );
  // }

  /**
   * Utility function to extract the real client IP address.
   */
  private getClientIp(request: Request): string {
    // Check for X-Forwarded-For header (used by proxies)
    const xForwardedFor = request.headers['x-forwarded-for'] as string;
    if (xForwardedFor) {
      // Return the first IP in the chain
      return xForwardedFor.split(',')[0].trim();
    }

    // Check for X-Real-IP header
    const xRealIp = request.headers['x-real-ip'] as string;
    if (xRealIp) {
      return xRealIp;
    }

    // Fallback to connection remoteAddress
    return request.socket.remoteAddress || 'Unknown IP';
  }

  // // Application Approval
  // @Post('applicationApproval')
  // async CreateApplicationApproval(
  //   @Body() applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   return this.approvalService.createApplicationApproval(
  //     applicationApprovalDto,
  //   );
  // }

  @Get('applicationApproval')
  async findAllApplicationApprovals() {
    return this.approvalService.findAllApplicationApprovals();
  }

  @Get('applicationApproval/:id')
  async findOneApplicationApproval(@Param('id') id: string) {
    return this.approvalService.findOneApplicationApproval(id);
  }

  @Get('applicationApproval/applicationId/:applicationId')
  async findOneApplicationApprovalByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.approvalService.findOneApplicationApprovalByApplicationId(
      applicationId,
    );
  }

  @Get('applicationApproval/:applicationId/status/:applicationStatusId')
  async findOneApplicationApprovalByApplicationIdAndApplicationStatus(
    @Param('applicationId') applicationId: string,
    @Param('applicationStatusId') applicationStatusId: string,
  ) {
    return this.approvalService.findApplicationApprovalByApplicationIdAndApprovalStatus(
      applicationId,
      applicationStatusId,
    );
  }

  @Get('applicationApproval/reviewer/:userId')
  async findApplicationsApprovalByUserId(@Param('userId') userId: string) {
    return this.approvalService.findApplicationsApprovalByUserId(userId);
  }

  @Post('applicationApproval/staffReport/')
  async findApprovalsByUserIdInDateRange(
    @Body() reviewerReportDto: ReviewerReportDto,
  ) {
    return this.approvalService.findApprovalsByUserIdInDateRange(
      reviewerReportDto,
    );
  }
  // @ApiTags('application')
  // @Post('applicationApproval/report')
  // async findApprovalsByUserIdInDateRange(
  //   @Body('userId') userId?: string,
  //   @Body('startDate') startDate?: string,
  //   @Body('endDate') endDate?: string,
  // ) {
  //   return this.approvalService.findApprovalsByUserIdInDateRange(
  //     userId,
  //     startDate,
  //     endDate,
  //   );
  // }

  @Patch('applicationApproval/:id')
  async updateApplicationApproval(
    @Param('id') id: string,
    @Body() applicationApprovalDto: ApplicationApprovalDto,
  ) {
    return this.approvalService.updateApplicationApproval(
      id,
      applicationApprovalDto,
    );
  }

  @Delete('applicationApproval/:id')
  async removeApplicationApproval(@Param('id') id: string) {
    return this.approvalService.removeApplicationApproval(id);
  }

  // Permit CheckList
  @Post('permitCheckList')
  async CreatePermitCheckList(@Body() permitCheckListDto: PermitCheckListDto) {
    return this.approvalService.createPermitCheckList(permitCheckListDto);
  }

  @Get('permitCheckList')
  async findAllPermitCheckLists() {
    return this.approvalService.findAllPermitCheckLists();
  }

  @Get('permitCheckList/:id')
  async findOnePermitCheckList(@Param('id') id: string) {
    return this.approvalService.findOnePermitCheckList(id);
  }

  @Patch('permitCheckList/:id')
  async updatePermitCheckList(
    @Param('id') id: string,
    @Body() permitCheckListDto: PermitCheckListDto,
  ) {
    return this.approvalService.updatePermitCheckList(id, permitCheckListDto);
  }

  @Delete('permitCheckList/:id')
  async removePermitCheckList(@Param('id') id: string) {
    return this.approvalService.removePermitCheckList(id);
  }

  @Get('permitCheckList/code/search')
  async searchPermitCheckListByCode(@Query('search') search: string) {
    const searchFields: (keyof PermitCheckList)[] = ['code'];
    return this.genericSearch3.search(
      this.permitCheckListRepository,
      searchFields,
      search,
    );
  }

  // // Application Approval CheckList
  // @Post('applicationApprovalCheckList')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiConsumes('multipart/form-data')
  // @ApiBody({
  //   description: 'Create Application checklist with file upload',
  //   type: ApplicationApprovalCheckListDto,
  // })
  // async CreateApplicationApprovalCheckList(
  //   @Body() applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  //   @UploadedFile() file: Express.Multer.File,
  // ) {
  //   return this.approvalService.createApplicationApprovalCheckList(
  //     applicationApprovalCheckListDto,
  //     file,
  //   );
  // }

  // Application Approval CheckList with the submissionlogs
  @Post('applicationApprovalCheckList')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Create Application checklist with file upload',
    type: ApplicationApprovalCheckListDto,
  })
  async CreateApplicationApprovalCheckList(
    @Body() applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request,
  ) {
    const clientIp = request.ip; // Get the client's IP address
    const userAgent = request.headers['user-agent']; // Get the user-agent header

    return this.approvalService.createApplicationApprovalCheckList(
      applicationApprovalCheckListDto,
      file,
      clientIp,
      userAgent,
    );
  }

  // // Application Approval CheckList
  // @Post('applicationApprovalCheckList')
  // async CreateApplicationApprovalCheckList(
  //   @Body() applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   return this.approvalService.createApplicationApprovalCheckList(
  //     applicationApprovalCheckListDto,
  //   );
  // }

  @Get('applicationApprovalCheckList')
  async findAllApplicationApprovalCheckLists() {
    return this.approvalService.findAllApplicationApprovalCheckLists();
  }

  @Get('applicationApprovalCheckList/:id')
  async findOneApplicationApprovalCheckList(@Param('id') id: string) {
    return this.approvalService.findOneApplicationApprovalCheckList(id);
  }

  @Get('applicationApprovalCheckList/allDetails/:id')
  async findOneApplicationApprovalCheckListAllDetails(@Param('id') id: string) {
    return this.approvalService.findOneApplicationApprovalCheckListAllDetails(
      id,
    );
  }

  @Get('applicationApprovalCheckList/user/:userId')
  async findAllApplicationApprovalCheckListByUserId(
    @Param('userId') userId: string,
  ) {
    return this.approvalService.findAllApplicationApprovalCheckListByUserId(
      userId,
    );
  }

  @Get('applicationApprovalCheckList/all/:applicationId')
  async findAllApplicationApprovalCheckListByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.approvalService.findAllApplicationApprovalCheckListByApplicationId(
      applicationId,
    );
  }

  @Patch('applicationApprovalCheckList/:id')
  async updateApplicationApprovalCheckList(
    @Param('id') id: string,
    @Body() applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  ) {
    return this.approvalService.updateApplicationApprovalCheckList(
      id,
      applicationApprovalCheckListDto,
    );
  }

  @Delete('applicationApprovalCheckList/:id')
  async removeApplicationApprovalCheckList(@Param('id') id: string) {
    return this.approvalService.removeApplicationApprovalCheckList(id);
  }

  // Approval Document
  @Post('approvalDocument')
  async CreateApprovalDocument(
    @Body() approvalDocumentDto: ApprovalDocumentDto,
  ) {
    return this.approvalService.createApprovalDocument(approvalDocumentDto);
  }

  @Get('approvalDocument')
  async findAllApprovalDocuments() {
    return this.approvalService.findAllApprovalDocuments();
  }

  @Get('approvalDocument/:id')
  async findOneApprovalDocument(@Param('id') id: string) {
    return this.approvalService.findOneApprovalDocument(id);
  }

  @Patch('approvalDocument/:id')
  async updateApprovalDocument(
    @Param('id') id: string,
    @Body() approvalDocumentDto: ApprovalDocumentDto,
  ) {
    return this.approvalService.updateApprovalDocument(id, approvalDocumentDto);
  }

  @Delete('approvalDocument/:id')
  async removeApprovalDocument(@Param('id') id: string) {
    return this.approvalService.removeApprovalDocument(id);
  }

  @ApiTags('application')
  @Get('application/dashboardAgency/RHA')
  async findAllApplicationByAgencyIdOnDashboardRHA() {
    return this.approvalService.countApplicationsByStatusByAgencyRHA();
  }

  @Get('application/approvalStatus/:approvalStatusId')
  async findAllApplicationByApprovalStatusId(
    @Param('approvalStatusId') approvalStatusId: string,
  ) {
    return this.approvalService.findAllApplicationsWithDetailsByApprovalStatus2(
      approvalStatusId,
    );
    // return this.approvalService.findAllApplicationsWithDetailsByApprovalStatus(
    //   approvalStatusId,
    // );
  }
}
