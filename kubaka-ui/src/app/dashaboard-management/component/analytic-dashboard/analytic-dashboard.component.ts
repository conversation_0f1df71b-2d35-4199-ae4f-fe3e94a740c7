import { Component } from '@angular/core';
import * as Highcharts from 'highcharts';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { ApplicationService } from 'src/app/application/services/application.service';
import { APIURLPATH } from 'src/app/shared/services/url-path';
@Component({
  selector: 'app-analytic-dashboard',
  templateUrl: './analytic-dashboard.component.html',
  styleUrls: ['./analytic-dashboard.component.scss']
})
export class AnalyticDashboardComponent {
  Highcharts: typeof Highcharts = Highcharts;
  chartOptions = {};
  pieChartOptions = {};
  userForm!: UntypedFormGroup;
  // public chartAgOptions!: AgChartOptions;
  isLoading: boolean = false;
  years: any[] = Array.from({ length: 41 }, (_, i) => 2000 + i);
  agencies: any[] = [];


  constructor(
    private formBuilder: UntypedFormBuilder,
    private applicationService: ApplicationService
  ) {

    this.applicationService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => { this.agencies = data; },
        error => { }
      )

  }



  initialiaze() {
    this.userForm = this.formBuilder.group({
      year: [""],
      agencyId: [""],
    }
    );
  }
  ngOnInit() {
    this.initialiaze();
    this.isLoading = false;
    this.applicationService.saveAssetWithPath(this.userForm.value, environment.applicationUrl + 'application/applicationChartDashboard')
      .subscribe(
        data => {
          
          const categories = data.barChartData.map((item: any) => item.category);
          const values = data.barChartData.map((item: any) => item.value);
         
          this.chartOptions = {
            chart: {
              type: 'column'
            },
            title: {
              text: 'All Permit types',
              style: {
                color: '#040202',
              }
            },
            exporting: {
              enabled: true
            },
            subtitle: {
              text: ''
            },
            xAxis: {
              style: {
                color: '#040202',
                // font: 'bold 16px "Trebuchet MS", Verdana, sans-serif'
              },
              categories: categories,
              crosshair: true,
            },
            yAxis: {
              title: {
                text: ''
              }
            },
            plotOptions: {
              line: {
                dataLabels: {
                  enabled: true
                },
                enableMouseTracking: false
              }
            },
            series: [{
              name: 'Number',
              data: values
            }]
          };


          this.pieChartOptions = {
            chart: {
              type: 'pie'
            },
            title: {
              text: 'Gender Statics',
              style: {
                color: '#040202',
              }
            },
            exporting: {
              enabled: true
            },
            subtitle: {
              text: ''
            },
            tooltip: {
              valueSuffix: '%'
            },
            plotOptions: {
              series: {
                allowPointSelect: true,
                cursor: 'pointer',
                dataLabels: [{
                  enabled: true,
                  distance: 20
                }, {
                  enabled: true,
                  distance: -40,
                  format: '{point.percentage:.1f}%',
                  style: {
                    fontSize: '1.2em',
                    textOutline: 'none',
                    opacity: 0.7
                  },
                  filter: {
                    operator: '>',
                    property: 'percentage',
                    value: 10
                  }
                }]
              }
            },
            series: data.pieChartData.series

            // [
            //   {
            //     name: 'Percentage',
            //     colorByPoint: true,
            //     data: [
            //       {
            //         name: 'Male',
            //         y: 55.02
            //       },
            //       {
            //         name: 'Female',
            //         // sliced: true,
            //         // selected: true,
            //         y: 26.71
            //       }
            //     ]
            //   }
            // ]
          };
          this.isLoading = true;
        }
      )


    // const jsonData = {
    //   data: [
    //     { category: "Renewal of permit", value: 100 },
    //     { category: "Construction of Fence", value: 280000 },
    //     { category: "Demolition - Full/Partial", value: 129000 },
    //     { category: "Occupancy Permit", value: 64300 },
    //     { category: "Project Modification", value: 54000 },
    //     { category: "Refurbishment with structural alteration", value: 34300 },
    //     { category: "Refurbishment without structural alteration", value: 34 },
    //     { category: "Temporary Structure", value: 34300 },
    //     { category: "New construction permit", value: 34300 },
    //     { category: "Inspection Permit", value: 34300 }
    //   ]
    // };
    // const categories = jsonData.data.map(item => item.category);
    // const values = jsonData.data.map(item => item.value);

    // this.chartOptions = {
    //   chart: {
    //     type: 'column'
    //   },
    //   title: {
    //     text: 'All Permit types',
    //     style: {
    //       color: '#040202',
    //     }
    //   },
    //   exporting: {
    //     enabled: true
    //   },
    //   subtitle: {
    //     text: ''
    //   },
    //   xAxis: {
    //     style: {
    //       color: '#040202',
    //       // font: 'bold 16px "Trebuchet MS", Verdana, sans-serif'
    //     },
    //     categories: categories,
    //     crosshair: true,
    //   },
    //   yAxis: {
    //     title: {
    //       text: ''
    //     }
    //   },
    //   plotOptions: {
    //     line: {
    //       dataLabels: {
    //         enabled: true
    //       },
    //       enableMouseTracking: false
    //     }
    //   },
    //   series: [{
    //     name: 'Number',
    //     data: values
    //   }]
    // };






    // this.pieChartOptions = {
    //   chart: {
    //     type: 'pie'
    //   },
    //   title: {
    //     text: 'Gender Statics',
    //     style: {
    //       color: '#040202',
    //     }
    //   },
    //   exporting: {
    //     enabled: true
    //   },
    //   subtitle: {
    //     text: ''
    //   },
    //   tooltip: {
    //     valueSuffix: '%'
    //   },
    //   plotOptions: {
    //     series: {
    //       allowPointSelect: true,
    //       cursor: 'pointer',
    //       dataLabels: [{
    //         enabled: true,
    //         distance: 20
    //       }, {
    //         enabled: true,
    //         distance: -40,
    //         format: '{point.percentage:.1f}%',
    //         style: {
    //           fontSize: '1.2em',
    //           textOutline: 'none',
    //           opacity: 0.7
    //         },
    //         filter: {
    //           operator: '>',
    //           property: 'percentage',
    //           value: 10
    //         }
    //       }]
    //     }
    //   },
    //   series: [
    //     {
    //       name: 'Percentage',
    //       colorByPoint: true,
    //       data: [
    //         {
    //           name: 'Male',
    //           y: 55.02
    //         },
    //         {
    //           name: 'Female',
    //           // sliced: true,
    //           // selected: true,
    //           y: 26.71
    //         }
    //       ]
    //     }
    //   ]
    // };
    

    // this.chartOptions = {
    //   // Data: Data to be displayed in the chart
    //   data: [
    //     { month: 'Jan', avgTemp: 2.3, iceCreamSales: 162000 },
    //     { month: 'Mar', avgTemp: 6.3, iceCreamSales: 302000 },
    //     { month: 'May', avgTemp: 16.2, iceCreamSales: 800000 },
    //     { month: 'Jul', avgTemp: 22.8, iceCreamSales: 1254000 },
    //     { month: 'Sep', avgTemp: 14.5, iceCreamSales: 950000 },
    //     { month: 'Nov', avgTemp: 8.9, iceCreamSales: 200000 },
    //   ],
    //   // Series: Defines which chart type and data to use
    //   series: [{ type: 'bar', xKey: 'month', yKey: 'iceCreamSales' }]
    // };
  }


  onSubmit() {
    this.isLoading = false;
    this.applicationService.saveAssetWithPath(this.userForm.value, environment.applicationUrl + 'application/applicationChartDashboard')
      .subscribe(
        data => {
          
          const categories = data.barChartData.map((item: any) => item.category);
          const values = data.barChartData.map((item: any) => item.value);
       
          this.chartOptions = {
            chart: {
              type: 'column'
            },
            title: {
              text: 'All Permit types',
              style: {
                color: '#040202',
              }
            },
            exporting: {
              enabled: true
            },
            subtitle: {
              text: ''
            },
            xAxis: {
              style: {
                color: '#040202',
                // font: 'bold 16px "Trebuchet MS", Verdana, sans-serif'
              },
              categories: categories,
              crosshair: true,
            },
            yAxis: {
              title: {
                text: ''
              }
            },
            plotOptions: {
              line: {
                dataLabels: {
                  enabled: true
                },
                enableMouseTracking: false
              }
            },
            series: [{
              name: 'Number',
              data: values
            }]
          };


          this.pieChartOptions = {
            chart: {
              type: 'pie'
            },
            title: {
              text: 'Gender Statics',
              style: {
                color: '#040202',
              }
            },
            exporting: {
              enabled: true
            },
            subtitle: {
              text: ''
            },
            tooltip: {
              valueSuffix: '%'
            },
            plotOptions: {
              series: {
                allowPointSelect: true,
                cursor: 'pointer',
                dataLabels: [{
                  enabled: true,
                  distance: 20
                }, {
                  enabled: true,
                  distance: -40,
                  format: '{point.percentage:.1f}%',
                  style: {
                    fontSize: '1.2em',
                    textOutline: 'none',
                    opacity: 0.7
                  },
                  filter: {
                    operator: '>',
                    property: 'percentage',
                    value: 10
                  }
                }]
              }
            },
            series: data.pieChartData.series
          };
          this.isLoading = true;
        }
      )
  }
}
