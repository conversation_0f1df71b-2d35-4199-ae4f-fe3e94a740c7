import { Component, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ngxCsv } from 'ngx-csv';
import { NgbdAdvancedSortableHeader } from "src/app/shared/directives/sortable.directive";
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { GENDERS, NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';
import { UserMgtService } from '../services/user-mgt.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { jwtDecode } from 'jwt-decode';
import { GlobalRefreshService } from 'src/app/shared/services/global-refresh.service';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent {
  userForm!: UntypedFormGroup;
  verfiyForm!: UntypedFormGroup;
  submitted = false;
  lists: any[] = [];
  total!: number;
  @ViewChildren(NgbdAdvancedSortableHeader)
  headers!: QueryList<NgbdAdvancedSortableHeader>;
  page = 1;
  pageSize = 50;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  econtent: any = {};
  masterSelected!: boolean;
  roles: any[] = [];
  userTypes: any[] = [];
  agencies: any[] = [];
  levels: any[] = [];
  genders = GENDERS;
  districts: any[] = [];
  sectors: any[] = [];
  fullList: any[] = [];
  filteredList: any[] = [];
  filteredAgency = '';
  currentUser: any =
    {};

  confirmData: any = {}
  private refreshSub: any;
  constructor(
    private modalService: NgbModal,
    public userService: UserMgtService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private sessionService: SessionService,
    private globalRefresh: GlobalRefreshService
  ) {


    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.loadList();
    this.lookups();
  }



  findSectorsByDistrict() {
    let district = this.districts.find((x: any) => x.id === this.userForm.value.districtId);
    this.userService.findAllWithPath(environment.authUrl + 'user-management/sectors/ByDistrictCode/' + district.code)
      .subscribe(
        data => { this.sectors = data; },
        error => { }
      )
  }



  resendCode(event: any) {
    this.userService.saveAssetWithPath({
      "email": event.email
    }, environment.authUrl + 'auth/resend-verification')
      .subscribe(data => {
        this.utilService.showNotification(NOTIFICATION_COLOR.success, "Verification code resent to the user's email", "bottom", "center")
      })
  }

  deactivate(event: any, content: any, action: any) {
    this.modalService.open(content, { size: "lg", centered: true });
    this.confirmData.name = 'Deactivate';
    this.confirmData.email = event.email;
    this.confirmData.action = action;
    // this.userService.saveAssetWithPath({
    //   "email": event.email
    // }, environment.authUrl + 'auth/deactivate-user')
    //   .subscribe(data => {
    //     this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been deactivated successfully.", "bottom", "center")
    //   })
  }

  activate(event: any, content: any, action: any) {
    this.modalService.open(content, { size: "lg", centered: true });
    this.confirmData.name = 'Activate';
    this.confirmData.email = event.email;
    this.confirmData.action = action;
    // this.userService.saveAssetWithPath({
    //   "email": event.email
    // }, environment.authUrl + 'auth/activate-user')
    //   .subscribe(data => {
    //     this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been activated successfully", "bottom", "center")
    //   })
  }

  unlockUser(event: any, content: any, action: any) {
    this.modalService.open(content, { size: "lg", centered: true });
    this.confirmData.name = 'Unlock';
    this.confirmData.email = event.email;
    this.confirmData.action = action;
    // this.userService.saveAssetWithPath({
    //   "email": event.email
    // }, environment.authUrl + 'auth/unlock-user')
    //   .subscribe(data => {
    //     this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been unlocked successfully", "bottom", "center")
    //   })
  }


  verifyUser(event: any, content: any, action: any) {
    this.modalService.open(content, { size: "lg", centered: true });
    this.confirmData.name = 'Verify';
    this.confirmData.email = event.email;
    this.confirmData.action = action;

    // this.userService.findAllWithPath(environment.authUrl + 'auth/getToken/' + event.email)
    //   .subscribe(
    //     data => {
    //       if (data) {
    //         this.verfiyForm.controls['otp'].setValue(data?.emailToken);
    //         this.modalService.open(content, { size: "md", centered: true });
    //       }
    //     }
    //   )
  }

  expiredPassword() {
    this.userService.findAllWithPath(environment.authUrl + 'auth/expire-staff-passwords/')
      .subscribe(
        data => {
          if (data) {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "All staff password has been expired successfully", "bottom", "center");
            this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "bottom", "center");
          }
        }
      )
  }

  expiredPasswordOnePerson(event: any) {
    this.userService.patchAssetWithoutParams({
      "email": event.email
    }, environment.authUrl + 'auth/expire-password/')
      .subscribe(data => {
        this.utilService.showNotification(NOTIFICATION_COLOR.success, "User  password has been expired successfully", "bottom", "center")
        this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "bottom", "center")
      })
  }

  onVerify() {
    this.submitted = true;
    this.userService.findAllWithPath(environment.authUrl + 'auth/verify/' + this.verfiyForm.value.otp)
      .subscribe(
        data => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "User verified successfully", "bottom", "center");
          this.loadList();
          this.modalService.dismissAll();
        }, error => {
          this.submitted = false;
        }
      )
  }


  doAction() {
    if (this.confirmData.action === '0') {
      this.userService.saveAssetWithPath({
        "email": this.confirmData.email
      }, environment.authUrl + 'auth/deactivate-user')
        .subscribe(data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been deactivated successfully.", "bottom", "center");
          this.loadList();
          this.modalService.dismissAll();
        })
    }
    if (this.confirmData.action === '1') {
      this.userService.saveAssetWithPath({
        "email": this.confirmData.email
      }, environment.authUrl + 'auth/activate-user')
        .subscribe(data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been activated successfully", "bottom", "center");
          this.loadList();
          this.modalService.dismissAll();
        })
    }
    if (this.confirmData.action === '2') {
      this.userService.saveAssetWithPath({
        "email": this.confirmData.email
      }, environment.authUrl + 'auth/unlock-user')
        .subscribe(data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "User account has been unlocked successfully", "bottom", "center");
          this.loadList();
          this.modalService.dismissAll();
        })
    }
    if (this.confirmData.action === '4') {
      this.userService.findAllWithPath(environment.authUrl + 'auth/getToken/' + this.confirmData.email)
        .subscribe(data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "User has been verified successfully", "bottom", "center");
          this.loadList();
          this.modalService.dismissAll();
        })
    }
  }



  ngOnDestroy() {
    this.userService.searchTerm = '';
    if (this.refreshSub) {
      this.refreshSub.unsubscribe();
    }
  }

  lookups() {
    this.userService.findAllWithPath(environment.authUrl + APIURLPATH.DISTRICTS)
      .subscribe(
        data => { this.districts = data; },
        error => { }
      )
    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.APPROVAL_LEVEL)
      .subscribe(
        data => { this.levels = data; },
        error => { }
      )
    this.userService.findAllWithPath(environment.authUrl + APIURLPATH.ROLE)
      .subscribe(
        data => { this.roles = data; },
      )

    this.userService.findAllWithPath(environment.authUrl + APIURLPATH.USERTYPE)
      .subscribe(
        data => { this.userTypes = data; },
      )

    this.userService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => { this.agencies = data; },
      )
  }


  loadList() {
    this.userService
      .findAllWithPath(environment.authUrl + "user-management/users")
      .subscribe((data) => {
        setTimeout(() => {
          document.getElementById("elmLoader")?.classList.add("d-none");
        }, 1200);
        this.fullList = data; // Store full data for search
        this.totalRecords = this.fullList.length;
        this.filterAndPaginate(); // Apply filter and pagination
      });
  }

  filterAndPaginate() {
    // Apply filtering on the full list
    const filtered = this.fullList.filter(item => this.applyFilter(item));

    // Update filtered list for search
    this.filteredList = filtered;
    this.totalRecords = filtered.length;

    // Pagination logic
    this.startIndex = (this.page - 1) * this.pageSize;
    this.endIndex = this.startIndex + this.pageSize;

    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }

    // Slice the filtered list for pagination
    this.lists = filtered.slice(this.startIndex, this.endIndex);
  }

  // applyFilter(item: any): boolean {
  //   const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
  //   const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';

  //   // Search in multiple fields (direct values and nested values)
  //   const matchesTerm = term ? (
  //     Object.values(item).some(val => String(val).toLowerCase().includes(term)) ||
  //     (item.role?.name && item.role.name.toLowerCase().includes(term)) ||
  //     (item.agency?.name && item.agency.name.toLowerCase().includes(term)) ||
  //     (item.userType?.name && item.userType.name.toLowerCase().includes(term))
  //   ) : true;

  //   const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

  //   return matchesTerm && matchesDate;
  // }

  applyFilter(item: any): boolean {
    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';
    const selectedRole = this.userService.status && this.userService.status !== 'all' ? this.userService.status : null;
    const selectedAgency = this.filteredAgency && this.filteredAgency !== 'all' ? this.filteredAgency : null;

    // Check if the item matches the search term (including nested fields)
    const matchesTerm = term ? (
      Object.values(item).some(val => String(val).toLowerCase().includes(term)) ||
      (item.role?.name && item.role.name.toLowerCase().includes(term)) ||
      (item.agency?.name && item.agency.name.toLowerCase().includes(term)) ||
      (item.userType?.name && item.userType.name.toLowerCase().includes(term))
    ) : true;

    // Check if the item matches the selected role
    const matchesRole = selectedRole ? item.role?.id === selectedRole : true;


    // Check if the item matches the selected agency
    const matchesAgency = selectedAgency ? item.agency?.id === selectedAgency : true;




    // Check if the item matches the date filter
    const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

    return matchesTerm && matchesRole && matchesAgency && matchesDate;
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.page = 1; // Reset to the first page when searching
    this.filterAndPaginate();
  }

  getPremiumData() {
    this.filterAndPaginate();
  }


  filterByRole() {
    // if (this.userService.status === 'all') {
    //   this.fullList = this.content;
    //   this.filterAndPaginate();
    // } else {
    //   this.fullList = this.content.filter((role: any) => role.role.id === this.userService.status);
    //   this.filterAndPaginate();
    // }

    this.page = 1; // Reset to first page when filtering
    this.filterAndPaginate();

  }


  filterByAgency() {
    // if (this.filteredAgency === 'all') {
    //   this.fullList = this.content;
    //   this.filterAndPaginate();
    // } else {
    //   this.fullList = this.content.filter((role: any) => role?.agency?.id === this.filteredAgency);
    //   this.filterAndPaginate();
    // }
    this.page = 1; // Reset to first page when filtering
    this.filterAndPaginate();
  }


  ngOnInit(): void {
    this.initiliazeForm();
    this.refreshSub = this.globalRefresh.refreshInterval$.subscribe(() => {
      this.loadList();
    });
  }


  initiliazeForm() {
    this.userForm = this.formBuilder.group({
      id: [""],
      firstName: ['', [Validators.required, Validators.pattern("^[A-Za-z]+$")]],
      lastName: ['', [Validators.required, Validators.pattern("^[A-Za-z]+$")]],
      email: ['', [Validators.required, Validators.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]],
      phoneNumber: ['', [Validators.required, Validators.pattern("^(078|079|073|072)\\d{7}$")]],
      gender: ['', [Validators.required]],
      userTypeId: ['', [Validators.required]],
      agencyId: ['', [Validators.required]],
      approvalLevelId: ['', [Validators.required]],
      roleId: [''],
      password: ['Jesus7@123'],
      isEmailValid: [''],
      districtId: [''],
      sectorId: [''],
    });

    this.verfiyForm = this.formBuilder.group({
      id: [""],
      otp: ['', [Validators.required]],
    });
  }


  openModal(content: any) {
    this.initiliazeForm();
    this.submitted = false;
    this.modalService.open(content, { size: "lg", centered: true });
  }

  checkedValGet: any[] = [];
  onCheckboxChange(e: any) {
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }





  editDataGet(id: any, content: any) {
    this.submitted = false;
    this.modalService.open(content, { size: "lg", centered: true });
    var modelTitle = document.querySelector(".modal-title") as HTMLAreaElement;
    modelTitle.innerHTML = "Edit User";
    // var updateBtn = document.getElementById("add-btn") as HTMLAreaElement;
    // updateBtn.innerHTML = "Update";

    this.userService.findOneWithPath(id, environment.authUrl + 'user-management/user').subscribe({
      next: (data: any) => {
        const users = data.data;
        this.econtent = users;
        this.userForm.controls['firstName'].setValue(this.econtent.firstName);
        this.userForm.controls['lastName'].setValue(this.econtent.lastName);
        this.userForm.controls['email'].setValue(this.econtent.email);
        this.userForm.controls['phoneNumber'].setValue(this.econtent.phoneNumber);
        this.userForm.controls['userTypeId'].setValue(this.econtent.userType.id);
        this.userForm.controls['agencyId'].setValue(this.econtent.agency.id);
        this.userForm.controls['approvalLevelId'].setValue(this.econtent.approvalLevelId);
        this.userForm.controls['roleId'].setValue(this.econtent.role.id);
        this.userForm.controls["gender"].setValue(this.econtent.gender ? this.econtent.gender : 'M')
        this.userForm.controls["id"].setValue(this.econtent.id);
        // this.outputData.type = 'editStudent';
        // this.outputData.userForm = this.userForm;
        // this.outputData.isOpen = true;

      },
    });
  }


  deleteData(id: any) {
    if (id) {
      this.userService.deleteWithPath(id, environment.authUrl + '').subscribe({
        next: data => { this.loadList(); },
        error: err => {
        }
      });

    } else {

    }
  }


  deleteMultiple(content: any) {
    var checkboxes: any = document.getElementsByName("checkAll");
    var result;
    var checkedVal: any[] = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        result = checkboxes[i].value;
        checkedVal.push(result);
      }
    }
    if (checkedVal.length > 0) {
      this.modalService.open(content, { centered: true });
    } else {
      Swal.fire({
        text: "Please select at least one checkbox",
        confirmButtonColor: "#239eba",
      });
    }
    this.checkedValGet = checkedVal;
  }

  checkUncheckAll(ev: any) {
    this.lists.forEach((x: { state: any }) => (x.state = ev.target.checked));
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }




  onSubmit() {
    if (this.userForm.get("id")?.value) {

      let dataToSave = this.userForm.value;
      dataToSave.updatedBy = this.currentUser.userId;


      this.submitted = true;



      this.userService.patchAssetWithPath(this.userForm.value.id, dataToSave, environment.authUrl + 'user-management/user/updateAnyUser')
        .subscribe(
          data => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "User updated successfully", "bottom", "center");
            this.loadList();
            this.modalService.dismissAll();
          }, error => {
            this.submitted = false;
          }
        )
    } else {

      let dataToSave = this.userForm.value;
      dataToSave.createdBy = this.currentUser.userId;
      this.submitted = true;
      this.userService.saveAssetWithPath(dataToSave, environment.authUrl + 'auth/createStaff')
        .subscribe(
          data => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "User saved successfully", "bottom", "center");
            this.loadList();
            this.modalService.dismissAll();
          }, error => {
            this.submitted = false;
          }
        )
    }
  }





  // Csv File Export
  csvFileExport() {
    var users = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalseparator: ".",
      showLabels: true,
      showTitle: true,
      title: "User Data",
      useBom: true,
      noDownload: false,
      headers: [
        "ID",
        "Role ",
        "isActive",
        "firstName",
        "lastName",
        "email",
        "phoneNumber",
      ],
    };
    new ngxCsv(this.content, "User", users);
  }
}
