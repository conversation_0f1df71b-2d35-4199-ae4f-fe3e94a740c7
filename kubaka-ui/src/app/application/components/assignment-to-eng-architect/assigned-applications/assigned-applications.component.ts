import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SessionService } from 'src/app/authentication-services/session.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../../services/application.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';


@Component({
  selector: 'app-assigned-applications',
  templateUrl: './assigned-applications.component.html',
  styleUrls: ['./assigned-applications.component.scss']
})
export class AssignedApplicationsComponent {
  currentUser: any = {};
  inTokenData: any = {};
  lists: any[] = [];
  outputData: any = {};
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];
  applicationStatuses: any[] = [];


  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    public applicationService: ApplicationService,
    private router: Router,
    private userService: UserMgtService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.inTokenData = this.sessionService.getTokenData();


    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus')
      .subscribe(
        data => { this.applicationStatuses = data; },
        error => { }
      )

    this.loadList();

  }


  filterByStatus() {
    this.fullList = this.content.filter((x: any) => x.projectStatusId === this.outputData.applicationStatusId);
    this.totalRecords = this.fullList.length;
    this.filterAndPaginate();
  }

  ngOnDestroy() {
    this.applicationService.searchTerm = '';
  }


  loadList() {
    if (this.inTokenData.LicenseArchitect) { this.getPendingLists(this.inTokenData.LicenseArchitect); }
    if (this.inTokenData.LicenseEngineer) { this.getPendingLists(this.inTokenData.LicenseEngineer) };
  }


  getPendingLists(licenceNumber: any) {

    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/assignee/licenseNumber/search?search=' + licenceNumber)
      .subscribe(
        (data) => {
          this.content = data.items;
          this.fullList = data.items;
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();

        });
  }


  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes(term)
    );
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    this.filterAndPaginate();
  }

  // getPremiumData() {
  //   this.lists = this.content
  //     .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
  // }


  openModal(content: any, sizeParams: any, value: any) {
    this.outputData = value;
    this.outputData.isViewing = true;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }


  closeContent(event: any) {
    this.outputData = {};
    this.modalService.dismissAll();
    this.loadList();
  }
}
