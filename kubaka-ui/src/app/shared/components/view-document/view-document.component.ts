import { Component, Input } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-view-document',
  templateUrl: './view-document.component.html',
  styleUrls: ['./view-document.component.scss']
})
export class ViewDocumentComponent {

  @Input() inputData: any;
  safeDocumentUrl: SafeResourceUrl = {};
  documentSrc: any = {};

  isMobile = false;

  constructor(
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    this.getPhoto();
  }



  // getPhoto() {
  //   // const linkSource = 'data:application/pdf;base64,' + this.inputData.document;
  //   const decodedPdf = atob(this.inputData.base64Data);
  //   const byteArray = new Uint8Array(decodedPdf.length);
  //   for (let i = 0; i < decodedPdf.length; i++) {
  //     byteArray[i] = decodedPdf.charCodeAt(i);
  //   }
  //   const blob = new Blob([byteArray.buffer], { type: 'application/pdf' });
  //   const _pdfurl = URL.createObjectURL(blob);
  //   this.documentSrc = this.sanitizer.bypassSecurityTrustResourceUrl(_pdfurl);

  //   // Store plain URL for mobile opening
  //   this.directPdfUrl = this.pdfUrl;
  // }
  getPhoto(): void {
    const base64 = this.inputData?.base64Data;
    if (!base64) return;

    const decodedPdf = atob(base64);
    const byteArray = new Uint8Array(decodedPdf.length);
    for (let i = 0; i < decodedPdf.length; i++) {
      byteArray[i] = decodedPdf.charCodeAt(i);
    }

    const blob = new Blob([byteArray.buffer], { type: 'application/pdf' });
    const pdfUrl = URL.createObjectURL(blob);
    this.documentSrc = this.sanitizer.bypassSecurityTrustResourceUrl(pdfUrl);

    // Store plain URL for mobile opening
    this.directPdfUrl = pdfUrl;
  }

  directPdfUrl: string = '';

openInNewTab(): void {
  if (this.directPdfUrl) {
    window.open(this.directPdfUrl, '_blank');
  }
}
}
