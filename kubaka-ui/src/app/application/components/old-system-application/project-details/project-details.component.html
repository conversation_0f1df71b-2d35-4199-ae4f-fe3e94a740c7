<div>
  <!-- Step content goes here -->
  <div class="step-panel">
    <div class="step-panel_header">
      <h2>Project Details</h2>
    </div>
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()" autocomplete="off">
      <div class="step-panel_body">
        <!-- <form (ngSubmit)="onNext()" [formGroup]="userForm" autocomplete="off"> -->
        <div class="form-set">
          <div class="form-input">
            <label>Permit Type</label>
            <div>
              <select name="permitTypeId" id="permitTypeId" formControlName="permitTypeId" required>
                <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
              </select>
            </div>
          </div>
          <div class="form-input">
            <label>Building Type</label>
            <div>
              <select name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                (change)="getBuildTypeObject($event)" required>
                <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
              </select>
            </div>
          </div>
          <div class="form-input">
            <label>Project Category {{userForm.value.categoryCode}}</label>
            <div>
              <!--  -->
              <select [class.disabled-select]="isSelectDisabled" name="categoryTypeId" id="categoryTypeId"
                formControlName="categoryTypeId" [disabled]="isSelectDisabled" required>
                <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}}</option>
              </select>
            </div>
          </div>
          <div class="form-input">
            <label>Built-up Area (In Square Meters)<span>*</span></label>
            <div>
              <input type="number" id="buildUpArea" name="buildUpArea" formControlName="buildUpArea"
                (focusout)="focusOutFunction()" required>
              <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> Build Up Area cannot be greater
                than Plot Size. </div>
            </div>
          </div>
          <div class="form-input">
            <label>Number of Floor / G+<span>*</span></label>
            <div>
              <input type="number" id="numberOfFloor" name="numberOfFloor" formControlName="numberOfFloor"
                (keyup)="calculateNumberOfFlow()" required>
            </div>
          </div>
          <div class="form-input">
            <label>Gross Floor Area</label>
            <div>
              <input type="number" id="grossFloorArea" name="grossFloorArea" formControlName="grossFloorArea" required>
            </div>
          </div>
          <div class="form-input">
            <label>Number of parking spaces</label>
            <div>
              <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                formControlName="numberOfParkingSpace" required>
            </div>
          </div>
          <div class="form-input">
            <label>Estimated price of dwelling unit in RWF</label>
            <div>
              <input type="number" id="priceOfDwellingUnitRwf" name="priceOfDwellingUnitRwf"
                formControlName="priceOfDwellingUnitRwf" required>
            </div>
          </div>
          <div class="form-input">
            <label>Description of operations</label>
            <div>
              <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                formControlName="DescriptionOfOperation" required>
            </div>
          </div>
          <div class="form-input">
            <label>Percentage Space Use</label>
            <div>
              <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                formControlName="percentageSpaceUse" readonly required>
            </div>
          </div>
          <div class="form-input">
            <label>Number of Dwelling Unit</label>
            <div>
              <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                formControlName="numberOfDwellingUnits">
            </div>
          </div>
          <div class="form-input">
            <label>Estimated monthly water consumption (m3)</label>
            <div>
              <input type="number" id="waterConsumption" name="waterConsumption" formControlName="waterConsumption"
                required>
            </div>
          </div>
          <div class="form-input">
            <label>Estimated monthly electricity consumption(watts)</label>
            <div>
              <input type="number" id="electricityConsumption" name="electricityConsumption"
                formControlName="electricityConsumption" required>
            </div>
          </div>
          <div class="form-input">
            <label>Distance to the nearest Land Line/ optic fiber cable(m)</label>
            <div>
              <input type="number" id="DistanceToTheNearestLandIn" name="DistanceToTheNearestLandIn"
                formControlName="DistanceToTheNearestLandIn" required>
            </div>
          </div>
          <div class="form-input">
            <label>Estimated project cost in USD</label>
            <div>
              <input type="number" id="ProjectCostInUSD" name="ProjectCostInUSD" formControlName="ProjectCostInUSD"
                required>
            </div>
          </div>
          <div class="form-input">
            <label>Estimated project cost in RWF</label>
            <div>
              <input type="number" id="ProjectCostInRwf" name="ProjectCostInRwf" formControlName="ProjectCostInRwf"
                required>
            </div>
          </div>
          <div class="form-input">
            <label>Capacity Information: Number of people / seats</label>
            <div>
              <input type="number" id="capacityInformation" name="capacityInformation"
                formControlName="capacityInformation" (keyup)="calculateNumberOfPeople()" required>
            </div>
          </div>
          <div class="form-input">
            <label>Technology Survey</label>
            <div>
              <select name="technologySurveyId" id="technologySurveyId" formControlName="technologySurveyId" required>
                <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}</option>
              </select>
            </div>
          </div>
        </div>
        <!-- <div class="form-input">
          <label>Project Brief</label>
          <div>
            <textarea name="projectDetail" formControlName="projectDetail" id="projectDetail" required cols="30"
              rows="10"></textarea>
          </div>
        </div> -->
        <!-- </form> -->
        <div class="step-panel_body">
          <div class="form-incheckbox" *ngIf="(userForm.value.categoryCode === 'CAT1' ||
          userForm.value.categoryCode === 'CAT2')">
            <label class="checkbox">
              <input type="checkbox" id="check" [checked]="formAccess.doYouWantToShareToEngineer"
                (click)="allowToAssign()" />
              <span class="checkbox_box"></span>
              <span class="checkbox_txt">Select this option if you want to assign this project to an engineer/architect
                or continue on your own.</span>
            </label>
          </div>
        </div>
      </div>
      <div class="step-panel_footer" *ngIf="
      (userForm.value.categoryCode === 'CAT1' ||
      userForm.value.categoryCode === 'CAT2') && (!formAccess.doYouWantToShareToEngineer)">
        <button (click)="onPrev()">Previous</button>
        <button type="submit">Next</button>
      </div>
    </form>
    <!-- EIA Checker form -->
    <div class="alert alert-danger" role="alert"
      *ngIf="
      !formAccess.isIAEverified &&
      userForm.value.categoryTypeId &&
      formAccess.isRetreived === 0"> Data not
      found. Please submit your request to RDB for an
       Environmental Impact Access certificate! </div>
    <app-eia-form-checker *ngIf="
      (userForm.value.categoryCode === 'CAT5' ||
      userForm.value.categoryCode === 'CAT4') && this.applicationDetails.upi"
      [inputData]="{upi: this.applicationDetails.upi}"
      (backToParent)="comeAndAllowToAssign($event)"></app-eia-form-checker>
    <!-- For of assigning to engineer && formAccess.isIAEverified -->
    <div class="form-incheckbox" *ngIf="
          (userForm.value.categoryCode === 'CAT5' ||
          userForm.value.categoryCode === 'CAT3' ||
          userForm.value.categoryCode === 'CAT4')">
      <label class="checkbox">
        <span class="checkbox_txt">Your project seems to be in category {{userForm.value.categoryCode}}. To make it
          simpler for you, you just need to decide whether you need an engineer or an architect.</span>
      </label>
    </div>
    <!-- && formAccess.isIAEverified? -->
    <!-- (
      ((userForm.value.categoryCode === 'CAT5' ||
    userForm.value.categoryCode === 'CAT3' ||
    userForm.value.categoryCode === 'CAT4')
    ) || (formAccess.doYouWantToShareToEngineer)) && (formAccess.isIAEverified)" -->
    <!-- <app-assign-to-engineer-form *ngIf="
      (

        ((
          userForm.value.categoryCode === 'CAT1' ||
          userForm.value.categoryCode === 'CAT2' ||
          userForm.value.categoryCode === 'CAT3') && formAccess.doYouWantToShareToEngineer)
        || ((userForm.value.categoryCode === 'CAT4' || userForm.value.categoryCode === 'CAT5')
        && formAccess.isIAEverified)
        )" [inputData]="userForm.value.projectId"></app-assign-to-engineer-form> -->

        <app-assign-to-engineer-form *ngIf="doYouWantToAssignToEngineer ||
                userForm.value.categoryCode === 'CAT3' ||
                        ((userForm.value.categoryCode === 'CAT4' ||
                        userForm.value.categoryCode === 'CAT5') && formAccess.isIAEverified
                        && formAccess.isRdbIAEchecked)
                            " [inputData]="userForm.value.projectId"
                    [inputData]="{allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form>
  </div>
</div>
