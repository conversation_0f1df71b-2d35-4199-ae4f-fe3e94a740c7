import { AbstractEntity } from '@app/common';
import { Column, Entity, OneToMany } from 'typeorm';
import { User } from './user.entity';

@Entity()
export class CivilStatus extends AbstractEntity<CivilStatus> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => User, (user) => user.civilStatus)
  users: User[];
}
