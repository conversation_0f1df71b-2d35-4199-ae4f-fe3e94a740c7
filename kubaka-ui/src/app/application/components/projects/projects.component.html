<!-- Applicant Applications -->
<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <div class="app-welnote">
        <div class="app-welnote_dtails">
          <span class="prim-nt">{{"projectComponent.welcomeMsg" | translate }}!</span>
          <h3>
            <span>{{ currentUser.data.user.lastName }}</span>
            {{ currentUser.data.user.firstName }}
          </h3>
        </div>
      </div>
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle">{{"projectComponent.projects" | translate }}</span>
            <div class="btns">
              <div class="form-input w-aut clear-m">
                <div class="form-input_search">
                  <input
                    type="text"
                    name="searchTerm"
                    [(ngModel)]="userService.searchTerm"
                    (ngModelChange)="onSearchTermChange($event)"
                    [placeholder]="'projectComponent.searchForSomething' | translate"

                  />
                  <button type="button" class="btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="search">
                          <rect width="24" height="24" opacity="0" />
                          <path
                            d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z"
                          />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
              </div>
              <a
                class="kbk-btn kbk-btn-main"
                data-bs-toggle="modal"
                id="create-btn"
                data-bs-target="#showModal"
                (click)="openModal(upiFromLandContent, 'lg')"
                >{{"projectComponent.newPermitApplication" | translate }}</a
              >
            </div>
          </div>
          <ul class="tblist" *ngIf="lists.length > 0">
            <li class="tblist-item" *ngFor="let li of lists">
              <!-- <div class="tblist-item_ck">
                                  <label class="form-checkbox">
                                      <input type="checkbox">
                                  </label>
                              </div> -->
              <div class="tblist-item_icon">
                <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
              </div>
              <div class="tblist-item_dt">
                <span> <span class="ttl">UPI</span> {{ li.upi }}</span>
                <span>
                  <span class="ttl">{{"projectComponent.plotSize" | translate }}</span> {{ li.plotSize }}</span
                >
              </div>
              <!-- <div class="tblist-item_dt">
                                <span class="ttl">Project Name</span> {{li.projects.applicationName}}
                            </div> -->
              <!-- <div class="tblist-item_dt">
                                <span class="ttl">Category</span> {{li.categoryTypes.name}}
                            </div> -->
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">{{"projectComponent.projectName" | translate }}</span> {{ li.projectName | titlecase }}
                </span>
                <span>
                  <span class="ttl">{{"projectComponent.projectDescription" | translate }}</span>
                  {{ li.projectDescription }}</span
                >
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"projectComponent.selectedCategoryUse" | translate }}</span>
                  {{ li.selectedCategoryUse }}</span
                >
                <span>
                  <span class="ttl">{{"projectComponent.selectedUse" | translate }}</span>
                  {{ li.selectedUse }}</span
                >
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"projectComponent.province" | translate }}</span> {{ li.provinceName }}</span
                >
                <span>
                  <span class="ttl">{{"projectComponent.district" | translate }}</span> {{ li.districtName }}</span
                >
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"projectComponent.sector" | translate }}</span> {{ li.sectorName }}</span
                >
                <span>
                  <span class="ttl">{{"projectComponent.status" | translate }}</span>
                  <span class="bdg bdg-pend mx-0">{{ li.projectStatus?.name }}</span>
                </span>

              </div>
              <!-- <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">Cell</span> {{li.cellName}}</span>
                                <span>
                                    <span class="ttl">Village</span> {{li.villageName}}</span>
                            </div> -->
              <!-- <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">Date</span> {{li.created_at | date}}</span>
                            </div> -->
              <!-- <div class="tblist-item_status">
                <span class="bdg bdg-pend">{{ li.projectStatus?.name }}</span>
              </div> -->
              <div class="tblist-item_xcn" *ngIf="lists.length > 0">
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="edit">
                    <img src="assets/ikons/colored/ikon-edit.svg" alt="" />
                  </a>
                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="delete">
                    <img src="assets/ikons/colored/ikon-trash.svg" alt="" />
                  </a> -->
                <a
                  class="kbk-link-btn hs-tp"
                  data-kbk-tooltip="view details"
                  [routerLink]="[
                    '/account/application/new-application-project',
                    li.id
                  ]"
                >
                  <!-- <img src="assets/ikons/colored/ikon-eye.svg" alt="" /> -->
                  {{"projectComponent.projectDetails" | translate }}
                </a>

                <!-- <a  class="kbk-link-btn hs-tp"
                *ngIf="li.projectStatus.code === 'PCRTD' ||
                li.projectStatus.code === 'PRJCT' ||
                li.projectStatus.code === 'PASGD'"
                                    data-kbk-tooltip="Remove engineer" id="create-btn"
                                    data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                                                    (click)="openToDelete(li,deleteModel)"

                                    >Remove Engineer/Architect </a> -->
                <a  class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="Remove engineer" id="create-btn"
                                    data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                                                    (click)="openToDelete(li,deleteModel)"

                                    >{{"projectComponent.removeEngineerArchitect" | translate }} </a>
                <!-- <a *ngIf="li.applicationStatus.code ==='CTFD'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="Related Application" id="create-btn"
                                    (click)="otherApplication(li)"> Related
                                    Application </a>
                                <a *ngIf="li.invoices.length > 0 && li.invoices[0].invoiceStatus.code !== 'PAD'"
                                    [routerLink]="['/account/payment/invoice-detail/', li.invoices[0].id]"
                                    class="kbk-link-btn hs-tp" data-kbk-tooltip="Related Application" id="create-btn">
                                    Pay </a>
                                <a *ngIf="li.invoices.length > 0 && li.invoices[0].invoiceStatus.code === 'PAD'"
                                    class="kbk-link-btn hs-tp" data-kbk-tooltip="Related Application" id="create-btn"
                                    [routerLink]="['/account/certificate/certificate-detail/', li.certificates[0].id]">
                                    View Certificate </a> -->
                <!-- <a class="kbk-link-btn">
                                      <img src="assets/ikons/colored/ikon-eye.svg" alt="">
                                  </a> -->
              </div>
            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div
                class="pagnation-dt"
                id="tickets-table_info"
                role="status"
                aria-live="polite"
              >
                <span class="ent">{{ totalRecords }}</span>
                <span class="cur">{{ startIndex }} - {{ endIndex }}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <ngb-pagination
                [collectionSize]="totalRecords"
                [(page)]="page"
                [pageSize]="pageSize"
                (pageChange)="getPremiumData()"
              >
              </ngb-pagination>
            </div>
          </div>
          <ul class="tblist" *ngIf="lists.length === 0">
            <li class="tblist-item">
              <h2
                *ngIf="lists.length === 0"
                id="exampleModalLabel"
                class="no-rcrds-found"
              >
                {{"projectComponent.noRecordsFound" | translate }}
              </h2>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #upiFromLandContent role="document" let-modal>
  <div class="modol-header">
    <!-- <h2 id="exampleModalLabel">Check the eligibility</h2>
        <p> In accordance with the master plan, track what is permitted, prohibited, and conditional to be built on your
            plot  </p> -->
    <h2 id="exampleModalLabel">{{"projectComponent.verification" | translate }}</h2>
    <p>{{"projectComponent.verificationDetails" | translate }}</p>
    <button
      type="button"
      class="kbk-btn kbk-btn-sec kbk-btn-close"
      data-bs-dismiss="modal"
      aria-label="Close"
      id="close-modal"
      (click)="modal.dismiss('Cross click')"
    ></button>
  </div>
  <div class="modol-content">
    <app-upi-info
      [info]="'1'"
      (backToParent)="getChoosenSelectedUse($event)"
    ></app-upi-info>
  </div>
</ng-template>


<ng-template #deleteModel let-modal>
  <div class="modal-content">
      <div class="modol-header">
          <h2 id="exampleModalLabel">{{"projectComponent.confirmRemoveEngineerArchitect" | translate }}</h2>
          <p>{{"projectComponent.removingEngineerArchitect" | translate }}. </p>
          <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
              id="btn-close" (click)="modal.dismiss('Cross click')"></button>
      </div>
      <div class="modal-body">
          <div class="mt-md">
              <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
          </div>
          <div class="kbk-x-c sp-sm mt-md">
              <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                  (click)="modal.close('Close click')" id="deleteRecord-close">{{"projectComponent.close" | translate }}</button>
              <button *ngIf="!loading" type="button" class="kbk-btn kbk-btn-error"
                  id="delete-product" (click)="deleteFile()">{{"projectComponent.yesDelete" | translate }}</button>
              <button *ngIf="loading" type="button" class="kbk-btn kbk-btn-error"
                  id="delete-product">{{"projectComponent.removing" | translate }}</button>
          </div>
      </div>
  </div>
</ng-template>
