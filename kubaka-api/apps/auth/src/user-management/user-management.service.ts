import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Role } from 'apps/auth/src/entities/user-management/role.entity';
import { UserActivity } from 'apps/auth/src/entities/user-management/userActivity.entity';
import { UserType } from 'apps/auth/src/entities/user-management/userType.entity';
import { CivilStatus } from 'apps/auth/src/entities/user-management/civilStatus.entity';
import { Permission } from 'apps/auth/src/entities/user-management/permission.entity';
import {
  // UserRepository,
  RoleRepository,
  // UserActivityRepository,
  UserTypeRepository,
  CivilStatusRepository,
  // PermissionRepository,
  CountryRepository,
  ProvinceRepository,
  CellRepository,
  DistrictRepository,
  SectorRepository,
  VillageRepository,
  AgencyRepository,
  AgencyDistrictRepository,
} from './user-management.repository';
import {
  CivilStatusDto,
  // PermissionDto,
  // PermissionDto,
  RoleDto,
  UpdateCivilStatusDto,
  // UpdatePermissionDto,
  // UpdatePermissionDto,
  UpdateRoleDto,
  // UpdateUserActivityDto,
  UpdateUserTypeDto,
  UserActivityDto,
  UserTypeDto,
} from '../dto/user-management.dto';
import { Country } from 'apps/auth/src/entities/delimitation/country.entity';
import {
  CellDto,
  CountryDto,
  DistrictDto,
  ProvinceDto,
  SectorDto,
  UpdatedDistrictDto,
  UploadSectorDto,
  VillageDto,
} from '../dto/delimitation.dto';
import { Province } from 'apps/auth/src/entities/delimitation/province.entity';
import { District } from 'apps/auth/src/entities/delimitation/district.entity';
import { Sector } from 'apps/auth/src/entities/delimitation/sector.entity';
import { Cell } from 'apps/auth/src/entities/delimitation/cell.entity';
import { Village } from 'apps/auth/src/entities/delimitation/village.entity';
import {
  AgencyDto,
  CreateAgencyDistrictDto,
  UpdateAgencyDto,
} from '../dto/agency.dto';
import { Agency } from 'apps/auth/src/entities/agency/agency.entity';
import { AgencyDistrict } from 'apps/auth/src/entities/agency/agencyDistrict.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CreateStaffDto,
  CreateUserDto,
  UpdateAnyUserDto,
  UpdateUserProfileDto,
  UserProfileDto,
} from '../dto/user.dto';

import * as bcrypt from 'bcryptjs';
import { User } from '../entities/user-management/user.entity';
import { PageMetaDto } from '@app/common/dto/page-meta.dto';
import { PageOptionsDto } from '@app/common/dto/page-obptions.dto';
import { PageDto } from '@app/common/dto/page.dto';
// import { AuthService } from '../auth.service';
import { EventPattern } from '@nestjs/microservices';
// import { MessagePattern } from '@nestjs/microservices';

const saltRounds = 10;

import * as ExcelJS from 'exceljs';

@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,

    private readonly roleRepository: RoleRepository,

    @InjectRepository(UserActivity)
    private readonly userActivityRepository: Repository<UserActivity>,

    // private readonly userActivityRepository: UserActivityRepository,

    private readonly userTypeRepository: UserTypeRepository,
    private readonly civilStatusRepository: CivilStatusRepository,
    // private readonly permissionRepository: PermissionRepository,

    private readonly countryRepository: CountryRepository,
    private readonly provinceRepository: ProvinceRepository,
    private readonly districtRepository: DistrictRepository,
    private readonly sectorRepository: SectorRepository,
    private readonly cellRepository: CellRepository,
    private readonly villageRepository: VillageRepository,

    private readonly agencyRepository: AgencyRepository,
    private readonly agencyDistrictRepository: AgencyDistrictRepository,

    @InjectRepository(AgencyDistrict)
    private readonly agencyDistrictEntityRepository: Repository<AgencyDistrict>,

    @InjectRepository(District)
    private readonly districtEntityRepository: Repository<District>,

    @InjectRepository(Sector)
    private sectorEntityRepository: Repository<Sector>,

    // @InjectRepository(Agency)
    // private agencyRepository: Repository<Agency>,

    @InjectRepository(Permission)
    private readonly userActivityRoleRepository: Repository<Permission>,

    @InjectRepository(User)
    private readonly userEntityRepository: Repository<User>,

    // private readonly authService: AuthService,
  ) {}

  // @MessagePattern({ cmd: 'validateUser' })
  // async validateUser(userId: string): Promise<boolean> {
  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const userFromDb = await this.userRepository.findOne({
  //     where: { id: userId },
  //   });
  //   // This could involve querying the database or any other validation logic
  //   if (!userFromDb) {
  //     return false;
  //   }
  //   const userExists = true; // Placeholder, replace with actual logic
  //   return userExists;
  // }

  // async findAllUsers(): Promise<User[]> {
  //   const users = await this.userRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .getMany();
  //   return users;
  // }

  @EventPattern('checkAgencyData')
  async checkAgencyData(agencyId: string) {
    return await this.agencyRepository.findOne({
      id: agencyId,
    });
  }

  async findAllUsers(): Promise<User[]> {
    return await this.userRepository.find({
      relations: {
        role: true,
        userType: true,
        civilStatus: true,
        agency: true,
        sector: true,
      },
    });
  }

  public async findAllUsersPaginated(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<User>> {
    const queryBuilder = this.userRepository.createQueryBuilder('user');
    queryBuilder
      .orderBy('user.created_at', pageOptionsDto.order)
      .skip(pageOptionsDto.skip)
      .take(pageOptionsDto.take);

    const itemCount = await queryBuilder.getCount();
    const { entities } = await queryBuilder.getRawAndEntities();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(entities, pageMetaDto);
  }

  async findUserByEmail(email: string): Promise<User> {
    return this.userRepository.findOne({
      where: { email },
      relations: {
        role: true,
        userType: true,
        agency: true,
        civilStatus: true,
        sector: true,
      },
    });
  }

  async findUserById(id: string): Promise<User> {
    return this.userRepository.findOne({
      where: { id },
      relations: {
        role: true,
        userType: true,
        agency: true,
        civilStatus: true,
        sector: true,
      },
    });
  }

  // async findUserByAgency(agencyId: string): Promise<User> {
  //   return this.userRepository.findOne({
  //     where: { agencyId },
  //     relations: {
  //       role: true,
  //       userType: true,
  //       agency: true,
  //       civilStatus: true,
  //     },
  //   });
  // }

  async findUserByAgency(agencyId: string) {
    console.log(agencyId);
    const users = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('user.sector', 'sector')
      .leftJoinAndSelect('user.userType', 'userType')
      .leftJoinAndSelect('user.agency', 'agency')
      .where('user.agency = :agencyId', {
        agencyId,
      })
      .getMany();

    return users;
  }

  async getAllUserByAgencyId(agencyId: string) {
    try {
      const users = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.role', 'role')
        .leftJoinAndSelect('user.sector', 'sector')
        .leftJoinAndSelect('user.userType', 'userType')
        .where('user.agency = :agencyId', { agencyId })
        .getMany();

      if (users.length === 0) {
        return {
          success: true,
          message: 'No users found for the specified agency ID.',
          data: [],
        };
      }

      return {
        success: true,
        message: 'Users fetched successfully',
        data: users,
      };
    } catch (error) {
      console.error('Error fetching users by agency ID:', error.message);
      throw new Error('Failed to fetch users. Please try again later.');
    }
  }

  // async findUserByAgency(agencyId: any): Promise<User[]> {
  //   console.log(agencyId);
  //   return this.userEntityRepository
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect('user.role', 'role')
  //     .getMany();
  // }

  isValidEmail(email: string) {
    if (email) {
      const re =
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(email);
    } else return false;
  }

  async createNewUser(newUser: CreateUserDto): Promise<User> {
    // check data before saving
    // // commenting the checking of national ID

    // const NIDAid = newUser.nationalId;
    // const existingUser = await this.userEntityRepository
    //   .createQueryBuilder('user')
    //   .where('user.nationalId = :nationalId', { nationalId: NIDAid })
    //   .getOne();

    // if (existingUser)
    //   throw new HttpException(
    //     `National ID already exist ${newUser.nationalId}`,
    //     HttpStatus.BAD_REQUEST,
    //   );

    const dataFromDb = await this.roleRepository.findOne({
      id: newUser.roleId,
    });
    if (!dataFromDb)
      throw new HttpException(
        `Role not found with id ${newUser.roleId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.roleId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb2 = await this.userTypeRepository.findOne({
      id: newUser.userTypeId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        `User type not found with id id ${newUser.userTypeId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.userTypeId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb3 = await this.civilStatusRepository.findOne({
      id: newUser.civilStatusId,
    });
    if (!dataFromDb3)
      throw new HttpException(
        `Civil status not found with id ${newUser.civilStatusId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.civilStatusId}`,
        HttpStatus.BAD_REQUEST,
      );

    // // check data before saving
    // const dataFromDb4 = await this.agencyRepository.findOne({
    //   id: newUser.agencyId,
    // });
    // if (!dataFromDb4)
    //   throw new HttpException(
    //     `Agency status not found with id ${newUser.agencyId}`,
    //     HttpStatus.BAD_REQUEST,
    //   );

    const userToBeRegistered = await this.validateToBeCreatedUser(newUser);
    if (userToBeRegistered) {
      throw new HttpException(
        'User already registered',
        // 'REGISTRATION.USER_ALREADY_REGISTERED',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (this.isValidEmail(newUser.email)) {
        const userRegistered = await this.findUserByEmail(newUser.email);

        if (!userRegistered) {
          const dataFromDb = await this.roleRepository.findOne({
            id: newUser.roleId,
          });
          if (!dataFromDb)
            throw new HttpException('Data Not Found', HttpStatus.BAD_REQUEST);

          const createdUser = new User();
          createdUser.firstName = newUser.firstName;
          createdUser.lastName = newUser.lastName;
          createdUser.email = newUser.email;
          createdUser.phoneNumber = newUser.phoneNumber;
          createdUser.nationalId = newUser.nationalId;
          // // // Assign passport to nationalId if nationalId is null or empty
          // createdUser.nationalId = newUser.nationalId?.trim()
          //   ? newUser.nationalId
          //   : newUser.passport;
          createdUser.passport = newUser.passport;
          createdUser.gender = newUser.gender;
          createdUser.verifiedAt = newUser.verifiedAt;
          createdUser.licenceIdArch = newUser.licenceIdArch;
          createdUser.licenceIdEng = newUser.licenceIdEng;
          createdUser.dateOfBirth = newUser.dateOfBirth;
          createdUser.password = newUser.password = await bcrypt.hash(
            newUser.password,
            saltRounds,
          );
          createdUser.role = { id: dataFromDb.id } as any;
          createdUser.userType = { id: dataFromDb2.id } as any;
          createdUser.civilStatus = { id: dataFromDb3.id } as any;
          // createdUser.agency = { id: dataFromDb4.id } as any;

          const userToBeCreated = this.userRepository.create(createdUser);
          return await userToBeCreated.save();
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'User already registered',
            // 'REGISTRATION.USER_ALREADY_REGISTERED',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'Missing mandatory parameters',
          // 'REGISTRATION.MISSING_MANDATORY_PARAMETERS',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  async createNewUserIrembo(newUser: CreateUserDto): Promise<User> {
    const dataFromDb = await this.roleRepository.findOne({
      id: newUser.roleId,
    });
    if (!dataFromDb)
      throw new HttpException(
        `Role not found with id ${newUser.roleId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.roleId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb2 = await this.userTypeRepository.findOne({
      id: newUser.userTypeId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        `User type not found with id id ${newUser.userTypeId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.userTypeId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb3 = await this.civilStatusRepository.findOne({
      id: newUser.civilStatusId,
    });
    if (!dataFromDb3)
      throw new HttpException(
        `Civil status not found with id ${newUser.civilStatusId}`,
        // `ROLE_BAD_REQUEST with id ${newUser.civilStatusId}`,
        HttpStatus.BAD_REQUEST,
      );

    const userToBeRegistered = await this.validateToBeCreatedUser(newUser);
    if (userToBeRegistered) {
      throw new HttpException(
        'User already registered',
        // 'REGISTRATION.USER_ALREADY_REGISTERED',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (this.isValidEmail(newUser.email)) {
        // Check if user with this email already exists
        const userRegistered = await this.findUserByEmail(newUser.email);
        if (userRegistered) {
          throw new HttpException('User already exist', HttpStatus.BAD_REQUEST);
        }
        // const userRegistered = await this.findUserByEmail(newUser.email);

        if (!userRegistered) {
          const dataFromDb = await this.roleRepository.findOne({
            id: newUser.roleId,
          });
          if (!dataFromDb)
            throw new HttpException('Data Not Found', HttpStatus.BAD_REQUEST);

          const createdUser = new User();
          createdUser.firstName = newUser.firstName;
          createdUser.lastName = newUser.lastName;
          createdUser.email = newUser.email;
          createdUser.phoneNumber = newUser.phoneNumber;
          createdUser.nationalId = newUser.nationalId;
          createdUser.passport = newUser.passport;
          createdUser.gender = newUser.gender;
          createdUser.verifiedAt = newUser.verifiedAt;
          createdUser.isEmailValid = true;
          createdUser.licenceIdArch = newUser.licenceIdArch;
          createdUser.licenceIdEng = newUser.licenceIdEng;
          createdUser.dateOfBirth = newUser.dateOfBirth;
          createdUser.password = newUser.password = await bcrypt.hash(
            newUser.password,
            saltRounds,
          );
          createdUser.role = { id: dataFromDb.id } as any;
          createdUser.userType = { id: dataFromDb2.id } as any;
          createdUser.civilStatus = { id: dataFromDb3.id } as any;
          // createdUser.agency = { id: dataFromDb4.id } as any;

          const userToBeCreated = this.userRepository.create(createdUser);
          return await userToBeCreated.save();
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'User already registered',
            // 'REGISTRATION.USER_ALREADY_REGISTERED',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'Missing mandatory parameters',
          // 'REGISTRATION.MISSING_MANDATORY_PARAMETERS',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  // updating user
  // async updateAnyUser(
  //   userId: string,
  //   updateUserDto: UpdateAnyUserDto,
  // ): Promise<User> {
  //   const existingUser = await this.userRepository.findOne({
  //     where: { id: userId },
  //   });
  //   if (!existingUser) {
  //     throw new HttpException(
  //       `User not found with id ${userId}`,
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   const NIDAid = updateUserDto.nationalId;
  //   if (NIDAid) {
  //     const existingUserWithNID = await this.userRepository
  //       .createQueryBuilder('user')
  //       .where('user.nationalId = :nationalId AND user.id != :userId', {
  //         nationalId: NIDAid,
  //         userId,
  //       })
  //       .getOne();

  //     if (existingUserWithNID) {
  //       throw new HttpException(
  //         `NID already exists ${updateUserDto.nationalId}`,
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }

  //   let role = existingUser.role;
  //   if (updateUserDto.roleId) {
  //     role = await this.roleRepository.findOne({ id: updateUserDto.roleId });
  //     if (!role) {
  //       throw new HttpException(
  //         `Role not found with id ${updateUserDto.roleId}`,
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }

  //   let userType = existingUser.userType;
  //   if (updateUserDto.userTypeId) {
  //     userType = await this.userTypeRepository.findOne({
  //       id: updateUserDto.userTypeId,
  //     });
  //     if (!userType) {
  //       throw new HttpException(
  //         `User type not found with id ${updateUserDto.userTypeId}`,
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }

  //   let civilStatus = existingUser.civilStatus;
  //   if (updateUserDto.civilStatusId) {
  //     civilStatus = await this.civilStatusRepository.findOne({
  //       id: updateUserDto.civilStatusId,
  //     });
  //     if (!civilStatus) {
  //       throw new HttpException(
  //         `Civil status not found with id ${updateUserDto.civilStatusId}`,
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }

  //   // const userToBeRegistered =
  //   //   await this.validateToBeCreatedUser(updateAnyUserDto);
  //   // if (userToBeRegistered && userToBeRegistered.id !== existingUser.id) {
  //   //   throw new HttpException('User already registered', HttpStatus.BAD_REQUEST);
  //   // }

  //   if (updateUserDto.email && this.isValidEmail(updateUserDto.email)) {
  //     const userWithSameEmail = await this.findUserByEmail(updateUserDto.email);
  //     if (userWithSameEmail && userWithSameEmail.id !== existingUser.id) {
  //       if (userWithSameEmail.isEmailValid !== true) {
  //         return userWithSameEmail;
  //       } else {
  //         throw new HttpException(
  //           'Email already in use',
  //           HttpStatus.BAD_REQUEST,
  //         );
  //       }
  //     }
  //   } else if (updateUserDto.email && !this.isValidEmail(updateUserDto.email)) {
  //     throw new HttpException('Invalid email address', HttpStatus.BAD_REQUEST);
  //   }

  //   existingUser.firstName = updateUserDto.firstName ?? existingUser.firstName;
  //   existingUser.lastName = updateUserDto.lastName ?? existingUser.lastName;
  //   existingUser.email = updateUserDto.email ?? existingUser.email;
  //   existingUser.phoneNumber =
  //     updateUserDto.phoneNumber ?? existingUser.phoneNumber;
  //   existingUser.nationalId =
  //     updateUserDto.nationalId ?? existingUser.nationalId;
  //   existingUser.passport = updateUserDto.passport ?? existingUser.passport;
  //   existingUser.gender = updateUserDto.gender ?? existingUser.gender;
  //   existingUser.verifiedAt =
  //     updateUserDto.verifiedAt ?? existingUser.verifiedAt;
  //   existingUser.licenceIdArch =
  //     updateUserDto.licenceIdArch ?? existingUser.licenceIdArch;
  //   existingUser.licenceIdEng =
  //     updateUserDto.licenceIdEng ?? existingUser.licenceIdEng;
  //   existingUser.dateOfBirth =
  //     updateUserDto.dateOfBirth ?? existingUser.dateOfBirth;
  //   existingUser.role = role;
  //   existingUser.userType = userType;
  //   existingUser.civilStatus = civilStatus;

  //   return await this.userRepository.save(existingUser);
  // }
  async updateAnyUser(
    userId: string,
    updateUserDto: UpdateAnyUserDto,
  ): Promise<User> {
    // Fetch the existing user
    const existingUser = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!existingUser) {
      throw new HttpException(
        `User not found with id ${userId}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Log the data from DTO
    console.log('UpdateUserDto:', updateUserDto);

    // Log the data from existing user
    console.log('Existing User From DB:', existingUser);

    // // check if the national Id exist in the database
    // const NIDAid = updateUserDto.nationalId;
    // if (NIDAid) {
    //   const existingUserWithNID = await this.userRepository
    //     .createQueryBuilder('user')
    //     .where('user.nationalId = :nationalId AND user.id != :userId', {
    //       nationalId: NIDAid,
    //       userId,
    //     })
    //     .getOne();

    //   if (existingUserWithNID) {
    //     throw new HttpException(
    //       `NID already exists ${updateUserDto.nationalId}`,
    //       HttpStatus.BAD_REQUEST,
    //     );
    //   }
    // }

    let role = existingUser.role;
    if (updateUserDto.roleId) {
      role = await this.roleRepository.findOne({ id: updateUserDto.roleId });
      if (!role) {
        throw new HttpException(
          `Role not found with id ${updateUserDto.roleId}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    let userType = existingUser.userType;
    if (updateUserDto.userTypeId) {
      userType = await this.userTypeRepository.findOne({
        id: updateUserDto.userTypeId,
      });
      if (!userType) {
        throw new HttpException(
          `User type not found with id ${updateUserDto.userTypeId}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    let civilStatus = existingUser.civilStatus;
    if (updateUserDto.civilStatusId) {
      civilStatus = await this.civilStatusRepository.findOne({
        id: updateUserDto.civilStatusId,
      });
      if (!civilStatus) {
        throw new HttpException(
          `Civil status not found with id ${updateUserDto.civilStatusId}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    let agency = existingUser.agency;
    if (updateUserDto.agencyId) {
      agency = await this.agencyRepository.findOne({
        id: updateUserDto.agencyId,
      });
      if (!agency) {
        throw new HttpException(
          `agency not found with id ${updateUserDto.agencyId}`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    // // Handle approval level (optional)
    // if (updateUserDto.approvalLevelId) {
    //   const approvalLevel = await this.approvalLevelRepository.findOne({
    //     id: updateUserDto.approvalLevelId,
    //   });

    //   if (!approvalLevel) {
    //     throw new HttpException(
    //       `Approval level not found with id ${updateUserDto.approvalLevelId}`,
    //       HttpStatus.BAD_REQUEST,
    //     );
    //   }
    // }

    if (updateUserDto.email && this.isValidEmail(updateUserDto.email)) {
      const userWithSameEmail = await this.findUserByEmail(updateUserDto.email);
      if (userWithSameEmail && userWithSameEmail.id !== existingUser.id) {
        if (userWithSameEmail.isEmailValid !== true) {
          return userWithSameEmail;
        } else {
          throw new HttpException(
            'Email already in use',
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    } else if (updateUserDto.email && !this.isValidEmail(updateUserDto.email)) {
      throw new HttpException('Invalid email address', HttpStatus.BAD_REQUEST);
    }

    existingUser.firstName = updateUserDto.firstName ?? existingUser.firstName;
    existingUser.lastName = updateUserDto.lastName ?? existingUser.lastName;
    existingUser.email = updateUserDto.email ?? existingUser.email;
    existingUser.phoneNumber =
      updateUserDto.phoneNumber ?? existingUser.phoneNumber;
    existingUser.nationalId =
      updateUserDto.nationalId ?? existingUser.nationalId;
    existingUser.passport = updateUserDto.passport ?? existingUser.passport;
    existingUser.gender = updateUserDto.gender ?? existingUser.gender;

    // Check if dateOfBirth and verifiedAt are valid dates before updating
    if (updateUserDto.verifiedAt) {
      const verifiedAtDate = new Date(updateUserDto.verifiedAt);
      if (!isNaN(verifiedAtDate.getTime())) {
        existingUser.verifiedAt = verifiedAtDate;
      } else if (updateUserDto.verifiedAt === '') {
        existingUser.verifiedAt = null;
      }
    }

    if (updateUserDto.dateOfBirth) {
      const dateOfBirthDate = new Date(updateUserDto.dateOfBirth);
      if (!isNaN(dateOfBirthDate.getTime())) {
        existingUser.dateOfBirth = dateOfBirthDate;
      } else if (updateUserDto.dateOfBirth === '') {
        existingUser.dateOfBirth = null;
      }
    }

    existingUser.licenceIdArch =
      updateUserDto.licenceIdArch ?? existingUser.licenceIdArch;
    existingUser.licenceIdEng =
      updateUserDto.licenceIdEng ?? existingUser.licenceIdEng;
    existingUser.approvalLevelId =
      updateUserDto.approvalLevelId ?? existingUser.approvalLevelId;
    existingUser.role = role;
    existingUser.userType = userType;
    existingUser.civilStatus = civilStatus;
    existingUser.agency = agency;

    // Log the final user object before saving
    console.log('Final User Object:', existingUser);

    // Save the updated user
    return await this.userRepository.save(existingUser);
  }

  // async createNewStaff(newStaff: CreateStaffDto): Promise<User> {
  //   // check data before saving

  //   const dataFromDb = await this.roleRepository.findOne({
  //     id: newStaff.roleId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       `Role not found with id ${newStaff.roleId}`,
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   // check data before saving
  //   const dataFromDb2 = await this.userTypeRepository.findOne({
  //     id: newStaff.userTypeId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       `User type not found with id id ${newStaff.userTypeId}`,
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   // check data before saving
  //   const dataFromDb3 = await this.agencyRepository.findOne({
  //     id: newStaff.agencyId,
  //   });
  //   if (!dataFromDb3)
  //     throw new HttpException(
  //       `Agency not found with id ${newStaff.agencyId}`,
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const userToBeRegistered = await this.validateToBeCreatedStaff(newStaff);
  //   if (userToBeRegistered) {
  //     throw new HttpException('User already registered', HttpStatus.BAD_REQUEST);
  //   } else {
  //     if (this.isValidEmail(newStaff.email)) {
  //       const userRegistered = await this.findUserByEmail(newStaff.email);

  //       if (!userRegistered) {
  //         const dataFromDb = await this.roleRepository.findOne({
  //           id: newStaff.roleId,
  //         });
  //         if (!dataFromDb)
  //           throw new HttpException('Data Not Found', HttpStatus.BAD_REQUEST);

  //         const createdUser = new User();
  //         createdUser.firstName = newStaff.firstName;
  //         createdUser.lastName = newStaff.lastName;
  //         createdUser.email = newStaff.email;
  //         createdUser.phoneNumber = newStaff.phoneNumber;
  //         createdUser.gender = newStaff.gender;
  //         createdUser.password = newStaff.password = await bcrypt.hash(
  //           newStaff.password,
  //           saltRounds,
  //         );
  //         createdUser.role = { id: dataFromDb.id } as any;
  //         createdUser.userType = { id: dataFromDb2.id } as any;
  //         createdUser.agency = { id: dataFromDb3.id } as any;

  //         const userToBeCreated = this.userRepository.create(createdUser);
  //         return await userToBeCreated.save();
  //       } else if (userRegistered.isEmailValid !== true) {
  //         return userRegistered;
  //       } else {
  //         throw new HttpException(
  //           'User already registered',
  //           HttpStatus.BAD_REQUEST,
  //         );
  //       }
  //     } else {
  //       throw new HttpException(
  //         'Missing mandatory parameters',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }
  // }

  async createNewStaffWithDefaultPSW(newStaff: CreateStaffDto): Promise<User> {
    const dataFromDb = await this.roleRepository.findOne({
      id: newStaff.roleId,
    });
    if (!dataFromDb)
      throw new HttpException(
        `Role not found with id ${newStaff.roleId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb2 = await this.userTypeRepository.findOne({
      id: newStaff.userTypeId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        `User type not found with id id ${newStaff.userTypeId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb3 = await this.agencyRepository.findOne({
      id: newStaff.agencyId,
    });
    if (!dataFromDb3)
      throw new HttpException(
        `Agency not found with id ${newStaff.agencyId}`,
        HttpStatus.BAD_REQUEST,
      );
    const dataFromDb4 = await this.sectorRepository.findOne({
      id: newStaff.sectorId,
    });
    if (!dataFromDb4)
      throw new HttpException(
        `Sector not found with id ${newStaff.sectorId}`,
        HttpStatus.BAD_REQUEST,
      );

    const userToBeRegistered = await this.validateToBeCreatedStaff(newStaff);
    if (userToBeRegistered) {
      throw new HttpException(
        'User already registered',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (this.isValidEmail(newStaff.email)) {
        const userRegistered = await this.findUserByEmail(newStaff.email);

        if (!userRegistered) {
          const dataFromDb = await this.roleRepository.findOne({
            id: newStaff.roleId,
          });
          if (!dataFromDb)
            throw new HttpException('Data Not Found', HttpStatus.BAD_REQUEST);

          const createdUser = new User();
          createdUser.firstName = newStaff.firstName;
          createdUser.lastName = newStaff.lastName;
          createdUser.email = newStaff.email;
          createdUser.phoneNumber = newStaff.phoneNumber;
          createdUser.gender = newStaff.gender;
          createdUser.approvalLevelId = newStaff.approvalLevelId;
          // Generate a random password
          const randomPassword = 'Kubaka@123!';
          // Hash the generated password before assigning it to the user
          createdUser.password = await bcrypt.hash(randomPassword, saltRounds);
          createdUser.role = { id: dataFromDb.id } as any;
          createdUser.userType = { id: dataFromDb2.id } as any;
          createdUser.agency = { id: dataFromDb3.id } as any;

          createdUser.sector = { id: dataFromDb4.id } as any;

          const userToBeCreated = this.userRepository.create(createdUser);
          return await userToBeCreated.save();
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'User already registered',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'Missing mandatory parameters',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  async createNewStaff(newStaff: CreateStaffDto): Promise<User> {
    const dataFromDb = await this.roleRepository.findOne({
      id: newStaff.roleId,
    });
    if (!dataFromDb)
      throw new HttpException(
        `Role not found with id ${newStaff.roleId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb2 = await this.userTypeRepository.findOne({
      id: newStaff.userTypeId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        `User type not found with id id ${newStaff.userTypeId}`,
        HttpStatus.BAD_REQUEST,
      );

    // check data before saving
    const dataFromDb3 = await this.agencyRepository.findOne({
      id: newStaff.agencyId,
    });
    if (!dataFromDb3)
      throw new HttpException(
        `Agency not found with id ${newStaff.agencyId}`,
        HttpStatus.BAD_REQUEST,
      );

    const dataFromDb4 = await this.sectorRepository.findOne({
      id: newStaff.sectorId,
    });
    if (!dataFromDb4)
      throw new HttpException(
        `Sector not found with id ${newStaff.sectorId}`,
        HttpStatus.BAD_REQUEST,
      );

    const userToBeRegistered = await this.validateToBeCreatedStaff(newStaff);
    if (userToBeRegistered) {
      throw new HttpException(
        'User already registered',
        HttpStatus.BAD_REQUEST,
      );
    } else {
      if (this.isValidEmail(newStaff.email)) {
        const userRegistered = await this.findUserByEmail(newStaff.email);

        if (!userRegistered) {
          const dataFromDb = await this.roleRepository.findOne({
            id: newStaff.roleId,
          });
          if (!dataFromDb)
            throw new HttpException('Data Not Found', HttpStatus.BAD_REQUEST);

          const createdUser = new User();
          createdUser.firstName = newStaff.firstName;
          createdUser.lastName = newStaff.lastName;
          createdUser.email = newStaff.email;
          createdUser.phoneNumber = newStaff.phoneNumber;
          createdUser.gender = newStaff.gender;
          createdUser.approvalLevelId = newStaff.approvalLevelId;
          // Generate a random password
          const randomPassword = this.generateRandomPassword();
          // Hash the generated password before assigning it to the user
          createdUser.password = await bcrypt.hash(randomPassword, saltRounds);

          // createdUser.password = newStaff.password = await bcrypt.hash(
          //   newStaff.password,
          //   saltRounds,
          // );
          createdUser.role = { id: dataFromDb.id } as any;
          createdUser.userType = { id: dataFromDb2.id } as any;
          createdUser.agency = { id: dataFromDb3.id } as any;
          createdUser.sector = { id: dataFromDb4.id } as any;

          const userToBeCreated = this.userRepository.create(createdUser);
          return await userToBeCreated.save();
        } else if (userRegistered.isEmailValid !== true) {
          return userRegistered;
        } else {
          throw new HttpException(
            'User already registered',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        throw new HttpException(
          'Missing mandatory parameters',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  generateRandomPassword(): string {
    const length = 8;
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  // async createNewUser(newUser: CreateUserDto): Promise<User> {
  //   const dataFromDb = await this.roleRepository.findOne({
  //     id: newUser.roleId,
  //   });
  //   console.log(dataFromDb);
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       `ROLE_BAD_REQUEST with id ${newUser.roleId}`,
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const userToBeRegistered = await this.validateToBeCreatedUser(newUser);
  //   if (userToBeRegistered) {
  //     throw new HttpException(
  //       'REGISTRATION.USER_ALREADY_REGISTERED',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   } else {
  //     if (this.isValidEmail(newUser.email)) {
  //       // if (this.isValidEmail(newUser.email) && newUser.password) {
  //       const userRegistered = await this.findUserByEmail(newUser.email);

  //       if (!userRegistered) {
  //         newUser.password = await bcrypt.hash(newUser.password, saltRounds);
  //         const createdUser = this.userRepository.create(newUser);
  //         // createdUser.roles = ["User"];
  //         // createdUser.role.id = newUser.roleId;

  //         return await createdUser.save();
  //       } else if (userRegistered.isEmailValid !== true) {
  //         return userRegistered;
  //       } else {
  //         throw new HttpException(
  //           'REGISTRATION.USER_ALREADY_REGISTERED',
  //           HttpStatus.BAD_REQUEST,
  //         );
  //       }
  //     } else {
  //       throw new HttpException(
  //         'REGISTRATION.MISSING_MANDATORY_PARAMETERS',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   }
  // }

  private async validateToBeCreatedUser(
    createUserDto: CreateUserDto,
  ): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email: createUserDto.email },
      });
      if (!userFromDb)
        throw new HttpException(
          ' Email already exist',
          // 'LOGIN.EMAIL.ALREADY.EXIST.ERROR',
          HttpStatus.BAD_REQUEST,
        );
    } catch (err) {
      return;
    }
    return true;
  }

  async validateToBeCreatedStaff(
    createStaffDto: CreateStaffDto,
  ): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email: createStaffDto.email },
      });
      if (!userFromDb)
        throw new HttpException(' Email already exist', HttpStatus.BAD_REQUEST);
    } catch (err) {
      return;
    }
    return true;
  }

  async setPassword(email: string, newPassword: string): Promise<boolean> {
    const userFromDb = await this.userRepository.findOne({ where: { email } });
    if (!userFromDb)
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);

    // userFromDb.password = bcrypt.hash(newPassword, saltRounds);
    userFromDb.password = await bcrypt.hash(newPassword, saltRounds);

    await this.userRepository.save(userFromDb);
    return true;
  }

  async setPasswordStaff(email: string, newPassword: string): Promise<boolean> {
    const userFromDb = await this.userRepository.findOne({ where: { email } });
    if (!userFromDb)
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);

    // userFromDb.password = bcrypt.hash(newPassword, saltRounds);
    userFromDb.password = await bcrypt.hash(newPassword, saltRounds);

    await this.userRepository.create(userFromDb);
    return true;
  }
  async setPasswordForChanging(
    email: string,
    newPassword: string,
  ): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email },
      });
      if (!userFromDb) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }

      console.log(newPassword);
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // const updateResult = await this.userRepository.update(userFromDb.id, {
      //   password: hashedPassword,
      // });
      // console.log(hashedPassword);
      // console.log(updateResult);

      // Update user entity
      const userUpdate = await this.userRepository
        .createQueryBuilder()
        .update(User)
        .set({
          password: hashedPassword,
          isStaffPasswordChanged: true,
        })
        .where('email = :email', { email: email })
        .execute();
      console.log(userUpdate);
      console.log(hashedPassword);

      if (userUpdate.affected === 0) {
        throw new HttpException(
          'Password update failed',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      throw new HttpException(
        'An error occurred while updating the password',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async setPasswordForChangingStaff(
    email: string,
    newPassword: string,
  ): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email },
      });
      if (!userFromDb) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }

      console.log(newPassword);
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // const updateResult = await this.userRepository.update(userFromDb.id, {
      //   password: hashedPassword,
      // });
      // console.log(hashedPassword);
      // console.log(updateResult);

      // Update user entity
      const userUpdate = await this.userRepository
        .createQueryBuilder()
        .update(User)
        .set({
          password: hashedPassword,
          isStaffPasswordChanged: true,
        })
        .where('email = :email', { email: email })
        .execute();
      console.log(userUpdate);
      console.log(hashedPassword);

      if (userUpdate.affected === 0) {
        throw new HttpException(
          'Password update failed',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      throw new HttpException(
        'An error occurred while updating the password',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async setPasswordExpired(email: string): Promise<boolean> {
    try {
      const userFromDb = await this.userRepository.findOne({
        where: { email },
      });
      if (!userFromDb) {
        throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
      }

      // Update user entity
      const userUpdate = await this.userRepository
        .createQueryBuilder()
        .update(User)
        .set({
          isPasswordExpired: true,
        })
        .where('email = :email', { email: email })
        .execute();
      console.log(userUpdate);

      if (userUpdate.affected === 0) {
        throw new HttpException(
          'Password update failed',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      return true;
    } catch (error) {
      console.error('Error updating password:', error);
      throw new HttpException(
        'An error occurred while updating the password',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateProfile(profileDto: UserProfileDto): Promise<User> {
    const userFromDb = await this.userRepository.findOne({
      // email: profileDto.email,
      where: { email: profileDto.email },
    });
    if (!userFromDb)
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    // throw new HttpException('COMMON.USER_BAD_REQUEST', HttpStatus.BAD_REQUEST);

    if (profileDto.profilePicture) {
      const base64Data = profileDto.profilePicture.replace(
        /^data:image\/png;base64,/,
        '',
      );
      const dir = '../public/users/' + userFromDb.email;

      const success = await this.writeFile(dir, 'profilepic.png', base64Data);
      if (success == true) {
        // userFromDb.photos = userFromDb.photos || {
        //   profilePic: new PhotoDto(),
        //   gallery: [],
        // };
        // userFromDb.photos.profilePic =
        //   userFromDb.photos.profilePic || new PhotoDto();
        // userFromDb.photos.profilePic.date = new Date();
        // userFromDb.photos.profilePic.url =
        //   '/public/users/' + userFromDb.email + '/profilepic.png';
      }
    }

    // await userFromDb.save();
    return userFromDb;
  }

  async updateUserProfile(updateUserProfileDto: UpdateUserProfileDto) {
    // Check if the user exists
    const userFromDb = await this.userRepository.findOne({
      where: { id: updateUserProfileDto.userId },
    });
    if (!userFromDb) {
      throw new HttpException('User not found', HttpStatus.BAD_REQUEST);
    }

    // Check if the email is already in use by another user
    const existingUserWithEmail = await this.userRepository.findOne({
      where: { email: updateUserProfileDto.email },
    });
    if (
      existingUserWithEmail &&
      existingUserWithEmail.id !== updateUserProfileDto.userId
    ) {
      throw new HttpException('Email already in use', HttpStatus.BAD_REQUEST);
    }

    // Update the phone and email of the user
    await this.userRepository
      .createQueryBuilder()
      .update(User)
      .set({
        phoneNumber: updateUserProfileDto.phoneNumber,
        email: updateUserProfileDto.email,
      })
      .where('id = :id', { id: updateUserProfileDto.userId })
      .execute();

    const updatedUser = await this.userRepository.findOne({
      where: { id: updateUserProfileDto.userId },
    });

    return updatedUser;
  }

  async writeFile(
    dir: string,
    filename: string,
    base64Data: string,
  ): Promise<any> {
    return new Promise(function (resolve, reject) {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const fs = require('fs');
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir);
      }
      fs.writeFile(dir + '/' + filename, base64Data, 'base64', function (err) {
        if (err) reject(err);
        else resolve(true);
      });
    });
  }

  // // User on admin level
  // async createUserByAdmin(createUserDto: CreateUserDto) {
  //   await this.validateCreateUserDto(createUserDto);
  //   const user = new User({
  //     ...createUserDto,
  //     password: await bcrypt.hash(createUserDto.password, 10),
  //   });
  //   return this.userRepository.create(user);
  // }

  // private async validateCreateUserDto(createUserDto: CreateUserDto) {
  //   try {
  //     await this.userRepository.findOne({ email: createUserDto.email });
  //   } catch (err) {
  //     return;
  //   }
  //   throw new UnprocessableEntityException('Email already exists.');
  // }

  // async verifyUser(email: string, password: string) {
  //   const user = await this.userRepository.findOne({ email });
  //   const passwordIsValid = await bcrypt.compare(password, user.password);
  //   if (!passwordIsValid) {
  //     throw new UnauthorizedException('Credentials are not valid.');
  //   }
  //   return user;
  // }

  // async getUser(getUserDto: UpdateUserDto) {
  //   return this.userRepository.findOne(getUserDto, { role: true });
  // }

  // async getAllUsers() {
  //   return this.userRepository.(User)
  //   .createQueryBuilder("user")
  //   .leftJoinAndSelect("user.role", "role")
  //   .getMany()
  // }

  // async getAllUsers() {
  //   return this.userRepository.find({
  //     relations: {
  //       role: true,
  //     },
  //   });
  // }

  // async getAllUsers() {
  //   return this.userRepository.find({});
  // }

  // async findAllUsers(): Promise<User[]> {
  //   return await this.userRepository.find({});
  // }

  // async findUsersByUserType(userTypeId: string): Promise<User> {
  //   console.log(userTypeId);
  //   return this.userRepository.findOne({
  //     where: { email },
  //     relations: {
  //       role: true,
  //       userType: true,
  //       agency: true,
  //       civilStatus: true,
  //     },
  //   });
  // }

  // async findUsersByUserType(userTypeId: string): Promise<User[]> {
  //   return this.userEntityRepository.find({
  //     where: {
  //       userType: { id: userTypeId },
  //     },
  //   });
  // }

  async findUsersByUserType(userTypeId: string): Promise<User[]> {
    console.log(userTypeId);
    const users = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.userType', 'userType')
      .where('user.userType = :userTypeId', {
        userTypeId,
      })
      .getMany();

    return users;
  }

  // async findUserByEngineerNumber(licenceIdEng: string) {
  //   const user = this.userRepository
  //     .createQueryBuilder('user')
  //     .where('user.licenceIdEng = :licenceIdEng', {
  //       licenceIdEng,
  //     })
  //     .getOne();
  //   if (!user) {
  //     throw new HttpException(
  //       'This Engineer is not found in BPMIS Kubaka as a user',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  //   delete (await user).password; // Remove the password property
  //   return user;
  // }

  async findUserByEngineerNumber(licenceIdEng: string) {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.licenceIdEng = :licenceIdEng', { licenceIdEng })
      .getOne();
    if (!user) {
      throw new HttpException(
        'This Engineer is not found in BPMIS Kubaka as a user',
        HttpStatus.BAD_REQUEST,
      );
    }
    // Remove the password property if the user is found
    delete user.password;
    return user;
  }

  async findUserByArchitectNumber(licenceIdArch: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .where('user.licenceIdArch = :licenceIdArch', { licenceIdArch })
      .getOne();

    if (!user) {
      throw new HttpException(
        'This Architect is not found in BPMIS Kubaka as a user',
        HttpStatus.BAD_REQUEST,
      );
    }
    delete (await user).password; // Remove the password property
    return user;
  }

  // Roles
  async createRole(roleDto: RoleDto) {
    const role = new Role({
      ...roleDto,
    });

    return this.roleRepository.create(role);
  }

  // async findAllRole() {
  //   return this.roleRepository.find({});
  // }
  async findAllRole() {
    const roles = await this.roleRepository.find({});

    const enrichedRoles = await Promise.all(
      roles.map(async (role) => {
        let createdByName: string | null = null;
        let updatedByName: string | null = null;

        if (role.createdBy) {
          const creator = await this.findUserById(role.createdBy);
          createdByName = creator
            ? `${creator.firstName} ${creator.lastName}`
            : null;
        }

        if (role.updatedBy) {
          const updater = await this.findUserById(role.updatedBy);
          updatedByName = updater
            ? `${updater.firstName} ${updater.lastName}`
            : null;
        }

        return {
          ...role,
          createdByName,
          updatedByName,
        };
      }),
    );

    return enrichedRoles;
  }

  async findOneRole(id: string) {
    return this.roleRepository.findOne({ id });
  }

  async updateRole(id: string, updateRoleDto: UpdateRoleDto) {
    return this.roleRepository.findOneAndUpdate({ id }, updateRoleDto);
  }

  async removeRole(id: string) {
    return this.roleRepository.findOneAndDelete({ id });
  }

  // user activities
  async createUserActivity(userActivityDto: UserActivityDto) {
    return this.userActivityRepository.save(userActivityDto);
  }

  async findAllUserActivities() {
    return this.userActivityRepository.find({});
  }

  // async findOneUserActivity(id: string) {
  //   return this.userActivityRepository.findOne({ id });
  // }

  // async updateUserActivity(
  //   id: string,
  //   updateUserActivityDto: UpdateUserActivityDto,
  // ) {
  //   return this.userActivityRepository.findOneAndUpdate(
  //     { id },
  //     updateUserActivityDto,
  //   );
  // }

  // async removeUserActivity(id: string) {
  //   return this.userActivityRepository.findOneAndDelete({ id });
  // }

  // user Types
  async createUserType(userTypeDto: UserTypeDto) {
    const userType = new UserType({
      ...userTypeDto,
    });
    return this.userTypeRepository.create(userType);
  }

  async findAllUserTypes() {
    return this.userTypeRepository.find({});
  }

  async findOneUserType(id: string) {
    return this.userTypeRepository.findOne({ id });
  }

  async updateUserType(id: string, updateUserTypeDto: UpdateUserTypeDto) {
    return this.userTypeRepository.findOneAndUpdate({ id }, updateUserTypeDto);
  }

  async removeUserType(id: string) {
    return this.userTypeRepository.findOneAndDelete({ id });
  }

  // Civil status
  async createCivilStatus(civilStatusDto: CivilStatusDto) {
    const civilStatus = new CivilStatus({
      ...civilStatusDto,
    });
    return this.civilStatusRepository.create(civilStatus);
  }

  async findAllCivilStatus() {
    return this.civilStatusRepository.find({});
  }

  async findOneCivilStatus(id: string) {
    return this.civilStatusRepository.findOne({ id });
  }

  async updateCivilStatus(
    id: string,
    updateCivilStatusDto: UpdateCivilStatusDto,
  ) {
    return this.civilStatusRepository.findOneAndUpdate(
      { id },
      updateCivilStatusDto,
    );
  }

  async removeCivilStatus(id: string) {
    return this.civilStatusRepository.findOneAndDelete({ id });
  }

  async createUserActivityRole(createUserActivityRole: {
    userActivityId: string;
    roleId: string;
  }): Promise<void> {
    const userActivity = await this.userActivityRepository.find({
      where: { id: createUserActivityRole.userActivityId },
    });
    if (!userActivity) {
      throw new NotFoundException();
    }

    await this.userActivityRoleRepository.save(createUserActivityRole);
  }

  // // Permissions
  // async createPermission(permissionDto: PermissionDto) {
  //   const permission = new Permission({
  //     ...permissionDto,
  //   });
  //   return this.permissionRepository.create(permission);
  // }

  async findAllPermissions() {
    return this.userActivityRoleRepository.find({
      relations: {
        roles: true,
        userActivities: true,
      },
    });
  }

  async findOnePermission(id: string) {
    const permissionByRoles = await this.userActivityRoleRepository
      .createQueryBuilder('permission')
      .leftJoin('permission.roles', 'role')
      .where('permission.roleId = :roleId', { roleId: id })
      .getOne();
    return permissionByRoles;
  }

  async findPermissionByRoleId(id: string) {
    const permissionByRoles = await this.userActivityRoleRepository
      .createQueryBuilder('permission')
      .leftJoinAndSelect('permission.roles', 'role')
      .leftJoinAndSelect('permission.userActivities', 'userActivity')
      .where('permission.roleId = :roleId', { roleId: id })
      .getMany();
    return permissionByRoles;
  }

  // async updatePermission(id: string, updatePermissionDto: UpdatePermissionDto) {
  //   return this.permissionRepository.findOneAndUpdate(
  //     { id },
  //     updatePermissionDto,
  //   );
  // }

  async updatePermission(updateUserActivityRole: {
    userActivityId: string;
    roleId: string;
    isDeleted: boolean;
  }) {
    const userActivity = await this.userActivityRepository.find({
      where: { id: updateUserActivityRole.userActivityId },
    });
    if (!userActivity) {
      throw new NotFoundException();
    }

    await this.userActivityRoleRepository.save(updateUserActivityRole);
  }

  async removePermission(id: string) {
    const question = await this.userActivityRoleRepository.findOne({
      relations: {
        roles: true,
      },
      where: { roleId: id },
    });

    await this.userActivityRoleRepository.remove(question);
  }

  async removePermissionByRoleAndUserActivity(
    role: string,
    userActivity: string,
  ) {
    // const question = await this.userActivityRoleRepository.findOne({
    //   relations: {
    //     roles: true,
    //     userActivities: true,
    //   },
    //   where: { roleId: role } && { userActivityId: userActivity },
    // });
    // await this.userActivityRoleRepository.remove(question);
    console.log(role, userActivity);
  }

  // Delimitation
  // Country
  async createCountry(countryDto: CountryDto) {
    const country = new Country({
      ...countryDto,
    });
    return this.countryRepository.create(country);
  }

  async findAllCountries() {
    return this.countryRepository.find({});
  }

  async removeCountry(id: string) {
    return this.countryRepository.findOneAndDelete({ id });
  }

  // Province
  async createProvince(provinceDto: ProvinceDto) {
    const province = new Province({
      ...provinceDto,
    });
    return this.provinceRepository.create(province);
  }

  // async findAllProvinces() {
  //   return this.provinceRepository.find({});
  // }

  async findAllProvinces() {
    return this.provinceRepository.findOne({
      country: true,
    });
  }

  async removeProvince(id: string) {
    return this.provinceRepository.findOneAndDelete({ id });
  }

  // District
  async createDistrict(districtDto: DistrictDto) {
    const district = new District({
      ...districtDto,
    });
    return this.districtRepository.create(district);
  }

  async findAllDistricts() {
    return this.districtRepository.find({
      provinces: true,
    });
  }

  async updateDistrict(id: string, updateDistrictDto: UpdatedDistrictDto) {
    return this.districtRepository.findOneAndUpdate({ id }, updateDistrictDto);
  }

  // Sector
  async createSector(sectorDto: SectorDto) {
    const sector = new Sector({
      ...sectorDto,
    });
    return this.sectorRepository.create(sector);
  }

  // async findAllSectors() {
  //   return this.sectorRepository.find({
  //     district: true,
  //   });
  // }
  async findAllSectors() {
    return this.sectorRepository.findAll({
      relations: {
        district: true,
      },
    });
  }

  // upload sector for data migration
  async createSectorForUpload(sectorDto: UploadSectorDto) {
    const { name, code, districtCode } = sectorDto;
    const district = await this.districtRepository.findOne({
      code: districtCode,
    });

    if (!district) {
      throw new BadRequestException(
        `District with code ${districtCode} not found`,
      );
    }

    const sector = await this.sectorEntityRepository.save({
      name,
      code,
      district,
    });

    return this.sectorEntityRepository.save(sector);
  }
  async uploadSectorsFromExcel(buffer: Buffer) {
    if (!Buffer.isBuffer(buffer)) {
      throw new BadRequestException('Provided data is not a buffer');
    }

    try {
      const workbook = new ExcelJS.Workbook();

      // Attempt to load the buffer into ExcelJS
      await workbook.xlsx.load(buffer as any); // Load the file correctly

      const worksheet = workbook.worksheets[0]; // Ensure there is at least one sheet

      if (!worksheet) {
        throw new BadRequestException(
          'The Excel file does not contain any worksheets.',
        );
      }

      const sectors: UploadSectorDto[] = [];

      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        // Extract values from the row
        const districtCode = row.getCell(1).text.trim();
        const sectorCode = row.getCell(2).text.trim();
        const sectorName = row.getCell(3).text.trim();

        sectors.push({ districtCode, code: sectorCode, name: sectorName });
      });

      const results = [];
      for (const sectorDto of sectors) {
        const result = await this.createSectorForUpload(sectorDto);
        results.push(result);
      }

      return results;
    } catch (error) {
      throw new BadRequestException(
        `Failed to process the Excel file: ${error.message}`,
      );
    }
  }

  // get sector by district code

  async findSectorByDistrictCode(code: string) {
    const district = await this.districtRepository.findOne({
      code,
      agencies: true,
    });

    if (!district) {
      throw new Error('District not found');
    }

    const sectors = await this.findAllSectors();

    const filteredSectors = sectors.filter(
      (sector) => sector.district.code === code,
    );

    if (filteredSectors.length === 0) {
      throw new Error('No sector found for the district');
    }

    return filteredSectors;
  }

  // Cell
  async createCell(cellDto: CellDto) {
    const cell = new Cell({
      ...cellDto,
    });
    return this.cellRepository.create(cell);
  }

  async findAllCells() {
    return this.cellRepository.findOne({
      sector: true,
    });
  }

  // Village
  async createVillage(villageDto: VillageDto) {
    const village = new Village({
      ...villageDto,
    });
    return this.villageRepository.create(village);
  }

  async findAllVillages() {
    return this.villageRepository.findOne({
      cell: true,
    });
  }

  // Agency
  async createAgency(agencyDto: AgencyDto) {
    const agency = new Agency({
      ...agencyDto,
    });
    return this.agencyRepository.create(agency);
  }

  async findAllAgencies() {
    return this.agencyRepository.find({});
  }

  async findOneAgency(id: string) {
    return this.agencyRepository.findOne({ id });
  }
  async updateAgency(id: string, updateAgencyDto: UpdateAgencyDto) {
    return this.agencyRepository.findOneAndUpdate({ id }, updateAgencyDto);
  }

  async removeAgency(id: string) {
    return this.agencyRepository.findOneAndDelete({ id });
  }

  @EventPattern('checkAgencyDataByDistrictCode')
  // @MessagePattern({ cmd: 'checkAgencyDataByDistrictCode' })
  async findAgencyByDistrictCode(code: string) {
    const district = await this.districtRepository.findOne({
      code,
      agencies: true,
    });

    console.log(district);
    if (!district) {
      throw new Error('District not found');
    }

    const agencyDistricts = await this.getAllAgencyDistricts();

    const filteredAgencyDistricts = agencyDistricts.filter(
      (agencyDistrict) => agencyDistrict.district.code === code,
    );

    if (filteredAgencyDistricts.length === 0) {
      throw new Error('No agency found for the district');
    }

    return filteredAgencyDistricts[0].agency;
  }

  async createAgencyDistrict(
    createAgencyDistrictDto: CreateAgencyDistrictDto,
  ): Promise<AgencyDistrict> {
    const dataFromDb = await this.districtRepository.findOne({
      id: createAgencyDistrictDto.districtId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.BAD_REQUEST,
      );
    const dataFromDb2 = await this.agencyRepository.findOne({
      id: createAgencyDistrictDto.agencyId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.BAD_REQUEST,
      );

    const agencyDistrict = new AgencyDistrict({
      ...createAgencyDistrictDto,
      district: (createAgencyDistrictDto.districtId = {
        id: dataFromDb.id,
      } as any),
      agency: (createAgencyDistrictDto.agencyId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.agencyDistrictRepository.create(agencyDistrict);
  }

  async getAllAgencyDistricts(): Promise<AgencyDistrict[]> {
    return this.agencyDistrictRepository.findAll({
      relations: {
        district: true,
        agency: true,
      },
    });
  }

  async getAgencyDistrictsByAgencyId(
    agencyId: string,
  ): Promise<AgencyDistrict[]> {
    return await this.agencyDistrictEntityRepository.find({
      where: { agency: { id: agencyId } },
      relations: ['district'],
    });
  }
}
