import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InvoiceRoutingModule } from './payment-routing.module';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { InvoicesComponent } from '../components/invoices/invoices.component';
import { InvoiceDetailComponent } from '../components/invoice-detail/invoice-detail.component';
import { InvoiceDetailWaterMarkComponent } from '../components/invoice-detail-water-mark/invoice-detail-water-mark.component';

@NgModule({
  declarations: [
    InvoicesComponent,
    InvoiceDetailComponent,
    InvoiceDetailWaterMarkComponent
  ],
  imports: [
    CommonModule,
    InvoiceRoutingModule,
    SharedModule
  ]
})
export class PaymentModule { }
