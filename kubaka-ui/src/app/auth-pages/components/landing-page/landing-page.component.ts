import { Component } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { ApplicationService } from 'src/app/application/services/application.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { SessionService } from 'src/app/authentication-services/session.service';
import { Router } from '@angular/router';
import { LanguageService } from 'src/app/services/language.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss']
})
export class LandingPageComponent {
  services: any[] = [];
  private subscription!: Subscription;
  selectedService: any = {};
  year: number = new Date().getFullYear();
  submitted: boolean = false;
  upiFinder: string = '';
  outputData: any = {};
  constructor(
    // private translateService: TranslateService,
    private languageService: LanguageService,
    private modalService: NgbModal,
    private utilService: UtilService,
    private applicationService: ApplicationService,
    private sessionService: SessionService,
    private router: Router
  ) {

    // const token = this.sessionService.getSession();
    // if (this.sessionService.getSession()) {
    //   this.router.navigate(['/account/dashboard']); // Redirect to dashboard
    // } else {
    //   this.router.navigate(['/']); // Redirect to home
    // }
  }

  ngOnInit() {
    this.subscription = this.languageService.services$.subscribe((data) => {
      this.services = data;
    });
  }

  defaultSelectedLang = this.languageService.getCurrentLanguage();
  switchLang(lang: string) {
    this.languageService.switchLang(lang);
    this.defaultSelectedLang = lang;
  }

  opentModalEligible(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }
  /**
   * Open modal
   * @param content modal content
   */
  openModal(content: any, sizeParams: any) {
    if (this.upiFinder) {
      this.submitted = true;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/applicationName/search?search=' + this.upiFinder)
        .subscribe(
          data => {
            this.submitted = false;
            this.outputData = data[0];

            if (data.statusCode == 400) {
              this.utilService.showNotification(NOTIFICATION_COLOR.error, data.message + "", "top", "right");
            } else {
              this.modalService.open(content, { size: sizeParams, centered: true });
              this.outputData.isViewingTracking = true;
            }
            // if (data.message !== "Application not found") {
            //   this.modalService.open(content, { size: sizeParams, centered: true });
            //   this.outputData.isViewingTracking = true;
            // }

          },
          error => {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, error + "", "top", "right"); this.submitted = false;
          }
        )
    }
  }

  openModalService(content: any, value: any) {
    this.selectedService = value;
    this.modalService.open(content, { size: "lg", centered: true });
  }

  close() {
    this.modalService.dismissAll();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
}
