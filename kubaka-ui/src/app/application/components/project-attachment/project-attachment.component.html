<!-- step1.component.html -->
<div>
  <!-- Step content goes here -->
  <div class="step-panel">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
      <div class="step-panel_header">
        <h2>{{"projectAttachment.title" | translate }}</h2>
      </div>
      <div class="step-panel_body" style="display: none;">
        <div class="kbk-x">
          <div class="form-set">
            <div class="form-input">
              <label>{{"projectAttachment.requiredTitle" | translate }}<span class="estrx"> *</span></label>
              <div>
                <select id="documentRequiredId" name="documentRequiredId" formControlName="documentRequiredId">
                  <option value="">{{"projectAttachment.choose" | translate }}</option>
                  <option *ngFor="let gn of documentRequired" [value]="gn.id">{{gn.name}}</option>
                </select>
              </div>
            </div>
            <div class="form-input">
              <label>{{"projectAttachment.chooseUploadDocument" | translate }} *</label>
              <div class="form-inputfield">
                <!-- <div class="form-uploaddocs">
                  <label class="form-uploaddocs-btn">
                    <input type="file" multiple (change)="getFileDetails($event)" id="document" name="document"
                      formControlName="document" style="display: none" />
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="attach">
                          <rect width="24" height="24" opacity="0" />
                          <path
                            d="M9.29 21a6.23 6.23 0 0 1-4.43-1.88 6 6 0 0 1-.22-8.49L12 3.2A4.11 4.11 0 0 1 15 2a4.48 4.48 0 0 1 3.19 1.35 4.36 4.36 0 0 1 .15 6.13l-7.4 7.43a2.54 2.54 0 0 1-1.81.75 2.72 2.72 0 0 1-1.95-.82 2.68 2.68 0 0 1-.08-3.77l6.83-6.86a1 1 0 0 1 1.37 1.41l-6.83 6.86a.68.68 0 0 0 .*********** 0 0 0 .*********** 0 0 0 .4-.16l7.39-7.43a2.36 2.36 0 0 0-.15-3.31 2.38 2.38 0 0 0-3.27-.15L6.06 12a4 4 0 0 0 .22 5.67 4.22 4.22 0 0 0 3 1.29 3.67 3.67 0 0 0 2.61-1.06l7.39-7.43a1 1 0 1 1 1.42 1.41l-7.39 7.43A5.65 5.65 0 0 1 9.29 21z" />
                        </g>
                      </g>
                    </svg> Choose a file </label>
                </div> -->
                <div class="form-uploaddocs-load">
                    <span class="form-uploaddocs-text">{{ fileData?.fileNameDisplay ? fileData.fileNameDisplay : ('otherApplicationDocument.uploadDocumentPdf' | translate) }}
                    </span>
                  <span class="form-uploaddocs-loader"></span>
                </div>
              </div>
            </div>
          </div>
          <div class="uploaded-content">
            <h3 class="mgn">{{"projectAttachment.uploadDocumentPdf" | translate }}</h3>
            <ul class="uploaded-list">
              <li class="uploaded-file" *ngFor="let up of uploadedDocuments">
                <div class="kbk-x-s kbk-ac">
                  <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                  <span>{{up.requiredDocumentName?.name}}</span>
                </div>
                <div class="xtions">
                  <!-- <a class="kbk-link">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="edit">
                          <rect width="24" height="24" opacity="0" />
                          <path
                            d="M19.4 7.34L16.66 4.6A2 2 0 0 0 14 4.53l-9 9a2 2 0 0 0-.57 1.21L4 18.91a1 1 0 0 0 .29.8A1 1 0 0 0 5 20h.09l4.17-.38a2 2 0 0 0 1.21-.57l9-9a1.92 1.92 0 0 0-.07-2.71zM9.08 17.62l-3 .28.27-3L12 9.32l2.7 2.7zM16 10.68L13.32 8l1.95-2L18 8.73z" />
                        </g>
                      </g>
                    </svg>
                  </a> -->
                  <a class="kbk-link" data-bs-target="#showModal" (click)="openDeleteModal(deleteModel, 'md', up)">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="trash-2">
                          <rect width="24" height="24" opacity="0" />
                          <path
                            d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                          <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                          <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                        </g>
                      </g>
                    </svg>
                  </a>
                </div>
              </li>
              <!-- <li class="uploaded-file">
              <div class="kbk-x-s kbk-ac">
                <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                <span>Document.pdf</span>
              </div>
              <div class="xtions">
                <a class="kbk-link">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="edit">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M19.4 7.34L16.66 4.6A2 2 0 0 0 14 4.53l-9 9a2 2 0 0 0-.57 1.21L4 18.91a1 1 0 0 0 .29.8A1 1 0 0 0 5 20h.09l4.17-.38a2 2 0 0 0 1.21-.57l9-9a1.92 1.92 0 0 0-.07-2.71zM9.08 17.62l-3 .28.27-3L12 9.32l2.7 2.7zM16 10.68L13.32 8l1.95-2L18 8.73z" />
                      </g>
                    </g>
                  </svg>
                </a>
                <a class="kbk-link">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="trash-2">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                        <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                        <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                      </g>
                    </g>
                  </svg>
                </a>
              </div>
            </li>
            <li class="uploaded-file">
              <div class="kbk-x-s kbk-ac">
                <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                <span>Document.pdf</span>
              </div>
              <div class="xtions">
                <a class="kbk-link">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="edit">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M19.4 7.34L16.66 4.6A2 2 0 0 0 14 4.53l-9 9a2 2 0 0 0-.57 1.21L4 18.91a1 1 0 0 0 .29.8A1 1 0 0 0 5 20h.09l4.17-.38a2 2 0 0 0 1.21-.57l9-9a1.92 1.92 0 0 0-.07-2.71zM9.08 17.62l-3 .28.27-3L12 9.32l2.7 2.7zM16 10.68L13.32 8l1.95-2L18 8.73z" />
                      </g>
                    </g>
                  </svg>
                </a>
                <a class="kbk-link">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="trash-2">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                        <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                        <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                      </g>
                    </g>
                  </svg>
                </a>
              </div>
            </li> -->
            </ul>
          </div>
        </div>
      </div>

      <!-- Required Documents list     -->
      <div class="kbk-updloadtable">
        <ul class="updload-list">
          <div class="updload-list_header">
            <li class="updload-list_item">
              <div class="updload-list_item-dt">
                <span class="text">{{"projectAttachment.requiredDocuments" | translate }} <a href="assets/files/BOQ-OF-REVIEW.xlsx">{{"projectAttachment.requiredBoqLink" | translate }}</a></span>
                <span class="kbk-warn"><img src="assets/ikons/colored/ikon-warn-o.svg">{{"projectAttachment.requiredWarning" | translate }}</span>

              </div>
              <div class="updload-list_item-dt">
                <span class="text">{{"projectAttachment.uploadDocumentPdf" | translate }}</span>
              </div>
            </li>
          </div>
          <div class="updload-list_body">
            <li class="updload-list_item" *ngFor="let dr of documentRequired">
              <div class="updload-list_item-dt">
                <span class="text">{{dr.name}}</span>
                <label *ngIf="!fileSubmit" class="kbk-btn kbk-btn-sm kbk-btn-sec">
                  <input type="file" multiple (change)="getFileDetails($event, dr)" id="document" name="document"
                    formControlName="document" style="display: none;"> {{"projectAttachment.upload" | translate }} </label>
                <label *ngIf="fileSubmit && pageController === dr.name" class="kbk-btn kbk-btn-sm kbk-btn-sec">
                  <input type="file" style="display: none;"> {{"projectAttachment.wait" | translate }} </label>
              </div>
              <div class="updload-list_item-dt">
                <div *ngIf="dr.uploaded">
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class="text">{{dr.uploaded?.fileName}}</span>
                </div>
                <div class="nofile" *ngIf="!dr.uploaded">
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class=" text">{{"projectAttachment.nofile" | translate }}</span>
                </div>
                <button *ngIf="dr.uploaded" class="kbk-btn kbk-btn-sm kbk-btn-error" type="button"
                  data-bs-target="#showModal" (click)="openDeleteModal(deleteModel, 'md', dr.uploaded)">{{"projectAttachment.delete" | translate }}</button>
              </div>
            </li>

            <app-additional-upload-file *ngIf="existingApplication" [inputData]="existingApplication">
            </app-additional-upload-file>
            <!-- <li class="updload-list_item">
              <div class="updload-list_item-dt">
                <span class="text">Application Concept</span>
                <label class="kbk-btn kbk-btn-sm kbk-btn-sec">
                  <input type="file" style="display: none;"> Upload </label>
              </div>
              <div class="updload-list_item-dt">
                <div>
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class="text">Uploaded filename.pdf</span>
                </div>
                <div class="nofile">
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class=" text">{{"projectAttachment.nofile" | translate }}</span>
                </div>
                <button class="kbk-btn kbk-btn-sm kbk-btn-error">{{"projectAttachment.delete" | translate }}</button>
              </div>
            </li>
            <li class="updload-list_item">
              <div class="updload-list_item-dt">
                <span class="text">Accessiblity Concept</span>
                <label class="kbk-btn kbk-btn-sm kbk-btn-sec">
                  <input type="file" style="display: none;"> Upload </label>
              </div>
              <div class="updload-list_item-dt">
                <div>
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class="text">Uploaded filename.pdf</span>
                </div>
                <div class="nofile">
                  <span class="icon">
                    <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                  </span>
                  <span class=" text">{{"projectAttachment.nofile" | translate }}</span>
                </div>
                <button class="kbk-btn kbk-btn-sm kbk-btn-error">{{"projectAttachment.delete" | translate }}</button>
              </div>
            </li> -->
          </div>
        </ul>
      </div>
      <!-- Required Documents list     -->
      <div class="step-panel_footer">
        <button (click)="onPrev()">{{"projectAttachment.previous" | translate }}</button>
        <button type="button" *ngIf="submitted"> {{"projectAttachment.wait" | translate }} </button>
        <!-- // The below commented it is an option 1 used before -->
          <!-- // ================================================= -->
        <!-- <button type="submit" *ngIf="!isReadyToSubmit && !submitted"> Save the document </button> -->
         <!-- // The below commented it is an option 1 used before -->
          <!-- // ================================================= -->
        <button data-bs-toggle="modal" type="button" data-bs-target="#showModal" *ngIf="isReadyToSubmit && !submitted"
          (click)="openModal(isAcceptThatTheInformationProvided, 'md')"> {{"projectAttachment.submit" | translate }} </button>
      </div>
    </form>
  </div>
</div>
<!-- Submit application modal -->
<ng-template #isAcceptThatTheInformationProvided role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"projectAttachment.confirm" | translate }} </h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="step-panel_body">
    <div class="form-incheckbox">
      <label class="checkbox">
        <input type="checkbox" id="check" [checked]="isChecked" (click)="allowToSubmit()" />
        <span class="checkbox_box"></span>
        <span class="checkbox_txt">{{"projectAttachment.certificationText" | translate }}</span>
      </label>
    </div>
  </div>
  <div class="modol-content">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> {{"projectAttachment.cancel" | translate }} </button>
      <button class="kbk-btn kbk-btn-main" type="button" [disabled]="!isChecked" (click)="onNext()"> {{"projectAttachment.submit" | translate }} </button>
    </div>
  </div>
</ng-template>
<!-- Submit application modal -->
<!--  -->
<!-- Delete file modal -->
<ng-template #deleteModel let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel">{{"projectAttachment.deleteFileTitle" | translate }} {{deleteData.fileName}} ?</h2>
      <p>{{"projectAttachment.deleteFileBodyA" | translate }} {{deleteData.name}} {{"projectAttachment.deleteFileBodyB" | translate }}</p>
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-md">
        <!-- <h4>{{"projectAttachment.deleteFileTitle" | translate }} {{deleteData.fileName}} ?</h4> -->
      </div>
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close">{{"projectAttachment.close" | translate }}</button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="deleteData('')"
          (click)="deleteFile()">{{"projectAttachment.confirmDelete" | translate }}</button>
      </div>
    </div>
  </div>
</ng-template>
<!-- Delete file modal -->
