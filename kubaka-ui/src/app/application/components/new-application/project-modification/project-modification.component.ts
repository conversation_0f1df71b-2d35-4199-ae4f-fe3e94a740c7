import { Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-project-modification',
  templateUrl: './project-modification.component.html',
  styleUrls: ['./project-modification.component.scss']
})
export class ProjectModificationComponent {
  userForm!: UntypedFormGroup;
  currentUser: any;
  submitted: boolean = false;
  agencies: any[] = [];
  paramsId: any = {};
  permitTypes: any[] = [];
  categoryTypes: any[] = [];
  buildingTypes: any[] = [];
  outputData: any = {};
  projectDetails: any = {};
  isSelectDisabled = true;
  isAccessQuestion: boolean = false;
  otherInfoData: any = {};
  technologySurveys: any[] = [];

  constructor(
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private router: Router,
    private sessionService: SessionService,
    private applicationService: ApplicationService,
    private modalService: NgbModal,
    private route: ActivatedRoute,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    route.params.subscribe((params: any) => {
      this.paramsId = params.projectId;
    });

    this.callLookups();
  }




  ngOnInit(): void {
    this.userForm = this.formBuilder.group({
      projectName: [""],
      projectDescription: [""],
      buildUpArea: ["", [Validators.required]],
      numberOfFloor: ["", [Validators.required]],
      grossFloorArea: ["", [Validators.required]],
      numberOfParkingSpace: ["", [Validators.required]],
      priceOfDwellingUnitRwf: ["", [Validators.required]],
      DescriptionOfOperation: ["", [Validators.required]],
      percentageSpaceUse: ["", [Validators.required]],
      numberOfDwellingUnits: ["", [Validators.required]],
      waterConsumption: ["", [Validators.required]],
      electricityConsumption: ["", [Validators.required]],
      DistanceToTheNearestLandIn: ["", [Validators.required]],
      ProjectCostInUSD: ["", [Validators.required]],
      ProjectCostInRwf: ["", [Validators.required]],
      capacityInformation: ["", [Validators.required]],
      certificateNumberEIA: [""],
      plotSize: [0],
      permitTypeId: ["", [Validators.required]],
      categoryTypeId: ["", [Validators.required]],
      buildTypeId: ["", [Validators.required]],
      permitTypeCode: [""],
      applicationStatusId: [""], // hard coded
      userId: [this.currentUser.userId],
      categoryCode: [''],
      // hardcoded
      technologySurveyId: [],
      // From existing project
      agencyId: [""],
      projectId: ["", [Validators.required]],
      agencyCode: ["", [Validators.required]],
    });

    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/' + this.paramsId)
      .subscribe(
        data => {
          this.projectDetails = data;

          // this.checkIfUPIIsExistInOldSystem(this.projectDetails.upi);
          this.outputData.project = this.projectDetails;
          this.userForm.controls['projectId'].setValue(this.projectDetails.id);
          this.userForm.controls['agencyId'].setValue(this.projectDetails.agencyId);


          // Get data from applicaiton by project ID and permit new construction code and add to project details
          this.getPermitTypeObjectByCode();
          // Get data from applicaiton by project ID and permit new construction code and add to project details



          // call permit type id by code
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=NCP')
            .subscribe(
              data => {
                this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.userForm.value.projectId + '/permit-type/' +
                  data.items[0].id
                ).subscribe(
                  dataItem => {

                    this.userForm.controls['categoryTypeId'].setValue(dataItem[0]?.categoryTypes.id);
                    this.userForm.controls['buildTypeId'].setValue(dataItem[0]?.buildTypes.id);

                    // Instead of using the existing one get modification permit
                    // this.userForm.controls['permitTypeId'].setValue(dataItem[0]?.permitTypes.id);
                    this.userForm.controls['permitTypeId'].setValue(this.permitTypes.find((x: any) => x.code === 'PMP').id);
                    this.userForm.controls['plotSize'].setValue(dataItem[0]?.projects.plotSize);
                    this.userForm.controls['projectDescription'].setValue(dataItem[0]?.projects.projectDescription);
                    this.userForm.controls['projectName'].setValue(dataItem[0]?.projects.projectName);
                    this.userForm.controls['buildUpArea'].setValue(dataItem[0]?.buildUpArea);
                    this.userForm.controls['numberOfFloor'].setValue(dataItem[0]?.numberOfFloor);
                    this.userForm.controls['grossFloorArea'].setValue(dataItem[0]?.grossFloorArea);
                    this.userForm.controls['numberOfParkingSpace'].setValue(dataItem[0]?.numberOfParkingSpace);
                    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(dataItem[0]?.priceOfDwellingUnitRwf);
                    this.userForm.controls['capacityInformation'].setValue(dataItem[0]?.capacityInformation);
                    this.userForm.controls['numberOfDwellingUnits'].setValue(dataItem[0]?.numberOfDwellingUnits);
                    this.userForm.controls['DescriptionOfOperation'].setValue(dataItem[0]?.DescriptionOfOperation);
                    this.userForm.controls['percentageSpaceUse'].setValue(dataItem[0]?.percentageSpaceUse);
                    this.userForm.controls['waterConsumption'].setValue(dataItem[0]?.waterConsumption);
                    this.userForm.controls['electricityConsumption'].setValue(dataItem[0]?.electricityConsumption);
                    this.userForm.controls['DistanceToTheNearestLandIn'].setValue(dataItem[0]?.DistanceToTheNearestLandIn);
                    this.userForm.controls['ProjectCostInUSD'].setValue(dataItem[0]?.ProjectCostInUSD);
                    this.userForm.controls['ProjectCostInRwf'].setValue(dataItem[0]?.ProjectCostInRwf);
                    this.userForm.controls['certificateNumberEIA'].setValue(dataItem[0]?.certificateNumberEIA);
                    this.userForm.controls['agencyCode'].setValue(dataItem[0]?.agencyCode);
                    this.userForm.controls['categoryTypeId'].setValue(dataItem[0]?.categoryTypes.id);
                    this.userForm.controls['buildTypeId'].setValue(dataItem[0]?.buildTypes.id);
                    this.userForm.controls['permitTypeCode'].setValue(this.permitTypes.find((x: any) => x.code === 'PMP').code);


                    // This is used to check if permit certificate is expired
                    this.checkIfCertificateIsExpired(dataItem[0]?.certificates?.expiredDate);
                    // This is used to check if permit certificate is expired
                    // jfa
                  }
                )
              }

            )
        }
      )
  }


  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / (this.paramsId === '0' ? this.projectDetails.size : this.userForm.value.plotSize)) * 100).toFixed(2));

    if ((this.userForm.value.numberOfFloor && this.userForm.value.buildUpArea) &&
      (+this.userForm.value.numberOfFloor === 1 && +this.userForm.value.numberOfFloor < 1) &&
      (+this.userForm.value.buildUpArea >= 200 && +this.userForm.value.buildUpArea <= 1500)
    ) {
      this.findCategoryByCode('CAT3');
      this.userForm.controls['categoryCode'].setValue('CAT3');
    } else if (
      (this.userForm.value.numberOfFloor !== undefined && this.userForm.value.buildUpArea !== undefined) &&
      (+this.userForm.value.numberOfFloor === 0) && (+this.userForm.value.buildUpArea <= 200)
    ) {
      this.findCategoryByCode('CAT2');
      this.userForm.controls['categoryCode'].setValue('CAT2');
    } else {
      this.getBuildTypeObject('');
    }

  }


  calculateNumberOfFlow() {


    if (this.userForm.value.numberOfFloor && this.userForm.value.buildUpArea &&
      +this.userForm.value.numberOfFloor === 1 &&
      (+this.userForm.value.buildUpArea >= 200 && +this.userForm.value.buildUpArea <= 1500)
    ) {
      this.findCategoryByCode('CAT3');
      this.userForm.controls['categoryCode'].setValue('CAT3');
    } else if (

      // (this.userForm.value.numberOfFloor && this.userForm.value.buildUpArea) &&
      // (+this.userForm.value.numberOfFloor === 0) && (+this.userForm.value.buildUpArea <= 200)
      (this.userForm.value.numberOfFloor !== undefined && this.userForm.value.buildUpArea !== undefined) &&
      (+this.userForm.value.numberOfFloor === 0) && (+this.userForm.value.buildUpArea <= 200)

    ) {
      this.findCategoryByCode('CAT2');
      this.userForm.controls['categoryCode'].setValue('CAT2');
    } else {
      this.getBuildTypeObject('');
    }
  }


  calculateNumberOfPeople() {
    let buildType = this.buildingTypes.find((x: any) => x.id === this.userForm.value.buildTypeId);
    if (+this.userForm.value.capacityInformation > 500 && (buildType.code === 'CMRC'
      || buildType.code === 'MRCTN' || buildType.code === 'RES' || buildType.code === 'MIXD'
    )) {
      this.findCategoryByCode('CAT5');
      this.userForm.controls['categoryCode'].setValue('CAT5');
    } else {
      this.calculateToGetCategory();

    }
  }


  getBuildTypeObject(event: any) {
    let buildType = this.buildingTypes.find((x: any) => x.id == this.userForm.value.buildTypeId);
    this.outputData.buildType = buildType;
    if (this.outputData.document && this.outputData.document.length > 0 && this.outputData.buildType) {
      this.outputData.isDocumenting = true;
    }
    // 'FAI' || 'EDU' || 'ASMLY' || 'INSTNAL'
    if (buildType.code === 'FAI' ||
      buildType.code === 'EDU' ||
      buildType.code === 'ASMBLY'
      || buildType.code === 'MEM'
      || buildType.code === 'STRG'
      || buildType.code === 'MISLNS'
      || buildType.code === 'INSTNAL') {
      this.findCategoryByCode('CAT5');
    } else {
      this.calculateToGetCategory()
    }
  }

  calculateToGetCategory() {
    let buildTypeCode = this.buildingTypes.find((buildingType: any) => buildingType.id == this.userForm.value.buildTypeId);

    if (+this.projectDetails.capacityInformation <= 500 &&
      +this.projectDetails.numberOfFloor === 2) {
      // If category is 4
      this.findCategoryByCode('CAT4');
      this.userForm.controls['categoryCode'].setValue('CAT4');

    } else if (
      +this.projectDetails.numberOfFloor === 0 || this.projectDetails.numberOfFloor === 1 &&
      (+this.projectDetails.buildUpArea >= 200 && +this.projectDetails.buildUpArea <= 1500
      )) {
      // If category is 3
      this.findCategoryByCode('CAT3');
      this.userForm.controls['categoryCode'].setValue('CAT3');
    } else if (
      this.projectDetails.numberOfFloor === 0 && this.projectDetails.buildUpArea <= 200) {
      // If category is 2
      this.findCategoryByCode('CAT2');
      this.userForm.controls['categoryCode'].setValue('CAT2');
    } else {
      // this.findCategoryByCode('CAT1');

      this.findCategoryByCode('CAT2');
      this.userForm.controls['categoryCode'].setValue('CAT2');
    }

  }


  findCategoryByCode(code: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/categoryType/code/search?search=' + code)
      .subscribe(data => {
        this.userForm.controls['categoryTypeId'].setValue(data.items[0].id);
      })
  }



  getPermitTypeObjectByCode() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe(
        data => {
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.userForm.value.projectId + '/permit-type/' + data.items[0].id)
            .subscribe(
              applicationData => {
                this.projectDetails.waterConsumption = applicationData[0]?.waterConsumption;
                this.projectDetails.DistanceToTheNearestLandIn = applicationData[0]?.DistanceToTheNearestLandIn;
                this.projectDetails.ProjectCostInUSD = applicationData[0]?.ProjectCostInUSD;
                this.projectDetails.ProjectCostInRwf = applicationData[0]?.ProjectCostInRwf;
                this.projectDetails.electricityConsumption = applicationData[0]?.electricityConsumption;
                this.projectDetails.grossFloorArea = applicationData[0]?.grossFloorArea;
                this.projectDetails.numberOfFloor = applicationData[0]?.numberOfFloor;
                this.projectDetails.numberOfParkingSpace = applicationData[0]?.numberOfParkingSpace;
                this.projectDetails.priceOfDwellingUnitRwf = applicationData[0]?.priceOfDwellingUnitRwf;
                this.projectDetails.buildUpArea = applicationData[0]?.buildUpArea;
                this.projectDetails.grossFloorArea = applicationData[0]?.grossFloorArea;
                this.projectDetails.capacityInformation = applicationData[0]?.capacityInformation;
              }
            )
        }
      )
  }

  checkIfCertificateIsExpired(expiredDate: any) {
    if (this.isExpired(expiredDate)) {
      const code = 'ROP';
      const index = this.permitTypes.findIndex((valueData: any) => valueData.code === code);
      if (index !== -1) {
        this.permitTypes.splice(index, 1);
      }
    }
  }

  isExpired(expiredDate: string): boolean {
    const currentDate = new Date();
    const expirationDate = new Date(expiredDate);
    return expirationDate < currentDate;
  }





  getPermitTypeObject(event: any) {
    let codeValue = this.permitTypes.find((x: any) => x.id == this.userForm.value.permitTypeId);

    this.loadRequiredDocumentByPermitType(codeValue);
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + codeValue.code)
      .subscribe(
        data => {
          this.isAccessQuestion = true;
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + data.items[0].id)
            .subscribe(
              data => {
                if (data.length > 0) {
                  // If data present means that application already existis with the permit type category exists
                  this.utilService.showNotification(NOTIFICATION_COLOR.warning, "An application already exists. Please consider applying for a different one, or ensure that all required documents are uploaded. You can also upload any missing documents.", "bottom", "center");
                  this.outputData.applicationSaved = data[0];
                  this.projectDetails.applyOtherApplication = false;

                  // this.modalRef = this.modalService.open(this.otherApplicationModel, { size: 'md' });
                  // sdf
                } else {
                  this.projectDetails.applyOtherApplication = true;
                  this.outputData.isDocumenting = false;
                }
              })
        })
  }


  loadRequiredDocumentByPermitType(event: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + event.id)
      .subscribe(
        data => {
          if (data.length > 0) {
            this.outputData.document = data;
          } else {
            this.userForm.controls['permitTypeId'].setValue('');
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Please contact the admin to provide the documents needed for the permit you've chosen.", "bottom", "center");
          }
        }
      )
  }

  callLookups() {

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.TECHNOLOGYSURVEY)
      .subscribe(
        data => { this.technologySurveys = data; }
      )


    this.applicationService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => {
          this.agencies = data;
        }
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => {
          this.permitTypes = data;
        },
      )


    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.BUILDING_TYPE)
      .subscribe(
        data => { this.buildingTypes = data; },
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => {
          this.categoryTypes = data;
        }
      )
  }



  updateOtherInfo(event: any) {
    this.otherInfoData = event;
  }

  onSubmit() {
    let permitTypeCode = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId).code;
    this.userForm.controls['permitTypeCode'].setValue(permitTypeCode);
    this.userForm.controls['agencyCode'].setValue(this.agencies.find((x: any) => x.id = this.userForm.value.agencyId).code);
    if (this.userForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Fill all required', "bottom", "center")
      return;
    } else {
      this.submitted = true;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + 'PND')
        .subscribe(
          data => {
            this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
            this.confirmSubmit();
          }, error => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
          });
    }
  }


  confirmSubmit() {
    let dataToSave = this.userForm.value;
    this.otherInfoData.permitTypeId = this.userForm.value.permitTypeId;
    this.otherInfoData.userId = this.currentUser.userId;
    dataToSave.other = this.otherInfoData;
    dataToSave.submittedByUserId = this.currentUser.userId;
    dataToSave.userId = this.projectDetails.userId;
    this.applicationService.saveWithPath(dataToSave, environment.applicationUrl + 'application/application')
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application saved successfully", "bottom", "center");
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + this.userForm.value.permitTypeId)
            .subscribe(
              data => {
                if (data.length > 0) {
                  // If data present means that application already existis with the permit type category exists
                  this.outputData.applicationSaved = data[0];
                  this.projectDetails.applyOtherApplication = false;
                  this.outputData.isDocumenting = true;
                } else {
                  this.projectDetails.applyOtherApplication = true;
                }
              })
          this.submitted = false;
        }, error => {
          this.submitted = false;
        }
      )
  }


  cancel() {
    this.router.navigate(['/account/application/applications']);
  }
}
