// import { AbstractEntity } from '@app/common';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  // OneToMany,
  UpdateDateColumn,
} from 'typeorm';
import { Role } from './role.entity';
import { CivilStatus } from './civilStatus.entity';
import { UserType } from './userType.entity';
import { Agency } from '../agency/agency.entity';
import { Sector } from '../delimitation/sector.entity';
// import { Exclude } from 'class-transformer';

export enum Gender {
  M = 'Male',
  F = 'Female',
}

@Entity()
export class User extends BaseEntity {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ type: 'varchar' })
  firstName: string;

  @Column({ type: 'varchar' })
  lastName: string;

  @Column({ type: 'varchar', unique: true })
  // @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ default: false })
  isEmailValid: boolean;

  // @Exclude()
  @Column()
  // @Column({ select: false })
  password: string;

  @Column()
  phoneNumber: string;

  @Column({ type: 'varchar', nullable: true })
  nationalId: string;

  @Column({ type: 'varchar', nullable: true })
  passport: string;

  @Column({ nullable: true })
  dateOfBirth: Date;

  @Column({ nullable: true })
  verifiedAt: Date;

  @Column({ default: false })
  verified: boolean;

  @Column({ type: 'varchar', nullable: true })
  licenceIdEng: string;

  @Column({ type: 'varchar', nullable: true })
  licenceIdArch: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: true })
  isActive: boolean;
  @Column({ nullable: true })
  isActing: boolean;

  @Column({ default: false })
  isStaffPasswordChanged: boolean;

  @Column({ default: false })
  isPasswordExpired: boolean;

  @Column({ type: 'enum', enum: ['Male', 'Female'] })
  gender: string;

  // @ManyToOne(() => Role, { cascade: true })
  // @JoinTable()
  // role: Role;

  // @OneToMany(() => Role, (role) => role.user)
  // roles: Role;

  @ManyToOne(() => Role, (role) => role.users)
  role: Role;

  @ManyToOne(() => Agency, (agency) => agency.users)
  agency: Agency;

  @ManyToOne(() => Sector, (sector) => sector.users)
  sector: Sector;

  @ManyToOne(() => UserType, (userType) => userType.users)
  userType: UserType;

  @ManyToOne(() => CivilStatus, (civilStatus) => civilStatus.users)
  civilStatus: CivilStatus;

  @Column({ type: 'varchar', nullable: true })
  approvalLevelId: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public deleted_at!: Date;

  // locking a user
  @Column({ default: 0 })
  failedLoginAttempts: number;

  @Column({ type: 'timestamp', nullable: true })
  lockUntil: Date | null;

  // token fot 2FA
  @Column({ type: 'varchar', nullable: true })
  otp2FA: string;

  @Column({ type: 'boolean', default: false })
  isOtpVerified: boolean;

  @Column({ type: 'timestamp', nullable: true })
  otpExpiresAt: Date | null;
}
