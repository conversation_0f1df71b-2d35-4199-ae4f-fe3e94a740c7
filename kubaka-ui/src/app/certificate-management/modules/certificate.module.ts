import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { CertificateDetailComponent } from '../components/certificate-detail/certificate-detail.component';
import { CertificatesComponent } from '../components/certificates/certificates.component';
import { CertificateRoutingModule } from './certificate-routing.module';
import { QrCodeModule } from 'ng-qrcode';
import { TransferedCertificatesComponent } from '../components/transfered-certificates/transfered-certificates.component';



@NgModule({
  declarations: [
    CertificatesComponent,
    CertificateDetailComponent,
    TransferedCertificatesComponent

  ],
  imports: [
    CommonModule,
    SharedModule,
    CertificateRoutingModule,
    QrCodeModule
  ]
})
export class CertificateModule { }
