import {
  <PERSON>,
  UsePipes,
  ValidationPipe,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { EventPattern, Payload } from '@nestjs/microservices';
import { EmailDto, NotifyEmailDto } from './dto/notify-email.dto';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateReadStatusDto } from './dto/update-read-status.dto';
import { ApiTags, ApiBody } from '@nestjs/swagger';
import {
  ChatBoardDto,
  ChatBoardReplyDto,
  IssueCategoryDto,
} from './dto/chat-board.dto';

@Controller()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @UsePipes(new ValidationPipe())
  @EventPattern('notify_email')
  async notifyEmail(@Payload() data: NotifyEmailDto) {
    this.notificationsService.notifyEmail(data);
  }

  @UsePipes(new ValidationPipe())
  @EventPattern('notify_email2')
  async notifyEmail2(@Payload() data: EmailDto) {
    this.notificationsService.notifyEmail2(data);
  }

  // Send a message
  @ApiTags('chatMessage')
  @Post('sendMessage')
  async sendMessage(@Body() createMessageDto: CreateMessageDto) {
    return this.notificationsService.sendMessage(createMessageDto);
  }

  // get all message
  @ApiTags('chatMessage')
  @Get('getAllConversation')
  async getAllConversation() {
    return this.notificationsService.getAllConversation();
  }

  // Get conversation between two users
  @ApiTags('chatMessage')
  @Get('getConversation/:userId1/:userId2')
  async getConversation(
    @Param('userId1') userId1: string,
    @Param('userId2') userId2: string,
  ) {
    return this.notificationsService.getConversation(userId1, userId2);
  }

  // count unread messages
  @ApiTags('chatMessage')
  @Get('getUnreadMessage/:userId')
  async getConversationUnread(@Param('userId') userId: string) {
    return this.notificationsService.getConversationUnread(userId);
  }
  // count unread messages
  @ApiTags('chatMessage')
  @Get('getUnreadMessage/receiver/:userId')
  async getConversationUnreadReceiver(@Param('userId') userId: string) {
    return this.notificationsService.getConversationUnreadReceiver(userId);
  }

  // Update message read status
  @ApiTags('chatMessage')
  @Patch('read-status')
  async updateReadStatus(@Body() updateReadStatusDto: UpdateReadStatusDto) {
    return this.notificationsService.updateReadStatus(updateReadStatusDto);
  }

  // Issue Category
  @ApiTags('chatBoard')
  @Post('issueCategory')
  async CreateIssueCategory(@Body() issueCategoryDto: IssueCategoryDto) {
    return this.notificationsService.createIssueCategory(issueCategoryDto);
  }

  @ApiTags('chatBoard')
  @Get('issueCategory')
  async findAllIssueCategory() {
    return this.notificationsService.findAllIssueCategory();
  }

  @ApiTags('chatBoard')
  @Get('issueCategory/:id')
  async findOneIssueCategory(@Param('id') id: string) {
    return this.notificationsService.findOneIssueCategory(id);
  }

  @ApiTags('chatBoard')
  @Patch('issueCategory/:id')
  async updateIssueCategory(
    @Param('id') id: string,
    @Body() issueCategoryDto: IssueCategoryDto,
  ) {
    return this.notificationsService.updateIssueCategory(id, issueCategoryDto);
  }

  @ApiTags('chatBoard')
  @Delete('issueCategory/:id')
  async removeIssueCategory(@Param('id') id: string) {
    return this.notificationsService.removeIssueCategory(id);
  }

  // ChatBoard
  @ApiTags('chatBoard')
  @Post('chatBoard')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        phoneNumber: { type: 'string' },
        agencyId: { type: 'string' },
        issueCategoryId: { type: 'string' },
        content: { type: 'string' },
        senderNames: { type: 'string' },
        isReply: { type: 'string', default: '0' },
        documentIds: { type: 'array'}
      },
    },
  })
  async CreateChatBoard(@Body() chatBoardDto: ChatBoardDto) {
    return this.notificationsService.createChatBoardAndSendEmail(chatBoardDto);
    // return this.notificationsService.createChatBoard(chatBoardDto);
  }

  // // ChatBoard
  // @ApiTags('chatBoard')
  // @Post('chatBoard/reply')
  // async CreateChatBoardReply(@Body() chatBoardReplyDto: ChatBoardReplyDto) {
  //   return this.notificationsService.createChatBoardReply(chatBoardReplyDto);
  // }

  // ChatBoard with SMS
  @ApiTags('chatBoard')
  @Post('chatBoard/reply')
  async createChatBoardReplyWithSMS(
    @Body() chatBoardReplyDto: ChatBoardReplyDto,
  ) {
    return this.notificationsService.createChatBoardReplyWithSMS(
      chatBoardReplyDto,
    );
  }

  @ApiTags('chatBoard')
  @Get('chatBoard')
  async findAllChatBoard() {
    return this.notificationsService.findAllChatBoard();
  }

  @ApiTags('chatBoard')
  @Get('chatBoardDistinct')
  async findAllChatBoardDistinct() {
    return this.notificationsService.findAllChatBoardDistinct();
  }

  @ApiTags('chatBoard')
  @Get('chatBoard/:id')
  async findOneChatBoard(@Param('id') id: string) {
    return this.notificationsService.findOneChatBoard(id);
  }

  @ApiTags('chatBoard')
  @Patch('chatBoard/:id')
  async updateChatBoard(
    @Param('id') id: string,
    @Body() chatBoardDto: ChatBoardDto,
  ) {
    return this.notificationsService.updateChatBoard(id, chatBoardDto);
  }

  // Report on chatboard
  @ApiTags('chatBoard')
  @Get('chatBoard/agency/:agencyId')
  async findAllChatBoardByAgencyId(@Param('agencyId') agencyId: string) {
    return this.notificationsService.findAllChatBoardByAgency(agencyId);
  }

  @ApiTags('chatBoard')
  @Get('chatBoardDistinct/agency/:agencyId')
  async findAllChatBoardByAgencyIdDistinct(
    @Param('agencyId') agencyId: string,
  ) {
    return this.notificationsService.findAllChatBoardByAgencyDistinct(agencyId);
  }

  @ApiTags('chatBoard')
  @Get('chatBoard/phone/:phoneNumber')
  async findAllChatBoardByAPhone(@Param('phoneNumber') phoneNumber: string) {
    return this.notificationsService.findAllChatBoardByAPhone(phoneNumber);
  }

  @ApiTags('chatBoard')
  @Get('chatBoard/category/:issueCategoryId')
  async findAllChatBoardByIssueCategory(
    @Param('issueCategoryId') issueCategoryId: string,
  ) {
    return this.notificationsService.findAllChatBoardByIssueCategory(
      issueCategoryId,
    );
  }

  //get all user
  @ApiTags('chatMessage')
  @Get('getAllUserWithUnreadMessage/:agencyId')
  async getAllUsersWithUnreadNumber(@Param('agencyId') agencyId: string) {
    return this.notificationsService.getAllUserWithUnreadConversation(agencyId);
  }
  //get all user
  @ApiTags('chatMessage')
  @Get('getAllApplicantWithUnreadMessage')
  async getAllApplicantWithUnreadMessage() {
    return this.notificationsService.getAllApplicantWithUnreadMessage();
  }
}
