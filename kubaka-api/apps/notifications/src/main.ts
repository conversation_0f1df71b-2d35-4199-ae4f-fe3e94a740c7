import '../../../instrumentation';
import { NestFactory } from '@nestjs/core';
import { NotificationsModule } from './notifications.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(NotificationsModule);
  // app.setGlobalPrefix('api/v1');
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });
  // -- Cors setup
  app.enableCors();

  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Building Permit API')
    .setDescription('API Documents to manage building permits.')
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  await app.listen(configService.get('HTTP_PORT'));

  await app.startAllMicroservices();
  // await app.listen(3004);
}
bootstrap();
