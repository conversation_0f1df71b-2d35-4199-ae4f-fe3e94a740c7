import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AgencyDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field paymentAccountIdentifier' })
  @IsOptional({ message: 'The field paymentAccountIdentifier ' })
  paymentAccountIdentifier: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;
}

export class UpdateAgencyDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The name agency is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field paymentAccountIdentifier' })
  @IsOptional({ message: 'The field paymentAccountIdentifier ' })
  paymentAccountIdentifier: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}
export class AgencyDistrictDto {
  @ApiProperty({ description: 'The field agency is required' })
  @IsNotEmpty({ message: 'The field agency cannot be empty' })
  agencyId: string;

  @ApiProperty({ description: 'The field district id is required' })
  @IsNotEmpty({ message: 'The field district cannot be empty' })
  districtId: string;
}

export class CreateAgencyDistrictDto {
  @ApiProperty({ description: 'The field agency id is required' })
  @IsNotEmpty()
  agencyId: string;

  @ApiProperty({ description: 'The field district id is required' })
  @IsNotEmpty()
  districtId: string;
}
