import { Observable } from 'rxjs';


// export interface CrudOperations<T, ID> {
//   save(t: T, url: T): Observable<T>;
//   update(id: ID, t: T, url: T): Observable<T>;
//   findOne(id: ID, url: T): Observable<T>;
//   findAll(url: T): Observable<T[]>;
//   delete(id: ID, url: T): Observable<any>;
// }


export interface CrudOperations<T, ID> {
  save(t: T): Observable<T>;
  update(id: ID, t: T): Observable<T>;
  findOne(id: ID): Observable<T>;
  findAll(): Observable<T[]>;
  delete(id: ID): Observable<any>;
}