import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-searched-upi',
  templateUrl: './searched-upi.component.html',
  styleUrls: ['./searched-upi.component.scss']
})
export class SearchedUpiComponent {

  @Input() inputData: any = {}


  constructor(
    private router: Router,
    private modalService: NgbModal
  ) { }



  ngOnInit() {
    console.log(this.inputData);
  }

  goToApplication(event: any) {
    this.modalService.dismissAll();
    this.router.navigate(['/account/all-applications/application-detail/', event.id]);
  }
}
