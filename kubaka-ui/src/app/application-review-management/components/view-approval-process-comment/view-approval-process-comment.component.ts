import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ApplicationService } from 'src/app/application/services/application.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-view-approval-process-comment',
  templateUrl: './view-approval-process-comment.component.html',
  styleUrls: ['./view-approval-process-comment.component.scss']
})
export class ViewApprovalProcessCommentComponent {
  @Input() inputData: any = {};
  applicationDetails: any = {};
  paramId: any;

  constructor(
    private applicationService: ApplicationService,
    private route: ActivatedRoute
  ) {



  }



  ngOnInit() {
    

    // if (this.inputData) {
    //   this.inputData.isBackAllowed = false;
    //   this.loadInfo(this.inputData.applicationId);
    // } else {
    this.route.params.subscribe((params: any) => {
      if (this.inputData && this.inputData.isBackNotAllowed) {
        this.loadInfo(this.inputData.applicationId);
      } else {
        this.paramId = params.id;
        this.inputData.isBackNotAllowed = false;
        this.loadInfo(params.id);
      }
      // if (params.id) {
      //   this.paramId = params.id;
      //   this.inputData.isBackAllowed = true;
      //   this.loadInfo(params.id);
      // }
    })
    // }

  }

  loadInfo(id: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/applicationApproval/applicationId/' + id)
      .subscribe(
        data => {
          this.applicationDetails = data;
        }
      )
  }



}
