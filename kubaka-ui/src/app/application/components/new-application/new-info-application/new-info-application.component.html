<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"newApplication.step1.step" | translate }} <span>1</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div *ngIf="!applicationDetail.isProjectAlreadyExists">
            <!-- {{"newApplication.step1..step" | translate }} content goes here -->
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"newApplication.step1.plotInformation" | translate }}</h2>
                    <div class="kbk-x kbk-aend mt-md">
                        <div class="track-info">
                            <div class="track-profile">
                                <img src="assets/imgs/profile1.svg" alt="" />
                            </div>
                            <div class="track-dtail">
                                <label>UPI:</label>
                                <!-- <span class="track-user">{{applicationDetail.names}}</span> -->
                                <span class="track-usersub">{{applicationDetail?.upi}}</span>
                            </div>
                        </div>
                        <form (ngSubmit)="onNext()" [formGroup]="userForm" autocomplete="off">
                            <div class="form-input">
                                <label>{{"newApplication.step1.plotNumberUpi" | translate }}</label>
                                <div class="inputgrp">
                                    <input type="text" id="upi" name="upi" formControlName="upi" readonly
                                        placeholder="(0/00/00/00/0000)">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="step-panel_body">
                    <div class="result">
                        <div class="result-item">
                            <h3 class="mgnb">{{"newApplication.step1.upiDetails" | translate }}</h3>
                            <div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.representative" | translate }}</label>
                                    <!-- <span>{{applicationDetail?.representative?.foreNames}}
                                        {{applicationDetail?.representative?.surname}}</span> -->
                                    <span>{{applicationDetail?.owners[0]?.fullName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.province" | translate }}</label>
                                    <span>{{applicationDetail?.representative?.address?.province?.provinceName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.district" | translate }}</label>
                                    <span>{{applicationDetail?.representative?.address?.district?.districtName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.sector" | translate }}</label>
                                    <span>{{applicationDetail?.representative?.address?.sector?.sectorName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.cell" | translate }}</label>
                                    <span>{{applicationDetail?.representative?.address?.cell?.cellName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.village" | translate }}</label>
                                    <span>{{applicationDetail?.representative?.address?.village?.villageName}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="result-item">
                            <h3 class="mgnb">{{"newApplication.step1.parcelDetails" | translate }}</h3>
                            <div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.applyFor" | translate }}</label>
                                    <div class="kbk-x-s sp-xs">
                                        <!-- <span *ngFor="let a of applicationDetail?.zonings">{{a.use}}</span> -->
                                        <span>{{applicationDetail?.applicationName }}</span>
                                    </div>
                                    <!-- <span>Transport Zone</span> -->
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.province" | translate }}</label>
                                    <span>{{applicationDetail?.parcelLocation?.province?.provinceName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.district" | translate }}</label>
                                    <span>{{applicationDetail?.parcelLocation?.district?.districtName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.sector" | translate }}</label>
                                    <span>{{applicationDetail?.parcelLocation?.sector?.sectorName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.cell" | translate }}</label>
                                    <span>{{applicationDetail?.parcelLocation?.cell?.cellName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.village" | translate }}</label>
                                    <span>{{applicationDetail?.parcelLocation?.village?.villageName}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="result-map">
                            <h3 class="mgnb">{{"newApplication.step1.parcelLocation" | translate }}</h3>
                            <div>
                                <label>{{"newApplication.step1.coordinates" | translate }}</label>
                                <div class="map-wrapper">
                                    <google-map [options]="mapOptions">
                                        <map-marker [position]="marker.position"></map-marker>
                                    </google-map>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="step-panel_footer">
                    <!-- <button (click)="onPrev()" [disabled]="isPrevButtonDisabled()">Previous</button> -->
                    <button type="button" (click)="onPrev()">{{"newApplication.step1.cancel" | translate }}</button>
                    <button type="button" (click)="onNext()">{{"newApplication.step1.next" | translate }}</button>
                </div>
            </div>
        </div>
        <div *ngIf="applicationDetail.isProjectAlreadyExists">
            <!-- {{"newApplication.step1..step" | translate }} content goes here -->
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"newApplication.step1.plotInformation" | translate }}</h2>
                    <div class="kbk-x kbk-aend mt-md">
                        <div class="track-info">
                            <div class="track-profile">
                                <img src="assets/imgs/profile1.svg" alt="" />
                            </div>
                            <div class="track-dtail">
                                <label>UPI:</label>
                                <!-- <span class="track-user">{{applicationDetail.names}}</span> -->
                                <span class="track-usersub">{{applicationDetail?.upi}}</span>
                            </div>
                        </div>
                        <form (ngSubmit)="onNext()" [formGroup]="userForm" autocomplete="off">
                            <div class="form-input">
                                <label>{{"newApplication.step1.plotNumberUpi" | translate }}</label>
                                <div class="inputgrp">
                                    <input type="text" id="upi" name="upi" formControlName="upi" readonly
                                        placeholder="(0/00/00/00/0000)">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="step-panel_body">
                    <div class="result">
                        <div class="result-item">
                            <h3 class="mgnb">{{"newApplication.step1.upiDetails" | translate }}</h3>
                            <div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.representative" | translate }}</label>
                                    <span>{{applicationDetail?.foreNames}} {{applicationDetail?.surname}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.province" | translate }}</label>
                                    <span>{{applicationDetail?.provinceName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.district" | translate }}</label>
                                    <span>{{applicationDetail?.districtName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.sector" | translate }}</label>
                                    <span>{{applicationDetail?.sectorName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.cell" | translate }}</label>
                                    <span>{{applicationDetail?.cellName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.village" | translate }}</label>
                                    <span>{{applicationDetail?.villageName}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="result-item">
                            <h3 class="mgnb">{{"newApplication.step1.parcelDetails" | translate }}</h3>
                            <div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.applyFor" | translate }}</label>
                                    <div class="kbk-x-s sp-xs">
                                        <!-- <span *ngFor="let a of applicationDetail?.zonings">{{a.use}}</span> -->
                                        <span>{{applicationDetail?.applicationName }}</span>
                                    </div>
                                    <!-- <span>Transport Zone</span> -->
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.province" | translate }}</label>
                                    <span>{{applicationDetail?.provinceName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.district" | translate }}</label>
                                    <span>{{applicationDetail?.districtName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.sector" | translate }}</label>
                                    <span>{{applicationDetail?.sectorName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.cell" | translate }}</label>
                                    <span>{{applicationDetail?.cellName}}</span>
                                </div>
                                <div class="form-out">
                                    <label>{{"newApplication.step1.village" | translate }}</label>
                                    <span>{{applicationDetail?.villageName}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="result-map">
                            <h3 class="mgnb">{{"newApplication.step1.parcelLocation" | translate }}</h3>
                            <div>
                                <label>{{"newApplication.step1.coordinates" | translate }}</label>
                                <div class="map-wrapper">
                                    <google-map [options]="mapOptions">
                                        <map-marker [position]="marker.position"></map-marker>
                                    </google-map>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="step-panel_footer">
                    <!-- <button (click)="onPrev()" [disabled]="isPrevButtonDisabled()">Previous</button> -->
                    <button type="button" (click)="onPrev()">{{"newApplication.step1.cancel" | translate }}</button>
                    <button type="button" (click)="onNext()">{{"newApplication.step1.next" | translate }}</button>
                </div>
            </div>
        </div>
    </div>
</div>