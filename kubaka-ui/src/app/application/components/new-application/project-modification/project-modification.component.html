<div class="stepper-content">
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"projectModification.title" | translate }}</h2>
                </div>
                <form (ngSubmit)="onSubmit()" [formGroup]="userForm" autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectModification.projectName" | translate }}</label>
                                <div>
                                    <textarea id="projectName" name="projectName" formControlName="projectName"
                                        cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.projectDescription" | translate }}</label>
                                <div>
                                    <textarea id="projectDescription" name="projectDescription"
                                        formControlName="projectDescription" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectModification.plotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.permitType" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled" name="permitTypeId" id="permitTypeId" formControlName="permitTypeId"
                                        (change)="getPermitTypeObject($event)" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.buildingType" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled" name="buildTypeId"
                                        id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}} </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.projectCategory" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled" name="categoryTypeId"
                                        id="categoryTypeId" formControlName="categoryTypeId" required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}} </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.builtUpArea" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()" required>
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')">{{"projectModification.builtUpAreaError" | translate }}</div>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.numberOfFloor" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor" name="numberOfFloor"
                                        formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.grossFloorArea" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                        formControlName="grossFloorArea" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.numberOfParkingSpaces" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.estimatedPriceDwellingRwf" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="priceOfDwellingUnitRwf" name="priceOfDwellingUnitRwf"
                                        formControlName="priceOfDwellingUnitRwf" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.descriptionOfOperations" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.percentageSpaceUse" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.numberOfDwellingUnits" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.estimatedWaterConsumption" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.estimatedElectricityConsumption" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.distanceToLandline" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn" formControlName="DistanceToTheNearestLandIn"
                                        required>
                                </div>
                            </div>

                        </div>

                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectModification.estimatedProjectCostUsd" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.estimatedProjectCostRwf" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="ProjectCostInRwf" name="ProjectCostInRwf"
                                        formControlName="ProjectCostInRwf" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.capacityInformation" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation" (keyup)="calculateNumberOfPeople()"
                                        required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectModification.technologySurvey" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId" required>
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- This is the component that will differenciate additionally properties based on permit type selected -->
                        <app-other-application-permit-answer *ngIf="userForm.value.permitTypeId &&
                        isAccessQuestion" [inputData]="
                            {userForm: userForm.value, permitTypes: this.permitTypes}"
                            (backToParent)="updateOtherInfo($event)"></app-other-application-permit-answer>
                    </div>
                    <div class="step-panel_footer">
                        <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()">{{"projectModification.cancel" | translate }}</button>
                        <button class="kbk-btn kbk-btn-main" type="submit" >{{"projectModification.next" | translate }} </button>

                            <!-- *ngIf="projectDetails.applyOtherApplication" -->
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
