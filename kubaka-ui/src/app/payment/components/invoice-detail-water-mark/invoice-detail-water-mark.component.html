<div class="app-gen">
    <div class="app-gen-header">
        <div class="genline">
            <div>
                <span class="hder" aria-label="header tittle">My Invoice</span>
                <span class="hder-sm" aria-label="header tittle">Invoice #{{invoiceDetails?.invoiceNumber}}</span>
            </div>
            <div class="btns">
                <button type="button" class="kbk-btn kbk-btn-sec" [routerLink]="['/account/payment']">Back</button>
            </div>
        </div>
    </div>
    <div class="app-gen-content">
        <div class="invo-wrapper">
            <!-- registration membership -->
            <div class="invbody">
                <div class="invoice-paper">
                    <div class="invoice-contain" id="call-pdf-here">
                        <div class="page-header">
                            <!-- <div class="tittle">
                                <h2>Invoice</h2>
                            </div> -->
                            <div class="header">
                                <div class="logo">
                                    <!-- <img src="assets/ikons/logo/logomark.svg"> -->
                                    <img src="assets/ikons/logo/NavLogo.svg">
                                </div>
                                <div class="address">
                                    <p>Address</p>
                                    <p>Kigali, Kigali</p>
                                    <p>PO Box: 0000 Kigali</p>
                                    <p>Tel: (+250) 788 888 888</p>
                                    <p>Email: <EMAIL></p>
                                    <p>www.kubaka.gov.rw</p>
                                </div>
                            </div>
                        </div>
                        <div class="page-body">
                            <div class="bdy-hder">
                                <div class="clnt">
                                    <div class="lb">
                                        <label>To:</label>
                                        <p>{{invoiceDetails?.applications?.userDetails?.firstName}}
                                            {{invoiceDetails?.applications?.userDetails?.lastName}}</p>
                                    </div>
                                    <div class="lb">
                                        <label>Application Number:</label>
                                        <p>{{invoiceDetails?.applications?.applicationName}}</p>
                                    </div>
                                </div>
                                <div class="ntmnt">
                                    <div class="nt">
                                        <label>Invoice #</label>
                                        <div class="hlder">
                                            <p>{{invoiceDetails?.invoiceNumber}}</p>
                                        </div>
                                    </div>
                                    <div class="nt">
                                        <label>Reference #</label>
                                        <div class="hlder">
                                            <p>{{invoiceDetails?.externalReferenceNumber}}</p>
                                        </div>
                                    </div>
                                    <div class="nt">
                                      <label>CreatedOn</label>
                                      <div class="hlder">
                                          <p>{{invoiceDetails?.created_at | date}}</p>
                                      </div>
                                    </div>
                                    <div class="nt">
                                      <label>Expiration Date</label>
                                      <div class="hlder">
                                          <p>{{invoiceDetails?.dueDate | date}}</p>
                                      </div>
                                  </div>
                                  <div class="nt">
                                      <label>Status</label>
                                      <div class="hlder">
                                          <p>{{invoiceDetails?.invoiceStatus.name}}</p>
                                      </div>
                                  </div>
                                    <!-- <div class="nt">
                                        <label>Application Name</label>
                                        <div class="hlder">
                                            <p>{{invoiceDetails?.created_at }}</p>
                                        </div>
                                    </div> -->
                                    <!-- <div class="nt">
                                        <label>Application Number</label>
                                        <div class="hlder">
                                            <p>{{invoiceDetails?.applications.applicationName}}</p>
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                            <div class="bdy-bdy">
                                <table>
                                    <tr>
                                        <th>#</th>
                                        <th>Item description</th>
                                        <th>Item price (Rwf)</th>
                                    </tr>
                                    <tbody>
                                        <tr>
                                            <td rowspan="3">1</td>
                                            <td>Construction Permit</td>
                                            <td>
                                                <p>Rwf {{invoiceDetails?.amount}}</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="2">Total Invoice amount</td>
                                            <td>Rwf {{invoiceDetails?.amount}}</td>
                                        </tr>
                                        <!-- <tr>
                                            <td colspan="2">VAT 18%</td>
                                            <td>--</td>
                                        </tr>
                                        <tr>
                                            <td colspan="2">Invoice amount net of VAT</td>
                                            <td>--</td>
                                        </tr> -->
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                        <div class="page-footer">
                            <div class="mhs">
                                <!-- <P>Lorem ipsum dolor sit amet consectetur adipisicing elit. Tempore
                                    rerum deleniti ullam dolorem saepe numquam eaque ipsam aliquam quam
                                    voluptas!. </P> -->
                            </div>
                            <div class="signw">
                                <div class="lsd">
                                    <div class="ftx">
                                        <label>Prepared by:</label>
                                        <p>Kubaka</p>
                                    </div>
                                </div>
                                <div class="py">
                                    <div class="pybutn">
                                        <label>Invoice status: {{invoiceDetails?.invoiceStatus?.name}}</label>
                                        <!-- <div class="bntw">
                                            <button class="bntpy kb" type="button"
                                                (click)="makePaymentTS(invoiceDetails)">

                                                <img src="assets/ikons/logo/pw-irembo.svg">
                                            </button>
                                        </div> -->
                                    </div>
                                    <!-- <div class="pybutn">
                                        <label>Pay with UrubutoPay</label>
                                        <div class="bntw">
                                            <button class="bntpy kb" type="button">
                                                <img src="assets/ikons/logo/bk.png">
                                            </button>
                                        </div>
                                    </div>
                                    <div class="pybutn">
                                        <label>Pay with MTN MOMO</label>
                                        <div class="bntw">
                                            <button class="bntpy mn" type="button">
                                                <img src="assets/ikons/logo/mtnmomo.png">
                                            </button>
                                        </div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="app-dash" style="display: none;">
    <div class="container">
        <div class="app-main">
            <div class="app-welnote">
                <div class="app-welnote_dtails">
                    <span class="prim-nt">Welcome!</span>
                    <!-- <h3> <span>{{ currentUser.data.user.firstName }}</span> {{ currentUser.data.user.lastName }}</h3> -->
                </div>
            </div>
            <div class="app-lists">
                <div class="app-tblist">
                    <div class="app-tblist_title">
                        <div>
                            <!-- <span class="hder" aria-label="header tittle">My Invoices</span>
                            <p>Invoice #{{invoiceDetails.invoiceNumber}}</p> -->
                        </div>
                        <div class="kbk-x-e">
                            <button type="button" class="kbk-btn kbk-btn-sec">Back</button>
                        </div>
                    </div>
                    <div class="app-tblist_body">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
