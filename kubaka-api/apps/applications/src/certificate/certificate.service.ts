import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
// import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import {
  CertificateRepository,
  DevelopmentStatusRepository,
  RandomInspectionRepository,
  SignatoryRepository,
  SiteStatusRepository,
  TransferCertificateRepository,
} from './certificate.repository';
import {
  CertificateDto,
  DevelopmentStatusDto,
  RandomInspectionDto,
  SignatoryDto,
  TransferCertificateDto,
} from '../dto/certificate.dto';
import {
  Certificate,
  DevelopmentStatus,
  RandomInspection,
  Signatory,
  SiteStatus,
  TransferCertificate,
  Application,
} from '../entities/certificate.entity';
import { AUTH_SERVICE } from '@app/common/constants';
import { ClientProxy } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SiteStatusDto } from '../dto/approval.dto';
import { ApplicationService } from '../application/application.service';
import axios from 'axios';

import config from '../../config';
import { validate as isUUID } from 'uuid';
import { InvoiceService } from '../invoice/invoice.service';
import { Invoice } from '../entities/invoice.entity';

export interface ApprovalDetails {
  approverName: string;
  position: string;
}

@Injectable()
export class CertificateService {
  constructor(
    // Certificate
    private readonly certificateRepository: CertificateRepository,
    private readonly signatoryRepository: SignatoryRepository,
    private readonly transferCertificateRepository: TransferCertificateRepository,
    private readonly developmentStatusRepository: DevelopmentStatusRepository,

    @InjectRepository(Certificate)
    private certificateEntityManagerRepository: Repository<Certificate>,

    @InjectRepository(TransferCertificate)
    private transferCertificateEntityManagerRepository: Repository<TransferCertificate>,

    @InjectRepository(RandomInspection)
    private randomInspectionEntityManagerRepository: Repository<RandomInspection>,

    @InjectRepository(Application)
    private applicationEntityManagerRepository: Repository<Application>,

    @InjectRepository(Invoice)
    private invoiceEntityManagerRepository: Repository<Invoice>,

    private readonly randomInspectionRepository: RandomInspectionRepository,
    private readonly siteStatusRepository: SiteStatusRepository,

    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,

    private readonly applicationService: ApplicationService,

    @Inject(forwardRef(() => InvoiceService))
    private readonly invoiceService: InvoiceService,
  ) {}

  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }

  // // When certificate has no expiration date put zeros ad also invoice with o amount must be valid certificate automatic
  // async createCertificate(certificateDto: CertificateDto) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       certificateDto.applicationId,
  //     );

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   const invoice = await this.invoiceService.findOneInvoice(
  //     certificateDto.invoiceNumber,
  //   );

  //   if (!invoice) {
  //     throw new NotFoundException('Invoice not found');
  //   }

  //   // Generate certificate number
  //   const timestamp = Date.now().toString().slice(-5);
  //   const currentYear = new Date().getFullYear();
  //   const numericPart = Math.floor(Math.random() * 100000);
  //   const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
  //   certificateDto.certificateNumber = `${certificateDto.agencyCode}-${currentYear}-${numericString}`;

  //   // Handle expiredDate
  //   if (invoice.amount === '0') {
  //     certificateDto.statusId = '1';
  //   }

  //   // Handle expiredDate
  //   if (!certificateDto.expiredDate) {
  //     certificateDto.expiredDate = new Date('0000-01-01T00:00:00.000Z');
  //     certificateDto.statusId = '1';
  //   }

  //   // Create a new certificate
  //   const certificate = new Certificate({
  //     ...certificateDto,
  //     applications: { id: certificateDto.applicationId } as any,
  //     permitTypes: { id: certificateDto.permitTypeId } as any,
  //     upi: application.upi ?? null,
  //   });

  //   return this.certificateRepository.create(certificate);
  // }

  // When certificate has no expiration date put zeros ad also invoice with o amount must be valid certificate automatic but check if there's any in invoice table
  async createCertificate(certificateDto: CertificateDto) {
    const application =
      await this.applicationService.findOneApplicationWithAllDetails(
        certificateDto.applicationId,
      );

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Generate certificate number
    const timestamp = Date.now().toString().slice(-5);
    const currentYear = new Date().getFullYear();
    const numericPart = Math.floor(Math.random() * 100000);
    const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
    certificateDto.certificateNumber = `${certificateDto.agencyCode}-${currentYear}-${numericString}`;

    // check if there's any invoice with zero amount on this application
    const hasZeroAmountInvoice =
      await this.invoiceService.findInvoiceWithZeroAmount(
        certificateDto.applicationId,
      );

    if (hasZeroAmountInvoice) {
      certificateDto.statusId = '1';
    }

    // Handle expiredDate
    if (!certificateDto.expiredDate) {
      certificateDto.expiredDate = new Date('0000-01-01T00:00:00.000Z');
      certificateDto.statusId = '1';
    }

    // Handle expiredDate for Foundation and occupancy permit
    const permit = await this.applicationService.findOnePermitType(
      certificateDto.permitTypeId,
    );

    if (!permit) {
      throw new NotFoundException('Permit not found');
    }

    // Handle expiredDate
    if (
      !certificateDto.expiredDate ||
      permit.code === 'FINS' ||
      permit.code === 'OCP'
    ) {
      certificateDto.expiredDate = new Date('0000-01-01T00:00:00.000Z');
      certificateDto.statusId = '1';
    }

    // Create a new certificate
    const certificate = new Certificate({
      ...certificateDto,
      applications: { id: certificateDto.applicationId } as any,
      permitTypes: { id: certificateDto.permitTypeId } as any,
      upi: application.upi ?? null,
    });

    await this.certificateRepository.create(certificate);
  }
  // // When certificate has no expiration date put zeros
  // async createCertificate(certificateDto: CertificateDto) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       certificateDto.applicationId,
  //     );

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   // Generate certificate number
  //   const timestamp = Date.now().toString().slice(-5);
  //   const currentYear = new Date().getFullYear();
  //   const numericPart = Math.floor(Math.random() * 100000);
  //   const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
  //   certificateDto.certificateNumber = `${certificateDto.agencyCode}-${currentYear}-${numericString}`;

  //   // Handle expiredDate
  //   if (!certificateDto.expiredDate) {
  //     certificateDto.expiredDate = new Date('0000-01-01T00:00:00.000Z');
  //     certificateDto.statusId = '1';
  //   }

  //   // Create a new certificate
  //   const certificate = new Certificate({
  //     ...certificateDto,
  //     applications: { id: certificateDto.applicationId } as any,
  //     permitTypes: { id: certificateDto.permitTypeId } as any,
  //     upi: application.upi ?? null,
  //   });

  //   return this.certificateRepository.create(certificate);
  // }

  async findAllCertificates() {
    return this.certificateRepository.findAll({
      relations: {
        applications: true,
        permitTypes: true,
        randomInspections: true,
        developmentStatus: true,
      },
    });
  }

  async findOneCertificate(id: string) {
    return this.certificateRepository.findOne({ id });
  }

  async getApprovalDetails(
    agencycode: string,
    permitTypeCode: string,
    userId: string,
    agencyUsers: any[],
  ): Promise<ApprovalDetails> {
    if (!Array.isArray(agencyUsers) || agencyUsers.length === 0) {
      throw new HttpException('No agency users found', HttpStatus.NOT_FOUND);
    }

    let permenetapprover = null;
    let actingApprover = null;
    let position = '';
    let approverName = '';

    if (permitTypeCode !== 'FINS') {
      // For non-FINS permit types, look for Director (DRCT)
      permenetapprover = agencyUsers.find(
        (user) =>
          user?.role?.code === 'DRCT' &&
          user?.isActing === false &&
          user?.isActive === true,
      );

      actingApprover = agencyUsers.find(
        (user) =>
          user?.role?.code === 'DRCT' &&
          user?.isActing === true &&
          user?.isActive === true,
      );

      if (!permenetapprover && !actingApprover) {
        throw new HttpException(
          'No director found for this agency',
          HttpStatus.NOT_FOUND,
        );
      }

      // Use permanent approver if available, otherwise use acting approver
      const approver = permenetapprover || actingApprover;
      approverName = `${approver.firstName} ${approver.lastName}`;
      position = 'Director Of One Stop Center';

      if (approver.id != userId) {
        approverName = `For ${approver.firstName} ${approver.lastName}`;
      }
    } else if (permitTypeCode === 'FINS' && agencycode === 'COK') {
      // For FINS permit type in COK agency, look for Senior Inspector (SINSP)
      permenetapprover = agencyUsers.find(
        (user) =>
          user?.role?.code === 'SINSP' &&
          user?.isActing === false &&
          user?.isActive === true,
      );

      actingApprover = agencyUsers.find(
        (user) =>
          user?.role?.code === 'SINSP' &&
          user?.isActing === true &&
          user?.isActive === true,
      );

      if (!permenetapprover && !actingApprover) {
        throw new HttpException(
          'No senior inspector found for this agency',
          HttpStatus.NOT_FOUND,
        );
      }

      // Use permanent approver if available, otherwise use acting approver
      const approver = permenetapprover || actingApprover;
      approverName = `${approver.firstName} ${approver.lastName}`;
      position = 'Senior Inspector';

      if (approver.id != userId) {
        approverName = `For ${approver.firstName} ${approver.lastName}`;
      }
    } else {
      // Default case - use Director
      permenetapprover = agencyUsers.find(
        (user) => user?.role?.code === 'DRCT' && user?.isActing === false,
      );

      actingApprover = agencyUsers.find(
        (user) => user?.role?.code === 'DRCT' && user?.isActing === true,
      );

      if (!permenetapprover && !actingApprover) {
        throw new HttpException(
          'No director found for this agency',
          HttpStatus.NOT_FOUND,
        );
      }

      // Use permanent approver if available, otherwise use acting approver
      const approver = permenetapprover || actingApprover;
      approverName = `${approver.firstName} ${approver.lastName}`;
      position = 'Director Of One Stop Center';

      if (approver.id != userId) {
        approverName = `For ${approver.firstName} ${approver.lastName}`;
      }
    }
    console.log(`Approver Name: ${approverName}, Position: ${position}`);

    return {
      approverName,
      position,
    };
  }

  async findOneCertificateAllData(id: string) {
    if (id === 'null') {
      throw new HttpException(
        'No certificate Fount, check your invoice',
        HttpStatus.BAD_REQUEST,
      );
    }
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.id = :id', { id })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
      .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
      .leftJoinAndSelect('randomInspections.siteStatus', 'siteStatuss')
      .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
      .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getMany();

    const userId = certificate[0].applications.userId;
    const approverUserId = certificate[0].userId;
    const userDetails = await this.checkUser(userId);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userDetailsWithoutPassword } = userDetails;

    // Get agency users
    const agencyId = certificate[0].applications.agencyId;
    const agencyCode = certificate[0].applications.agencyCode;

    // Debug logging for auth service call
    // console.log('Calling auth service with agencyId:', agencyId);
    const agencyUsersResponse = await this.authService
      .send({ cmd: 'getAllUserByAgency' }, agencyId)
      .toPromise();
    // console.log('Auth service response:', agencyUsersResponse);

    // Ensure we have an array of users
    const agencyUsers = Array.isArray(agencyUsersResponse)
      ? agencyUsersResponse
      : agencyUsersResponse?.data || [];

    // Get approval details
    const approvalDetails = await this.getApprovalDetails(
      agencyCode,
      certificate[0].permitTypes.code,
      approverUserId,
      agencyUsers,
    );

    const invoicesWithUserDetails = certificate.map((cert) => ({
      ...cert,
      applications: {
        ...cert.applications,
        userDetails: userDetailsWithoutPassword,
        approvedBy: approvalDetails.approverName,
        approverPosition: approvalDetails.position,
        approvalDetails: approvalDetails,
      },
    }));

    return invoicesWithUserDetails;
  }

  async findAllCertificateAllData(userId: string) {
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.userId = :userId', { userId })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
      .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
      .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
      .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getMany();
    const userIds = certificate[0].applications.userId;
    const userDetails = await this.checkUser(userIds);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userDetailsWithoutPassword } = userDetails;

    const invoicesWithUserDetails = certificate.map((cert) => ({
      ...cert,
      applications: {
        ...cert.applications,
        userDetails: userDetailsWithoutPassword,
      },
    }));

    return invoicesWithUserDetails;
  }

  // invoice by agency
  // async findAllCertificateByAgency(agencyCode: string) {
  //   const certificate = await this.certificateEntityManagerRepository
  //     .createQueryBuilder('certificate')
  //     .where('certificate.agencyCode = :agencyCode', { agencyCode })
  //     .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('certificate.applications', 'application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .getMany();
  //   const userId = certificate[0].applications.userId;
  //   const userDetails = await this.checkUser(userId);
  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { password, ...userDetailsWithoutPassword } = userDetails;
  //   const invoicesWithUserDetails = certificate.map((cert) => ({
  //     ...cert,
  //     applications: {
  //       ...cert.applications,
  //       userDetails: userDetailsWithoutPassword,
  //     },
  //   }));

  //   return invoicesWithUserDetails;
  // }

  async findAllCertificateByAgency(agencyCode: string) {
    const certificates = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.agencyCode = :agencyCode', { agencyCode })
      .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
      .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
      .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
      .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
      .leftJoinAndSelect('certificate.applications', 'application')
      .leftJoinAndSelect('application.projects', 'projects')
      .getMany();

    if (!certificates.length) {
      throw new NotFoundException(
        'No certificates found for the given agency code',
      );
    }

    const userId = certificates[0].applications.userId;
    const userDetails = await this.checkUser(userId);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userDetailsWithoutPassword } = userDetails;
    const invoicesWithUserDetails = certificates.map((cert) => ({
      ...cert,
      applications: {
        ...cert.applications,
        userDetails: userDetailsWithoutPassword,
      },
    }));

    return invoicesWithUserDetails;
  }

  async updateCertificate(id: string, certificateDto: CertificateDto) {
    return this.certificateRepository.findOneAndUpdate({ id }, certificateDto);
  }

  async removeCertificate(id: string) {
    return this.certificateRepository.findOneAndDelete({ id });
  }

  async findCertificateByApplicantUserId(applicantUserId: string) {
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.applicantUserId = :applicantUserId', {
        applicantUserId,
      })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
      .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
      .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
      .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getMany();

    return certificate;
  }
  // async findCertificateByApplicantUserId(applicantUserId: string) {
  //   const certificate = await this.certificateEntityManagerRepository
  //     .createQueryBuilder('certificate')
  //     .leftJoinAndSelect('certificate.application', 'application')
  //     .where('application.userId = :userId', { userId: applicantUserId })

  //     .getMany();
  //   return certificate;
  // }

  // signatory
  async createSignatory(signatoryDto: SignatoryDto) {
    const signatory = new Signatory({
      ...signatoryDto,
    });
    return this.signatoryRepository.create(signatory);
  }

  async findAllSignatorys() {
    return this.signatoryRepository.find({});
  }

  async findOneSignatory(id: string) {
    return this.signatoryRepository.findOne({ id });
  }

  async updateSignatory(id: string, signatoryDto: SignatoryDto) {
    return this.signatoryRepository.findOneAndUpdate({ id }, signatoryDto);
  }

  async removeSignatory(id: string) {
    return this.signatoryRepository.findOneAndDelete({ id });
  }

  // // updating certificate Status
  // async UpdateCertificateStatus(applicantUserId: string) {
  //   const certificate = await this.certificateEntityManagerRepository
  //     .createQueryBuilder('certificate')
  //     .where('certificate.applicantUserId = :applicantUserId', {
  //       applicantUserId,
  //     })
  //     .getOne();

  //   // const certificate = await this.certificateEntityManagerRepository.findOne({
  //   //   where: { id: applicantUserId },
  //   // });
  //   if (!certificate) {
  //     // throw new HttpException('Data not found', HttpStatus.NOT_FOUND);
  //     console.log('Data not found');
  //   }
  //   certificate.statusId = '1';
  //   return await this.certificateEntityManagerRepository.save(certificate);
  // }

  // Updating certificate Status
  async UpdateCertificateStatus(applicantUserId: string, invoice: string) {
    // Fetch the certificate using the applicant user ID
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.applicantUserId = :applicantUserId', {
        applicantUserId,
      })
      .getOne();

    // If the certificate is not found, throw an error
    if (!certificate) {
      throw new Error('Certificate not found for the given applicant user ID');
    }

    // Update the status of the certificate when invoice is paid
    const certificateUpdate = await this.certificateEntityManagerRepository
      .createQueryBuilder()
      .update(Certificate)
      .set({ statusId: '1', invoiceNumber: invoice })
      .where('applicantUserId = :applicantUserId', { applicantUserId })
      .execute();

    console.log(certificateUpdate);

    return certificateUpdate;
  }

  async updateStatusTo1OnCertificate(invoiceNumber: string) {
    // Fetch the certificate using the certificate ID
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.invoiceNumber = :invoiceNumber', { invoiceNumber })
      .getOne();

    // If the certificate is not found, throw an error
    if (!certificate) {
      throw new HttpException(
        'Certificate not found for the given ID',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update the status of the certificate
    const result = await this.certificateEntityManagerRepository
      .createQueryBuilder()
      .update(Certificate)
      .set({ statusId: '1' })
      .where('invoiceNumber = :invoiceNumber', { invoiceNumber })
      .execute();

    if (result.affected === 0) {
      throw new HttpException(
        'Failed to update the certificate status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      status: HttpStatus.OK,
      message: 'Certificate status updated successfully',
    };
  }

  async cancelCertificate(certificateId: string) {
    // Fetch the certificate using the certificate ID
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.id = :certificateId', { certificateId })
      .getOne();

    // If the certificate is not found, throw an error
    if (!certificate) {
      throw new HttpException(
        'Certificate not found for the given ID',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update the status of the certificate
    const result = await this.certificateEntityManagerRepository
      .createQueryBuilder()
      .update(Certificate)
      .set({ statusId: '3' })
      .where('id = :certificateId', { certificateId })
      .execute();

    if (result.affected === 0) {
      throw new HttpException(
        'Failed to update the certificate status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      status: HttpStatus.OK,
      message: 'Certificate status updated successfully',
    };
  }
  // // Updating certificate Status
  // async UpdateCertificateStatus(applicantUserId: string) {
  //   const certificate = await this.certificateEntityManagerRepository
  //     .createQueryBuilder('certificate')
  //     .where('certificate.applicantUserId = :applicantUserId', {
  //       applicantUserId,
  //     })
  //     .getOne();

  //   if (!certificate) {
  //     throw new Error('Certificate not found for the given applicant user ID');
  //   }

  //   certificate.statusId = '1';
  //   return await this.certificateEntityManagerRepository.create(certificate);
  // }

  // Transfer the certificate to someone else
  async transferCertificate(transferCertificateDto: TransferCertificateDto) {
    const certificateNumber = transferCertificateDto.certificateNumber;

    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.certificateNumber = :certificateNumber', {
        certificateNumber,
      })
      .getOne();

    if (!certificate) {
      throw new HttpException('Certificate not found', HttpStatus.NOT_FOUND);
    }

    // Update the status of the certificate
    certificate.statusId = '2';
    await this.certificateEntityManagerRepository.save(certificate);

    // Create a new transfer certificate
    const transferCertificate = new TransferCertificate({
      ...transferCertificateDto,
    });

    return await this.transferCertificateRepository.create(transferCertificate);
  }

  // get all transfer certificates
  async findAllTransfers() {
    return this.transferCertificateRepository.find({});
  }

  //Find transfer by certificateNumber
  // async findTransferCertificateByCertificateNumber(certificateNumber: string) {
  //   const certificate = await this.transferCertificateEntityManagerRepository
  //     .createQueryBuilder('transferCertificate')
  //     .where('transferCertificate.certificateNumber = :certificateNumber', {
  //       certificateNumber,
  //     })
  //     .getOne();
  //   return certificate;
  // }

  // Find transfer by certificateNumber
  async findTransferCertificateByCertificateNumber(certificateNumber: string) {
    const transferCertificate =
      await this.transferCertificateEntityManagerRepository
        .createQueryBuilder('transferCertificate')
        .where('transferCertificate.certificateNumber = :certificateNumber', {
          certificateNumber,
        })
        .getOne();

    if (!transferCertificate) {
      throw new HttpException(
        'Transfer certificate not found',
        HttpStatus.NOT_FOUND,
      );
    }

    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.certificateNumber = :certificateNumber', {
        certificateNumber,
      })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getOne();

    if (!certificate) {
      throw new HttpException('Certificate not found', HttpStatus.NOT_FOUND);
    }

    return {
      transferCertificate,
      certificate,
    };
  }

  // certificate
  async createDevelopmentStatus(developmentStatusDto: DevelopmentStatusDto) {
    // Get last 5 digits of current timestamp
    const developmentStatus = new DevelopmentStatus({
      ...developmentStatusDto,
      certificate: (developmentStatusDto.certificateId = {
        id: developmentStatusDto.certificateId,
      } as any),
      applications: (developmentStatusDto.applicationId = {
        id: developmentStatusDto.applicationId,
      } as any),
      siteStatus: (developmentStatusDto.siteStatusId = {
        id: developmentStatusDto.siteStatusId,
      } as any),
    });
    return this.developmentStatusRepository.create(developmentStatus);
  }

  async findAllDevelopmentStatus() {
    return this.developmentStatusRepository.findAll({
      relations: {
        certificate: true,
        siteStatus: true,
      },
    });
  }

  async findOneDevelopmentStatus(id: string) {
    return this.developmentStatusRepository.findOne({ id });
  }

  // site Status
  async createSiteStatus(siteStatusDto: SiteStatusDto) {
    const siteStatus = new SiteStatus({
      ...siteStatusDto,
    });
    return this.siteStatusRepository.create(siteStatus);
  }

  async findAllSiteStatus() {
    return this.siteStatusRepository.findAll({});
  }

  async findOneSiteStatus(id: string) {
    return this.siteStatusRepository.findOne({ id });
  }

  async updateSiteStatus(id: string, siteStatusDto: SiteStatusDto) {
    return this.siteStatusRepository.findOneAndUpdate({ id }, siteStatusDto);
  }

  async removeSiteStatus(id: string) {
    return this.siteStatusRepository.findOneAndDelete({ id });
  }

  // Random Inspection without file
  // async createRandomInspection(randomInspectionDto: RandomInspectionDto) {
  //   const dataFromDb = await this.siteStatusRepository.findOne({
  //     id: randomInspectionDto.siteStatusId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const dataFromDb2 = await this.certificateRepository.findOne({
  //     id: randomInspectionDto.certificateId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const randomInspection = new RandomInspection({
  //     ...randomInspectionDto,
  //     siteStatus: (randomInspectionDto.siteStatusId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     certificate: (randomInspectionDto.certificateId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //   });
  //   return this.randomInspectionRepository.create(randomInspection);
  // }

  // Random Inspection with a file
  async createRandomInspection(
    randomInspectionDto: RandomInspectionDto,
    file: Express.Multer.File,
  ) {
    try {
      let fileBase64: string = null;

      // Convert file to base64 if provided
      if (file) {
        fileBase64 = file.buffer.toString('base64');
      }

      const dataFromDb = await this.siteStatusRepository.findOne({
        id: randomInspectionDto.siteStatusId,
      });
      if (!dataFromDb)
        throw new HttpException(
          'Referenced Data Not Found',
          HttpStatus.NOT_FOUND,
        );
      const dataFromDb2 = await this.certificateRepository.findOne({
        id: randomInspectionDto.certificateId,
      });
      if (!dataFromDb2)
        throw new HttpException(
          'Referenced Data Not Found',
          HttpStatus.NOT_FOUND,
        );

      // Fetch the related certificate from the database
      const certificate = await this.certificateRepository.findOne({
        id: randomInspectionDto.certificateId,
      });
      if (!certificate) {
        throw new HttpException('Certificate Not Found', HttpStatus.NOT_FOUND);
      }

      // Create the RandomInspection instance
      const randomInspection = new RandomInspection({
        ...randomInspectionDto,
        siteStatus: (randomInspectionDto.siteStatusId = {
          id: dataFromDb.id,
        } as any),
        certificate: (randomInspectionDto.certificateId = {
          id: dataFromDb2.id,
        } as any),

        fileBase64,
      });

      // Save the randomInspection entity
      const savedEntity =
        await this.randomInspectionRepository.create(randomInspection);

      return savedEntity;
    } catch (error) {
      console.error('Error in createApplicationApprovalCheckList:', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAllRandomInspections() {
    return await this.randomInspectionEntityManagerRepository
      .createQueryBuilder('randomInspection')
      .leftJoinAndSelect('randomInspection.siteStatus', 'siteStatus')
      .leftJoinAndSelect('randomInspection.certificate', 'certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();
  }

  async findOneRandomInspection(id: string) {
    return this.randomInspectionRepository.find({ id });
  }

  async findOneRandomInspectionAllData(id: string) {
    return await this.randomInspectionEntityManagerRepository
      .createQueryBuilder('randomInspection')
      .where('randomInspection.id = :id', { id })
      .leftJoinAndSelect('randomInspection.siteStatus', 'siteStatus')
      .leftJoinAndSelect('randomInspection.certificate', 'certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getOne();
  }

  async findRandomInspectionAllDataByUPI(UPI: string) {
    return await this.randomInspectionEntityManagerRepository
      .createQueryBuilder('randomInspection')
      .where('randomInspection.UPI = :UPI', { UPI })
      .leftJoinAndSelect('randomInspection.siteStatus', 'siteStatus')
      .leftJoinAndSelect('randomInspection.certificate', 'certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('applications.projects', 'projects')
      .getMany();
  }

  async findRandomInspectionAllDataByStatus(siteStatusId: string) {
    const random = await this.randomInspectionEntityManagerRepository
      .createQueryBuilder('randomInspection')
      .where('randomInspection.siteStatusId = :siteStatusId', {
        siteStatusId,
      })
      .leftJoinAndSelect('randomInspection.siteStatus', 'siteStatus')
      .leftJoinAndSelect('randomInspection.certificate', 'certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();

    return random;
  }

  async findRandomInspectionByCertificateId(certificateId: string) {
    const random = await this.randomInspectionEntityManagerRepository
      .createQueryBuilder('randomInspection')
      .where('randomInspection.certificateId = :certificateId', {
        certificateId,
      })
      .leftJoinAndSelect('randomInspection.siteStatus', 'siteStatus')
      .leftJoinAndSelect('randomInspection.certificate', 'certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();

    return random;
  }

  // check certificate validity it returns true if a valid certificate is found
  async isCertificateValid(upi: string): Promise<boolean> {
    const today = new Date();

    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.upi = :upi', { upi })
      .andWhere('certificate.expiredDate >= :today', { today })
      .getOne();

    return !!certificate;
  }

  // check certificate validity it returns true if a valid certificate is found on NCP
  async isNCPCertificateValid(upi: string): Promise<boolean> {
    try {
      // Ensure today is in UTC
      const today = new Date();
      today.setUTCHours(0, 0, 0, 0); // Normalize to midnight UTC

      console.log('Checking certificate for UPI:', upi);
      console.log("Today's UTC date for comparison:", today.toISOString());

      const certificate = await this.certificateEntityManagerRepository
        .createQueryBuilder('certificate')
        .innerJoinAndSelect('certificate.permitTypes', 'permitType')
        .where('certificate.upi = :upi', { upi })
        .andWhere('permitType.code = :code', { code: 'NCP' })
        .andWhere('certificate.expiredDate >= :today', {
          today: today.toISOString(),
        })
        .getOne();

      if (certificate) {
        console.log('Certificate found:', certificate);
      } else {
        console.log(
          'No certificate found for the provided UPI or expiredDate is invalid.',
        );
      }

      return !!certificate;
    } catch (error) {
      console.error('Error while validating certificate:', error);
      throw error;
    }
  }

  // check certificate validity it returns true if a valid certificate is found on NCP with 30 days
  // async isRenewalForNCPCertificateValid(upi: string): Promise<boolean> {
  //   const certificate = await this.certificateEntityManagerRepository
  //     .createQueryBuilder('certificate')
  //     .innerJoin('certificate.permitTypes', 'permitType')
  //     .where('certificate.upi = :upi', { upi })
  //     .andWhere('permitType.code = :code', { code: 'NCP' })
  //     .andWhere(
  //       `(certificate.expiredDate <= CURRENT_DATE OR certificate.expiredDate - CURRENT_DATE <= interval '30 days')`,
  //     )
  //     .getOne();

  //   return !!certificate;
  // }

  async isRenewalForNCPCertificateValid(
    upi: string,
  ): Promise<{ answer: boolean }> {
    try {
      // Normalize current date to UTC midnight
      const today = new Date();
      today.setUTCHours(0, 0, 0, 0);

      console.log('Checking renewal validity for UPI:', upi);
      console.log("Today's UTC date for comparison:", today.toISOString());

      // Fetch the certificate for the provided UPI
      const certificate = await this.certificateEntityManagerRepository
        .createQueryBuilder('certificate')
        .innerJoinAndSelect('certificate.permitTypes', 'permitType')
        .where('certificate.upi = :upi', { upi })
        .andWhere('permitType.code = :code', { code: 'NCP' })
        .getOne();

      // If no certificate is found, return true
      if (!certificate) {
        console.log('No certificate found for the provided UPI:', upi);
        return { answer: true };
      }

      console.log('Certificate found:', certificate);

      // Calculate days until expiration
      const expiredDateUTC = new Date(certificate.expiredDate);
      expiredDateUTC.setUTCHours(0, 0, 0, 0); // Normalize to UTC midnight

      const daysUntilExpiration =
        (expiredDateUTC.getTime() - today.getTime()) / (1000 * 60 * 60 * 24);

      console.log(
        'Certificate expiredDate in UTC:',
        expiredDateUTC.toISOString(),
      );
      console.log('Days until expiration:', daysUntilExpiration);

      // Determine validity based on expiration date
      const isExpiredOrNearExpiration =
        daysUntilExpiration <= 0 || daysUntilExpiration <= 30;

      console.log(
        'Is certificate expired or near expiration:',
        isExpiredOrNearExpiration,
      );

      return { answer: isExpiredOrNearExpiration };
    } catch (error) {
      console.error(
        'Error while validating renewal for NCP certificate:',
        error,
      );
      throw error;
    }
  }

  // expiring certificates
  async getExpiringCertificates(): Promise<Certificate[]> {
    const today = new Date();
    const expirationThreshold = new Date();
    expirationThreshold.setDate(today.getDate() + 30);

    const certificates = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.expiredDate >= :today', { today })
      .andWhere('certificate.expiredDate <= :expirationThreshold', {
        expirationThreshold,
      })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();

    return certificates;
  }

  // get expiring permit then send the sms notification
  async getExpiringCertificatesAndSendSMS() {
    const today = new Date();
    const expirationThreshold = new Date();
    expirationThreshold.setDate(today.getDate() + 30);

    const certificates = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.expiredDate >= :today', { today })
      .andWhere('certificate.expiredDate <= :expirationThreshold', {
        expirationThreshold,
      })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();

    console.log(certificates);

    for (const certificate of certificates) {
      const application = certificate.applications?.[0]; // Handle applications as array

      if (application && application.id) {
        const userId = application.userId;
        const userDetail = await this.checkUser(userId);

        console.log(userDetail);
        if (userDetail) {
          const requestData = {
            sender_name: 'KUBAKA MIS',
            sender_email: `${config.notification.senderEmail}`,
            // sender_email: '<EMAIL>',
            receiver_name: 'KUBAKA User',
            receiver_email: userDetail.email,
            subject: 'Invoice Notification',
            message: `
          Dear ${userDetail.firstName} ${userDetail.lastName}, <br>
          Your certificate with number "${certificate.certificateNumber}" is expiring soon.
          Best regards, <br>
          KUBAKA Team
        `,
          };
          try {
            const [emailResponse, smsResponse] = await Promise.all([
              // Sending email responses
              axios.post(
                `${config.notification.emailAPI}/email/send/`,
                requestData,
              ),
              // Sending SMS responses
              axios.post(`${config.notification.smsAPI}/sms/send/`, {
                msisdn: userDetail.phoneNumber,
                message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nYour certificate with number "${certificate.certificateNumber}" is expiring soon.\n\nBest regards,\nKUBAKA Team`,
              }),
            ]);

            console.log('Email sent successfully:', emailResponse);
            console.log('SMS sent successfully:', smsResponse);
            return userDetail;
          } catch (error) {
            console.error('Error sending email:', error);
            throw new HttpException(
              'Email not sent',
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        }
      }
    }
  }

  // get expiring permit then send the sms notification
  async getExpiringCertificatesAndSendNotifications(): Promise<void> {
    const today = new Date();
    const expirationThreshold = new Date();
    expirationThreshold.setDate(today.getDate() + 30);

    // Fetch expiring certificates
    const certificates = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .where('certificate.expiredDate >= :today', { today })
      .andWhere('certificate.expiredDate <= :expirationThreshold', {
        expirationThreshold,
      })
      .leftJoinAndSelect('certificate.applications', 'applications')
      .getMany();

    console.log('Fetched Certificates:', certificates);

    for (const certificate of certificates) {
      const applications = Array.isArray(certificate.applications)
        ? certificate.applications
        : [certificate.applications]; // Handle single object or array

      for (const application of applications) {
        if (!application?.id || !isUUID(application.id)) {
          console.warn(
            `Skipping application with invalid or missing ID: ${application?.id}`,
          );
          continue;
        }

        try {
          // Fetch user details for the application
          const userDetail = await this.checkUser(application.userId);
          console.log('Fetched User Details:', userDetail);

          if (userDetail) {
            // Prepare notification payloads
            const emailRequestData = {
              sender_name: 'KUBAKA MIS',
              sender_email: config.notification.senderEmail,
              receiver_name: `${userDetail.firstName} ${userDetail.lastName}`,
              receiver_email: userDetail.email,
              subject: 'Certificate Expiration Notification',
              message: `
                Dear ${userDetail.firstName} ${userDetail.lastName},<br><br>
                Your certificate with number "${certificate.certificateNumber}" is expiring soon.<br><br>
                Best regards,<br>
                KUBAKA Team
              `,
            };

            const smsMessage = `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nYour certificate with number "${certificate.certificateNumber}" is expiring soon.\n\nBest regards,\nKUBAKA Team`;

            // Send notifications
            const [emailResponse, smsResponse] = await Promise.all([
              axios.post(
                `${config.notification.emailAPI}/email/send/`,
                emailRequestData,
              ),
              axios.post(`${config.notification.smsAPI}/sms/send/`, {
                msisdn: userDetail.phoneNumber,
                message: smsMessage,
              }),
            ]);

            console.log('Email Response:', emailResponse.data);
            console.log('SMS Response:', smsResponse.data);
          } else {
            console.warn(
              `No user details found for application ID: ${application.id}`,
            );
          }
        } catch (error) {
          console.error(
            `Error processing application ID: ${application.id} for certificate: ${certificate.certificateNumber}`,
            error,
          );
        }
      }
    }
  }
  async findCertificateByApplicationId(applicationId: string) {
    console.log('Application ID:', applicationId);
    const certificate = await this.certificateEntityManagerRepository
      .createQueryBuilder('certificate')
      .leftJoinAndSelect('certificate.applications', 'applications')
      .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
      .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
      .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
      .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
      .leftJoinAndSelect('applications.projects', 'projects')
      .where('applications.id = :applicationId', { applicationId })
      .getOne();
    if (certificate) {
      return certificate;
    } else {
      // let us check  if applcation with this id exists
      const application = await this.applicationEntityManagerRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
        .leftJoinAndSelect('application.permitTypes', 'permitTypes')
        .where('application.id = :applicationId', { applicationId })
        .getOne();

      if (!application) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }
      // chech if application status is not approved
      if (application.applicationStatus.code !== 'CTFD') {
        throw new HttpException(
          'Application is not approved',
          HttpStatus.BAD_REQUEST,
        );
      }
      const agencyId = application.agencyId;
      // get users by agencyId
      const agencyUsersResponse = await this.authService
        .send({ cmd: 'getAllUserByAgency' }, agencyId)
        .toPromise();

      // Ensure we have an array of users
      const agencyUsers = Array.isArray(agencyUsersResponse)
        ? agencyUsersResponse
        : agencyUsersResponse?.data || [];

      if (!agencyUsers.length) {
        throw new HttpException('No agency users found', HttpStatus.NOT_FOUND);
      }

      // from agencyUsers in need to select one user with userType code is STF and
      //  role code is DRCT and is active is true
      const agencyDirectorUser = agencyUsers.find(
        (user) =>
          user?.userType?.code === 'STF' &&
          user?.role?.code === 'DRCT' &&
          user?.isActive === true,
      );

      if (!agencyDirectorUser) {
        throw new HttpException(
          'No agency director found',
          HttpStatus.NOT_FOUND,
        );
      }
      // getting agency by agency code - remove this if the command doesn't exist
      // i need paymentAccountIdentifier  form egency entity
      const agency = await this.authService
        .send({ cmd: 'checkAgencyDataById' }, agencyId)
        .toPromise();
      console.log('Agency Response: hfhffhfhfhf', agencyId);
      if (!agency) {
        throw new HttpException('Agency not found', HttpStatus.NOT_FOUND);
      }

      const directID = agencyDirectorUser.id;
      const userId = directID;
      const permitTypeId = application.permitTypes.id;
      const buildupsize = application.buildUpArea;
      const applicantUserId = application.userId;
      const agencyCode = application.agencyCode;
      const paymentAccountIdentifier = agency.paymentAccountIdentifier;
      const transactionNumber = 'String'; // Replace with actual value or logic to get it

      // getting getPriceByPermitTypeAndRange
      const priceResponse =
        await this.invoiceService.getPriceByPermitTypeAndRange(
          permitTypeId,
          buildupsize,
        );
      const amount = priceResponse; // Default to 0 if no price found
      console.log('Price Response:', priceResponse);

      // get invoices by applicationId
      const invoices = await this.invoiceEntityManagerRepository
        .createQueryBuilder('invoice')
        .leftJoinAndSelect('invoice.applications', 'applications')
        .where('applications.id = :applicationId', { applicationId })
        .getMany();
      // getting invoiceID  type
      const invoiceType =
        await this.invoiceService.findAllInvoiceTypeWithCode('INVPER');
      const invoiceTypeId = invoiceType[0].id;
      const invoiceItem = await this.invoiceService.findAllInvoiceItemByName(
        'Construction Permit',
      );
      const invoiceItemId = invoiceItem[0].id;
      // invoice status by code
      let invoiceStatusId = null;
      if (amount == 0) {
        const invoiceStatus =
          await this.invoiceService.findOneInvoiceStatusByCode('EXMTD');
        invoiceStatusId = invoiceStatus?.id;
      } else {
        const invoiceStatus =
          await this.invoiceService.findOneInvoiceStatusByCode('PND');
        invoiceStatusId = invoiceStatus?.id;
      }

      // Check if no invoices or all are cancelled
      if (
        invoices.length === 0 ||
        invoices.every((inv) => inv.invoiceStatus?.code === 'CCD')
      ) {
        // Create invoice with proper parameters based on your invoice service
        const createdInvoice = await this.invoiceService.createInvoice({
          applicationId,
          applicantUserId,
          userId,
          agencyCode,
          paymentAccountIdentifier,
          amount: amount.toString(), // Default to 0 if no price found
          transactionNumber,
          invoiceTypeId,
          invoiceItemId,
          invoiceStatusId,
          invoiceNumber: '', // Will be generated in service
          email: '', // Placeholder, should be replaced with actual user email
          phone: '', // Placeholder, should be replaced with actual user phone
          description: 'Construction Permit', // Example description
          dueDate: new Date(), // Placeholder, should be replaced with actual due date if needed
          externalReferenceNumber: '', // Placeholder, will be set by service if needed
        });
        if (!createdInvoice) {
          throw new HttpException(
            'Failed to create invoice',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
        // create sertificate dto
        const certificateDto: CertificateDto = {
          applicantUserId,
          userId,
          permitTypeId,
          agencyCode,
          // Fill in the other required fields with appropriate values or placeholders
          title: 'Certificate Title',
          lows: 'Certificate Lows',
          backgroundUrl: 'Background URL',
          applicationId: applicationId,
          // Optional fields as needed
          statusId: invoiceStatusId,
          certificateNumber: '', // Placeholder, will be generated in service
          invoiceNumber: '', // Placeholder, will be set after invoice creation
          expiredDate: new Date('0000-01-01T00:00:00.000Z'), // Placeholder for no expiration
        };
        try {
          await this.createCertificate(certificateDto);
        } catch (error) {
          console.error('Error creating certificate:', error);
          throw new HttpException(
            'Failed to create certificate',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
        console.log('Created Invoice:', createdInvoice);
      } else {
        const existingInvoice = await this.invoiceEntityManagerRepository
          .createQueryBuilder('invoice')
          .leftJoinAndSelect('invoice.applications', 'applications')
          .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
          .where('applications.id = :applicationId', { applicationId })
          .andWhere('invoiceStatus.code IN (:...codes)', {
            codes: ['EXMTD', 'PND', 'PAD'],
          })
          .getMany();
        if (existingInvoice.length === 0) {
          throw new HttpException(
            'No valid invoice with status code EXMTD,PND or PAD  found for the application',
            HttpStatus.NOT_FOUND,
          );
        }
        // let amount = '0';
        let statusId = '1'; // Default to 1 if no invoice found
        const invoiceStatus = existingInvoice[0].invoiceStatus.code;
        if (invoiceStatus === 'PND') {
          // amount = existingInvoice[0].amount;
          statusId = '0';
        }
        const newcertificateDto: CertificateDto = {
          applicantUserId: existingInvoice[0].applicantUserId,
          userId: existingInvoice[0].userId,
          permitTypeId,
          agencyCode: existingInvoice[0].agencyCode,
          // Fill in the other required fields with appropriate values or placeholders
          title: 'Certificate Title',
          lows: 'Certificate Lows',
          backgroundUrl: 'Background URL',
          applicationId: applicationId,
          // Optional fields as needed
          statusId: statusId,
          certificateNumber: '', // Placeholder, will be generated in service
          invoiceNumber: existingInvoice[0].invoiceNumber, // Placeholder, will be set after invoice creation
          expiredDate: new Date('0000-01-01T00:00:00.000Z'), // Placeholder for no expiration
        };
        try {
          await this.createCertificate(newcertificateDto);
        } catch (error) {
          console.error('Error creating certificate:', error);
          throw new HttpException(
            'Failed to create certificate',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }

      // getting created certificate
      const savedCertificate = await this.certificateEntityManagerRepository
        .createQueryBuilder('certificate')
        .leftJoinAndSelect('certificate.applications', 'applications')
        .leftJoinAndSelect('certificate.permitTypes', 'permitTypes')
        .leftJoinAndSelect('certificate.randomInspections', 'randomInspections')
        .leftJoinAndSelect('certificate.developmentStatus', 'developmentStatus')
        .leftJoinAndSelect('developmentStatus.siteStatus', 'siteStatus')
        .leftJoinAndSelect('applications.projects', 'projects')
        .where('applications.id = :applicationId', { applicationId })
        .getOne();
      return savedCertificate;
    }
  }
}

export { Application };
