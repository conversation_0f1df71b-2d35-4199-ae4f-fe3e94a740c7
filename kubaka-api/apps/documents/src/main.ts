import '../../../instrumentation';
import { NestFactory } from '@nestjs/core';
import { DocumentsModule } from './documents.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

// import * as express from 'express';

async function bootstrap() {
  const app = await NestFactory.create(DocumentsModule);
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });

  // -- Cors setup
  app.enableCors();

  // // Increase body size limit
  // app.use(
  //   express.json({ limit: '50mb' }),
  //   express.urlencoded({ extended: true, limit: '50mb' }),
  // );

  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Building Permit API')
    .setDescription('API Documents to manage building permits.')
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // -- Port listening
  await app.listen(configService.get('HTTP_PORT'));
  // await app.listen(3005);
  await app.startAllMicroservices();
}
bootstrap();
