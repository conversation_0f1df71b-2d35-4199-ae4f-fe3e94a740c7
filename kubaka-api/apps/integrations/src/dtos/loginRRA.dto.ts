import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class LoginRRADto {
  @ApiProperty({ description: 'The field username is required' })
  @IsNotEmpty({ message: 'The field username cannot be empty' })
  username: string;

  @ApiProperty({ description: 'The field password is required' })
  @IsNotEmpty({ message: 'The field password cannot be empty' })
  password: string;
}
