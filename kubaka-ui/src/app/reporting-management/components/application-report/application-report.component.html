<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-lists">
                <div class="app-tblist">
                    <div class="app-tblist_title">
                        <span class="hder" aria-label="header tittle">All applications</span>
                    </div>
                    <!-- Export Button -->
                    <button (click)="onExport()">Export</button>
                    <!-- <app-application-charts></app-application-charts> -->
                    <ag-grid-angular class="ag-theme-quartz" style="height: 500px;" [rowData]="rowData"
                        [columnDefs]="colDefs" [defaultColDef]="defaultColDef"
                        [pagination]="true" [rowSelection]="'multiple'" (gridReady)="onGridReady($event)"
                        (cellValueChanged)="onCellValueChanged($event)" (selectionChanged)="onSelectionChanged($event)">
                    </ag-grid-angular>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="app-dash" style="display: none;">
    <div class="container">
        <div class="app-main">
            <div class="app-lists">
                <div class="app-tblist">
                    <div class="app-tblist_title">
                        <span class="hder" aria-label="header tittle">All applications</span>
                    </div>
                    <!-- <app-application-charts></app-application-charts> -->
                    <ag-grid-angular class="ag-theme-quartz" style="height: 500px;" [rowData]="rowData"
                        [columnDefs]="colDefs" [columnDefs]="colDefs" [defaultColDef]="defaultColDef"
                        [pagination]="true" [rowSelection]="'multiple'" (gridReady)="onGridReady($event)"
                        (cellValueChanged)="onCellValueChanged($event)" (selectionChanged)="onSelectionChanged($event)">
                    </ag-grid-angular>
                </div>
            </div>
        </div>
    </div>
</div>