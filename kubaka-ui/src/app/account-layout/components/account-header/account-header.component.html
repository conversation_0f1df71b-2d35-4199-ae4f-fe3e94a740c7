<app-header-notification *ngIf="currentUser" (backToParent)="getNotification($event)"></app-header-notification>

<div class="inner-bg">
  <div class="account-hder">
    <div class="account-hder-content">

      <div class="lng-nav">
        <div class="container">
          <nav class="kbk-x-e lang-links">
            <a (click)="switchLang('kin')" [class.active]="defaultSelectedLang === 'kin'">
              <img src="../../../../assets/flags/kiny.svg" alt="Kinyarwanda" /></a>
            <a (click)="switchLang('fr')" [class.active]="defaultSelectedLang === 'fr'"><img src="assets/flags/fr.svg" alt="Francais" /></a>
            <a (click)="switchLang('en')" [class.active]="defaultSelectedLang === 'en'"><img src="assets/flags/uk.svg" alt="English" /></a>
          </nav>
        </div>
      </div>
      <div class="container">
        <div class="links-container xtn-links">
          <a href="https://engineersrwanda.rw/ier-compliant-members?page=1" target="_blank">{{"accountHeaderLinksContainer.engineer" | translate }}</a><span></span>
          <a href="https://ria.rw/licensed-architects-2025/" target="_blank">{{"accountHeaderLinksContainer.architect" | translate }}</a>
          <!-- <a href="https://ria.rw/member-directory/all-members-architects/" target="_blank">Registered Architects</a> -->
        </div>
        <nav class="prim-nav">
          <input type="checkbox" id="acbgMenu" style="display: none" />
          <a class="logo">
            <img src="assets/ikons/logo/NavLogo.svg" alt="" />
          </a>
          <div class="kbk-x-e sp-1 kbk-ac">
            <label class="mob-menu" for="acbgMenu">
              <img src="assets/ikons/colored/ikon-menu.svg" alt="" />
            </label>
            <div class="main-nav">
              <div class="main-nav-list">
                <label class="kbk-btn-close hide" for="acbgMenu">
                  <img src="assets/ikons/colored/ikon-close.svg" alt="" />
                </label>
                <ul class="kbk-x">
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeDashboard" class="kbk-nav-item"
                    routerLink="/account/dashboard" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.dashboard" | translate }}</a>
                  </li>
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeAdministration" class="kbk-nav-item"
                    routerLinkActive="active" routerLink="/account/setting">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.systemAdministrator" | translate }}</a>
                  </li>
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeMyProjects" class="kbk-nav-item"
                    routerLinkActive="active" routerLink="/account/application/projects">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.myProjects" | translate }} </a>
                  </li>
                  <!-- <li class="kbk-nav-item" routerLinkActive="active"

                            routerLink="/account/setting/role">
                                <a class="kbk-nav-link">Role</a>
                            </li> -->
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeAllApplication" class="kbk-nav-item"
                    routerLinkActive="active" routerLink="/account/all-applications/lists">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.allApplications" | translate }} <!-- <span class="bdg bdg-sm bdg-notif">2</span> -->
                    </a>
                  </li>
                  <li *ngIf="
                      currentUser?.permissions
                        ?.isAllowToSeeTransferedCertificate
                    " class="kbk-nav-item" routerLinkActive="active"
                    routerLink="/account/certificate/transfered-certificates">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.transferredPermits" | translate }}</a>
                  </li>
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeApplication" class="kbk-nav-item"
                    routerLinkActive="active" routerLink="/account/application/applications">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.myApplications" | translate }} </a>
                  </li>
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeMyBox" class="kbk-nav-item" routerLinkActive="active"
                    routerLink="/account/application/my-box">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.myBox" | translate }}
                      <span *ngIf="notificationNumber.myBoxNumber > 0"
                        class="bdg bdg-sm bdg-notif">{{notificationNumber.myBoxNumber}}
                      </span></a>
                  </li>
                  <!-- <li *ngIf="currentUser?.permissions?.isAllowToAssignedApplication" class="kbk-nav-item"
                      routerLinkActive="active" routerLink="/account/all-applications/assigned-applications">
                      <a class="kbk-nav-link">My Application </a>
                  </li> -->
                  <li *ngIf="
                      currentUser?.permissions?.isAllowToAssignedApplication
                    " class="kbk-nav-item" routerLinkActive="active" routerLink="/account/application/engineers-tasks">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.projectRequests" | translate }}<a style="display: none" class="usermenu-link hs-tp"
                        data-kbk-tooltip="Notifications">
                        <!-- {{ lists.length }} -->
                        <!-- <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="bell">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.52 15.21l-1.8-1.81V8.94a6.86 6.86 0 0 0-5.82-6.88 6.74 6.74 0 0 0-7.62 6.67v4.67l-1.8 1.81A1.64 1.64 0 0 0 4.64 18H8v.34A3.84 3.84 0 0 0 12 22a3.84 3.84 0 0 0 4-3.66V18h3.36a1.64 1.64 0 0 0 1.16-2.79zM14 18.34A1.88 1.88 0 0 1 12 20a1.88 1.88 0 0 1-2-1.66V18h4zM5.51 16l1.18-1.18a2 2 0 0 0 .59-1.42V8.73A4.73 4.73 0 0 1 8.9 5.17 4.67 4.67 0 0 1 12.64 4a4.86 4.86 0 0 1 4.08 4.9v4.5a2 2 0 0 0 .58 1.42L18.49 16z"
                        />
                      </g>
                    </g>
                  </svg> -->
                      </a>
                      <div class="bdg bdg-notif">{{ lists.length }}</div>
                    </a>
                  </li>

                  <li *ngIf="
                      currentUser?.permissions?.isAllowToSeeInvoice &&
                      currentUser.data?.user?.agency?.code !== 'RHA'
                    " class="kbk-nav-item" [routerLink]="['/account/payment']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.invoices" | translate }} <span class="ikonring"
                        *ngIf="notificationNumber?.isInvoicePending">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <g id="Layer_2" data-name="Layer 2">
                            <g id="Layer_1-2" data-name="Layer 1">
                              <g id="ikon-bell">
                                <g id="not">
                                  <path class="cls-1"
                                    d="M12,6.15A2.2,2.2,0,1,1,14.19,4,2.19,2.19,0,0,1,12,6.15Zm0-2.92a.73.73,0,1,0,.73.73A.73.73,0,0,0,12,3.23Z" />
                                  <path class="cls-1"
                                    d="M14.19,17.85H9.81a.73.73,0,0,0-.73.73v.73a2.92,2.92,0,1,0,5.84,0v-.73A.73.73,0,0,0,14.19,17.85Z" />
                                  <path class="cls-2"
                                    d="M21.1,17.93a4.55,4.55,0,0,1-2.52-4.08V11.27a6.58,6.58,0,0,0-13.16,0v2.58A4.55,4.55,0,0,1,2.9,17.93a.73.73,0,0,0-.32,1,.71.71,0,0,0,.65.4H20.77a.73.73,0,0,0,.73-.73A.71.71,0,0,0,21.1,17.93Z" />
                                </g>
                                <rect class="cls-3" width="24" height="24" />
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </a>
                  </li>
                  <!-- all invoices in all country  -->

                  <li *ngIf="currentUser?.permissions?.isAllowToSeeAdministration" class="kbk-nav-item"
                    [routerLink]="['/account/payment']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.allInvoices" | translate }} <span class="ikonring"
                        *ngIf="notificationNumber?.isInvoicePending">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                          <g id="Layer_2" data-name="Layer 2">
                            <g id="Layer_1-2" data-name="Layer 1">
                              <g id="ikon-bell">
                                <g id="not">
                                  <path class="cls-1"
                                    d="M12,6.15A2.2,2.2,0,1,1,14.19,4,2.19,2.19,0,0,1,12,6.15Zm0-2.92a.73.73,0,1,0,.73.73A.73.73,0,0,0,12,3.23Z" />
                                  <path class="cls-1"
                                    d="M14.19,17.85H9.81a.73.73,0,0,0-.73.73v.73a2.92,2.92,0,1,0,5.84,0v-.73A.73.73,0,0,0,14.19,17.85Z" />
                                  <path class="cls-2"
                                    d="M21.1,17.93a4.55,4.55,0,0,1-2.52-4.08V11.27a6.58,6.58,0,0,0-13.16,0v2.58A4.55,4.55,0,0,1,2.9,17.93a.73.73,0,0,0-.32,1,.71.71,0,0,0,.65.4H20.77a.73.73,0,0,0,.73-.73A.71.71,0,0,0,21.1,17.93Z" />
                                </g>
                                <rect class="cls-3" width="24" height="24" />
                              </g>
                            </g>
                          </g>
                        </svg>
                      </span>
                    </a>
                  </li>
                  <!-- all permits -->
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeAdministration" class="kbk-nav-item"
                    [routerLink]="['/account/certificate']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.allPermits" | translate }}</a>
                  </li>

                  <li *ngIf="
                      currentUser?.permissions?.isAllowToSeeCertificate &&
                      currentUser?.data?.user?.agency?.code !== 'RHA'" class="kbk-nav-item"
                    [routerLink]="['/account/certificate']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.permits" | translate }}</a>
                  </li>
                  <li *ngIf="currentUser?.permissions?.isAllowToSeeReport ||
                  currentUser?.data?.user?.agency?.code === 'RHA' " class="kbk-nav-item"
                    [routerLink]="['/account/report']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.reports" | translate }}</a>
                  </li>
                  <li class="kbk-nav-item" [routerLink]="['/account/notification/chat-room']" routerLinkActive="active">
                    <a class="kbk-nav-link">{{"accountHeaderMenu.chat" | translate }}
                      <span
                        *ngIf="this.notificationNumber?.chatNumber > 0 && this.currentUser?.data?.user?.role?.code !== 'APP'"
                        class="bdg bdg-sm bdg-notif">{{notificationNumber.chatNumber}}</span></a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="usermenu">
              <ul class="usermenu-list">
                <li *ngIf="currentUser?.permissions?.isAllowToSeeInvoice" class="usermenu-item m-p">
                  <!-- <a
                  class="usermenu-link hs-tp"
                  data-kbk-tooltip="Notifications"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="bell">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.52 15.21l-1.8-1.81V8.94a6.86 6.86 0 0 0-5.82-6.88 6.74 6.74 0 0 0-7.62 6.67v4.67l-1.8 1.81A1.64 1.64 0 0 0 4.64 18H8v.34A3.84 3.84 0 0 0 12 22a3.84 3.84 0 0 0 4-3.66V18h3.36a1.64 1.64 0 0 0 1.16-2.79zM14 18.34A1.88 1.88 0 0 1 12 20a1.88 1.88 0 0 1-2-1.66V18h4zM5.51 16l1.18-1.18a2 2 0 0 0 .59-1.42V8.73A4.73 4.73 0 0 1 8.9 5.17 4.67 4.67 0 0 1 12.64 4a4.86 4.86 0 0 1 4.08 4.9v4.5a2 2 0 0 0 .58 1.42L18.49 16z"
                        />
                      </g>
                    </g>
                  </svg>
                </a> -->
                  <!-- <div class="dropdown-navmenu">
                  <ul class="dropdown-navmenu-list">
                    <li class="dropdown-navmenu-item">
                      <a class="dropdown-navmenu-link notf-n">
                        <div class="icn">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                          >
                            <g data-name="Layer 2">
                              <g data-name="bell">
                                <rect width="24" height="24" opacity="0" />
                                <path
                                  d="M20.52 15.21l-1.8-1.81V8.94a6.86 6.86 0 0 0-5.82-6.88 6.74 6.74 0 0 0-7.62 6.67v4.67l-1.8 1.81A1.64 1.64 0 0 0 4.64 18H8v.34A3.84 3.84 0 0 0 12 22a3.84 3.84 0 0 0 4-3.66V18h3.36a1.64 1.64 0 0 0 1.16-2.79zM14 18.34A1.88 1.88 0 0 1 12 20a1.88 1.88 0 0 1-2-1.66V18h4zM5.51 16l1.18-1.18a2 2 0 0 0 .59-1.42V8.73A4.73 4.73 0 0 1 8.9 5.17 4.67 4.67 0 0 1 12.64 4a4.86 4.86 0 0 1 4.08 4.9v4.5a2 2 0 0 0 .58 1.42L18.49 16z"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div>
                          <span class="sender">Appliaction Request</span>
                          <span class="msg">request from Mugenzi </span>
                        </div>
                      </a>
                    </li>
                    <li class="dropdown-navmenu-item">
                      <a class="dropdown-navmenu-link notf-w">
                        <div class="icn">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                          >
                            <g data-name="Layer 2">
                              <g data-name="calendar">
                                <rect width="24" height="24" opacity="0" />
                                <path
                                  fill="#fe9b39"
                                  d="M18 4h-1V3a1 1 0 0 0-2 0v1H9V3a1 1 0 0 0-2 0v1H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3zM6 6h1v1a1 1 0 0 0 2 0V6h6v1a1 1 0 0 0 2 0V6h1a1 1 0 0 1 1 1v4H5V7a1 1 0 0 1 1-1zm12 14H6a1 1 0 0 1-1-1v-6h14v6a1 1 0 0 1-1 1z"
                                />
                                <circle fill="#fe9b39" cx="8" cy="16" r="1" />
                                <path
                                  fill="#fe9b39"
                                  d="M16 15h-4a1 1 0 0 0 0 2h4a1 1 0 0 0 0-2z"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div>
                          <span class="sender">Requirements</span>
                          <span class="msg">Contraction permit </span>
                        </div>
                      </a>
                    </li>
                    <li class="dropdown-navmenu-item">
                      <a class="dropdown-navmenu-link">
                        <div class="icn">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                          >
                            <g data-name="Layer 2">
                              <g data-name="map">
                                <rect
                                  width="24"
                                  height="24"
                                  transform="rotate(180 12 12)"
                                  opacity="0"
                                />
                                <path
                                  d="M20.41 5.89l-4-1.8H15.59L12 5.7 8.41 4.09h-.05L8.24 4h-.6l-4 1.8a1 1 0 0 0-.64 1V19a1 1 0 0 0 .46.84A1 1 0 0 0 4 20a1 1 0 0 0 .41-.09L8 18.3l3.59 1.61h.05a.85.85 0 0 0 .72 0h.05L16 18.3l3.59 1.61A1 1 0 0 0 20 20a1 1 0 0 0 .54-.16A1 1 0 0 0 21 19V6.8a1 1 0 0 0-.59-.91zM5 7.44l2-.89v10l-2 .89zm4-.89l2 .89v10l-2-.89zm4 .89l2-.89v10l-2 .89zm6 10l-2-.89v-10l2 .89z"
                                />
                              </g>
                            </g>
                          </svg>
                        </div>
                        <div>
                          <span class="sender">Appliaction Request</span>
                          <span class="msg">request from Mugenzi </span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div> -->
                </li>
                <!-- <li class="usermenu-item">
                <a class="usermenu-link hs-tp" data-kbk-tooltip="settings">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="settings">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M8.61 22a2.25 2.25 0 0 1-1.35-.46L5.19 20a2.37 2.37 0 0 1-.49-3.22 2.06 2.06 0 0 0 .23-1.86l-.06-.16a1.83 1.83 0 0 0-1.12-1.22h-.16a2.34 2.34 0 0 1-1.48-2.94L2.93 8a2.18 2.18 0 0 1 1.12-1.41 2.14 2.14 0 0 1 1.68-.12 1.93 1.93 0 0 0 1.78-.29l.13-.1a1.94 1.94 0 0 0 .73-1.51v-.24A2.32 2.32 0 0 1 10.66 2h2.55a2.26 2.26 0 0 1 1.6.67 2.37 2.37 0 0 1 .68 1.68v.28a1.76 1.76 0 0 0 .69 1.43l.11.08a1.74 1.74 0 0 0 1.59.26l.34-.11A2.26 2.26 0 0 1 21.1 7.8l.79 2.52a2.36 2.36 0 0 1-1.46 2.93l-.2.07A1.89 1.89 0 0 0 19 14.6a2 2 0 0 0 .25 1.65l.26.38a2.38 2.38 0 0 1-.5 3.23L17 21.41a2.24 2.24 0 0 1-3.22-.53l-.12-.17a1.75 1.75 0 0 0-1.5-.78 1.8 1.8 0 0 0-1.43.77l-.23.33A2.25 2.25 0 0 1 9 22a2 2 0 0 1-.39 0zM4.4 11.62a3.83 3.83 0 0 1 2.38 2.5v.12a4 4 0 0 1-.46 3.62.38.38 0 0 0 0 .51L8.47 20a.25.25 0 0 0 .37-.07l.23-.33a3.77 3.77 0 0 1 6.2 0l.12.18a.3.3 0 0 0 .18.12.25.25 0 0 0 .19-.05l2.06-1.56a.36.36 0 0 0 .07-.49l-.26-.38A4 4 0 0 1 17.1 14a3.92 3.92 0 0 1 2.49-2.61l.2-.07a.34.34 0 0 0 .19-.44l-.78-2.49a.35.35 0 0 0-.2-.19.21.21 0 0 0-.19 0l-.34.11a3.74 3.74 0 0 1-3.43-.57L15 7.65a3.76 3.76 0 0 1-1.49-3v-.31a.37.37 0 0 0-.1-.26.31.31 0 0 0-.21-.08h-2.54a.31.31 0 0 0-.29.33v.25a3.9 3.9 0 0 1-1.52 3.09l-.13.1a3.91 3.91 0 0 1-3.63.59.22.22 0 0 0-.14 0 .28.28 0 0 0-.12.15L4 11.12a.36.36 0 0 0 .22.45z"
                          data-name="&lt;Group&gt;"
                        />
                        <path
                          d="M12 15.5a3.5 3.5 0 1 1 3.5-3.5 3.5 3.5 0 0 1-3.5 3.5zm0-5a1.5 1.5 0 1 0 1.5 1.5 1.5 1.5 0 0 0-1.5-1.5z"
                        />
                      </g>
                    </g>
                  </svg>
                  <span class="badge-notify"></span>
                </a>
              </li> -->
              </ul>
              <div class="userprofile">
                <label class="userdrpdwn-btn" for="ddwnMenu">
                  <input type="checkbox" style="display: none" id="ddwnMenu" />
                  <div class="userprofile-avtar">
                    <img src="assets/imgs/profile1.svg" alt="profile picture" />
                  </div>
                  <div class="userprofile-info">
                    <span class="names">{{ currentUser?.data?.user?.firstName }} {{ currentUser?.data?.user?.lastName
                      }}</span>
                    <span class="title">
                      <!-- {{
                    currentUser.data.user.role.name
                  }}  --> {{ currentUser?.data?.user?.userType?.name }} </span>
                  </div>
                  <a style="display: none" class="usermenu-link" data-kbk-tooltip="Logout" (click)="logout()">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="log-out">
                          <rect width="24" height="24" transform="rotate(90 12 12)" opacity="0" />
                          <path d="M7 6a1 1 0 0 0 0-2H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h2a1 1 0 0 0 0-2H6V6z" />
                          <path
                            d="M20.82 11.42l-2.82-4a1 1 0 0 0-1.39-.24 1 1 0 0 0-.24 1.4L18.09 11H10a1 1 0 0 0 0 2h8l-1.8 2.4a1 1 0 0 0 .2 1.4 1 1 0 0 0 .6.2 1 1 0 0 0 .8-.4l3-4a1 1 0 0 0 .02-1.18z" />
                        </g>
                      </g>
                    </svg>
                  </a>
                </label>
                <div class="userdrpdwn-menu">
                  <div class="userdrpdwn-links">
                    <a class="kbk-nav-link" [routerLink]="['/account/edit-profile']">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <g id="Layer_2" data-name="Layer 2">
                          <g id="Layer_1-2" data-name="Layer 1">
                            <g id="ikon-editprofile">
                              <rect class="cls-1" width="24" height="24" />
                              <path
                                d="M22.28,9.59a2.48,2.48,0,0,0-3.5,0l-3.1,3.09a8.43,8.43,0,0,0-2.75-1.83,5.24,5.24,0,1,0-6.81.07A8.63,8.63,0,0,0,1,18.79a1,1,0,0,0,2,0,6.61,6.61,0,0,1,11.27-4.7l-3.7,3.71a1,1,0,0,0-.26.44L9.54,21a1,1,0,0,0,.25,1,1,1,0,0,0,.71.3,1,1,0,0,0,.24,0l2.86-.71a1,1,0,0,0,.47-.26l8.21-8.21A2.49,2.49,0,0,0,22.28,9.59ZM9.48,3.7A3.24,3.24,0,1,1,6.24,6.94,3.24,3.24,0,0,1,9.48,3.7Zm11.38,8-8,8-.93.23.25-.9,8-8a.48.48,0,0,1,.67,0A.5.5,0,0,1,20.86,11.68Z" />
                            </g>
                          </g>
                        </g>
                      </svg>{{"accountHeaderUserDropDown.edit" | translate }}</a>
                    <a class="kbk-nav-link" [routerLink]="['/account/change-password']">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <g id="Layer_2" data-name="Layer 2">
                          <g id="Layer_1-2" data-name="Layer 1">
                            <g id="ikon-changepswd">
                              <rect class="cls-1" width="24" height="24" />
                              <path
                                d="M11.87,16.11a1,1,0,0,0-1.42,1.42l.12.12H4V6.35h3a1,1,0,0,0,0-2H3a1,1,0,0,0-1,1v13.3a1,1,0,0,0,1,1h7.58l-.12.12a1,1,0,0,0,0,1.41,1,1,0,0,0,1.42,0l1.82-1.83a1,1,0,0,0,.3-.7,1,1,0,0,0-.29-.71Z" />
                              <path
                                d="M21,4.35H13.4l.12-.12a1,1,0,0,0-1.41-1.41L10.28,4.65a1,1,0,0,0,0,1.41l1.83,1.83a1,1,0,0,0,1.41-1.42l-.12-.12H20v11.3H17a1,1,0,0,0,0,2h4a1,1,0,0,0,1-1V5.35A1,1,0,0,0,21,4.35Z" />
                              <path
                                d="M16.05,13h.29v.52a1,1,0,0,0,2,0V12a1,1,0,0,0-1-1H11.88a3.2,3.2,0,1,0,0,2h2.17v.16a1,1,0,0,0,2,0Zm-7.19.2a1.2,1.2,0,1,1,1.2-1.2A1.2,1.2,0,0,1,8.86,13.2Z" />
                            </g>
                          </g>
                        </g>
                      </svg>{{"accountHeaderUserDropDown.change" | translate }}</a>
                    <hr />
                    <a class="kbk-nav-link" data-kbk-tooltip="Logout" (click)="logout()">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <g data-name="Layer 2">
                          <g data-name="log-out">
                            <rect width="24" height="24" transform="rotate(90 12 12)" opacity="0" />
                            <path d="M7 6a1 1 0 0 0 0-2H5a1 1 0 0 0-1 1v14a1 1 0 0 0 1 1h2a1 1 0 0 0 0-2H6V6z" />
                            <path
                              d="M20.82 11.42l-2.82-4a1 1 0 0 0-1.39-.24 1 1 0 0 0-.24 1.4L18.09 11H10a1 1 0 0 0 0 2h8l-1.8 2.4a1 1 0 0 0 .2 1.4 1 1 0 0 0 .6.2 1 1 0 0 0 .8-.4l3-4a1 1 0 0 0 .02-1.18z" />
                          </g>
                        </g>
                      </svg> {{"accountHeaderUserDropDown.logout" | translate }} </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  </div>
  <div class="appli-content">
    <div class="container">
      <router-outlet> </router-outlet>
    </div>
  </div>
</div>