<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> Step <span>{{ currentStep }}</span> / <span>{{ steps.length }}</span> </div>
    </div>
    <div class="stepper-body">
        <ng-container [ngSwitch]="currentStep">
            <app-plot-info *ngSwitchCase="1"></app-plot-info>
            <app-old-development-details *ngSwitchCase="2"
                (backToParent)="saveTheResponse($event)"></app-old-development-details>
            <!-- <div *ngIf="outputData.isProjectDetails || currentStep + '' === '3'">
                <app-old-project-details *ngSwitchCase="3" [inputData]="outputData"></app-old-project-details>
            </div> -->
            <!-- <app-project-estimates *ngSwitchCase="4"></app-project-estimates> -->
            <app-project-attachment *ngSwitchCase="4"></app-project-attachment>
        </ng-container>
    </div>
</div>