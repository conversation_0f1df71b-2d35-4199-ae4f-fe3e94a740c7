import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-search-associated-upi',
  templateUrl: './search-associated-upi.component.html',
  styleUrls: ['./search-associated-upi.component.scss']
})
export class SearchAssociatedUpiComponent {
  @Input() inputData: any = {}


  constructor(
    private router: Router,
    private modalService: NgbModal
  ) { }



  ngOnInit() {
    console.log(this.inputData);
  }

  goToApplication(event: any) {
    this.modalService.dismissAll();
    this.router.navigate(['/account/all-applications/application-detail/', event.id]);
  }

}
