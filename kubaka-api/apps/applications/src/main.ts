import '../../../instrumentation';
import { NestFactory } from '@nestjs/core';
import { ApplicationsModule } from './applications.module';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { Transport } from '@nestjs/microservices';
import { RedocModule, RedocOptions } from '@jozefazz/nestjs-redoc';
import * as fs from 'fs';

async function bootstrap() {
  const app = await NestFactory.create(ApplicationsModule);
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

  const configService = app.get(ConfigService);

  // Microservice setup
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });
  await app.startAllMicroservices();

  // Enable CORS
  app.enableCors({
    origin: [
      'http://localhost:4200',
      'https://testing.kubaka.gov.rw',
      'https://kubaka.gov.rw',
      'https://staging.kubaka.gov.rw',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'kubaka_api_key'],
  });

  // Swagger Documentation setup
  const config = new DocumentBuilder()
    .setTitle('KUBAKA BPMIS')
    .setDescription(
      'KUBAKA is a national platform that allows citizens and investors to apply for a construction permit in accordance with the land use and urbanization master plan. This system is intended to be used by: \n\n' +
        '- **Rwandan citizen** that has a national ID and a plot in Rwanda\n' +
        '- **Investor** with a valid passport and a plot in Rwanda \n' +
        '- **Engineer**: Any engineer who is recognized by the association of engineers in Rwanda.\n' +
        '- **Architect**: Any Architect who is recognized by the association of architects in Rwanda.\n\n' +
        '**Application process to receive a construction permit**\n\n' +
        '- **Create a project:** A project and its details will be created based on your UPI and what is allowed to be built on your plot under Rwandan law.\n' +
        '- **Submit your application:** Your application needs to be submitted, and you need to make sure you don not have any unpaid taxes or EIA certificates.\n' +
        '- **Application under Review:** Upon receiving your application, a member of the RHA team will review it, and the engineer will make sure everything is in compliance with the law.\n' +
        '- **Invoice to be paid:** Invoices will be generated after the application is approved, and you must pay to receive the certificate.\n' +
        '- **Certificate generated :** Your certificate will be available for download after payment has been made.\n\n' +
        '**How to integrate with KUBAKA APIs**\n\n' +
        'Integration with the Kubaka API requires you to follow the steps below:\n' +
        '1. The public and secret keys for the test environment would be shared with you during signup.\n' +
        '2. Setup: The requestor must have a Kubaka API key that must be sent in header of any request that goes to the server.\n' +
        '3. VPN chanel: They must be a VPN chanel between two parties.\n' +
        '### Resource\n' +
        ' - **Test:** http://testing.kubaka.gov.rw/docs\n' +
        ' - **Production:** http://kubaka.gov.rw/docs\n' +
        '- **Headers:** kubaka-secretKey: secreKey \n',
    )
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Save the OpenAPI JSON to a file for download (optional)
  fs.writeFileSync('./openapi-spec.json', JSON.stringify(document));

  // Swagger UI setup
  SwaggerModule.setup('api/v1', app, document);

  // Redoc Documentation setup
  const redocOptions: RedocOptions = {
    title: 'API Documentation',
    sortPropsAlphabetically: true,
    hideDownloadButton: false,
    hideHostname: false,
  };

  await RedocModule.setup('/docs', app, document, redocOptions);

  // // Serve OpenAPI specification JSON file
  // app.getHttpAdapter().get('/openapi-json', (req, res) => {
  //   res.sendFile(`${process.cwd()}/openapi-spec.json`);
  // });

  // Start the HTTP server
  await app.listen(configService.get('HTTP_PORT'));
  console.info(
    `API Documentation available at http://localhost:${configService.get('HTTP_PORT')}/docs`,
  );
  console.info(
    `OpenAPI JSON available at http://localhost:${configService.get('HTTP_PORT')}/openapi-json`,
  );
  // console.log(JSON.stringify(document, null, 2));
}

bootstrap();
