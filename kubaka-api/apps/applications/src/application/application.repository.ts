import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Answer,
  Application,
  Assignee,
  AssociatedUPI,
  FoundationInspection,
  OccupancyInspection,
  OtherInfoApplication,
  PermitQuestion,
  Project,
  ReviewersOnApplication,
  SubmissionLog,
} from '../entities/application.entity';
import { AbstractRepository } from '@app/common';
import { EntityManager, Repository } from 'typeorm';
import {
  BuildType,
  CategoryRule,
  CategoryType,
  PermitType,
  DocumentType,
  RequiredDocument,
  TechnologySurvey,
  ApplicationStatus,
  ProjectStatus,
  // EquipmentCapacity,
  // ConstructionMethod,
  QuestionCategory,
} from '../entities/settings.entity';

// application
@Injectable()
export class ProjectsRepository extends AbstractRepository<Project> {
  constructor(
    @InjectRepository(Project)
    projectRepository: Repository<Project>,
    entityManager: EntityManager,
  ) {
    super(entityManager, projectRepository);
  }
}

@Injectable()
export class ApplicationRepository extends AbstractRepository<Application> {
  constructor(
    @InjectRepository(Application)
    applicationRepository: Repository<Application>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationRepository);
  }
}

@Injectable()
export class AssociatedUPIsRepository extends AbstractRepository<AssociatedUPI> {
  constructor(
    @InjectRepository(AssociatedUPI)
    associatedUPIRepository: Repository<AssociatedUPI>,
    entityManager: EntityManager,
  ) {
    super(entityManager, associatedUPIRepository);
  }
}

// settings
@Injectable()
export class PermitTypesRepository extends AbstractRepository<PermitType> {
  constructor(
    @InjectRepository(PermitType)
    permitTypesRepository: Repository<PermitType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, permitTypesRepository);
  }
}

@Injectable()
export class CategoryRulesRepository extends AbstractRepository<CategoryRule> {
  constructor(
    @InjectRepository(CategoryRule)
    categoryRulesRepository: Repository<CategoryRule>,
    entityManager: EntityManager,
  ) {
    super(entityManager, categoryRulesRepository);
  }
}

@Injectable()
export class CategoryTypesRepository extends AbstractRepository<CategoryType> {
  constructor(
    @InjectRepository(CategoryType)
    categoryTypesRepository: Repository<CategoryType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, categoryTypesRepository);
  }
}

@Injectable()
export class BuildTypesRepository extends AbstractRepository<BuildType> {
  constructor(
    @InjectRepository(BuildType)
    buildTypesRepository: Repository<BuildType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, buildTypesRepository);
  }
}

@Injectable()
export class DocumentTypesRepository extends AbstractRepository<DocumentType> {
  constructor(
    @InjectRepository(DocumentType)
    documentTypesRepository: Repository<DocumentType>,
    entityManager: EntityManager,
  ) {
    super(entityManager, documentTypesRepository);
  }
}

@Injectable()
export class RequiredDocumentsRepository extends AbstractRepository<RequiredDocument> {
  constructor(
    @InjectRepository(RequiredDocument)
    requiredDocumentsRepository: Repository<RequiredDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, requiredDocumentsRepository);
  }
}

@Injectable()
export class TechnologySurveysRepository extends AbstractRepository<TechnologySurvey> {
  constructor(
    @InjectRepository(TechnologySurvey)
    technologySurveysRepository: Repository<TechnologySurvey>,
    entityManager: EntityManager,
  ) {
    super(entityManager, technologySurveysRepository);
  }
}

@Injectable()
export class ApplicationStatusRepository extends AbstractRepository<ApplicationStatus> {
  constructor(
    @InjectRepository(ApplicationStatus)
    applicationStatusRepository: Repository<ApplicationStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationStatusRepository);
  }
}

@Injectable()
export class ProjectStatusRepository extends AbstractRepository<ProjectStatus> {
  constructor(
    @InjectRepository(ProjectStatus)
    projectStatusRepository: Repository<ProjectStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, projectStatusRepository);
  }
}

@Injectable()
export class AssigneeRepository extends AbstractRepository<Assignee> {
  constructor(
    @InjectRepository(Assignee)
    assigneeRepository: Repository<Assignee>,
    entityManager: EntityManager,
  ) {
    super(entityManager, assigneeRepository);
  }
}

@Injectable()
export class OtherInfoApplicationRepository extends AbstractRepository<OtherInfoApplication> {
  constructor(
    @InjectRepository(OtherInfoApplication)
    otherInfoApplicationRepository: Repository<OtherInfoApplication>,
    entityManager: EntityManager,
  ) {
    super(entityManager, otherInfoApplicationRepository);
  }
}

@Injectable()
export class PermitQuestionRepository extends AbstractRepository<PermitQuestion> {
  constructor(
    @InjectRepository(PermitQuestion)
    permitQuestionRepository: Repository<PermitQuestion>,
    entityManager: EntityManager,
  ) {
    super(entityManager, permitQuestionRepository);
  }
}

@Injectable()
export class AnswerRepository extends AbstractRepository<Answer> {
  constructor(
    @InjectRepository(Answer)
    answerRepository: Repository<Answer>,
    entityManager: EntityManager,
  ) {
    super(entityManager, answerRepository);
  }
}

@Injectable()
export class QuestionCategoryRepository extends AbstractRepository<QuestionCategory> {
  constructor(
    @InjectRepository(QuestionCategory)
    questionCategoryRepository: Repository<QuestionCategory>,
    entityManager: EntityManager,
  ) {
    super(entityManager, questionCategoryRepository);
  }
}

// @Injectable()
// export class ConstructionMethodRepository extends AbstractRepository<ConstructionMethod> {
//   constructor(
//     @InjectRepository(ConstructionMethod)
//     constructionMethodRepository: Repository<ConstructionMethod>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, constructionMethodRepository);
//   }
// }

// @Injectable()
// export class EquipmentCapacityRepository extends AbstractRepository<EquipmentCapacity> {
//   constructor(
//     @InjectRepository(EquipmentCapacity)
//     equipmentCapacityRepository: Repository<EquipmentCapacity>,
//     entityManager: EntityManager,
//   ) {
//     super(entityManager, equipmentCapacityRepository);
//   }
// }
@Injectable()
export class ReviewersOnApplicationRepository extends AbstractRepository<ReviewersOnApplication> {
  constructor(
    @InjectRepository(ReviewersOnApplication)
    reviewersOnApplicationRepository: Repository<ReviewersOnApplication>,
    entityManager: EntityManager,
  ) {
    super(entityManager, reviewersOnApplicationRepository);
  }
}
@Injectable()
export class FoundationInspectionRepository extends AbstractRepository<FoundationInspection> {
  constructor(
    @InjectRepository(FoundationInspection)
    foundationInspectionRepository: Repository<FoundationInspection>,
    entityManager: EntityManager,
  ) {
    super(entityManager, foundationInspectionRepository);
  }
}
@Injectable()
export class OccupancyInspectionRepository extends AbstractRepository<OccupancyInspection> {
  constructor(
    @InjectRepository(OccupancyInspection)
    occupancyInspectionRepository: Repository<OccupancyInspection>,
    entityManager: EntityManager,
  ) {
    super(entityManager, occupancyInspectionRepository);
  }
}
@Injectable()
export class SubmissionLogRepository extends AbstractRepository<SubmissionLog> {
  constructor(
    @InjectRepository(SubmissionLog)
    submissionLogRepository: Repository<SubmissionLog>,
    entityManager: EntityManager,
  ) {
    super(entityManager, submissionLogRepository);
  }
}
