import { Component } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  CellValueChangedEvent,
  ColDef,
  GridReadyEvent,
  SelectionChangedEvent,
  ValueFormatterParams,
  GridApi, // Import GridApi
  CsvExportParams
} from 'ag-grid-community';
import { AgGridAngular } from 'ag-grid-angular';
import { environment } from 'src/environments/environment';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { jwtDecode } from 'jwt-decode';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';


@Component({
  selector: 'app-reviewer-weekly-report',
  templateUrl: './reviewer-weekly-report.component.html',
  styleUrls: ['./reviewer-weekly-report.component.scss']
})
export class ReviewerWeeklyReportComponent {
  currentUser: any = {};
  users: any[] = [];
  // Variables to store the grid API and column API
  private gridApi!: GridApi;
  isUserShow: boolean = false;
  userId: any = '';
  rowData: any[] = [];
  colDefs: ColDef[] = [
    { field: 'applications.upi', headerName: 'UPI', width: 150, checkboxSelection: true },
    { field: 'applications.applicationName', headerName: 'Application Name', width: 150, checkboxSelection: true },
    { field: 'applications.submittedDate', headerName: 'submittedDate', width: 130 },
    { field: 'applications.resubmittedDate', headerName: 'resubmittedDate', width: 130 },
    { field: 'applications.districtName', headerName: 'districtName', width: 130 },
    { field: 'applications.sectorName', headerName: 'sectorName', width: 130 },
    { field: 'applications.cellName', headerName: 'cellName', width: 130 },
    { field: 'applications.villageName', headerName: 'villageName', width: 130 },
    { field: 'applications.buildUpArea', headerName: 'Build Up Area', width: 130 },
    { field: 'applications.numberOfFloor', headerName: 'Number Of Floor', width: 130 },
    { field: 'applications.grossFloorArea', headerName: 'Gross Floor Area', width: 130 },
    { field: 'applications.priceOfDwellingUnitRwf', headerName: 'Price Of Dwelling Unit Rwf', width: 130 },
    { field: 'applications.permitTypeCode', headerName: 'Permit Type Code', width: 130 },
    { field: 'applications.capacityInformation', headerName: 'Capacity Information', width: 130 },
    { field: 'applications.electricityConsumption', headerName: 'Electricity Consumption', width: 130 },
    { field: 'applications.waterConsumption', headerName: 'Water Consumption', width: 130 },
    { field: 'applications.ProjectCostInUSD', headerName: 'Project Cost In USD', width: 130 },
    { field: 'applications.ProjectCostInRwf', headerName: 'Project Cost In Rw', width: 130 },
    { field: 'approvalStatus.name', headerName: 'Status', width: 130 },
    { field: 'comment', headerName: 'Comment', width: 130 },
    { field: 'created_at', headerName: 'created_at', width: 130 },
    { field: 'browser', headerName: 'browser', width: 130 },
    { field: 'operatingSystem', headerName: 'operatingSystem', width: 130 },
    { field: 'applicationStatusId', headerName: 'applicationStatusId', width: 130 },
    { field: 'applicationStatus.name', headerName: 'application Status', width: 130 },
  ];

  defaultColDef: ColDef = {
    filter: true,
    editable: true,
  };



  constructor(private http: HttpClient,
    public reportService: ApplicationService
    , private sessionService: SessionService,
    private utilService: UtilService,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    // this.reportService.findAllWithPath(environment.authUrl + 'user-management/users')
    //   .subscribe(
    //     data => { this.users = data }
    //   )

    if (this.currentUser.data.user.agency) {
      this.reportService.findAllWithPath(environment.authUrl + 'auth/user/' + this.currentUser.data.user.agency.id)
        .subscribe(
          data => {
            this.users = data
          }
        )
    }


  }


  chooseId(event: any) {
    let user = this.users.find((x: any) => x.id === event.id);
    this.reportService.searchTerm = user.firstName + ' ' + user.lastName;
    this.userId = user.id;
    this.isUserShow = false;
    this.onSearchTermChange('');
  }

  open() {
    this.isUserShow = true;
  }

  onSearchTermChange(event: any) {

    if (this.reportService.filterByDate && this.reportService.filterEndDate &&
      this.reportService.searchTerm
    ) {
      let dataToSave = {
        "userId": this.userId,
        "startDate": this.reportService.filterByDate,
        "endDate": this.reportService.filterEndDate
      }
      this.reportService.saveAssetWithPath(dataToSave, environment.applicationUrl + 'approval/applicationApproval/staffReport')
        .subscribe(
          data => { this.rowData = data }
        )
    }

  }

  onSearchByDateChange(event: any) {


    if ((this.reportService.filterByDate && this.reportService.filterEndDate &&
      this.reportService.searchTerm) &&
      (this.currentUser.data.user.role.code !== 'STF' &&
        this.currentUser.data.user.userType.code !== 'STF')
    ) {
      // let dataToSave = {
      //   "userId": this.userId,
      //   "startDate": this.reportService.filterByDate,
      //   "endDate": this.reportService.filterEndDate
      // }
      // this.reportService.saveAssetWithPath(dataToSave, environment.applicationUrl + 'approval/applicationApproval/staffReport')
      //   .subscribe(
      //     data => { this.rowData = data }
      //   )
      this.callApi(this.userId);
    } else if ((this.currentUser.data.user.role.code === 'STF' &&
      this.currentUser.data.user.userType.code === 'STF') &&
      this.reportService.filterByDate && this.reportService.filterEndDate) {
      this.callApi2(this.currentUser.userId);
    }
  }


  callApi(userId: any) {
    let dataToSave = {
      "userId": userId,
      "startDate": this.reportService.filterByDate,
      "endDate": this.reportService.filterEndDate
    }
    this.reportService.saveAssetWithPath(dataToSave, environment.applicationUrl + 'approval/applicationApproval/staffReport')
      .subscribe(
        data => { this.rowData = data },
        error => {
          console.log(error);
          this.utilService.showNotification(NOTIFICATION_COLOR.warning, error, "bottom", "center");
        }
      )
  }

  callApi2(userId: any) {
    let dataToSave = {
      "userId": userId,
      "startDate": this.reportService.filterByDate,
      "endDate": this.reportService.filterEndDate
    }
    this.reportService.saveAssetWithPath(dataToSave, environment.applicationUrl + 'application/reviewersOnApplication/staffReport')
      .subscribe(
        data => { this.rowData = data },
        error => {
          console.log(error);
          this.utilService.showNotification(NOTIFICATION_COLOR.warning, error, "bottom", "center");
        }
      )
  }


  onGridReady(params: GridReadyEvent) {
    // Save the grid API reference
    this.gridApi = params.api;

    this.http
      .get<any[]>(environment.authUrl + 'chatBoard')
      .subscribe((data) => (this.rowData = data));
  }

  onSelectionChanged(event: SelectionChangedEvent) {

  }

  onCellValueChanged(event: CellValueChangedEvent) {
    console.log(`New Cell Value: ${event.value}`);
  }

  dateFormatter(params: ValueFormatterParams) {
    return new Date(params.value).toLocaleDateString('en-us', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Export to Excel using the grid API
  // onBtnExport() {
  //   const params = {
  //     fileName: 'application-report.xlsx',
  //     onlySelected: true, // Export only selected rows
  //     columnKeys: ['firstName', 'lastName', 'email'], // Export specific columns
  //   };
  //   // Use the grid API to export data as Excel
  //   this.gridApi.exportDataAsExcel(params);
  // }
      onBtnExport() {
         const params: CsvExportParams = {
              fileName: 'application.csv',
              columnSeparator: ',',
            };
            this.gridApi.exportDataAsCsv(params);
      }
}
