import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { Country } from './country.entity';
import { AbstractEntity } from '@app/common';
import { District } from './district.entity';

@Entity()
export class Province extends AbstractEntity<Province> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;

  @Column()
  code: string;

  @ManyToOne(() => Country, (country) => country.provinces)
  country: Country;

  @OneToMany(() => District, (district) => district.provinces)
  districts: District[];
}
