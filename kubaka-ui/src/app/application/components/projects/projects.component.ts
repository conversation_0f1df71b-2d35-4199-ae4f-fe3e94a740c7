import { Component, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../services/application.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss']
})
export class ProjectsComponent {
  @ViewChild("yourSelfContent") modalContent!: TemplateRef<any>;
  @ViewChild("isNewPermit") modalNewPermitContent!: TemplateRef<any>
  currentUser: any = {};
  applicationDetail: any = {};
  lists: any[] = [];
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];
  loading!: boolean;
  projectData: any = {}

  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    private applicationService: ApplicationService,
    private router: Router,
    private appConfig: AppConfig,
    public utilService: UtilService,
    public userService: UserMgtService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.loadList();
  }

  // loadList() {
  //   // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application')
  //   this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/user/' + this.currentUser.userId)
  //     .subscribe(
  //       data => {
  //         this.lists = data;
  //         // this.lists = data.filter((item: any) => item.projects.userId === this.currentUser.userId);
  //       },
  //     )
  // }


  loadList() {
    this.userService
      .findAllWithPath(environment.applicationUrl + 'application/project/user/' + this.currentUser.userId)
      .subscribe(
        (data) => {
          this.fullList = data;
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();
          // this.lists = data;
          // this.content = data;
          // setTimeout(() => {
          //   document.getElementById("elmLoader")?.classList.add("d-none");

          // }, 1200);
          // this.collectionSize = this.lists.length;
          // this.total = this.lists.length;
          // this.totalRecords = this.lists.length;
          // this.startIndex = (this.page - 1) * this.pageSize + 1;
          // this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
          // if (this.endIndex > this.totalRecords) {
          //   this.endIndex = this.totalRecords;
          // }
          // this.lists = this.lists.slice(
          //   this.startIndex - 1,
          //   this.endIndex
          // );
        });
  }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }
  ngOnDestroy() {
    this.userService.searchTerm = '';
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes(term)
    );
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    this.filterAndPaginate();
  }

  // getPremiumData() {

  //   this.lists = this.content
  //     .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);

  // }



  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }



  otherApplication(data: any) {
    this.router.navigate(['/account/application/other-application', data.projects.id])
  }



  getChoosenSelectedUse(event: any) {
    if (!event.isFromOldSystem) {
      this.checKUpiHasAlreayApplicationInBPMIS(event)
      // let existingUpiInApplication = this.lists.find((l: any) => l.projects.upi === event.upi);
      // this.applicationDetail = event;
      // if (existingUpiInApplication) {
      //   this.applicationDetail.isUpiExists = true;
      //   // Make it temporary here
      //   this.goToApplication();
      // } else {
      //   this.applicationDetail.isUpiExists = false;
      //   this.goToApplication();
      // }
    } else {
      this.applicationDetail.isUpiExists = false;
      this.applicationDetail = event;
      this.goToApplication();
    }
  }



  checKUpiHasAlreayApplicationInBPMIS(event: any) {
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/application/upi/search?search=' + event.upi)
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + event.upi)
      .subscribe(
        data => {
          
          if (data && data.items.length > 0) {
            // this.router.navigate(['account/application/resume-application', data.items[0].id]);
            // this.router.navigate(['account/application/resume-application', data.items[0].id]);
            this.router.navigate(['account/application/new-application-project/', data.items[0].id]);
          } else {

            // new application
            localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(event));
            // localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(event));
            this.router.navigate(['account/application/new-application', 0]);
          }
        }, error => {
          localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(event));
          // localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(event));
          this.utilService.showNotification(
            NOTIFICATION_COLOR.error, error + '', "bottom", "center"
          )
        }
      )
  }


  // isNewPermitM(event: any) {
  //   if (event === 'yes') {
  //     this.applicationDetail.isNewPermit = false;
  //     this.cancel();
  //     this.goToApplication();
  //   } else {
  //     this.applicationDetail.isNewPermit = true;
  //   }
  // }

  goToApplication() {
    localStorage.removeItem(this.appConfig.UPI_old_INFO);
    localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(this.applicationDetail));
    this.router.navigate(['/account/application/new-application/0/0']);
  }





  rraIntegration() {

  }




  searchEngineerByLicenseNumber() {
    // search existing certificate number through this.applicationDetail.certificateNumber properties
    // then
    this.checkIsNewPermit();


    // this.cancel();
    // this.router.navigate(['/account/application/new-application/1/0']);
  }


  checkIsNewPermit() {
    this.cancel();
    this.modalService.open(this.modalNewPermitContent, { size: 'md', centered: true });
  }




  cancel() {
    this.modalService.dismissAll();
  }






  submitCertificateNumber() {
    this.goToApplication();
    // applicationDetail.isNewPermit
    // applicationDetail.certificateNumber
  }



  // chooseOwner(event: any) {
  //   // // this.applicationDetail = event;
  //   // fasdf
  //   // this.modalService.open(this.modalContent, { size: 'md', centered: true });
  // }



  openToDelete(event: any, content: any) {
    this.projectData = event;


    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/assignee/byProject/' + this.projectData.id)
      .subscribe(
        data => {
          if (data.length > 0) {
            this.projectData.licenseNumber = data[0].licenseNumber;
            this.modalService.open(content, { size: 'md', centered: true });
          } else {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, 'No engineer assigned to this project', "bottom", "center");
          }
        }, error => {
          this.utilService.showNotification(NOTIFICATION_COLOR.error, 'No engineer assigned to this project', "bottom", "center");
        })
  }



  deleteFile() {
    let dataTosave = {
      "projectId": this.projectData.id,
      "licenseNumber": this.projectData.licenseNumber,
      "userId": this.currentUser.userId
    }

    this.applicationService.saveAssetWithPath(dataTosave, environment.applicationUrl + 'application/assignee/deleteEngineerOnProject')
      .subscribe(
        data => {
          this.loadList();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, 'Engineer removed successfully', "bottom", "center");
        }
      )
  }
}
