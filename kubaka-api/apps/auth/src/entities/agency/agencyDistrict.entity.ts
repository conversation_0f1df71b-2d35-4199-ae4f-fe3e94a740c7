import { AbstractEntity } from '@app/common';
import { Entity, ManyToOne, JoinColumn } from 'typeorm';
import { District } from '../delimitation/district.entity';
import { Agency } from './agency.entity';
@Entity()
export class AgencyDistrict extends AbstractEntity<AgencyDistrict> {
  @ManyToOne(() => Agency, (agency) => agency.districts)
  @JoinColumn({ name: 'agencyId' })
  agency: Agency;

  @ManyToOne(() => District, (district) => district.agencies)
  @JoinColumn({ name: 'districtId' })
  district: District;
}
