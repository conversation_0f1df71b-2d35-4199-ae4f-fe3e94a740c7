import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { CertificateService } from './certificate.service';
import {
  CertificateDto,
  DevelopmentStatusDto,
  RandomInspectionDto,
  SignatoryDto,
  TransferCertificateDto,
} from '../dto/certificate.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Certificate } from '../entities/certificate.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  GenericSearchWithRelations,
  GenericSearchWithRelations2,
} from './generic-search.service';
import { SiteStatusDto } from '../dto/approval.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('certificate')
@Controller('certificate')
export class CertificateController {
  constructor(
    private readonly certificateService: CertificateService,

    @InjectRepository(Certificate)
    protected readonly certificateSearchRepository: Repository<Certificate>,
    protected readonly genericSearch: GenericSearchWithRelations<Certificate>,
    protected readonly genericSearch2: GenericSearchWithRelations2<Certificate>,
    protected readonly genericSearchWithRelationsOptions2: GenericSearchWithRelations<Certificate>,
  ) {}

  // certificateF
  @Post('certificate')
  async CreateCertificate(@Body() certificateDto: CertificateDto) {
    return this.certificateService.createCertificate(certificateDto);
  }

  @Get('certificate')
  async findAllCertificates() {
    return this.certificateService.findAllCertificates();
  }

  @Get('certificate/:id')
  async findOneCertificate(@Param('id') id: string) {
    return this.certificateService.findOneCertificate(id);
  }

  @Get('certificate/allData/:id')
  async findOneCertificateAllData(@Param('id') id: string): Promise<any> {
    return this.certificateService.findOneCertificateAllData(id);
  }
  @Get('certificate/user/:userId')
  async findAllCertificateAllData(@Param('userId') userId: string) {
    return this.certificateService.findAllCertificateAllData(userId);
  }

  @Patch('certificate/:id')
  async updateCertificate(
    @Param('id') id: string,
    @Body() certificateDto: CertificateDto,
  ) {
    return this.certificateService.updateCertificate(id, certificateDto);
  }

  @Get('certificate/agency/:agencyCode')
  async findAllCertificateByAgency(@Param('agencyCode') agencyCode: string) {
    return this.certificateService.findAllCertificateByAgency(agencyCode);
  }

  @Patch('certificate/cancel/:id')
  async cancelCertificate(@Param('id') id: string) {
    return this.certificateService.cancelCertificate(id);
  }

  @Patch('certificate/updateStatus/:invoiceNumber')
  async updateStatusTo1OnCertificate(
    @Param('invoiceNumber') invoiceNumber: string,
  ) {
    return this.certificateService.updateStatusTo1OnCertificate(invoiceNumber);
  }

  @Delete('certificate/:id')
  async removeCertificate(@Param('id') id: string) {
    return this.certificateService.removeCertificate(id);
  }

  @Get('certificate/certificateNumber/search')
  async findCertificateByCertificateNumber(@Query('search') search: string) {
    const searchFields: (keyof Certificate)[] = ['certificateNumber'];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const relations: string[] = ['permitTypes', 'signatories', 'applications'];
    return this.genericSearch.search(
      this.certificateSearchRepository,
      searchFields,
      search,
      relations,
    );
  }

  @Get('certificate/applicantUserId/search')
  async searchCertificatesByUserId(@Query('userId') search: string) {
    const searchFields: (keyof Certificate)[] = ['applicantUserId'];
    const relations: string[] = ['applications', 'permitTypes'];

    return this.genericSearch.search(
      this.certificateSearchRepository,
      searchFields,
      search,
      relations,
    );
  }

  // async searchCertificatesByUserId(@Query('userId') userId: string) {
  //   const searchFields: (keyof Certificate)[] = ['applications'];
  //   const relations: string[] = ['applications'];
  //   return this.genericSearch2.search(
  //     this.certificateSearchRepository,
  //     searchFields,
  //     userId,
  //     relations,
  //   );
  // }

  // async searchCertificatesByUserId(@Query('search') search: string) {
  //   const searchFields: (keyof Certificate)[] = ['applications'];
  //   const relations: string[] = ['applications', 'permitTypes'];
  //   const dataToBeReturned =
  //     await this.genericSearchWithRelationsOptions2.search(
  //       this.certificateSearchRepository,
  //       searchFields,
  //       search,
  //       relations,
  //     );

  //   // Check if dataToBeReturned has items and applications field exists
  //   if (
  //     dataToBeReturned &&
  //     dataToBeReturned.items.length > 0 &&
  //     dataToBeReturned.items[0].applications
  //   ) {
  //     const applicantUserId = dataToBeReturned.items[0].applications.userId;
  //     const filteredDataItems = dataToBeReturned.items.filter(
  //       (item) => item.applications.userId === applicantUserId,
  //     );

  //     return filteredDataItems;
  //   } else {
  //     return []; // or appropriate error handling
  //   }
  // }

  @Get('certificate/cert/:applicantUserId')
  async getCertificatesByUserId(
    @Param('applicantUserId') applicantUserId: string,
  ) {
    return this.certificateService.findCertificateByApplicantUserId(
      applicantUserId,
    );
  }

  // signatory
  @Post('signatory')
  async CreateSignatory(@Body() signatoryDto: SignatoryDto) {
    return this.certificateService.createSignatory(signatoryDto);
  }

  @Get('signatory')
  async findAllSignatorys() {
    return this.certificateService.findAllSignatorys();
  }

  @Get('signatory/:id')
  async findOneSignatory(@Param('id') id: string) {
    return this.certificateService.findOneSignatory(id);
  }

  @Patch('signatory/:id')
  async updateSignatory(
    @Param('id') id: string,
    @Body() signatoryDto: SignatoryDto,
  ) {
    return this.certificateService.updateSignatory(id, signatoryDto);
  }

  @Delete('signatory/:id')
  async removeSignatory(@Param('id') id: string) {
    return this.certificateService.removeSignatory(id);
  }

  @Post('transfer')
  async TransferCertificate(
    @Body() transferCertificateDto: TransferCertificateDto,
  ) {
    return this.certificateService.transferCertificate(transferCertificateDto);
  }

  @Get('transfer')
  async findAllTransfers() {
    return this.certificateService.findAllTransfers();
  }

  @Get('transfer/:certificateNumber')
  async getTransferCertificateByCertificateNumber(
    @Param('certificateNumber') certificateNumber: string,
  ) {
    return this.certificateService.findTransferCertificateByCertificateNumber(
      certificateNumber,
    );
  }

  // developmentStatus
  @Post('developmentStatus')
  async DevelopmentStatus(@Body() developmentStatusDto: DevelopmentStatusDto) {
    return this.certificateService.createDevelopmentStatus(
      developmentStatusDto,
    );
  }

  @Get('developmentStatus')
  async findAllDevelopmentStatus() {
    return this.certificateService.findAllDevelopmentStatus();
  }

  @Get('developmentStatus/:id')
  async findOneDevelopmentStatus(@Param('id') id: string) {
    return this.certificateService.findOneDevelopmentStatus(id);
  }

  // Site Status
  @Post('siteStatus')
  async CreateSiteStatus(@Body() siteStatusDto: SiteStatusDto) {
    return this.certificateService.createSiteStatus(siteStatusDto);
  }

  @Get('siteStatus')
  async findAllSiteStatus() {
    return this.certificateService.findAllSiteStatus();
  }

  @Get('siteStatus/:id')
  async findOneSiteStatus(@Param('id') id: string) {
    return this.certificateService.findOneSiteStatus(id);
  }

  @Patch('siteStatus/:id')
  async updateSiteStatus(
    @Param('id') id: string,
    @Body() siteStatusDto: SiteStatusDto,
  ) {
    return this.certificateService.updateSiteStatus(id, siteStatusDto);
  }

  @Delete('siteStatus/:id')
  async removeSiteStatus(@Param('id') id: string) {
    return this.certificateService.removeSiteStatus(id);
  }

  // Random Inspection
  // @Post('randomInspection')
  // async CreateRandomInspection(
  //   @Body() randomInspectionDto: RandomInspectionDto,
  // ) {
  //   return this.certificateService.createRandomInspection(randomInspectionDto);
  // }

  // Random Inspection with a file
  @Post('randomInspection')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Create random inspection with file upload',
    type: RandomInspectionDto,
  })
  async CreateRandomInspection(
    @Body() randomInspectionDto: RandomInspectionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.certificateService.createRandomInspection(
      randomInspectionDto,
      file,
    );
  }

  @Get('expiring')
  async getExpiringCertificates() {
    return await this.certificateService.getExpiringCertificates();
  }

  @Get('expiringSendSMS')
  async getExpiringCertificatesAndSendSMS() {
    return await this.certificateService.getExpiringCertificatesAndSendSMS();
  }

  @Get('expiringSendNotifications')
  async getExpiringCertificatesAndSendNotifications() {
    return await this.certificateService.getExpiringCertificatesAndSendNotifications();
  }

  @Get('randomInspection')
  async findAllRandomInspection() {
    return this.certificateService.findAllRandomInspections();
  }

  @Get('randomInspection/:id')
  async findOneRandomInspection(@Param('id') id: string) {
    return this.certificateService.findOneRandomInspection(id);
  }

  @Get('randomInspection/allData/:id')
  async findOneRandomInspectionAllData(@Param('id') id: string) {
    return this.certificateService.findOneRandomInspectionAllData(id);
  }

  @Get('randomInspection/upi/:UPI')
  async findRandomInspectionAllDataByUPI(@Param('UPI') UPI: string) {
    return this.certificateService.findRandomInspectionAllDataByUPI(UPI);
  }

  @Get('randomInspection/siteStatus/:siteStatusId')
  async findRandomInspectionAllDataByStatus(
    @Param('siteStatusId') siteStatusId: string,
  ) {
    return this.certificateService.findRandomInspectionAllDataByStatus(
      siteStatusId,
    );
  }

  @Get('randomInspection/certificate/:certificateId')
  async findRandomInspectionByCertificateId(
    @Param('certificateId') certificateId: string,
  ) {
    return this.certificateService.findRandomInspectionByCertificateId(
      certificateId,
    );
  }

  // checking NCP certificate validity
  @ApiBody({
    description: 'UPI for check the certificate validity',
    schema: {
      type: 'object',
      properties: {
        upi: {
          type: 'string',
          format: 'string',
          example: '5/06/11/04/642',
          description: 'UPI for check the certificate validity',
        },
      },
    },
  })
  @Post('check-NCPCertificateValidity')
  public async isNCPCertificateValid(@Body('upi') upi: string) {
    return this.certificateService.isNCPCertificateValid(upi);
  }

  // checking Renew certificate validity
  @ApiBody({
    description: 'UPI for check the certificate validity',
    schema: {
      type: 'object',
      properties: {
        upi: {
          type: 'string',
          format: 'string',
          example: '5/06/11/04/642',
          description: 'UPI for check the certificate validity',
        },
      },
    },
  })
  @Post('check-IfRenewalOfNCPCertificateValidity')
  public async isRenewalForNCPCertificateValid(@Body('upi') upi: string) {
    return this.certificateService.isRenewalForNCPCertificateValid(upi);
  }

  @Get('certificate/application/:applicationId')
  async getCertificatesByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.certificateService.findCertificateByApplicationId(
      applicationId,
    );
  }
}
