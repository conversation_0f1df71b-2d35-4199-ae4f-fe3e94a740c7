import '../../../instrumentation';
import { NestFactory } from '@nestjs/core';
import { IntegrationsModule } from './integrations.module';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(IntegrationsModule);
  const configService = app.get(ConfigService);
  app.connectMicroservice({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: configService.get('TCP_PORT'),
    },
  });
  await app.startAllMicroservices();

  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));

  // -- Cors setup
  app.enableCors();

  // -- Swagger Documentation
  const config = new DocumentBuilder()
    .setTitle('Building Permit API')
    .setDescription(
      'API Integration to connect with external APIs for building permits.',
    )
    .addBasicAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // -- Port listening
  await app.listen(configService.get('HTTP_PORT'));
  // await app.listen(3007);
}
bootstrap();
