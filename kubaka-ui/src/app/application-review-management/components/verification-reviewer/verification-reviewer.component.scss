.profile {
  display: flex;
  gap: 1.5rem;
  &-pic {
    $pseizer: 11rem;
    width: $pseizer;
    height: calc($pseizer / (3 / 3.75));
    border: 4px solid #def1ff;
    border-radius: 0.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    .avt {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      height: 100%;
      object-fit: contain;
      margin: 0;
      z-index: 1;
    }
    img {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      height: 100%;
      object-fit: cover;
      margin: 0;
      z-index: 2;
    }
  }
  &-dtail {
    width: 100%;
    .dtail-out {
      margin-bottom: 0.5rem;
      flex: 1;
      .lebo {
        display: block;
        font-size: 0.85rem;
        font-weight: 400;
        color: #64748b;
        font-size: 0.875rem;
        line-height: 1.25;
      }
      .txt {
        display: block;
        font-weight: 500;
        color: #0f172a;
        font-size: 0.875rem;
        letter-spacing: 0;
        font-weight: 600;
        line-height: 1.5;
      }
    }
  }
}
