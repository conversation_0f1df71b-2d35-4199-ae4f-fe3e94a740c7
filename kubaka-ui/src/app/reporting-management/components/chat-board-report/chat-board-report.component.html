<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <!-- <div class="app-welnote">
                <div class="app-welnote_dtails">
                    <span class="prim-nt">Welcome!</span>
                    <h3>
                        <span> {{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
                    </h3>
                </div>
            </div> -->
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle">Chat board report</span>
            <div class="tbleFilter">
              <div class="form-input_search">
                <input type="text" name="searchTerm" [(ngModel)]="userService.searchTerm"
                  (ngModelChange)="onSearchTermChange($event)" placeholder="Search for something...">
                <button type="button" class="btn">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>Filter unread message</Strong> </label>
                <div>
                  <select name="filterValue" id="filterValue" [(ngModel)]="filterValue" (change)="filterByRead()">
                    <option value="false">Unread</option>
                    <option value="true">Read</option>
                    <!-- <option *ngFor="let r of utilService.tableArraySelector" [value]="r.id"> {{ r.id
                                            }} </option> -->
                  </select>
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>Filter </Strong> </label>
                <div>
                  <select name="reply" id="reply" [(ngModel)]="searchData.reply" (change)="filterByIsReply()">
                    <option value="0">Not Replied</option>
                    <option value="1">Replied</option>
                    <!-- <option *ngFor="let r of utilService.tableArraySelector" [value]="r.id"> {{ r.id
                                            }} </option> -->
                  </select>
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>From date</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateFrom" [(ngModel)]="searchData.dateFrom"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>To date</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateTo" [(ngModel)]="searchData.dateTo"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
            </div>
          </div>
          <ul class="tblist">
            <li class="tblist-item" *ngFor="let li of lists  | genericfilter: userService.searchTerm">
              <div class="tblist-item_icon bg-l-o">
                <img src="assets/ikons/colored/ikon-conspaper.svg" alt="" />
              </div>

              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">Phone number</span> {{ li.phoneNumber }} </span>
                <span>
                  <span class="ttl">Reply Status</span> {{ li.isReply == '0' ?'NO' : 'YES' }} </span>
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">Message</span> {{ li.content }} </span>
                <!-- <span>
                                    <span class="ttl">Agency Code</span> {{ li.applications.agencyCode }} </span> -->
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Phone number</span> {{ li.phoneNumber }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Issue Category</span> {{ li.issueCategory.name }} </span>
                <span>
                  <span class="ttl">Sent On</span> {{ li.createdAt | date:'short' }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">Is Read</span> {{ li.isRead === 'true' || li.isRead ? 'Yes' : 'No' }} </span>
                <span>
                  <span class="ttl">Agency</span> {{ li?.agencyName }} </span>
              </div>

<!-- 
              <div class="tblist-item_xcn">
                <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Reply to chats" data-bs-toggle="modal"
                  data-bs-target="#showModal" (click)="openModal(openChatContent, 'lg', li)"> Reply </a>
              </div> -->


              <div class="kbk-table-dropdown">
                <button class="kbk-link-btn">More</button>
                <div class="kbk-table-dropdown_list">



                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Reply to chats" data-bs-toggle="modal"
                    data-bs-target="#showModal" (click)="openModal(openChatContent, 'lg', li)"> Reply </a>


                  <a class="kbk-btn kbk-btn-sec kbk-btn-sm" (click)="verifyUser()">Verify</a>
                </div>
              </div>



            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} - {{endIndex}}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="getPremiumData();">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #openChatContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Reply to message</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="step-panel_body">
    <div class="pg-chatroom">
      <div class="chatcontent">
        <form [formGroup]="messageForm" (ngSubmit)="onSubmit()">
          <div class="chatwrap">
            <div class="chathder">
              <div class="user-avtar">
                <img src="assets/imgs/profile1.svg" alt="profile picture" />
              </div>
              <div class="user-txt">
                <!-- <h6>{{userData.lastName | titlecase}} {{userData.firstName | titlecase}}</h6> -->
                <span class="status offline"></span>
              </div>
            </div>
            <div class="chatbdy">
              <div class="chatbox">
                <div #chatContainer class="chatbox-bdy">
                  <!-- Loop through all messages -->
                  <ng-container *ngFor="let message of messages">
                    <!-- Incoming messages (where current user is the receiver) -->
                    <!-- <div class="chatbox_msg" *ngIf="message.senderId !== this.currentUser.userId"> -->
                    <div class="chatbox_msg" *ngIf="message.isReply === '0'">
                      <div class="msgbox">
                        <div class="msgbox-txt">
                          <p>{{ message.content }}</p>
                        </div>
                        <!-- Attachments for Chat Messages -->
                        <div class="kbkchat-nonreply kbkchat-attachments"
                          *ngIf="message.documents && message.documents.length > 0">
                          <div class="kbkchat-attachment-item" *ngFor="let doc of message.documents">
                            <div class="kbkchat-attachment-content" (click)="previewDocument(doc)">
                              <img *ngIf="isImageFile(doc.fileName)"
                                [src]="(attachementsServerUrl + doc.fileName) | filePreview"
                                [alt]="doc?.fileName || 'icon'" class="file-image" (loan)="onImageLoad()" />
                              <!-- Icon preview if it's not an image -->
                              <div *ngIf="!isImageFile(doc.fileName)" class="file-icon-preview">
                                <img [src]="getFileIcon(doc.fileName)" [alt]="doc?.fileName || 'icon'" />
                              </div>

                              <!-- File metadata: icon, name, size -->
                              <div class="kbkchat-file-info">
                                <img [src]="getFileIcon(doc.fileName)" [alt]="doc?.fileName || 'icon'"
                                  class="kbkchat-file-icon" />
                                <div>
                                  <span class="kbkchat-file-name">{{ doc.fileName || '' }}</span>
                                </div>
                              </div>
                              <i class="fas fa-download kbkchat-download-icon"></i>
                            </div>
                          </div>
                        </div>
                        <div class="msgbox-tm">
                          <span class="tm">{{ message.createdAt | date:'short'}}</span>
                          <span class="typ">Received</span>
                        </div>
                      </div>
                    </div>
                    <div class="chatbox_msg outmsg" *ngIf="message.isReply === '1'">
                      <div class="msgbox">
                        <div class="msgbox-txt">
                          <p>{{ message.content }}</p>
                        </div>
                        <!-- Attachments for Chat Messages -->
                        <div class="kbkchat-reply kbkchat-attachments"
                          *ngIf="message.documents && message.documents.length > 0">
                          <div class="kbkchat-attachment-item" *ngFor="let doc of message.documents">
                            <div class="kbkchat-attachment-content" (click)="previewDocument(doc)">
                              <img *ngIf="isImageFile(doc.fileName)"
                                [src]="(attachementsServerUrl + doc.fileName) | filePreview"
                                [alt]="doc?.fileName || 'icon'" class="file-image" (loan)="onImageLoad()" />
                              <!-- Icon preview if it's not an image -->
                              <div *ngIf="!isImageFile(doc.fileName)" class="file-icon-preview">
                                <img [src]="getFileIcon(doc.fileName)" [alt]="doc?.fileName || 'icon'" />
                              </div>

                              <!-- File metadata: icon, name, size -->
                              <div class="kbkchat-file-info">
                                <img [src]="getFileIcon(doc.fileName)" [alt]="doc?.fileName || 'icon'"
                                  class="kbkchat-file-icon" />
                                <div>
                                  <span class="kbkchat-file-name">{{ doc.fileName || '' }}</span>
                                </div>
                              </div>
                              <i class="fas fa-download kbkchat-download-icon"></i>
                            </div>
                          </div>
                        </div>
                        <div class="msgbox-tm">
                          <span class="tm">{{ message.createdAt | date:'short' }}</span>
                          <span class="typ">Sent</span>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>
            </div>
            <div class="selected-files-preview" *ngIf="selectedFiles.length > 0">
              <div *ngFor="let file of selectedFiles; let i = index" class="file-preview-wrapper">
                <div class="file-thumb" [title]="file.name">
                  <img *ngIf="file.type.startsWith('image/')" [src]="file | filePreview" alt="Image"
                    class="file-image" />
                  <div *ngIf="!file.type.startsWith('image/')" class="file-icon-preview">
                    <img [src]="getFileIcon(file.name)" alt="File icon" />
                  </div>
                  <span class="file-name">{{ file.name }}</span>
                  <button class="remove-btn" (click)="removeFile(i)" type="button">✕</button>
                </div>
              </div>
            </div>
            <div class="chatfter">
              <div class="form-input">
                <div>
                  <textarea name="content" id="content" formControlName="content"></textarea>
                </div>
              </div>
              <!-- Attachment Icon-->
              <label class="kbk-btn kbk-btn-sec" for="fileUpload">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="attach">
                      <rect class="cls-1" width="24" height="24" />
                      <path class="cls-2"
                        d="M9.29 21a6.23 6.23 0 0 1-4.43-1.88 6 6 0 0 1-.22-8.49L12 3.2A4.11 4.11 0 0 1 15 2a4.48 4.48 0 0 1 3.19 1.35 4.36 4.36 0 0 1 .15 6.13l-7.4 7.43a2.54 2.54 0 0 1-1.81.75 2.72 2.72 0 0 1-1.95-.82 2.68 2.68 0 0 1-.08-3.77l6.83-6.86a1 1 0 0 1 1.37 1.41l-6.83 6.86a.68.68 0 0 0 .*********** 0 0 0 .*********** 0 0 0 .4-.16l7.39-7.43a2.36 2.36 0 0 0-.15-3.31 2.38 2.38 0 0 0-3.27-.15L6.06 12a4 4 0 0 0 .22 5.67 4.22 4.22 0 0 0 3 1.29 3.67 3.67 0 0 0 2.61-1.06l7.39-7.43a1 1 0 1 1 1.42 1.41l-7.39 7.43A5.65 5.65 0 0 1 9.29 21z" />
                    </g>
                  </g>
                </svg>
              </label>
              <input type="file" id="fileUpload" (change)="onFileSelected($event)" hidden multiple />
              <!-- -->
              <button type="submit" class="kbk-btn kbk-btn-sec">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g id="Layer_2" data-name="Layer 2">
                    <g id="Layer_1-2" data-name="Layer 1">
                      <g id="ikon-send">
                        <rect class="cls-1" width="24" height="24" />
                        <path class="cls-2"
                          d="M21.49,3.44a1.19,1.19,0,0,0,0-.26.36.36,0,0,0,0-.09,1.17,1.17,0,0,0-.19-.3,1.17,1.17,0,0,0-.3-.19l-.09,0a1.09,1.09,0,0,0-.26-.05h-.08a1.07,1.07,0,0,0-.31.06l-17,5.95a1,1,0,0,0-.08,1.85l7.3,3.25,3.25,7.3a1,1,0,0,0,.91.59h0a1,1,0,0,0,.9-.67l6-17a1.07,1.07,0,0,0,.06-.31S21.49,3.47,21.49,3.44ZM6.21,9.56,16.69,5.89l-5.76,5.77Zm8.23,8.23-2.1-4.72,5.77-5.76Z" />
                      </g>
                    </g>
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</ng-template>
<div class="app-dash" style="display: none;">
  <div class="container">
    <div class="app-main">
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle">Chat board</span>
          </div>
          <ag-grid-angular class="ag-theme-quartz" style="height: 500px;" [rowData]="rowData" [columnDefs]="colDefs"
            [columnDefs]="colDefs" [defaultColDef]="defaultColDef" [pagination]="true" [rowSelection]="'multiple'"
            (gridReady)="onGridReady($event)" (cellValueChanged)="onCellValueChanged($event)"
            (selectionChanged)="onSelectionChanged($event)">
          </ag-grid-angular>
        </div>
      </div>
    </div>
  </div>
</div>