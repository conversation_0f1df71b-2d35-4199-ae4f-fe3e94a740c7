import { Module } from '@nestjs/common';
import { ApplicationService } from './application.service';
import {
  ApplicationStatusRepository,
  ApplicationRepository,
  AssigneeRepository,
  AssociatedUPIsRepository,
  BuildTypesRepository,
  CategoryRulesRepository,
  CategoryTypesRepository,
  DocumentTypesRepository,
  PermitTypesRepository,
  ProjectStatusRepository,
  ProjectsRepository,
  RequiredDocumentsRepository,
  TechnologySurveysRepository,
  AnswerRepository,
  // ConstructionMethodRepository,
  // EquipmentCapacityRepository,
  OtherInfoApplicationRepository,
  PermitQuestionRepository,
  QuestionCategoryRepository,
  ReviewersOnApplicationRepository,
  FoundationInspectionRepository,
  OccupancyInspectionRepository,
  SubmissionLogRepository,
} from './application.repository';
import { ApplicationController } from './application.controller';
import { DatabaseModule } from '@app/common';
import {
  Answer,
  Application,
  Assignee,
  AssociatedUPI,
  FoundationInspection,
  OccupancyInspection,
  OtherInfoApplication,
  PermitQuestion,
  Project,
  ReviewersOnApplication,
  SubmissionLog,
} from '../entities/application.entity';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import {
  AUTH_SERVICE,
  DOCUMENTS_SERVICE,
  NOTIFICATIONS_SERVICE,
  PAYMENTS_SERVICE,
} from '@app/common/constants';
import {
  PermitType,
  CategoryRule,
  CategoryType,
  BuildType,
  RequiredDocument,
  DocumentType,
  TechnologySurvey,
  ApplicationStatus,
  ProjectStatus,
  // EquipmentCapacity,
  // ConstructionMethod,
  QuestionCategory,
} from '../entities/settings.entity';
import {
  GenericSearch,
  GenericSearchWithMany,
  GenericSearchWithRelations,
  GenericSearchWithRelations2,
} from './generic-search.service';
import { Repository } from 'typeorm';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      // application
      Project,
      Application,
      AssociatedUPI,

      // for other info
      OtherInfoApplication,
      PermitQuestion,
      Answer,

      // settings
      PermitType,
      CategoryRule,
      CategoryType,
      BuildType,
      DocumentType,
      RequiredDocument,
      TechnologySurvey,
      ApplicationStatus,
      ProjectStatus,
      Assignee,

      // Setting for other info
      // EquipmentCapacity,
      // ConstructionMethod,
      QuestionCategory,

      ReviewersOnApplication,

      FoundationInspection,
      OccupancyInspection,

      SubmissionLog,
    ]),

    ConfigModule.forRoot({
      isGlobal: true,
    }),

    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: PAYMENTS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('PAYMENTS_HOST'),
            port: configService.get('PAYMENTS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: NOTIFICATIONS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATIONS_HOST'),
            port: configService.get('NOTIFICATIONS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: DOCUMENTS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('DOCUMENTS_HOST'),
            port: configService.get('DOCUMENTS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [ApplicationController],
  providers: [
    ApplicationService,
    GenericSearch,
    GenericSearchWithMany,
    GenericSearchWithRelations,
    GenericSearchWithRelations2,
    Repository,

    ProjectsRepository,
    ApplicationRepository,
    AssociatedUPIsRepository,

    PermitTypesRepository,
    CategoryRulesRepository,
    CategoryTypesRepository,
    BuildTypesRepository,
    DocumentTypesRepository,
    RequiredDocumentsRepository,
    TechnologySurveysRepository,
    ApplicationStatusRepository,
    ProjectStatusRepository,
    AssigneeRepository,
    OtherInfoApplicationRepository,
    PermitQuestionRepository,
    AnswerRepository,
    QuestionCategoryRepository,
    // ConstructionMethodRepository,
    // EquipmentCapacityRepository,
    ReviewersOnApplicationRepository,

    OccupancyInspectionRepository,
    FoundationInspectionRepository,

    SubmissionLogRepository,
  ],
  exports: [ApplicationModule, ApplicationService],
})
export class ApplicationModule {}
