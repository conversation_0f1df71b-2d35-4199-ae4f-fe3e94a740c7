import * as jwt from 'jsonwebtoken';
// import { default as config } from '../config';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { User } from './entities/user-management/user.entity';
import config from '../config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
// import passport from 'passport';

@Injectable()
export class JWTService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}
  async createToken(
    email,
    id,
    firstName,
    lastName,
    licenceIdEng,
    licenceIdArch,
    nationalId,
    passport,
    phoneNumber,
    isPasswordExpired,
    isStaffPasswordChanged,
  ) {
    const expiresIn = config.jwt.expiresIn,
      secretOrKey = config.jwt.secretOrKey;
    const userInfo = {
      Email: email,
      UserId: id,
      FirstName: firstName,
      LastName: lastName,
      NationalId: nationalId,
      Passport: passport,
      PhoneNumber: phoneNumber,
      LicenseEngineer: licenceIdEng,
      LicenseArchitect: licenceIdArch,
      IsPasswordExpired: isPasswordExpired,
      IsStaffPasswordChanged: isStaffPasswordChanged,
    };
    const token = jwt.sign(userInfo, secretOrKey, { expiresIn });
    return {
      expires_in: expiresIn,
      access_token: token,
    };
  }

  async validateUser(signedUser): Promise<User> {
    const userFromDb = await this.userRepository.findOne({
      where: { email: signedUser.email },
    });
    if (userFromDb) {
      return userFromDb;
    }
    return null;
  }

  // validate the token
  async validateToken(token: string): Promise<any> {
    try {
      return jwt.verify(token, config.jwt.secretOrKey);
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
