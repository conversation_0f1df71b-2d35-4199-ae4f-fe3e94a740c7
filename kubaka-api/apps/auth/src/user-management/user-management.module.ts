import { forwardRef, Module } from '@nestjs/common';
import { UserManagementController } from './user-management.controller';
import { UserManagementService } from './user-management.service';
import { DatabaseModule } from '@app/common';

import { Role } from 'apps/auth/src/entities/user-management/role.entity';
import { UserType } from 'apps/auth/src/entities/user-management/userType.entity';
import { UserActivity } from 'apps/auth/src/entities/user-management/userActivity.entity';
import { CivilStatus } from 'apps/auth/src/entities/user-management/civilStatus.entity';
import { Permission } from 'apps/auth/src/entities/user-management/permission.entity';
import { Country } from 'apps/auth/src/entities/delimitation/country.entity';
import { Province } from 'apps/auth/src/entities/delimitation/province.entity';
import { District } from 'apps/auth/src/entities/delimitation/district.entity';
import { Sector } from 'apps/auth/src/entities/delimitation/sector.entity';
import { Cell } from 'apps/auth/src/entities/delimitation/cell.entity';
import { Village } from 'apps/auth/src/entities/delimitation/village.entity';
import { Agency } from 'apps/auth/src/entities/agency/agency.entity';
import { AgencyDistrict } from 'apps/auth/src/entities/agency/agencyDistrict.entity';
import {
  AgencyDistrictRepository,
  AgencyRepository,
  CellRepository,
  CivilStatusRepository,
  CountryRepository,
  DistrictRepository,
  ProvinceRepository,
  RoleRepository,
  SectorRepository,
  UserActivityRepository,
  UserTypeRepository,
  UserRepository,
  VillageRepository,
} from './user-management.repository';
import { User } from '../entities/user-management/user.entity';
import { GenericSearch, GenericSearch2 } from '../generic-search.service';
import { MulterModule } from '@nestjs/platform-express';
import { AuthModule } from '../auth.module';

@Module({
  imports: [
    MulterModule.register({
      dest: './uploads', // Optional: Temporary file storage location
    }),
    DatabaseModule,
    DatabaseModule.forFeature([
      User,

      Role,
      UserType,
      UserActivity,
      CivilStatus,
      Permission,

      Country,
      Province,
      District,
      Sector,
      Cell,
      Village,

      Agency,
      AgencyDistrict,
    ]),
    forwardRef(() => AuthModule),
  ],
  controllers: [UserManagementController],
  providers: [
    UserManagementService,

    UserRepository,

    RoleRepository,
    UserActivityRepository,
    UserTypeRepository,
    CivilStatusRepository,
    // PermissionRepository,

    CountryRepository,
    ProvinceRepository,
    DistrictRepository,
    SectorRepository,
    CellRepository,
    VillageRepository,

    AgencyDistrictRepository,
    AgencyRepository,

    GenericSearch,
    GenericSearch2,
  ],
  exports: [UserManagementService],
})
export class UserManagementModule {}
