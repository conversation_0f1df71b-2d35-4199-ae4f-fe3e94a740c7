import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NgbAccordionModule, NgbDropdownModule, NgbModule, NgbNavModule, } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { AgChartsAngularModule } from 'ag-charts-angular';

import { AgGridModule } from 'ag-grid-angular';
import { HighchartsChartModule } from 'highcharts-angular';
import { HeaderNotificationComponent } from 'src/app/account-layout/components/header-notification/header-notification.component';
import { AssignApplicationToUserComponent } from 'src/app/application-review-management/components/assign-application-to-user/assign-application-to-user.component';
import { AssociatedUpiApplicationComponent } from 'src/app/application-review-management/components/associated-upi-application/associated-upi-application.component';
import { FoundationInspectionComponent } from 'src/app/application-review-management/components/foundation-inspection/foundation-inspection.component';
import { GeneralInspectionComponent } from 'src/app/application-review-management/components/general-inspection/general-inspection.component';
import { OccupancyInspectionComponent } from 'src/app/application-review-management/components/occupancy-inspection/occupancy-inspection.component';
import { ReassignReviewersComponent } from 'src/app/application-review-management/components/reassign-reviewers/reassign-reviewers.component';
import { GeneralSearchComponent } from 'src/app/application-review-management/general-search/general-search.component';
import { AdditionalUploadFileComponent } from 'src/app/application/components/additional-upload-file/additional-upload-file.component';
import { EiaFormCheckerComponent } from 'src/app/application/components/eia-form-checker/eia-form-checker.component';
import { OtherApplicationPermitAnswerComponent } from 'src/app/application/components/other-application-permit-answer/other-application-permit-answer.component';
import { GenericApprovalComponent } from 'src/app/approval-management/generic-approval/generic-approval.component';
import { UpiInfoComponent } from 'src/app/auth-pages/components/upi-info/upi-info.component';
import { DevelopmentStatusComponent } from 'src/app/certificate-management/components/development-status/development-status.component';
import { ChatBotComponent } from 'src/app/notification-management/chat-bot/chat-bot.component';
import { AssignToEngineerFormComponent } from '../../../application/components/assignment-to-eng-architect/assign-to-engineer-form/assign-to-engineer-form.component';
import { InputFileUploadComponent } from '../../components/input-file-upload/input-file-upload.component';
import { OneFieldFormComponent } from '../../components/one-field-form/one-field-form.component';
import { SpinnerComponent } from '../../components/spinner/spinner.component';
import { ViewDocumentComponent } from '../../components/view-document/view-document.component';
import { ViewPlotOnMapComponent } from '../../components/view-plot-on-map/view-plot-on-map.component';
import { CommaSeparatorDirective, CurrencyFormatterDirective, NoNegativeDirective, NoSpacesDirective } from '../../directives/currency-formatter.directive';
import { NgbdAdvancedSortableHeader } from '../../directives/sortable.directive';
import { FilterPipe, FilePreviewPipe } from '../../pipes/generic-filter.pipe';
import { LowercaseEmailDirective } from '../../directives/mixed-formatter.directive';
import { SearchedUpiComponent } from 'src/app/dashaboard-management/component/searched-upi/searched-upi.component';
import { SearchAssociatedUpiComponent } from 'src/app/dashaboard-management/component/search-associated-upi/search-associated-upi.component';
// import { TextMaskModule } from 'angular2-text-mask';


@NgModule({
  declarations: [
    InputFileUploadComponent,
    NgbdAdvancedSortableHeader,
    UpiInfoComponent,
    FilterPipe,
    FilePreviewPipe,
    OneFieldFormComponent,
    ViewDocumentComponent,
    GenericApprovalComponent,
    EiaFormCheckerComponent,
    AssignToEngineerFormComponent,
    SpinnerComponent,
    ViewPlotOnMapComponent,
    OtherApplicationPermitAnswerComponent,
    FoundationInspectionComponent,
    GeneralInspectionComponent,
    OccupancyInspectionComponent,
    AssociatedUpiApplicationComponent,
    DevelopmentStatusComponent,
    CurrencyFormatterDirective,
    CommaSeparatorDirective,
    NoNegativeDirective,
    ChatBotComponent,
    AssignApplicationToUserComponent,
    HeaderNotificationComponent,
    ReassignReviewersComponent,
    GeneralSearchComponent,
    AdditionalUploadFileComponent,
    NoSpacesDirective,
    LowercaseEmailDirective,
    SearchedUpiComponent,
    SearchAssociatedUpiComponent,

  ],
  imports: [
    ReactiveFormsModule,
    // AgChartsAngularModule,
    AgGridModule,
    CommonModule,
    GoogleMapsModule,
    MatSnackBarModule,
    MatFormFieldModule,
    NgbNavModule,
    TranslateModule,
    FormsModule,
    NgbAccordionModule,
    NgbDropdownModule,
    // TextMaskModule,
    NgbModule,

  ],
  exports: [
    CommonModule,
    GoogleMapsModule,
    TranslateModule,
    // TextMaskModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    HighchartsChartModule,
    FormsModule,
    MatSnackBarModule,
    NgbNavModule,
    NgbAccordionModule,
    NgbDropdownModule,
    AgChartsAngularModule,
    NgbModule,
    NgbdAdvancedSortableHeader,
    AgGridModule,
    InputFileUploadComponent,
    FilterPipe,
    FilePreviewPipe,
    UpiInfoComponent,
    OneFieldFormComponent,
    ViewDocumentComponent,
    GenericApprovalComponent,
    EiaFormCheckerComponent,
    AssignToEngineerFormComponent,
    SpinnerComponent,
    ViewPlotOnMapComponent,
    OtherApplicationPermitAnswerComponent,
    FoundationInspectionComponent,
    GeneralInspectionComponent,
    OccupancyInspectionComponent,
    AssociatedUpiApplicationComponent,
    DevelopmentStatusComponent,
    CurrencyFormatterDirective,
    CommaSeparatorDirective,
    NoNegativeDirective,
    ChatBotComponent,
    AssignApplicationToUserComponent,
    HeaderNotificationComponent,
    ReassignReviewersComponent,
    GeneralSearchComponent,
    AdditionalUploadFileComponent,
    NoSpacesDirective,
    LowercaseEmailDirective,
    SearchedUpiComponent,
    SearchAssociatedUpiComponent
  ]
})
export class SharedModule { }
