import { AbstractEntity } from '@app/common';
import { Column, Entity, OneToMany } from 'typeorm';
import { Province } from './province.entity';

@Entity()
export class Country extends AbstractEntity<Country> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;

  @Column()
  code: string;

  @OneToMany(() => Province, (province) => province.country)
  provinces: Province[];
}
