import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
// import { Transform } from 'class-transformer';

export class CalculateCategoryDto {
  @ApiProperty({
    description: 'The field permitTypeId is  mandatory',
    required: true,
  })
  @IsNotEmpty({ message: 'The field permitTypeId is required' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  permitTypeId: string | null = null;

  @ApiProperty({
    description: 'The field buildTypeId is  mandatory',
    required: true,
  })
  @IsNotEmpty({ message: 'The field buildTypeId is mandatory' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  buildTypeId: string | null = null;

  @ApiProperty({
    description: 'The field buildUpArea is mandatory',
    required: true,
  })
  @IsNotEmpty({ message: 'The field buildUpArea is mandatory' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  buildUpArea: number | null = null;

  @ApiProperty({
    description: 'The field numberOfFloors is mandatory',
    required: true,
  })
  @IsNotEmpty({ message: 'The field numberOfFloors is mandatory' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  numberOfFloors: number | null = null;

  @ApiProperty({
    description: 'The field capacityInformation is  mandatory',
    required: true,
  })
  @IsNotEmpty({ message: 'The field capacityInformation is mandatory' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  capacityInformation: number | null = null;

  @ApiProperty({
    description: 'The field plotSize is  mandatory',
    required: true,
  })
  @IsOptional({ message: 'The field plotSize is mandatory' })
  // @Transform(({ value }) => (value === 'string' || value === 0 ? null : value))
  plotSize: number | null = null;
}
