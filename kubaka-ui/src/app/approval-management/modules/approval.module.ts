import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { ApprovalLevelComponent } from '../approval-level/approval-level.component';
import { ApprovalStatusComponent } from '../approval-status/approval-status.component';


export const approvalRoutes: Routes = [
  { path: 'level', component: ApprovalLevelComponent },
  { path: 'status', component: ApprovalStatusComponent },
]


@NgModule({
  declarations: [
    ApprovalLevelComponent,
    ApprovalStatusComponent,

  ],
  imports: [
    CommonModule,
    RouterModule.forChild(approvalRoutes),
    SharedModule
  ],
  exports: [RouterModule,
  ]
})
export class ApprovalModule { }
