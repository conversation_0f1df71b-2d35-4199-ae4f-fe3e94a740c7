import {
  Answer,
  Application,
  Assignee,
  AssociatedUPI,
  FoundationInspection,
  OccupancyInspection,
  OtherInfoApplication,
  PermitQuestion,
  Project,
  ReviewersOnApplication,
  SubmissionLog,
} from '../entities/application.entity';
import {
  AUTH_SERVICE,
  // NOTIFICATIONS_SERVICE,
  // PAYMENTS_SERVICE,
} from '@app/common/constants';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
// import { ClientProxy, EventPattern } from '@nestjs/microservices';
import {
  ApplicationStatusRepository,
  ApplicationRepository,
  AssigneeRepository,
  AssociatedUPIsRepository,
  BuildTypesRepository,
  CategoryRulesRepository,
  CategoryTypesRepository,
  DocumentTypesRepository,
  PermitTypesRepository,
  ProjectStatusRepository,
  ProjectsRepository,
  RequiredDocumentsRepository,
  TechnologySurveysRepository,
  // EquipmentCapacityRepository,
  // ConstructionMethodRepository,
  QuestionCategoryRepository,
  OtherInfoApplicationRepository,
  PermitQuestionRepository,
  AnswerRepository,
  ReviewersOnApplicationRepository,
  FoundationInspectionRepository,
  OccupancyInspectionRepository,
} from './application.repository';
import {
  AnswerDto,
  ApplicationDto,
  ApplicationDtoUpdate,
  AssigneeDto,
  AssociatedUPIDto,
  DataForChartDto,
  DeleteEngineerOnProjectDto,
  FoundationInspectionDto,
  LockApplicationDtoUpdate,
  OccupancyInspectionDto,
  OtherInfoApplicationDto,
  PermitQuestionDto,
  ProjectDto,
  ProjectDtoGet,
  // ProjectDtoGet,
  RejectProjectDto,
  ReviewersOnApplicationDto,
  UpdateAssigneeDto,
  ApplicationResubmitIremboDto,
} from '../dto/application.dto';
import {
  ApplicationStatus,
  BuildType,
  CategoryRule,
  CategoryType,
  DocumentType,
  PermitType,
  ProjectStatus,
  QuestionCategory,
  RequiredDocument,
  TechnologySurvey,
} from '../entities/settings.entity';
import {
  ApplicationStatusDto,
  BuildTypeDto,
  CategoryRuleDto,
  CategoryTypeDto,
  DocumentTypeDto,
  PermitTypeDto,
  ProjectStatusDto,
  QuestionCategoryDto,
  RequiredDocumentDto,
  TechnologySurveyDto,
} from '../dto/settings.dto';
import { DeepPartial, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
// import { PageOptionsDto } from '@app/common/dto/page-options.dto';
import { PageDto } from '@app/common/dto/page.dto';
import { PageMetaDto } from '@app/common/dto/page-meta.dto';

import axios from 'axios';

import config from '../../config';
import { ReviewerReportDto } from '../dto/approval.dto';
import { CalculateCategoryDto } from '../dto/calculatecategory.dto';
import { PageOptionsDto } from '@app/common/dto/page-obptions.dto';
// import { AgencyRepository } from 'apps/auth/src/user-management/user-management.repository';

export interface ApplicationWithUserDetails {
  id: string;
  userDetails: any;
  senderDetails: any;
}

@Injectable()
export class ApplicationService {
  constructor(
    // @Inject(PAYMENTS_SERVICE)
    // private readonly paymentsService: ClientProxy,
    // @Inject(NOTIFICATIONS_SERVICE)
    // private readonly notificationsServiceService: ClientProxy,
    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,
    // Note: DOCUMENTS_SERVICE injection removed as we use direct HTTP calls

    // application
    private readonly projectsRepository: ProjectsRepository,
    private readonly applicationRepository: ApplicationRepository,
    private readonly associatedUPIsRepository: AssociatedUPIsRepository,

    public readonly otherInfoApplicationRepository: OtherInfoApplicationRepository,
    // otherInfoApplication manager
    @InjectRepository(OtherInfoApplication)
    public readonly otherInfoApplicationEntityManagerRepository: Repository<OtherInfoApplication>,
    public readonly permitQuestionRepository: PermitQuestionRepository,
    public readonly answerRepository: AnswerRepository,

    public readonly reviewersOnApplicationRepository: ReviewersOnApplicationRepository,

    // public readonly submissionLogRepository: SubmissionLogRepository,

    @InjectRepository(Project)
    private projectEntityManagerRepository: Repository<Project>,

    @InjectRepository(SubmissionLog)
    private submissionLogRepository: Repository<SubmissionLog>,

    @InjectRepository(Application)
    private applicationEntityManagerRepository: Repository<Application>,

    @InjectRepository(ApplicationStatus)
    private applicationStatusEntityManagerRepository: Repository<ApplicationStatus>,

    @InjectRepository(RequiredDocument)
    private requiredDocumentEntityManagerRepository: Repository<RequiredDocument>,

    @InjectRepository(Assignee)
    private assigneeEntityManagerRepository: Repository<Assignee>,

    @InjectRepository(PermitQuestion)
    private permitQuestionEntityManagerRepository: Repository<PermitQuestion>,

    @InjectRepository(ReviewersOnApplication)
    private reviewersOnApplicationEntityRepository: Repository<ReviewersOnApplication>,

    @InjectRepository(OccupancyInspection)
    private occupancyInspectionEntityRepository: Repository<OccupancyInspection>,

    @InjectRepository(FoundationInspection)
    private foundationInspectionEntityRepository: Repository<FoundationInspection>,

    @InjectRepository(AssociatedUPI)
    private associatedUPIEntityRepository: Repository<AssociatedUPI>,

    // setting
    public readonly permitTypesRepository: PermitTypesRepository,
    private readonly categoryRulesRepository: CategoryRulesRepository,
    private readonly categoryTypesRepository: CategoryTypesRepository,
    private readonly buildTypesRepository: BuildTypesRepository,
    private readonly documentTypesRepository: DocumentTypesRepository,
    private readonly requiredDocumentsRepository: RequiredDocumentsRepository,
    private readonly technologySurveysRepository: TechnologySurveysRepository,
    private readonly applicationStatusRepository: ApplicationStatusRepository,
    private readonly projectStatusRepository: ProjectStatusRepository,
    private readonly assigneeRepository: AssigneeRepository,

    public readonly questionCategoryRepository: QuestionCategoryRepository,
    // public readonly equipmentCapacityRepository: EquipmentCapacityRepository,
    // public readonly constructionMethodRepository: ConstructionMethodRepository,

    public readonly occupancyInspectionRepository: OccupancyInspectionRepository,
    public readonly foundationInspectionRepository: FoundationInspectionRepository,
    // private readonly agencyRepository: AgencyRepository,
  ) {}

  // application
  // project

  // @EventPattern('checkApplicationData')
  // async checkApplicationData(applicationId: string) {
  //   return await this.applicationEntityManagerRepository.findOne({
  //     where: { id: applicationId },
  //   });
  // }

  async checkUserExists(userId: string): Promise<boolean> {
    return this.authService
      .send<boolean>({ cmd: 'userExists' }, userId)
      .toPromise();
  }

  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }
  async checkUserData(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }

  async checkAgencyDataById(agencyId: string) {
    return this.authService
      .send<any>({ cmd: 'checkAgencyDataById' }, agencyId)
      .toPromise();
  }

  async checkAgencyDataDistrictCode(districtCode: string) {
    return this.authService
      .send<any>({ cmd: 'checkAgencyDataByDistrictCode' }, districtCode)
      .toPromise();
  }

  async checkAgencyExists2(agencyId: string): Promise<boolean> {
    return this.authService
      .send<boolean>({ cmd: 'agencyExists' }, agencyId)
      .toPromise();
  }

  async checkAgencyExists(agencyId: string) {
    return this.authService
      .send<boolean>({ cmd: 'agencyExists' }, agencyId)
      .toPromise();
  }

  // async createProject(projectDto: ProjectDto) {
  //   // const upiFromDb = await this.projectsRepository.findOne({
  //   //   upi: projectDto.upi,
  //   // });
  //   // if (upiFromDb)
  //   //   throw new HttpException('UPI Already Exist', HttpStatus.BAD_REQUEST);

  //   const dataFromDb2 = await this.projectStatusRepository.findOne({
  //     id: projectDto.projectStatusId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Project Status Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const project = new Project({
  //     ...projectDto,
  //     projectStatus: (projectDto.projectStatusId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //   });

  //   return this.projectsRepository.create(project);
  // }

  async createProject(projectDto: ProjectDto) {
    // const upiFromDb = await this.projectsRepository.findOne({
    //   upi: projectDto.upi,
    // });
    // if (upiFromDb)
    //   throw new HttpException('UPI Already Exist', HttpStatus.BAD_REQUEST);
    const agencyData = await this.checkAgencyDataDistrictCode(
      projectDto.districtCode,
    );

    if (!agencyData) {
      throw new HttpException('Agency Data Not Found', HttpStatus.BAD_REQUEST);
    }

    const dataFromDb2 = await this.projectStatusRepository.findOne({
      id: projectDto.projectStatusId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        'Project Status Not Found',
        HttpStatus.BAD_REQUEST,
      );

    const project = new Project({
      ...projectDto,
      projectStatus: (projectDto.projectStatusId = {
        id: dataFromDb2.id,
      } as any),
      agencyId: agencyData.id,
    });

    return this.projectsRepository.create(project);
  }

  // json format of anything in project name and description fields
  // async createProject(projectDto: ProjectDto) {
  //   const dataFromDb = await this.technologySurveysRepository.findOne({
  //     id: projectDto.technologySurveyId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Technology Survey Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const dataFromDb2 = await this.projectStatusRepository.findOne({
  //     id: projectDto.projectStatusId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Project Status Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   const project = new Project({
  //     ...projectDto,
  //     technologySurvey: (projectDto.technologySurveyId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     projectStatus: (projectDto.projectStatusId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //   });
  //   return this.projectsRepository.create(project);
  // }

  async findAllProjects() {
    return this.projectsRepository.findAll({
      relations: {
        projectStatus: true,
      },
      order: {
        created_at: 'ASC',
      },
    });
  }

  async searchProject(
    upi: string,
  ): Promise<{ items: any[]; totalCount: number }> {
    const projectData = await this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.upi = :upi', { upi })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .getOne();

    // Check if the project exists
    if (!projectData) {
      return {
        items: [],
        totalCount: 0,
      };
    }

    return {
      items: [
        {
          id: projectData.id,
          upi: projectData.upi,
          isRRAVerified: projectData.isRRAVerified,
          ownerFullName: projectData.ownerFullName,
          ownerIdNo: projectData.ownerIdNo,
          isFromOldSystem: projectData.isFromOldSystem,
          isFromOldSystemDevelopersName:
            projectData.isFromOldSystemDevelopersName,
          isFromOldSystemPermitNumber: projectData.isFromOldSystemPermitNumber,
          isFromOldSystemInvoiceNumber:
            projectData.isFromOldSystemInvoiceNumber,
          isUnderMortgage: projectData.isUnderMortgage,
          isUnderRestriction: projectData.isUnderRestriction,
          centralCoordinateX: projectData.centralCoordinateX,
          centralCoordinateY: projectData.centralCoordinateY,
          villageCode: projectData.villageCode,
          villageName: projectData.villageName,
          cellCode: projectData.cellCode,
          cellName: projectData.cellName,
          sectorCode: projectData.sectorCode,
          sectorName: projectData.sectorName,
          districtCode: projectData.districtCode,
          districtName: projectData.districtName,
          provinceCode: projectData.provinceCode,
          provinceName: projectData.provinceName,
          selectedUse: projectData.selectedUse,
          selectedCategoryUse: projectData.selectedCategoryUse,
          agencyId: projectData.agencyId,
          projectName: projectData.projectName,
          projectDescription: projectData.projectDescription,
          plotSize: projectData.plotSize,
          originalPlotSize: projectData.originalPlotSize,
          userId: projectData.userId,
          created_at: projectData.created_at,
          updated_at: projectData.updated_at,
        },
      ],
      totalCount: 1,
    };
  }
  // searchProjectAllApplication
  async searchProjectAllApplication(upi: string) {
    const projectData = await this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.upi = :upi', { upi })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .leftJoinAndSelect('project.applications', 'applications')
      .leftJoinAndSelect('applications.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .getOne();
    if (!projectData) {
      throw new NotFoundException('Project not found');
    }

    return projectData;
  }

  // async findAllApplicantInTheAgency(agencyId: string) {
  //   return this.projectEntityManagerRepository
  //     .createQueryBuilder('project')
  //     .select('project.userId')
  //     .where('project.agencyId = :agencyId', { agencyId })
  //     .getMany();
  // }

  // async findAllApplicantInTheAgency(agencyId: string) {
  //   // Query to find all unique users for a specific agency
  //   const applicants = await this.projectEntityManagerRepository
  //     .createQueryBuilder('project')
  //     .select('project.userId')
  //     .where('project.agencyId = :agencyId', { agencyId })
  //     .getRawMany();

  //   // Extract the user IDs from the raw query results
  //   const userIds = applicants.map((applicant) => applicant.userId);

  //   // Fetch user details for each userId individually
  //   const userDetails = await Promise.all(
  //     userIds.map(async (userId) => await this.checkUserData(userId)),
  //   );

  //   // Remove password field from the user details
  //   const usersWithoutPassword = userDetails.map((user) => {
  //     const { password, ...userWithoutPassword } = user;
  //     return userWithoutPassword;
  //   });

  //   return usersWithoutPassword;
  // }
  async findAllApplicantInTheAgency(agencyId: string) {
    // Query to find all unique user IDs for a specific agency
    const applicants = await this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .select('project.userId')
      .where('project.agencyId = :agencyId', { agencyId })
      .getMany();

    // Extract the user IDs from the raw query results
    const userIds = applicants.map((applicant) => applicant.userId);

    // Check if the userIds array is empty
    if (userIds.length === 0) {
      throw new Error('No users found for the given agencyId');
    }

    // Fetch user details for each user ID
    const userDetails = await Promise.all(
      userIds.map(async (userId) => {
        // Use checkUser to fetch user details
        const user = await this.checkUser(userId);
        if (!user) {
          throw new Error(`User with ID ${userId} could not be found`);
        }
        // Omit password from user details
        const userWithoutPassword = { ...user };
        delete userWithoutPassword.password;
        return userWithoutPassword;
      }),
    );

    return userDetails;
  }

  public async findAllProjectsPaginated(
    pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<ProjectDtoGet>> {
    const queryBuilder =
      this.projectEntityManagerRepository.createQueryBuilder('project');

    queryBuilder
      .orderBy('project.created_at', pageOptionsDto.order)
      .skip(pageOptionsDto.skip)
      .take(pageOptionsDto.take);

    const itemCount = await queryBuilder.getCount();
    const { entities } = await queryBuilder.getRawAndEntities();

    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    return new PageDto(entities, pageMetaDto);
  }

  async findOneProject(id: string) {
    /* The above code is using TypeORM to query the database for a specific entity using the `id` as a
    filter. It is fetching the entity along with its related entity `projectStatus`. The `await`
    keyword is used to wait for the asynchronous operation to complete before returning the result. */
    return await this.projectEntityManagerRepository.findOne({
      where: { id: id },
      relations: { projectStatus: true },
    });
  }
  // async findOneProject(id: string) {
  //   return this.projectsRepository.findOne({ id });
  // }

  async findProjectByUserId(userId: string): Promise<Project[]> {
    console.log(userId);
    return this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.userId = :userId', { userId })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .orderBy('project.created_at')
      .getMany();
  }

  // get application by UPI but group  by project
  async searchApplicationByUPI(upi: string): Promise<Application[]> {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.upi = :upi', { upi })
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();
  }

  // get application by associated UPI but group  by project
  async searchApplicationByUPIAssociated(upi: string): Promise<any[]> {
    return this.associatedUPIEntityRepository
      .createQueryBuilder('associatedUPI')
      .where('associatedUPI.upi = :upi', { upi })
      .leftJoinAndSelect('associatedUPI.projects', 'projects')
      .getMany();
  }

  async findProjectAndApplicationBySubmittedUserId(submittedByUserId: string) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.submittedByUserId = :submittedByUserId', {
        submittedByUserId,
      })
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .getMany();
  }

  async findProjectAndApplicationByUserId(userId: string): Promise<Project[]> {
    return this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.userId = :userId', { userId })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .leftJoinAndSelect('project.applications', 'applications')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .leftJoinAndSelect('applications.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('applications.certificates', 'certificates')
      .getMany();
  }

  // async findProjectAndApplicationBySubmittedUserId(
  //   userId: string,
  // ): Promise<Project[]> {
  //   return this.projectEntityManagerRepository
  //     .createQueryBuilder('project')
  //     .where('project.submittedByUserId = :submittedByUserId', { userId })
  //     .leftJoinAndSelect('project.projectStatus', 'projectStatus')
  //     .leftJoinAndSelect('project.applications', 'applications')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .getMany();
  // }

  async updateProject(id: string, updateProjectDto: ProjectDto) {
    const { projectName, projectDescription } = updateProjectDto;

    const updateData: DeepPartial<Project> = {};
    if (projectName) updateData.projectName = projectName;
    if (projectDescription) updateData.projectDescription = projectDescription;

    const updateResult = await this.projectEntityManagerRepository.update(
      id,
      updateData,
    );

    if (updateResult.affected === 0) {
      throw new HttpException('Project not found', HttpStatus.NOT_FOUND);
    }

    return {
      statusCode: 200,
      message: 'Data saved successfully',
    };
  }

  // async updateProject(id: string, updateProjectDto: ProjectDto) {
  //   return this.projectsRepository.findOneAndUpdate({ id }, updateProjectDto);
  // }

  async removeProject(id: string) {
    return this.projectsRepository.findOneAndDelete({ id });
  }

  // // associatedUPI
  // async createAssociatedUPI(associatedUPIDto: AssociatedUPIDto) {
  //   const dataFromDb = await this.projectsRepository.findOne({
  //     id: associatedUPIDto.projectId,
  //   });
  //   if (!dataFromDb) {
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   if (dataFromDb.upi === associatedUPIDto.upi) {
  //     throw new HttpException(
  //       'UPI already associated with this project',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // Create a new AssociatedUPI entity
  //   const associatedUPI = new AssociatedUPI({
  //     ...associatedUPIDto,
  //     projects: { id: associatedUPIDto.projectId } as any,
  //   });

  //   // Save the AssociatedUPI entity
  //   const createdAssociatedUPI =
  //     await this.associatedUPIsRepository.create(associatedUPI);

  //   // Update the project to set isAssociatedUpi to true
  //   await this.projectEntityManagerRepository.update(
  //     { id: associatedUPIDto.projectId },
  //     { isAssociatedUpi: true },
  //   );

  //   return createdAssociatedUPI;
  // }

  // associatedUPI check if the upi is not already exist
  async createAssociatedUPI(associatedUPIDto: AssociatedUPIDto) {
    const projectData = await this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.upi = :upi', { upi: associatedUPIDto.upi })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .getOne();

    // Check if the project exists
    if (projectData) {
      throw new HttpException(
        'UPI is already used as main UPI on another project.',
        HttpStatus.BAD_REQUEST,
      );
    }
    // 1. Check if the UPI is already in associated UPI table
    const existingAssociatedUPI = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .where('associatedupi.upi = :upi', { upi: associatedUPIDto.upi })
      .getOne();
    if (existingAssociatedUPI) {
      // throw new BadRequestException('UPI is already associated.');
      throw new HttpException(
        'UPI is already associated.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 2. Verify the project exists
    console.log(associatedUPIDto.projectId);
    const project = await this.projectsRepository.findOne({
      id: associatedUPIDto.projectId,
    });

    if (!project) {
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 3. Check if the UPI matches the project UPI
    if (project.upi === associatedUPIDto.upi) {
      throw new HttpException(
        'UPI already associated with this project',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 4. Create the Associated UPI
    const associatedUPI = new AssociatedUPI({
      ...associatedUPIDto,
      projects: { id: associatedUPIDto.projectId } as any,
    });
    const createdAssociatedUPI =
      await this.associatedUPIsRepository.create(associatedUPI);

    // 5. Update project to indicate it has an associated UPI
    await this.projectEntityManagerRepository.update(
      { id: associatedUPIDto.projectId },
      { isAssociatedUpi: true },
    );

    return createdAssociatedUPI;
  }

  // sychronization of application owners and project name and description
  // async synchronization() {
  //   const applications = await this.applicationRepository.find({
  //     relations: ['projects'],
  //   });
  //   // 1. Check if the UPI is already in associated UPI table
  //   return 'hello end point for synchronization is working well ';
  // }

  async synchronization() {
    // const applications = await this.applicationRepository.findAll();
    const applications = await this.applicationRepository.findAll({
      relations: {
        projects: true,
      },
    });

    for (const app of applications) {
      try {
        const project = await this.projectsRepository.findOne({
          id: app.projects.id,
        });

        // adjust according to your sche
        if (!project) continue;

        // Set from project
        app.projectName = app.projectName || project.projectName;
        app.projectDescription =
          app.projectDescription || project.projectDescription;
        app.originalPlotSize = app.originalPlotSize || project.originalPlotSize;
        app.isRRAVerified = project.isRRAVerified;
        app.isUnderMortgage = project.isUnderMortgage;
        app.isUnderRestriction = project.isUnderRestriction;

        // Set coordinates from Project
        app.centralCoordinateX = project.centralCoordinateX;
        app.centralCoordinateY = project.centralCoordinateY;

        // Call external land API
        const response = await axios.get(
          `${config.notification.landAPI}${project.upi}`,
        );

        const landData = response.data?.data;
        if (!landData) continue;

        // Build parcelOwners and parcelOwnersIds from owners array
        const owners = landData.owners || [];
        if (owners) {
          const ownerFullName = owners.map((o) => `${o.fullName}`).join(', ');
          const ownerIds = owners.map((o) => `${o.idNo}`).join(', ');
          app.parcelOwners = ownerFullName;
          app.parcelOwnersIds = ownerIds;
        }

        // // Representative info
        const rep = landData.representative;
        if (rep) {
          const repFullNames = `${rep.surname} ${rep.foreNames}`;
          const repIds = `${rep.idNo}`;
          app.parcelRepresentatives = repFullNames;
          app.parcelRepresentativesIds = repIds;
        }
        // // Set location from parcelLocation
        const location = landData.parcelLocation || {};
        app.villageCode = location.village?.villageCode || null;
        app.villageName = location.village?.villageName || null;
        app.cellCode = location.cell?.cellCode || null;
        app.cellName = location.cell?.cellName || null;
        app.sectorCode = location.sector?.sectorCode || null;
        app.sectorName = location.sector?.sectorName || null;
        app.districtCode = location.district?.districtCode || null;
        app.districtName = location.district?.districtName || null;
        app.provinceCode = location.province?.provinceCode || null;
        app.provinceName = location.province?.provinceName || null;

        // await this.applicationRepository.save(app);
        await this.applicationRepository.findOneAndUpdate(
          { id: app.id }, // filter
          app, // update data
        );
      } catch (error) {
        console.error(
          `Error processing application with UPI ${app.projects?.upi}:`,
          error.message,
        );
        continue;
      }
    }

    return 'Synchronization completed';
  }
  async calculateCategory(calculateCategoryDto: CalculateCategoryDto) {
    // check if permit type is not null
    let category = '';
    let categoryType;
    if (calculateCategoryDto.permitTypeId) {
      // Check permit type exists
      const permitTypeData = await this.permitTypesRepository.findOne({
        id: calculateCategoryDto.permitTypeId,
      });
      if (!permitTypeData) {
        throw new HttpException(
          'Permit Type Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }
      const permitTypeCode = permitTypeData.code;
      if (permitTypeCode == 'TSAP') {
        category = 'CAT1';
        // caculate category by category code
        categoryType = await this.categoryTypesRepository.findOne({
          code: category,
        });
        if (!categoryType)
          throw new HttpException(
            'Category Type Not Found',
            HttpStatus.BAD_REQUEST,
          );
        return categoryType;
      }
    }
    // Check if build Type exists
    if (calculateCategoryDto.buildTypeId) {
      // Check if building type exists

      const buildTypedata = await this.buildTypesRepository.findOne({
        id: calculateCategoryDto.buildTypeId,
      });
      if (!buildTypedata) {
        throw new HttpException('Build Type Not Found', HttpStatus.BAD_REQUEST);
      }
      const buildTypeCode = buildTypedata.code;
      if (
        buildTypeCode === 'FAI' ||
        buildTypeCode === 'EDU' ||
        buildTypeCode === 'ASMB' ||
        buildTypeCode === 'INSTNAL' ||
        buildTypeCode === 'MEM' ||
        buildTypeCode === 'STRG' ||
        buildTypeCode === 'MISLNS' ||
        buildTypeCode === 'INST' ||
        buildTypeCode === 'ASMBLY' ||
        buildTypeCode === 'INST' ||
        buildTypeCode === 'HLFFCT' ||
        buildTypeCode === 'HZRDS' ||
        buildTypeCode === 'INST' ||
        buildTypeCode === 'INST' ||
        buildTypeCode === 'TWS' ||
        +calculateCategoryDto.capacityInformation > 500
      ) {
        category = 'CAT5';
      } else if (
        +calculateCategoryDto.numberOfFloors >= 2 ||
        +calculateCategoryDto.buildUpArea > 1500
      ) {
        category = 'CAT4';
      } else if (
        // +formValue.buildUpArea < 1500 ||
        +calculateCategoryDto.numberOfFloors >= 2 &&
        +calculateCategoryDto.capacityInformation > 50 &&
        +calculateCategoryDto.capacityInformation <= 500
      ) {
        category = 'CAT4';
      } else if (
        // +formValue.buildUpArea < 1500 ||
        (!calculateCategoryDto.numberOfFloors ||
          +calculateCategoryDto.numberOfFloors < 2) &&
        +calculateCategoryDto.capacityInformation > 50 &&
        +calculateCategoryDto.capacityInformation <= 500
      ) {
        category = 'CAT4';
      } else if (+calculateCategoryDto.numberOfFloors === 1) {
        category = 'CAT3';
      } else if (
        +calculateCategoryDto.buildUpArea > 200 &&
        +calculateCategoryDto.buildUpArea <= 1500 &&
        (+calculateCategoryDto.numberOfFloors === 1 ||
          +calculateCategoryDto.numberOfFloors === 0) &&
        (!calculateCategoryDto.capacityInformation ||
          +calculateCategoryDto.capacityInformation > 15 ||
          +calculateCategoryDto.numberOfFloors <= 50)
      ) {
        category = 'CAT3';
      } else if (
        +calculateCategoryDto.buildUpArea > 200 &&
        +calculateCategoryDto.buildUpArea <= 1500 &&
        +calculateCategoryDto.numberOfFloors === 1 &&
        (+calculateCategoryDto.capacityInformation > 15 ||
          +calculateCategoryDto.numberOfFloors <= 50)
      ) {
        category = 'CAT3';
      } else if (
        +calculateCategoryDto.numberOfFloors === 0 &&
        +calculateCategoryDto.capacityInformation > 15 &&
        +calculateCategoryDto.capacityInformation <= 50
      ) {
        category = 'CAT3';
      } else {
        category = 'CAT2';
      }

      // caculate category by category code
      categoryType = await this.categoryTypesRepository.findOne({
        code: category,
      });
      if (!categoryType)
        throw new HttpException(
          'Category Type Not Found',
          HttpStatus.BAD_REQUEST,
        );
      // return categoryType;
    }

    return categoryType;
  }

  async findAllAssociatedUPIs() {
    return this.associatedUPIsRepository.findAll({
      relations: {
        projects: true,
      },
    });
  }

  async findOneAssociatedUPI(id: string) {
    return this.associatedUPIsRepository.findOne({ id });
  }

  // async findOneAssociatedUPIByApplicationId(projectId: string) {
  //   return await this.associatedUPIEntityRepository
  //     .createQueryBuilder('associatedUPI')
  //     .leftJoinAndSelect('associatedUPI.projects', 'projects')
  //     .where('associatedUPI.projectId = :projectId', { projectId })
  //     .getMany();
  // }
  async findOneAssociatedUPIByProjectId(projectId: string) {
    const associatedUPIs = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .leftJoinAndSelect('associatedupi.projects', 'projects')
      .where('associatedupi.projects = :projectId', { projectId })
      .getMany();

    return associatedUPIs.length > 0 ? associatedUPIs : [];
  }

  async checkUPIInAssociatedUPI(upi: string) {
    const associatedUPI = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .where('associatedupi.upi = :upi', { upi })
      .getOne();

    if (associatedUPI) {
      // throw new BadRequestException(`UPI is already associated.`);
      throw new HttpException(
        'UPI is already associated.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return { message: 'UPI is available.' };
  }

  // async checkIfUPIExistInAssociatedUPI(upi: string) {
  //   const associatedUPI = await this.associatedUPIEntityRepository
  //     .createQueryBuilder('associatedupi')
  //     .where('associatedupi.upi = :upi', { upi })
  //     .getOne();

  //   if (associatedUPI) {
  //     // throw new BadRequestException('UPI is already associated.');
  //     throw new HttpException(
  //       'UPI is already associated.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   return true;
  // }

  async checkIfUPIExistInAssociatedUPI(upi: string) {
    const associatedUPI = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .where('associatedupi.upi = :upi', { upi })
      .getOne();

    if (associatedUPI) {
      throw new HttpException(
        'UPI is already associated.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return true;
  }

  // async updateAssociatedUPI(
  //   id: string,
  //   updateAssociatedUPIDto: AssociatedUPIDto,
  // ) {
  //   console.log(id);
  //   console.log('hdhdhdhhd hdhdhhdhd');
  //   console.log('updateAssociatedUPIDto', updateAssociatedUPIDto);
  //   return this.associatedUPIsRepository.findOneAndUpdate(
  //     { id },
  //     updateAssociatedUPIDto,
  //   );
  // }
  // async updateAssociatedUPI(id, updateAssociatedUPIDto: AssociatedUPIDto) {
  //   const projectData = await this.projectEntityManagerRepository
  //     .createQueryBuilder('project')
  //     .where('project.upi = :upi', { upi: updateAssociatedUPIDto.upi })
  //     .leftJoinAndSelect('project.projectStatus', 'projectStatus')
  //     .getOne();

  //   // Check if the project exists
  //   if (projectData) {
  //     throw new HttpException(
  //       'UPI is already used as main UPI on another project.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  //   // 1. Check if the UPI is already in associated UPI table
  //   const existingAssociatedUPI = await this.associatedUPIEntityRepository
  //     .createQueryBuilder('associatedupi')
  //     .where('associatedupi.upi = :upi', { upi: updateAssociatedUPIDto.upi })
  //     .getOne();
  //   if (existingAssociatedUPI) {
  //     // throw new BadRequestException('UPI is already associated.');
  //     throw new HttpException(
  //       'UPI is already associated.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  //   // 1. Check if associated UPI exists by ID
  //   const checkifAssociateExist =
  //     await this.associatedUPIEntityRepository.findOne({
  //       where: { id },
  //     });

  //   if (!checkifAssociateExist) {
  //     // throw new BadRequestException('UPI is already associated.');
  //     throw new HttpException(
  //       'associate record not fund.',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // 2. Verify the project exists
  //   // console.log(associatedUPIDto.projectId);

  //   const project = await this.projectsRepository.findOne({
  //     id: updateAssociatedUPIDto.projectId,
  //   });

  //   if (!project) {
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // 3. Check if the UPI matches the project UPI
  //   if (project.upi === updateAssociatedUPIDto.upi) {
  //     throw new HttpException(
  //       'UPI already associated with this project',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }

  //   // // 4. Create the Associated UPI
  //   // const associatedUPI = new AssociatedUPI({
  //   //   ...associatedUPIDto,
  //   //   projects: { id: associatedUPIDto.projectId } as any,
  //   // });
  //   // const createdAssociatedUPI =
  //   //   await this.associatedUPIsRepository.create(associatedUPI);
  //   // update the existing associated UPI
  //   const updatedAssociatedUPI =
  //     await this.associatedUPIEntityRepository.update(
  //       { id },
  //       {
  //         ...updateAssociatedUPIDto,
  //         projects: project,
  //       },
  //     );

  //   // 5. Update project to indicate it has an associated UPI
  //   await this.projectEntityManagerRepository.update(
  //     { id: updateAssociatedUPIDto.projectId },
  //     { isAssociatedUpi: true },
  //   );

  //   return updatedAssociatedUPI;
  // }
  //find findAssociatedUPIByUPI
  async findAssociatedUPIByUPI(upi: string) {
    const associatedUPI = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .where('associatedupi.upi = :upi', { upi })
      .leftJoinAndSelect('associatedupi.projects', 'projects')
      .getOne();

    if (!associatedUPI) {
      throw new HttpException(
        'Associated UPI not found.',
        HttpStatus.NOT_FOUND,
      );
    }
    return associatedUPI;
  }

  async updateAssociatedUPI(
    id: string,
    updateAssociatedUPIDto: AssociatedUPIDto,
  ) {
    // 1. Check if this UPI is already used as a main UPI on any project
    const projectData = await this.projectEntityManagerRepository
      .createQueryBuilder('project')
      .where('project.upi = :upi', { upi: updateAssociatedUPIDto.upi })
      .leftJoinAndSelect('project.projectStatus', 'projectStatus')
      .getOne();

    if (projectData) {
      throw new HttpException(
        'UPI is already used as main UPI on another project.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 2. Check if the UPI is already associated elsewhere (other than the current one)
    const existingAssociatedUPI = await this.associatedUPIEntityRepository
      .createQueryBuilder('associatedupi')
      .where('associatedupi.upi = :upi', { upi: updateAssociatedUPIDto.upi })
      .andWhere('associatedupi.id != :id', { id })
      .getOne();

    if (existingAssociatedUPI) {
      throw new HttpException(
        'UPI is already associated.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 3. Check if the current associated UPI record exists
    const checkIfAssociateExist =
      await this.associatedUPIEntityRepository.findOne({
        where: { id },
      });

    if (!checkIfAssociateExist) {
      throw new HttpException(
        'Associated UPI record not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 4. Fetch the project by ID
    const project = await this.projectsRepository.findOne({
      id: updateAssociatedUPIDto.projectId,
    });

    if (!project) {
      throw new HttpException(
        'Referenced project not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 5. Check if the provided UPI matches the project's main UPI
    if (project.upi === updateAssociatedUPIDto.upi) {
      throw new HttpException(
        'UPI is already set as the main UPI of this project.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // 6. Update the associated UPI record
    const { ...associatedUPIData } = updateAssociatedUPIDto;
    const associatedUPI = new AssociatedUPI({
      ...associatedUPIData,
      projects: { id: project.id } as any,
    });
    console.log('associatedUPI gxggxgxggxgxgxgxgxx');
    console.log(associatedUPI);
    const updatedAssociatedUPI =
      await this.associatedUPIEntityRepository.update(
        { id },
        {
          ...associatedUPI,
        },
      );

    if (!updatedAssociatedUPI) {
      throw new HttpException(
        'Failed to update associated UPI.',
        HttpStatus.BAD_REQUEST,
      );
    }
    // 7. Mark the project as having an associated UPI
    await this.projectEntityManagerRepository.update(
      { id: project.id },
      { isAssociatedUpi: true },
    );
    const updateAsociateUpi = await this.associatedUPIEntityRepository.findOne({
      where: { id },
    });
    return updateAsociateUpi;
  }

  async removeAssociatedUPI(id: string) {
    // Check if the associated UPI exists
    const associatedUPI = await this.associatedUPIEntityRepository.findOne({
      where: { id },
    });
    if (!associatedUPI) {
      throw new HttpException('Associated UPI not found', HttpStatus.NOT_FOUND);
    }
    return this.associatedUPIsRepository.findOneAndDelete({ id });
  }

  // generateAlphanumericPart(): string {
  //   const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  //   let alphabeticPart = '';
  //   for (let i = 0; i < 4; i++) {
  //     alphabeticPart += alphabet.charAt(
  //       Math.floor(Math.random() * alphabet.length),
  //     ); // Randomly select alphabetic character
  //   }
  //   const timestamp = Date.now().toString().slice(-5); // Get last 5 digits of current timestamp
  //   const numericPart = Math.floor(Math.random() * 100000); // Generate random number
  //   const numericString = (numericPart + `-` + timestamp).padStart(10, '0'); // Concatenate random number with timestamp and format as a 10-digit string
  //   return `${alphabeticPart}-${numericString}`;
  // }

  // // application
  // async createApplication(applicationDto: ApplicationDto) {
  //   const dataFromDb = await this.projectsRepository.findOne({
  //     id: applicationDto.projectId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const dataFromDb2 = await this.permitTypesRepository.findOne({
  //     id: applicationDto.permitTypeId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb3 = await this.categoryTypesRepository.findOne({
  //     id: applicationDto.categoryTypeId,
  //   });
  //   if (!dataFromDb3)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb4 = await this.buildTypesRepository.findOne({
  //     id: applicationDto.buildTypeId,
  //   });
  //   if (!dataFromDb4)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb5 = await this.applicationStatusRepository.findOne({
  //     id: applicationDto.applicationStatusId,
  //   });
  //   if (!dataFromDb5)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb6 = await this.technologySurveysRepository.findOne({
  //     id: applicationDto.technologySurveyId,
  //   });
  //   if (!dataFromDb6)
  //     throw new HttpException(
  //       'Technology Survey Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   // login for creating application Number
  //   const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  //   let alphabeticPart = '';
  //   for (let i = 0; i < 4; i++) {
  //     alphabeticPart += alphabet.charAt(
  //       Math.floor(Math.random() * alphabet.length),
  //     ); // Randomly select alphabetic character
  //   }
  //   const timestamp = Date.now().toString().slice(-5); // Get last 5 digits of current timestamp
  //   const numericPart = Math.floor(Math.random() * 100000); // Generate random number
  //   const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
  //   const alphanumericPart = `${alphabeticPart}-${numericString}`;
  //   applicationDto.applicationName = `${applicationDto.agencyCode}-${applicationDto.permitTypeCode}-${alphanumericPart}`;

  //   const application = new Application({
  //     ...applicationDto,
  //     projects: (applicationDto.projectId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     permitTypes: (applicationDto.permitTypeId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //     categoryTypes: (applicationDto.categoryTypeId = {
  //       id: dataFromDb3.id,
  //     } as any),
  //     buildTypes: (applicationDto.buildTypeId = {
  //       id: dataFromDb4.id,
  //     } as any),
  //     applicationStatus: (applicationDto.applicationStatusId = {
  //       id: dataFromDb5.id,
  //     } as any),
  //     technologySurveys: (applicationDto.technologySurveyId = {
  //       id: dataFromDb6.id,
  //     } as any),
  //     other: applicationDto.other,
  //   });
  //   return this.applicationRepository.create(application);
  // }

  // // save application without application Number
  // // save application with the log of ip, browser
  // // application
  // async createApplication(
  //   applicationDto: ApplicationDto,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     // Parse user agent for browser and OS
  //     const clientInfo = this.parseUserAgent(userAgent);

  //     // Validate related data in the database
  //     const dataFromDb = await this.projectsRepository.findOne({
  //       id: applicationDto.projectId,
  //     });
  //     if (!dataFromDb)
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );

  //     const dataFromDb2 = await this.permitTypesRepository.findOne({
  //       id: applicationDto.permitTypeId,
  //     });
  //     if (!dataFromDb2)
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );

  //     const dataFromDb3 = await this.categoryTypesRepository.findOne({
  //       id: applicationDto.categoryTypeId,
  //     });
  //     if (!dataFromDb3)
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );

  //     const dataFromDb4 = await this.buildTypesRepository.findOne({
  //       id: applicationDto.buildTypeId,
  //     });
  //     if (!dataFromDb4)
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );

  //     const dataFromDb5 = await this.applicationStatusRepository.findOne({
  //       id: applicationDto.applicationStatusId,
  //     });
  //     if (!dataFromDb5)
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );

  //     const dataFromDb6 = await this.technologySurveysRepository.findOne({
  //       id: applicationDto.technologySurveyId,
  //     });
  //     if (!dataFromDb6)
  //       throw new HttpException(
  //         'Technology Survey Not Found',
  //         HttpStatus.BAD_REQUEST,
  //       );

  //     // Check if another application with the same permit type and UPI exists, except if the status is CXL
  //     const existingApplication = await this.applicationEntityManagerRepository
  //       .createQueryBuilder('application')
  //       .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //       .where('application.permitTypes = :permitTypeId', {
  //         permitTypeId: applicationDto.permitTypeId,
  //       })
  //       .andWhere('application.upi = :upi', { upi: applicationDto.upi })
  //       .andWhere('applicationStatus.code != :excludedStatus', {
  //         excludedStatus: 'CXL',
  //       })
  //       .getOne();

  //     if (existingApplication) {
  //       throw new HttpException(
  //         'An application with this permit type already exists and it has never been cancelled',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     // Create the application instance with conditional 'other' field
  //     const application = new Application({
  //       ...applicationDto,
  //       projects: { id: dataFromDb.id } as any,
  //       permitTypes: { id: dataFromDb2.id } as any,
  //       categoryTypes: { id: dataFromDb3.id } as any,
  //       buildTypes: { id: dataFromDb4.id } as any,
  //       applicationStatus: { id: dataFromDb5.id } as any,
  //       technologySurveys: { id: dataFromDb6.id } as any,
  //       ...(applicationDto.other ? { other: applicationDto.other } : {}),
  //     });

  //     // Save the application and obtain the saved entity with an ID
  //     const savedApplication =
  //       await this.applicationRepository.create(application);

  //     // Create and save submission log with the saved application ID
  //     const submissionLog = new SubmissionLog({
  //       application: savedApplication,
  //       userId: applicationDto.userId,
  //       applicationStatusId: applicationDto.applicationStatusId,
  //       ipAddress: clientIp,
  //       browser: clientInfo.browser,
  //       operatingSystem: clientInfo.operatingSystem,
  //     });

  //     await this.submissionLogRepository.save(submissionLog);

  //     return savedApplication;
  //   } catch (err) {
  //     console.error('Error creating application or submission log:', err);
  //     throw err;
  //   }
  // }

  // before launch modification ===> creating application with all checks

  // async createApplication(
  //   applicationDto: ApplicationDto,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     // Parse user agent for browser and OS
  //     const clientInfo = this.parseUserAgent(userAgent);

  //     // Validate related data in the database
  //     const relatedEntities = await Promise.all([
  //       this.projectsRepository.findOne({ id: applicationDto.projectId }),
  //       this.permitTypesRepository.findOne({ id: applicationDto.permitTypeId }),
  //       this.categoryTypesRepository.findOne({
  //         id: applicationDto.categoryTypeId,
  //       }),
  //       this.buildTypesRepository.findOne({ id: applicationDto.buildTypeId }),
  //       this.applicationStatusRepository.findOne({
  //         id: applicationDto.applicationStatusId,
  //       }),
  //       this.technologySurveysRepository.findOne({
  //         id: applicationDto.technologySurveyId,
  //       }),
  //     ]);

  //     const [
  //       project,
  //       permitType,
  //       categoryType,
  //       buildType,
  //       applicationStatus,
  //       technologySurvey,
  //     ] = relatedEntities;

  //     if (

  //     ) {
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     // Check if another application with the same permit type exists (excluding CXL status)
  //     const conflictingApplication =
  //       await this.applicationEntityManagerRepository
  //         .createQueryBuilder('application')
  //         .leftJoinAndSelect(
  //           'application.applicationStatus',
  //           'applicationStatus',
  //         )
  //         .where('application.permitTypes = :permitTypeId', {
  //           permitTypeId: applicationDto.permitTypeId,
  //         })
  //         .andWhere('applicationStatus.code != :excludedStatus', {
  //           excludedStatus: 'CXL',
  //         })
  //         .getOne();

  //     if (conflictingApplication) {
  //       throw new HttpException(
  //         'An application with this permit type already exists and it has never been cancelled',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     // Check if another application with the same UPI and permit type exists (excluding CXL status)
  //     const upiConflict = await this.applicationEntityManagerRepository
  //       .createQueryBuilder('application')
  //       .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //       .where('application.permitTypes = :permitTypeId', {
  //         permitTypeId: applicationDto.permitTypeId,
  //       })
  //       .andWhere('application.upi = :upi', { upi: applicationDto.upi })
  //       .andWhere('applicationStatus.code != :excludedStatus', {
  //         excludedStatus: 'CXL',
  //       })
  //       .getOne();

  //     if (upiConflict) {
  //       throw new HttpException(
  //         'An application with this UPI and permit type already exists and it has never been cancelled',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     // Create the application instance with conditional 'other' field
  //     const application = new Application({
  //       ...applicationDto,
  //       projects: { id: project.id } as any,
  //       permitTypes: { id: permitType.id } as any,
  //       categoryTypes: { id: categoryType.id } as any,
  //       buildTypes: { id: buildType.id } as any,
  //       applicationStatus: { id: applicationStatus.id } as any,
  //       technologySurveys: { id: technologySurvey.id } as any,
  //       ...(applicationDto.other ? { other: applicationDto.other } : {}),
  //     });

  //     // Save the application and obtain the saved entity with an ID
  //     const savedApplication =
  //       await this.applicationRepository.create(application);

  //     // Create and save submission log with the saved application ID
  //     const submissionLog = new SubmissionLog({
  //       application: savedApplication,
  //       userId: applicationDto.userId,
  //       applicationStatusId: applicationDto.applicationStatusId,
  //       ipAddress: clientIp,
  //       browser: clientInfo.browser,
  //       operatingSystem: clientInfo.operatingSystem,
  //     });

  //     await this.submissionLogRepository.save(submissionLog);

  //     return savedApplication;
  //   } catch (err) {
  //     console.error('Error creating application or submission log:', err);
  //     throw err;
  //   }
  // }

  // submit application
  // synchronization of application with land and project
  async automaticSynchronization(projectId: string, applicationId: string) {
    // const applications = await this.applicationRepository.findAll();
    const applications = await this.applicationRepository.findOne({
      id: applicationId,
    });
    if (!applications) {
      throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
    }
    const app = applications;

    try {
      const project = await this.projectsRepository.findOne({
        id: projectId,
      });

      if (!project) {
        throw new HttpException('Project not found', HttpStatus.NOT_FOUND);
      }

      // Set from project
      app.projectName = app.projectName || project.projectName;
      app.projectDescription =
        app.projectDescription || project.projectDescription;
      app.originalPlotSize = app.originalPlotSize || project.originalPlotSize;
      app.isRRAVerified = project.isRRAVerified;
      app.isUnderMortgage = project.isUnderMortgage;
      app.isUnderRestriction = project.isUnderRestriction;

      // Set coordinates from Project
      app.centralCoordinateX = project.centralCoordinateX;
      app.centralCoordinateY = project.centralCoordinateY;

      // Call external land API
      const response = await axios.get(
        `${config.notification.landAPI}${project.upi}`,
      );

      const landData = response.data?.data;
      if (!landData) {
        throw new HttpException('Land data not found', HttpStatus.NOT_FOUND);
      }

      // Build parcelOwners and parcelOwnersIds from owners array
      const owners = landData.owners || [];
      if (owners) {
        const ownerFullName = owners.map((o) => `${o.fullName}`).join(', ');
        const ownerIds = owners.map((o) => `${o.idNo}`).join(', ');
        app.parcelOwners = ownerFullName;
        app.parcelOwnersIds = ownerIds;
      }

      // // Representative info
      const rep = landData.representative;
      if (rep) {
        const repFullNames = `${rep.surname} ${rep.foreNames}`;
        const repIds = `${rep.idNo}`;
        app.parcelRepresentatives = repFullNames;
        app.parcelRepresentativesIds = repIds;
      }
      // // Set location from parcelLocation
      const location = landData.parcelLocation || {};
      app.villageCode = location.village?.villageCode || null;
      app.villageName = location.village?.villageName || null;
      app.cellCode = location.cell?.cellCode || null;
      app.cellName = location.cell?.cellName || null;
      app.sectorCode = location.sector?.sectorCode || null;
      app.sectorName = location.sector?.sectorName || null;
      app.districtCode = location.district?.districtCode || null;
      app.districtName = location.district?.districtName || null;
      app.provinceCode = location.province?.provinceCode || null;
      app.provinceName = location.province?.provinceName || null;

      // await this.applicationRepository.save(app);
      await this.applicationRepository.findOneAndUpdate(
        { id: app.id }, // filter
        app, // update data
      );
    } catch (error) {
      console.error(
        `Error processing application with UPI ${app.projects?.upi}:`,
        error.message,
      );
      throw new HttpException(
        `Error processing application with UPI ${app.projects?.upi}: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return 'Synchronization completed';
  }
  // // after production with RHA error
  async createApplication(
    applicationDto: ApplicationDto,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      // Parse user agent for browser and OS
      const clientInfo = this.parseUserAgent(userAgent);

      // Validate related data in the database
      const relatedEntities = await Promise.all([
        this.projectsRepository.findOne({ id: applicationDto.projectId }),
        this.permitTypesRepository.findOne({ id: applicationDto.permitTypeId }),
        this.categoryTypesRepository.findOne({
          id: applicationDto.categoryTypeId,
        }),
        this.buildTypesRepository.findOne({ id: applicationDto.buildTypeId }),
        this.applicationStatusRepository.findOne({
          id: applicationDto.applicationStatusId,
        }),
        this.technologySurveysRepository.findOne({
          id: applicationDto.technologySurveyId,
        }),
      ]);

      const [
        project,
        permitType,
        categoryType,
        buildType,
        applicationStatus,
        technologySurvey,
      ] = relatedEntities;

      if (
        !project ||
        !permitType ||
        !categoryType ||
        !buildType ||
        !applicationStatus ||
        !technologySurvey
      ) {
        throw new HttpException(
          'Referenced Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check if another application with the same permit type and UPI exists (excluding CXL status)
      // const conflictingApplication =
      //   await this.applicationEntityManagerRepository
      //     .createQueryBuilder('application')
      //     .leftJoinAndSelect(
      //       'application.applicationStatus',
      //       'applicationStatus',
      //     )
      //     .where('application.permitTypes = :permitTypeId', {
      //       permitTypeId: applicationDto.permitTypeId,
      //     })
      //     .andWhere('application.upi = :upi', {
      //       upi: applicationDto.upi,
      //     })
      //     .andWhere('applicationStatus.code != :excludedStatus', {
      //       excludedStatus: 'CXL',
      //     })
      //     .getOne();

      // if (conflictingApplication) {
      //   throw new HttpException(
      //     'An application with this permit type and UPI already exists and it has never been cancelled',
      //     HttpStatus.BAD_REQUEST,
      //   );
      // }
      const existingApplication = await this.applicationEntityManagerRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
        .where('application.permitTypes = :permitTypeId', {
          permitTypeId: applicationDto.permitTypeId,
        })
        .andWhere('application.upi = :upi', {
          upi: applicationDto.upi,
        })
        .getOne();

      // Block only if there's an existing application with a different status than 'CXL'
      if (
        existingApplication &&
        existingApplication.applicationStatus &&
        existingApplication.applicationStatus.code !== 'CXL'
      ) {
        throw new HttpException(
          'An application with this permit type and UPI already exists and it has never been cancelled',
          HttpStatus.BAD_REQUEST,
        );
      }

      // const agencyData = await this.checkAgencyDataById(
      //   applicationDto.agencyId,
      // );
      const agencyData = await this.checkAgencyDataById(project.agencyId);

      if (!agencyData) {
        throw new HttpException(
          'Agency Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }
      // Create the application instance with conditional 'other' field
      const application = new Application({
        ...applicationDto,
        projects: { id: project.id } as any,
        permitTypes: { id: permitType.id } as any,
        categoryTypes: { id: categoryType.id } as any,
        buildTypes: { id: buildType.id } as any,
        applicationStatus: { id: applicationStatus.id } as any,
        technologySurveys: { id: technologySurvey.id } as any,
        agencyCode: agencyData.code,
        upi: project.upi,
        permitTypeCode: permitType.code,
        ...(applicationDto.other ? { other: applicationDto.other } : {}),
      });

      // Save the application and obtain the saved entity with an ID
      const savedApplication =
        await this.applicationRepository.create(application);
      //  sychronization
      const projectId = applicationDto.projectId;
      const applicationId = savedApplication.id;
      if (savedApplication) {
        await this.automaticSynchronization(projectId, applicationId);
      }

      // Create and save submission log with the saved application ID
      const submissionLog = new SubmissionLog({
        application: savedApplication,
        userId: applicationDto.userId,
        applicationStatusId: applicationDto.applicationStatusId,
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,
      });

      await this.submissionLogRepository.save(submissionLog);

      return savedApplication;
    } catch (err) {
      console.error('Error creating application or submission log:', err);
      throw err;
    }
  }

  // // after production code
  // async createApplication(
  //   applicationDto: ApplicationDto,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     // Parse user agent for browser and OS
  //     const clientInfo = this.parseUserAgent(userAgent);

  //     // Validate related data in the database
  //     const relatedEntities = await Promise.all([
  //       this.projectsRepository.findOne({ id: applicationDto.projectId }),
  //       this.permitTypesRepository.findOne({ id: applicationDto.permitTypeId }),
  //       this.categoryTypesRepository.findOne({
  //         id: applicationDto.categoryTypeId,
  //       }),
  //       this.buildTypesRepository.findOne({ id: applicationDto.buildTypeId }),
  //       this.applicationStatusRepository.findOne({
  //         id: applicationDto.applicationStatusId,
  //       }),
  //       this.technologySurveysRepository.findOne({
  //         id: applicationDto.technologySurveyId,
  //       }),
  //     ]);

  //     const [
  //       project,
  //       permitType,
  //       categoryType,
  //       buildType,
  //       applicationStatus,
  //       technologySurvey,
  //     ] = relatedEntities;

  //     if (
  // !project ||
  // !permitType ||
  // !categoryType ||
  // !buildType ||
  // !applicationStatus ||
  // !technologySurvey
  //     ) {
  //       throw new HttpException(
  //         'Referenced Data Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     // Check if another application with the same permit type and UPI exists (excluding CXL status)
  //     const conflictingApplication =
  //       await this.applicationEntityManagerRepository
  //         .createQueryBuilder('application')
  //         .leftJoinAndSelect(
  //           'application.applicationStatus',
  //           'applicationStatus',
  //         )
  //         .where('application.permitTypes = :permitTypeId', {
  //           permitTypeId: applicationDto.permitTypeId,
  //         })
  //         .andWhere('application.upi = :upi', {
  //           upi: applicationDto.upi,
  //         })
  //         .andWhere('applicationStatus.code != :excludedStatus', {
  //           excludedStatus: 'CXL',
  //         })
  //         .getOne();

  //     if (conflictingApplication) {
  //       throw new HttpException(
  //         'An application with this permit type and UPI already exists and it has never been cancelled',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //     // Create the application instance with conditional 'other' field
  //     const application = new Application({
  //       ...applicationDto,
  //       projects: { id: project.id } as any,
  //       permitTypes: { id: permitType.id } as any,
  //       categoryTypes: { id: categoryType.id } as any,
  //       buildTypes: { id: buildType.id } as any,
  //       applicationStatus: { id: applicationStatus.id } as any,
  //       technologySurveys: { id: technologySurvey.id } as any,
  //       ...(applicationDto.other ? { other: applicationDto.other } : {}),
  //     });

  //     // Save the application and obtain the saved entity with an ID
  //     const savedApplication =
  //       await this.applicationRepository.create(application);

  //     // Create and save submission log with the saved application ID
  //     const submissionLog = new SubmissionLog({
  //       application: savedApplication,
  //       userId: applicationDto.userId,
  //       applicationStatusId: applicationDto.applicationStatusId,
  //       ipAddress: clientIp,
  //       browser: clientInfo.browser,
  //       operatingSystem: clientInfo.operatingSystem,
  //     });

  //     await this.submissionLogRepository.save(submissionLog);

  //     return savedApplication;
  //   } catch (err) {
  //     console.error('Error creating application or submission log:', err);
  //     throw err;
  //   }
  // }

  // Method to parse user agent
  private parseUserAgent(userAgent: string) {
    let operatingSystem = 'Unknown OS';
    if (/windows/i.test(userAgent)) {
      operatingSystem = 'Windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      operatingSystem = 'macOS';
    } else if (/linux/i.test(userAgent)) {
      operatingSystem = 'Linux';
    } else if (/android/i.test(userAgent)) {
      operatingSystem = 'Android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      operatingSystem = 'iOS';
    }

    let browser = 'Unknown Browser';
    if (/chrome/i.test(userAgent)) {
      browser = 'Chrome';
    } else if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) {
      browser = 'Safari';
    } else if (/firefox/i.test(userAgent)) {
      browser = 'Firefox';
    } else if (/edge/i.test(userAgent)) {
      browser = 'Edge';
    } else if (/msie|trident/i.test(userAgent)) {
      browser = 'Internet Explorer';
    }

    return { operatingSystem, browser };
  }

  // // application
  // async createApplication(applicationDto: ApplicationDto) {
  //   const dataFromDb = await this.projectsRepository.findOne({
  //     id: applicationDto.projectId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const dataFromDb2 = await this.permitTypesRepository.findOne({
  //     id: applicationDto.permitTypeId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb3 = await this.categoryTypesRepository.findOne({
  //     id: applicationDto.categoryTypeId,
  //   });
  //   if (!dataFromDb3)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb4 = await this.buildTypesRepository.findOne({
  //     id: applicationDto.buildTypeId,
  //   });
  //   if (!dataFromDb4)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb5 = await this.applicationStatusRepository.findOne({
  //     id: applicationDto.applicationStatusId,
  //   });
  //   if (!dataFromDb5)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   const dataFromDb6 = await this.technologySurveysRepository.findOne({
  //     id: applicationDto.technologySurveyId,
  //   });
  //   if (!dataFromDb6)
  //     throw new HttpException(
  //       'Technology Survey Not Found',
  //       HttpStatus.BAD_REQUEST,
  //     );

  //   // login for creating application Number
  //   const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  //   let alphabeticPart = '';
  //   for (let i = 0; i < 4; i++) {
  //     alphabeticPart += alphabet.charAt(
  //       Math.floor(Math.random() * alphabet.length),
  //     ); // Randomly select alphabetic character
  //   }
  //   const timestamp = Date.now().toString().slice(-5); // Get last 5 digits of current timestamp
  //   const numericPart = Math.floor(Math.random() * 100000); // Generate random number
  //   const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
  //   const alphanumericPart = `${alphabeticPart}-${numericString}`;
  //   applicationDto.applicationName = `${applicationDto.agencyCode}-${applicationDto.permitTypeCode}-${alphanumericPart}`;

  //   const application = new Application({
  //     ...applicationDto,
  //     projects: (applicationDto.projectId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     permitTypes: (applicationDto.permitTypeId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //     categoryTypes: (applicationDto.categoryTypeId = {
  //       id: dataFromDb3.id,
  //     } as any),
  //     buildTypes: (applicationDto.buildTypeId = {
  //       id: dataFromDb4.id,
  //     } as any),
  //     applicationStatus: (applicationDto.applicationStatusId = {
  //       id: dataFromDb5.id,
  //     } as any),
  //     technologySurveys: (applicationDto.technologySurveyId = {
  //       id: dataFromDb6.id,
  //     } as any),
  //   });
  //   return this.applicationRepository.create(application);
  //   // const dataSaved = await this.applicationRepository.create(application);
  //   // const responseBody = JSON.stringify(dataSaved);
  //   // return new Response(responseBody, { status: 200 });
  // }

  // async findAllSubmissionLogs() {
  //   return this.submissionLogRepository
  //     .createQueryBuilder('submissionLog')
  //     .leftJoinAndSelect('submissionLog.application', 'application')
  //     .getMany();
  // }

  async findAllSubmissionLogs() {
    const submissionLogs = await this.submissionLogRepository
      .createQueryBuilder('submissionLog')
      .leftJoinAndSelect('submissionLog.application', 'application')
      .getMany();

    // Retrieve application status details for each submission log
    for (const log of submissionLogs) {
      if (log.applicationStatusId) {
        const applicationStatusData =
          await this.applicationStatusRepository.findOne({
            id: log.applicationStatusId,
          });
        log['applicationStatus'] = applicationStatusData;
      }
    }

    return submissionLogs;
  }

  async findOneSubmissionLog(applicationId: string) {
    return this.submissionLogRepository
      .createQueryBuilder('submissionLog')
      .where('submissionLog.applicationId = :applicationId', {
        applicationId,
      })
      .leftJoinAndSelect('submissionLog.application', 'application')
      .getOne();
  }

  async findAllSubmissionLog(applicationId: string) {
    return this.submissionLogRepository
      .createQueryBuilder('submissionLog')
      .where('submissionLog.applicationId = :applicationId', {
        applicationId,
      })
      .leftJoinAndSelect('submissionLog.application', 'application')
      .getMany();
  }

  // async findAllApplications() {
  //   return this.applicationRepository.findAll({
  //     relations: {
  //       projects: true,
  //       permitTypes: true,
  //       categoryTypes: true,
  //       buildTypes: true,
  //       applicationStatus: true,
  //       invoices: true,
  //       certificates: true,
  //     },
  //   });
  // }

  // async findAllApplications() {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')

  //     .getMany();
  // }
  async findAllApplicationsForCharts() {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')

      .getMany();
  }

  async findAllApplications(
    page: number = 1,
    limit: number = 100,
    search?: string,
    statusId?: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.submittedDate', 'ASC')
      .skip(skip)
      .take(limit);

    if (search) {
      query.where(
        `
        application.applicationName ILIKE :search
        OR permitTypes.name ILIKE :search
        OR categoryTypes.name ILIKE :search
        OR buildTypes.name ILIKE :search
        OR projects.upi ILIKE :search
        OR projects.projectDescription ILIKE :search
        OR projects.villageName ILIKE :search
        OR applicationStatus.name ILIKE :search
        `,
        { search: `%${search}%` },
      );
    }

    if (statusId) {
      query.andWhere('applicationStatus.id = :statusId', { statusId });
    }

    if (startDate && endDate) {
      query.andWhere('application.created_at BETWEEN :startDate AND :endDate', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      });
    }

    const [applications, total] = await query.getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async countApplicationsByStatus(userId: any) {
  //   const applications =
  //     await this.findAllApplicationsWithDetailsByUserId(userId);
  //   const countingBoard: { [status: string]: number } = {
  //     all: applications.length,
  //     pending: 0,
  //     approved: 0,
  //     rejected: 0,
  //   };

  //   applications.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PNDG':
  //         countingBoard.pending++;
  //         break;
  //       case 'APRD':
  //         countingBoard.approved++;
  //         break;
  //       case 'RJTD':
  //         countingBoard.rejected++;
  //         break;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard;
  // }

  // // get by userId and count the applications based on application statis with badrequest return empty
  // async countApplicationsByStatus(userId: any) {
  //   const applicationData =
  //     await this.findAllApplicationsWithDetailsByUserId(userId);

  //   if (applicationData.length === 0) {
  //     // throw new BadRequestException('No application found');
  //     return [];
  //   }

  //   const countingBoard: { [status: string]: number } = {
  //     all: applicationData.length,
  //     pending: 0,
  //     // approved: 0,
  //     certified: 0,
  //     review: 0,
  //     rejected: 0,
  //     rha: 0,

  //     submitted: 0,
  //     underReview: 0,
  //     underCorrection: 0,
  //     reSubmitted: 0,
  //     rhaReturned: 0,

  //     nonObjectionReviewed: 0,
  //     nonObjectionUnderReview: 0,
  //     preApproval: 0,
  //   };

  //   applicationData.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PND':
  //         countingBoard.pending++;
  //         break;
  //       // case 'SMB':
  //       //   countingBoard.approved++;
  //       //   break;
  //       case 'UNRV':
  //         countingBoard.underReview++;
  //         break;
  //       case 'RVW':
  //         countingBoard.review++;
  //         break;
  //       case 'CTFD':
  //         countingBoard.certified++;
  //         break;
  //       case 'CXL':
  //         countingBoard.rejected++;
  //         break;
  //       case 'NORHA':
  //         countingBoard.rha++;
  //         break;
  //       case 'SUB':
  //         countingBoard.submitted++;
  //         break;
  //       case 'UNCRN':
  //         countingBoard.underCorrection++;
  //         break;
  //       case 'RSMB':
  //         countingBoard.reSubmitted++;
  //         break;
  //       case 'RTNNO':
  //         countingBoard.rhaReturned++;
  //         break;
  //       case 'NORVW':
  //         countingBoard.nonObjectionReviewed++;
  //         break;
  //       case 'NOUNRV':
  //         countingBoard.nonObjectionUnderReview++;
  //         break;
  //       case 'PAPRV':
  //         countingBoard.preApproval++;
  //         break;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard;
  // }

  // get by userId and count return zerooos
  async countApplicationsByStatus(userId: any) {
    // Fetch application data by user ID and get data by land owner or engineer
    const applicationData =
      // await this.findAllApplicationsWithDetailsByUserId2(userId);
      await this.findAllApplicationsWithDetailsByUserId(userId);

    // Initialize counting board with zero counts
    const countingBoard: { [status: string]: number } = {
      all: 0, // Initialize total count to 0
      pending: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,
      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,
      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    // Return zeroed counting board if no applications are found
    if (applicationData.length === 0) {
      return countingBoard;
    }

    // Update counts based on application status codes
    countingBoard.all = applicationData.length; // Total number of applications
    applicationData.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard.pending++;
          break;
        case 'UNRV':
          countingBoard.underReview++;
          break;
        case 'RVW':
          countingBoard.review++;
          break;
        case 'CTFD':
          countingBoard.certified++;
          break;
        case 'CXL':
          countingBoard.rejected++;
          break;
        case 'NORHA':
          countingBoard.rha++;
          break;
        case 'SUB':
          countingBoard.submitted++;
          break;
        case 'UNCRN':
          countingBoard.underCorrection++;
          break;
        case 'RSMB':
          countingBoard.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard.nonObjectionReviewed++;
          break;
        case 'NOUNRV':
          countingBoard.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard.preApproval++;
          break;
        default:
          break;
      }
    });

    return countingBoard;
  }

  // get by userId and count the applications based on application statis with badrequest
  async countApplicationsByStatusSubmittedUser(submittedUserId: any) {
    const applicationData =
      await this.findAllApplicationsWithDetailsBySubmittedUserId(
        submittedUserId,
      );

    if (applicationData.length === 0) {
      // throw new BadRequestException('No application found');
      return [];
    }

    const countingBoard: { [status: string]: number } = {
      all: applicationData.length,
      pending: 0,
      // approved: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,

      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,

      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    applicationData.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard.pending++;
          break;
        // case 'SMB':
        //   countingBoard.approved++;
        //   break;
        case 'UNRV':
          countingBoard.underReview++;
          break;
        case 'RVW':
          countingBoard.review++;
          break;
        case 'CTFD':
          countingBoard.certified++;
          break;
        case 'CXL':
          countingBoard.rejected++;
          break;
        case 'NORHA':
          countingBoard.rha++;
          break;
        case 'SUB':
          countingBoard.submitted++;
          break;
        case 'UNCRN':
          countingBoard.underCorrection++;
          break;
        case 'RSMB':
          countingBoard.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard.nonObjectionReviewed++;
          break;
        case 'NOUNRV':
          countingBoard.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard.preApproval++;
          break;
        default:
          break;
      }
    });

    return countingBoard;
  }

  // get and count the applications based on application statis with badrequest
  // async countAllApplicationsByStatus() {
  //   const applicationData1 = await this.findAllApplicationsWithDetails();

  //   if (applicationData1.length === 0) {
  //     throw new BadRequestException('No application found');
  //   }

  //   const countingBoard1: { [status: string]: number } = {
  //     all: applicationData1.length,
  //     pending: 0,
  //     approved: 0,
  //     certified: 0,
  //     rejected: 0,
  //   };

  //   applicationData1.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PND':
  //         countingBoard1.pending++;
  //         break;
  //       case 'SMB':
  //         countingBoard1.approved++;
  //         break;
  //       case 'CTFD':
  //         countingBoard1.certified++;
  //         break;
  //       case 'RJTD':
  //         countingBoard1.rejected++;
  //         break;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard1;
  // }
  async countAllApplicationsByStatus() {
    try {
      const applicationData1 =
        await this.findAllApplicationsWithDetailsForCounts();

      if (applicationData1.length === 0) {
        // throw new BadRequestException('No application found');
        return [];
      }

      const countingBoard1: { [status: string]: number } = {
        all: applicationData1.length,
        pending: 0,
        // approved: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,

        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        rhaReviewed: 0,
        rhaUnderReviewed: 0,

        preApproval: 0,
      };

      applicationData1.forEach((application) => {
        if (
          application.applicationStatus &&
          application.applicationStatus.code
        ) {
          switch (application.applicationStatus.code) {
            case 'PND':
              countingBoard1.pending++;
              break;
            case 'UNRV':
              countingBoard1.underReview++;
              break;
            case 'RVW':
              countingBoard1.review++;
              break;
            case 'CTFD':
              countingBoard1.certified++;
              break;
            case 'CXL':
              countingBoard1.rejected++;
              break;
            case 'NORHA':
              countingBoard1.rha++;
              break;
            case 'SUB':
              countingBoard1.submitted++;
              break;
            case 'NORVW':
              countingBoard1.rhaReviewed++;
              break;
            case 'UNCRN':
              countingBoard1.underCorrection++;
              break;
            case 'RSMB':
              countingBoard1.reSubmitted++;
              break;
            case 'RTNNO':
              countingBoard1.rhaReturned++;
              break;
            case 'NOUNRV':
              countingBoard1.rhaUnderReviewed++;
              break;
            case 'PAPRV':
              countingBoard1.preApproval++;
              break;
            default:
              break;
          }
        } else {
          console.error(
            'Missing or invalid application status for application:',
            application,
          );
        }
      });

      return countingBoard1;
    } catch (error) {
      console.error('Error in countAllApplicationsByStatus:', error);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
  // async countAllApplicationsByStatus() {
  //   try {
  //     const applicationData1 = await this.findAllApplicationsWithDetails();

  //     if (applicationData1.length === 0) {
  //       // throw new BadRequestException('No application found');
  //       return [];
  //     }

  //     const countingBoard1: { [status: string]: number } = {
  //       all: applicationData1.length,
  //       pending: 0,
  //       approved: 0,
  //       certified: 0,
  //       review: 0,
  //       rejected: 0,
  //     };

  //     applicationData1.forEach((application) => {
  //       if (
  //         application.applicationStatus &&
  //         application.applicationStatus.code
  //       ) {
  //         switch (application.applicationStatus.code) {
  //           case 'PND':
  //             countingBoard1.pending++;
  //             break;
  //           case 'SMB':
  //             countingBoard1.approved++;
  //             break;
  //           case 'UNRV':
  //             countingBoard1.review++;
  //             break;
  //           case 'CTFD':
  //             countingBoard1.certified++;
  //             break;
  //           case 'RJTD':
  //             countingBoard1.rejected++;
  //             break;
  //           default:
  //             break;
  //         }
  //       } else {
  //         console.error(
  //           'Missing or invalid application status for application:',
  //           application,
  //         );
  //       }
  //     });

  //     return countingBoard1;
  //   } catch (error) {
  //     console.error('Error in countAllApplicationsByStatus:', error);
  //     throw new InternalServerErrorException('Internal Server Error');
  //   }
  // }

  // get by userId and count the applications based on application statis with badrequest

  // async countApplicationsByStatusByAgency(agencyId: any) {
  //   const applicationsData2 =
  //     await this.findAllApplicationsWithDetailsByAgencyId(agencyId);

  //   if (applicationsData2.length === 0) {
  //     // throw new BadRequestException('No application found');
  //     return [];
  //   }

  //   const countingBoard2: { [status: string]: number } = {
  //     all: applicationsData2.length,
  //     pending: 0,
  //     approved: 0,
  //     certified: 0,
  //     review: 0,
  //     rejected: 0,
  //     rha: 0,

  //     submitted: 0,
  //     underReview: 0,
  //     underCorrection: 0,
  //     reSubmitted: 0,
  //     rhaReturned: 0,
  //     rhaReviewed: 0,
  //     rhaUnderReviewed: 0,
  //   };

  //   applicationsData2.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PND':
  //         countingBoard2.pending++;
  //         break;
  //       case 'SMB':
  //         countingBoard2.approved++;
  //         break;
  //       case 'UNRV':
  //         countingBoard2.underReview++;
  //         break;
  //       case 'RVW':
  //         countingBoard2.review++;
  //         break;
  //       case 'CTFD':
  //         countingBoard2.certified++;
  //         break;
  //       case 'CXL':
  //         countingBoard2.rejected++;
  //         break;
  //       case 'NORHA':
  //         countingBoard2.rha++;
  //         break;
  //       case 'SUB':
  //         countingBoard2.submitted++;
  //         break;
  //       case 'NORVW':
  //         countingBoard2.rhaReviewed++;
  //         break;
  //       case 'UNCRN':
  //         countingBoard2.underCorrection++;
  //         break;
  //       case 'RSMB':
  //         countingBoard2.reSubmitted++;
  //         break;
  //       case 'RTNNO':
  //         countingBoard2.rhaReturned++;
  //         break;
  //       case 'NOUNRV':
  //         countingBoard2.rhaUnderReviewed++;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard2;
  // }

  async countApplicationsByStatusByAgency(agencyId: string) {
    const applicationsData2 =
      await this.findAllApplicationsByAgencyId(agencyId);

    if (applicationsData2.length === 0) {
      return [];
    }

    const countingBoard2: { [status: string]: number } = {
      all: applicationsData2.length,
      pending: 0,
      // approved: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,
      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,

      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    applicationsData2.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard2.pending++;
          break;
        // case 'SMB':
        //   countingBoard2.approved++;
        //   break;
        case 'RVW':
          countingBoard2.review++;
          break;
        case 'CTFD':
          countingBoard2.certified++;
          break;
        case 'CXL':
          countingBoard2.rejected++;
          break;
        case 'NORHA':
          countingBoard2.rha++;
          break;
        case 'SUB':
          countingBoard2.submitted++;
          break;
        case 'UNRV':
          countingBoard2.underReview++;
          break;
        case 'UNCRN':
          countingBoard2.underCorrection++;
          break;
        case 'RSMB':
          countingBoard2.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard2.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard2.nonObjectionReviewed++;
          break;

        case 'NOUNRV':
          countingBoard2.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard2.preApproval++;
          break;
        default:
          break;
      }
    });

    return countingBoard2;
  }

  // // dashboard of inspection
  // async countApplicationsByStatusByAgencyInspection(agencyId: string) {
  //   const applicationsData2 =
  //     await this.findAllApplicationsByAgencyIdForInspection(agencyId);

  //   if (applicationsData2.length === 0) {
  //     return [];
  //   }

  //   const countingBoard2: { [status: string]: number } = {
  //     all: applicationsData2.length,
  //     pending: 0,
  //     // approved: 0,
  //     certified: 0,
  //     review: 0,
  //     rejected: 0,
  //     rha: 0,
  //     submitted: 0,
  //     underReview: 0,
  //     underCorrection: 0,
  //     reSubmitted: 0,
  //     rhaReturned: 0,

  //     nonObjectionReviewed: 0,
  //     nonObjectionUnderReview: 0,
  //     preApproval: 0,
  //   };

  //   applicationsData2.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PND':
  //         countingBoard2.pending++;
  //         break;
  //       // case 'SMB':
  //       //   countingBoard2.approved++;
  //       //   break;
  //       case 'RVW':
  //         countingBoard2.review++;
  //         break;
  //       case 'CTFD':
  //         countingBoard2.certified++;
  //         break;
  //       case 'CXL':
  //         countingBoard2.rejected++;
  //         break;
  //       case 'NORHA':
  //         countingBoard2.rha++;
  //         break;
  //       case 'SUB':
  //         countingBoard2.submitted++;
  //         break;
  //       case 'UNRV':
  //         countingBoard2.underReview++;
  //         break;
  //       case 'UNCRN':
  //         countingBoard2.underCorrection++;
  //         break;
  //       case 'RSMB':
  //         countingBoard2.reSubmitted++;
  //         break;
  //       case 'RTNNO':
  //         countingBoard2.rhaReturned++;
  //         break;
  //       case 'NORVW':
  //         countingBoard2.nonObjectionReviewed++;
  //         break;

  //       case 'NOUNRV':
  //         countingBoard2.nonObjectionUnderReview++;
  //         break;
  //       case 'PAPRV':
  //         countingBoard2.preApproval++;
  //         break;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard2;
  // }
  // dashboard of inspection foundation
  async countApplicationsByStatusByAgencyInspectionFoundation(
    agencyId: string,
  ) {
    const applicationsData2 =
      await this.findAllApplicationsByAgencyIdForInspectionFoundation(agencyId);

    if (applicationsData2.length === 0) {
      if (applicationsData2.length === 0) {
        const countingBoard2: { [status: string]: number } = {
          all: 0,
          pending: 0,
          certified: 0,
          review: 0,
          rejected: 0,
          rha: 0,
          submitted: 0,
          underReview: 0,
          underCorrection: 0,
          reSubmitted: 0,
          rhaReturned: 0,
          nonObjectionReviewed: 0,
          nonObjectionUnderReview: 0,
          preApproval: 0,
        };
        return countingBoard2;
      }
    }

    const countingBoard2: { [status: string]: number } = {
      all: applicationsData2.length,
      pending: 0,
      // approved: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,
      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,

      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    applicationsData2.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard2.pending++;
          break;
        // case 'SMB':
        //   countingBoard2.approved++;
        //   break;
        case 'RVW':
          countingBoard2.review++;
          break;
        case 'CTFD':
          countingBoard2.certified++;
          break;
        case 'CXL':
          countingBoard2.rejected++;
          break;
        case 'NORHA':
          countingBoard2.rha++;
          break;
        case 'SUB':
          countingBoard2.submitted++;
          break;
        case 'UNRV':
          countingBoard2.underReview++;
          break;
        case 'UNCRN':
          countingBoard2.underCorrection++;
          break;
        case 'RSMB':
          countingBoard2.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard2.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard2.nonObjectionReviewed++;
          break;

        case 'NOUNRV':
          countingBoard2.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard2.preApproval++;
          break;
        default:
          break;
      }
    });

    return countingBoard2;
  }

  // dashboard of inspection
  async countApplicationsByStatusByAgencyInspectionOccupancy(agencyId: string) {
    const applicationsData2 =
      await this.findAllApplicationsByAgencyIdForInspectionOccupancy(agencyId);

    if (applicationsData2.length === 0) {
      if (applicationsData2.length === 0) {
        const countingBoard2: { [status: string]: number } = {
          all: 0,
          pending: 0,
          certified: 0,
          review: 0,
          rejected: 0,
          rha: 0,
          submitted: 0,
          underReview: 0,
          underCorrection: 0,
          reSubmitted: 0,
          rhaReturned: 0,
          nonObjectionReviewed: 0,
          nonObjectionUnderReview: 0,
          preApproval: 0,
        };
        return countingBoard2;
      }
    }

    const countingBoard2: { [status: string]: number } = {
      all: applicationsData2.length,
      pending: 0,
      // approved: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,
      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,

      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    applicationsData2.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard2.pending++;
          break;
        // case 'SMB':
        //   countingBoard2.approved++;
        //   break;
        case 'RVW':
          countingBoard2.review++;
          break;
        case 'CTFD':
          countingBoard2.certified++;
          break;
        case 'CXL':
          countingBoard2.rejected++;
          break;
        case 'NORHA':
          countingBoard2.rha++;
          break;
        case 'SUB':
          countingBoard2.submitted++;
          break;
        case 'UNRV':
          countingBoard2.underReview++;
          break;
        case 'UNCRN':
          countingBoard2.underCorrection++;
          break;
        case 'RSMB':
          countingBoard2.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard2.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard2.nonObjectionReviewed++;
          break;

        case 'NOUNRV':
          countingBoard2.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard2.preApproval++;
          break;
        default:
          break;
      }
    });

    return countingBoard2;
  }

  // get total application of MyBox
  // Get total application of MyBox
  async countApplicationsByStatusByAgencyAndRole(
    agencyId: string,
    code: string,
  ): Promise<{ number: number }> {
    const applicationsData = await this.findAllApplicationsByAgencyId(agencyId);

    if (applicationsData.length === 0) {
      return { number: 0 };
    }

    // Define the parameters and associated statuses
    const statusMapping = {
      DRCT: ['preApproval'],
      TMLD: ['review'],
      OFCMNG: ['reSubmitted', 'submitted'],
    };

    // Initialize counting board
    const countingBoard: { [status: string]: number } = {
      all: applicationsData.length,
      pending: 0,
      certified: 0,
      review: 0,
      rejected: 0,
      rha: 0,
      submitted: 0,
      underReview: 0,
      underCorrection: 0,
      reSubmitted: 0,
      rhaReturned: 0,
      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      preApproval: 0,
    };

    // Count each application status
    applicationsData.forEach((application) => {
      switch (application.applicationStatus.code) {
        case 'PND':
          countingBoard.pending++;
          break;
        case 'RVW':
          countingBoard.review++;
          break;
        case 'CTFD':
          countingBoard.certified++;
          break;
        case 'CXL':
          countingBoard.rejected++;
          break;
        case 'NORHA':
          countingBoard.rha++;
          break;
        case 'SUB':
          countingBoard.submitted++;
          break;
        case 'UNRV':
          countingBoard.underReview++;
          break;
        case 'UNCRN':
          countingBoard.underCorrection++;
          break;
        case 'RSMB':
          countingBoard.reSubmitted++;
          break;
        case 'RTNNO':
          countingBoard.rhaReturned++;
          break;
        case 'NORVW':
          countingBoard.nonObjectionReviewed++;
          break;
        case 'NOUNRV':
          countingBoard.nonObjectionUnderReview++;
          break;
        case 'PAPRV':
          countingBoard.preApproval++;
          break;
        default:
          break;
      }
    });

    // Calculate total based on the provided code
    const total = statusMapping[code]
      ? statusMapping[code].reduce(
          (accum, status) => accum + (countingBoard[status] || 0),
          0,
        )
      : 0;

    return { number: total };
  }

  async findAllApplicationsWithDetailsForCounts() {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();
  }
  // async findAllApplicationsWithDetails() {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .getMany();
  // }

  // with pagination
  async findAllApplicationsWithDetails(page: number = 1, limit: number = 100) {
    const skip = (page - 1) * limit;

    try {
      const [applications, total] =
        await this.applicationEntityManagerRepository
          .createQueryBuilder('application')
          .leftJoinAndSelect(
            'application.applicationStatus',
            'applicationStatus',
          )
          .orderBy('application.submittedDate', 'ASC')
          .skip(skip)
          .take(limit)
          .getManyAndCount();

      return {
        data: applications,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error in findAllApplicationsWithDetails:', error);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  // async findAllApplicationsWithDetails() {
  //   try {
  //     return await this.applicationEntityManagerRepository
  //       .createQueryBuilder('application')
  //       .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //       .getMany();
  //   } catch (error) {
  //     console.error('Error in findAllApplicationsWithDetails:', error);
  //     throw new InternalServerErrorException('Internal Server Error');
  //   }
  // }

  async findAllApplicationsWithDetailsByUserId(userId: any) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.userId = :userId', { userId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.created_at')
      .getMany();
  }

  async findAllApplicationsWithDetailsByLandOwnerUserId(userId: string) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .where('projects.userId = :userId', { userId }) // Changed from application.userId
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.created_at', 'DESC')
      .getMany();
  }

  // find all applications by submission date range
  // async findAllApplicationsBySubmissionDateRange(
  //   startDate: Date,
  //   endDate: Date,
  // ) {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoin('application.projects', 'project')
  //     .leftJoin('application.buildTypes', 'buildType')
  //     .leftJoin('application.applicationStatus', 'status')
  //     .leftJoin('application.applicationApprovals', 'approval')
  //     .leftJoin('application.certificates', 'certificate')
  //     .leftJoin('application.permitTypes', 'permitType') // <-- add this line
  //     .where('application.created_at BETWEEN :startDate AND :endDate', {
  //       startDate: startDate.toISOString(),
  //       endDate: endDate.toISOString(),
  //     })
  //     .select([
  //       'application.applicationName AS "Application_number"',
  //       'application.upi AS "Plot_No"',
  //       'application.originalPlotSize AS "Plot_size"',
  //       'application.combiningPlotSize AS "combiningPlotSize"',
  //       'application.buildUpArea AS "buildUpArea"',
  //       'application.numberOfFloor AS "Number_of_Floors"',
  //       'application.capacityInformation AS "Capacity_Info"',
  //       'project.districtName AS "DistrictName"',
  //       'project.selectedUse AS "Registered_Usage"',
  //       'application.ProjectCostInRwf AS "ESTIMATED_COST_OF_PROJECT_RWF"',
  //       'buildType.name AS "Building_Type"',
  //       'permitType.name AS "Permit_Type"', // <-- add this line
  //       'status.name AS "Application_Status"',
  //       'approval.updated_at AS "date_of_response"',
  //       'certificate.expiredDate AS "expiredDate"',
  //     ])
  //     .orderBy('application.created_at', 'DESC')
  //     .getRawMany();
  // }
  // async findAllApplicationsBySubmissionDateRange(
  //   startDate: Date,
  //   endDate: Date,
  // ) {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoin('application.projects', 'project')
  //     .leftJoin('application.buildTypes', 'buildType')
  //     .leftJoin('application.applicationStatus', 'status')
  //     .leftJoin('application.applicationApprovals', 'approval')
  //     .leftJoin('application.certificates', 'certificate')
  //     .leftJoin('application.permitTypes', 'permitType')
  //     .where('application.created_at BETWEEN :startDate AND :endDate', {
  //       startDate: startDate.toISOString(),
  //       endDate: endDate.toISOString(),
  //     })
  //     .andWhere('status.name != :draft', { draft: 'Draft' }) // <-- Exclude Draft
  //     .select([
  //       'application.applicationName AS "Application_number"',
  //       'application.upi AS "Plot_No"',
  //       'application.originalPlotSize AS "Plot_size"',
  //       'application.combiningPlotSize AS "combiningPlotSize"',
  //       'application.buildUpArea AS "buildUpArea"',
  //       'application.numberOfFloor AS "Number_of_Floors"',
  //       'application.capacityInformation AS "Capacity_Info"',
  //       'project.districtName AS "DistrictName"',
  //       'project.selectedUse AS "Registered_Usage"',
  //       'application.ProjectCostInRwf AS "ESTIMATED_COST_OF_PROJECT_RWF"',
  //       'buildType.name AS "Building_Type"',
  //       'permitType.name AS "Permit_Type"',
  //       'status.name AS "Application_Status"',
  //       'approval.updated_at AS "date_of_response"',
  //       'certificate.expiredDate AS "expiredDate"',
  //       'application.submittedDate AS "Date_of_Submission"', // <-- Add this
  //     ])
  //     .orderBy('application.created_at', 'DESC')
  //     .getRawMany();
  // }
  async findAllApplicationsBySubmissionDateRange(
    startDate: Date,
    endDate: Date,
  ) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoin('application.projects', 'project')
      .leftJoin('application.buildTypes', 'buildType')
      .leftJoin('application.applicationStatus', 'status')
      .leftJoin('application.applicationApprovals', 'approval')
      .leftJoin('application.certificates', 'certificate')
      .leftJoin('application.permitTypes', 'permitType')
      .where('application.created_at BETWEEN :startDate AND :endDate', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      .andWhere('status.name != :draft', { draft: 'Draft' })
      .select([
        'application.applicationName AS "Application_number"',
        'application.upi AS "Plot_No"',
        'application.originalPlotSize AS "Plot_size"',
        'application.combiningPlotSize AS "combiningPlotSize"',
        'application.buildUpArea AS "buildUpArea"',
        'application.numberOfFloor AS "Number_of_Floors"',
        'application.capacityInformation AS "Capacity_Info"',
        'project.districtName AS "DistrictName"',
        'project.selectedUse AS "Registered_Usage"',
        'application.ProjectCostInRwf AS "ESTIMATED_COST_OF_PROJECT_RWF"',
        'buildType.name AS "Building_Type"',
        'permitType.name AS "Permit_Type"',
        'status.name AS "Application_Status"',
        'approval.updated_at AS "date_of_response"',
        'certificate.expiredDate AS "expiredDate"',
        'application.submittedDate AS "Date_of_Submission"',
        `CASE 
         WHEN approval.updated_at IS NOT NULL 
           THEN DATE_PART('day', approval.updated_at - application.submittedDate)
         ELSE DATE_PART('day', NOW() - application.submittedDate)
       END AS "Days_Taken"`,
      ])
      .orderBy('application.created_at', 'DESC')
      .getRawMany();
  }

  async searchApplicationsByDateRangeFormonioting(
    startDate: Date,
    endDate: Date,
  ) {
    console.log('Start Date:', startDate);
    console.log('End Date:', endDate);
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoin('application.projects', 'project')
      .leftJoin('application.buildTypes', 'buildType')
      .leftJoin('application.applicationStatus', 'status')
      .leftJoin('application.applicationApprovals', 'approval')
      .leftJoin('application.certificates', 'certificate')
      .leftJoin('application.permitTypes', 'permitType')
      .leftJoin('application.invoices', 'invoice')
      .leftJoin('invoice.invoiceStatus', 'invoiceStatus')
      // .leftJoin('invoice.invoiceType', 'invoiceType') // <-- join with invoice type
      // invoice type
      .addSelect([
        'invoice.amount as amount',
        'invoice.invoiceNumber as invoice_number',
        'invoice.agencyCode',
        //paymentStatus
        // invoice type name
        // 'invoiceType.name AS invoiceTypeName', // <-- use the correct alias
        'invoice.paymentStatus as payment_status',
        'invoice.created_at AS invoiceCreatedAt',
        'invoice.updated_at AS invoicePaidAt',
        'invoiceStatus.name AS invoiceStatusName', // <-- use the correct alias
        // ...other fields
      ])

      .where('application.created_at BETWEEN :startDate AND :endDate', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      })
      .orderBy('application.created_at', 'DESC')
      .getRawMany();
  }

  // async findAllApplicationsWithDetailsByUserId(userId: any) {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.userId = :userId', { userId })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .orderBy('application.created_at')
  //     .getMany();
  // }

  async findAllApplicationsWithDetailsBySubmittedUserId(
    submittedByUserId: any,
  ) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.submittedByUserId = :submittedByUserId', {
        submittedByUserId,
      })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.created_at')
      .getMany();
  }

  // check if it is the user or submittedUser
  // async findAllApplicationsWithDetailsByUserId(
  //   userId: any,
  //   submittedByUserId: any,
  // ) {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where(
  //       new Brackets((qb) => {
  //         qb.where('application.userId = :userId', { userId }).orWhere(
  //           'application.submittedByUserId = :submittedByUserId',
  //           { submittedByUserId },
  //         );
  //       }),
  //     )
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .orderBy('application.created_at')
  //     .getMany();
  // }

  // with pagination
  async findAllApplicationsWithDetailsByAgency(
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ) {
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.submittedDate', 'ASC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
  // async findAllApplicationsWithDetailsByAgency(agencyId: any) {
  //   return await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .orderBy('application.submittedDate')
  //     .getMany();

  //   // .orderBy('application.created_at')
  // }
  async findAllApplicationsWithDetailsByAgencyForCharts(agencyId: any) {
    return await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .orderBy('application.submittedDate')
      .getMany();

    // .orderBy('application.created_at')
  }

  // async findAllApplicationsWithDetailsByApplicationStatus(
  //   applicationStatusId: any,
  // ) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.applicationStatusId = :applicationStatusId', {
  //       applicationStatusId,
  //     })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .orderBy('application.submittedDate')
  //     .getMany();

  //   // if (applications.length === 0) {
  //   //   throw new NotFoundException(
  //   //     `No applications found with status id ${applicationStatusId}`,
  //   //   );
  //   // }

  //   return applications;
  // }

  // with pagination
  async findAllApplicationsWithDetailsByApplicationStatus(
    applicationStatusId: string,
    page: number = 1,
    limit: number = 100,
  ) {
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.applicationStatusId = :applicationStatusId', {
        applicationStatusId,
      })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.submittedDate', 'ASC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
  //   applicationStatusId: any,
  // ) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.applicationStatusId = :applicationStatusId', {
  //       applicationStatusId,
  //     })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .getMany();

  //   // if (applications.length === 0) {
  //   //   throw new NotFoundException(
  //   //     `No applications found with status id ${applicationStatusId}`,
  //   //   );
  //   // }

  //   return applications;
  // }

  // async findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
  //   applicationStatusId: string,
  //   agencyId: string,
  // ): Promise<Application[]> {
  //   // retrive angency data by agencyId
  //   // const agency = await this.agencyRepository.findOne({
  //   //   where: { id: agencyId },
  //   // });
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere('application.applicationStatus.id = :applicationStatusId', {
  //       applicationStatusId,
  //     })
  //     .getMany();

  //   if (!applications || applications.length === 0) {
  //     throw new NotFoundException(
  //       `No applications found for agency ID: ${agencyId} and application status ID: ${applicationStatusId}`,
  //     );
  //   }

  //   return applications;
  // }
  // fuction to fund angecy by agencyId from extenal
  // async findAgencyById(agencyId: string) {
  //   const agency = await this.agencyRepository.findOne({
  //     where: { id: agencyId },
  //   });
  //   if (!agency) {
  //     throw new NotFoundException(`Agency with ID ${agencyId} not found`);
  //   }
  //   return agency;
  // }

  // with pagination
  async findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
    applicationStatusId: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    // retrieve agency data
    const agency = await this.checkAgencyDataById(agencyId);
    if (!agency) {
      throw new NotFoundException(`Agency with ID ${agencyId} not found`);
    }

    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId,
      })
      .orderBy('application.submittedDate', 'ASC');

    if (agency.code === 'COK') {
      query.andWhere('application.permitTypeCode != :code1', { code1: 'FINS' });
    }

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // with pagination
  async findApplicationsByApplicationCodeAndAgencyId(
    applicationStatusCode: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const agency = await this.checkAgencyDataById(agencyId);
    if (!agency) {
      throw new NotFoundException(`Agency with ID ${agencyId} not found`);
    }

    const status = await this.applicationStatusEntityManagerRepository
      .createQueryBuilder('applicationStatus')
      .where('applicationStatus.code = :code', { code: applicationStatusCode })
      .getOne();

    if (!status) {
      throw new NotFoundException(
        `ApplicationStatus with code '${applicationStatusCode}' not found`,
      );
    }

    const applicationStatusID = status.id;

    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId: applicationStatusID,
      })
      .orderBy('application.submittedDate', 'ASC');

    if (agency.code === 'COK') {
      query.andWhere('application.permitTypeCode != :code1', { code1: 'FINS' });
    }

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
  //   applicationStatusId: string,
  //   agencyId: string,
  // ): Promise<Application[]> {
  //   // retrive angency data by agencyId
  //   const agency = await this.checkAgencyDataById(agencyId);

  //   if (!agency) {
  //     throw new NotFoundException(`Agency with ID ${agencyId} not found`);
  //   }
  //   const query = this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere('application.applicationStatus.id = :applicationStatusId', {
  //       applicationStatusId,
  //     })
  //     .orderBy('application.submittedDate');
  //   if (agency.code === 'COK') {
  //     query.andWhere('application.permitTypeCode != :code1', { code1: 'FINS' });
  //   }
  //   const applications = await query.getMany();
  //   // if (!applications || applications.length === 0) {
  //   //   throw new NotFoundException(
  //   //     `No applications found for agency ID: ${agencyId} and application status ID: ${applicationStatusId}`,
  //   //   );
  //   // }

  //   return applications;
  // }

  // with pagination
  async findAllApplicationForDirectorAndAgencyId(
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('applicationStatus.code IN (:...statusCodes)', {
        statusCodes: ['RVW', 'SUB', 'RSMB'],
      })
      .orderBy('application.submittedDate', 'ASC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
  // async findAllApplicationForDirectorAndAgencyId(agencyId: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere('applicationStatus.code IN (:...statusCodes)', {
  //       statusCodes: ['RVW', 'SUB', 'RSMB'],
  //     })
  //     .orderBy('application.submittedDate')
  //     .getMany();

  //   return applications;
  // }

  async findApplicationsByParams(
    applicationStatusId?: string,
    agencyId?: string,
    permitTypeId?: string,
  ) {
    const queryBuilder = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes');

    if (applicationStatusId) {
      queryBuilder.andWhere(
        'application.applicationStatusId = :applicationStatusId',
        {
          applicationStatusId,
        },
      );
    }

    if (permitTypeId) {
      queryBuilder.andWhere('application.permitTypeId = :permitTypeId', {
        permitTypeId,
      });

      if (agencyId) {
        queryBuilder.andWhere('application.agencyId = :agencyId', { agencyId });
      }
    }

    const applications = await queryBuilder.getMany();

    if (applications.length === 0) {
      throw new NotFoundException(
        'No applications found for the given parameters.',
      );
    }

    return applications;
  }

  async findAllApplicationsWithNonObjections() {
    const isNonObjection = '1';
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.isNonObjection = :isNonObjection', {
        isNonObjection,
      })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.submittedDate')
      .getMany();

    if (applications.length === 0) {
      throw new NotFoundException(`No applications found with non objections`);
    }

    return applications;
  }

  // async findAllApplicationsWithNonObjectionsRHA() {
  //   const isNonObjection = '1';
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .select(
  //       'DISTINCT ON (application.applicationNumber) application.id',
  //       'id',
  //     )
  //     .where('application.isNonObjection = :isNonObjection', {
  //       isNonObjection,
  //     })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .orderBy('application.submittedDate')
  //     .addOrderBy('application.updated_at', 'DESC')
  //     .getMany();

  //   return applications;
  // }

  async findAllApplicationsWithNonObjectionsRHA() {
    const isNonObjection = '1';
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      // .distinctOn(['application.applicationNumberForIremboHub']) // correct way in TypeORM
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .where('application.isNonObjection = :isNonObjection', { isNonObjection })
      .orderBy('application.applicationNumberForIremboHub', 'ASC') // must match distinctOn field
      .addOrderBy('application.submittedDate', 'ASC')
      .addOrderBy('application.updated_at', 'DESC')
      .getMany();

    return applications;
  }

  async findAllApplicationsWithNonObjectionInAgency(agencyId: any) {
    const isNonObjection = '1';
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.isNonObjection = :isNonObjection', {
        isNonObjection,
      })
      .andWhere('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .getMany();

    if (applications.length === 0) {
      throw new NotFoundException(`No applications found with non objections`);
    }

    return applications;
  }

  async findApplicationsWithDetailsByCategoryTypeId(categoryTypeId: any) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.categoryTypes = :categoryTypeId', { categoryTypeId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .getMany();

    if (applications.length === 0) {
      throw new NotFoundException(
        `No applications found with id ${categoryTypeId}`,
      );
    }

    return applications;
  }

  async searchApplicationByApplicationName(applicationName: any) {
    const application = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.applicationName = :applicationName', {
        applicationName,
      })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect(
        'application.applicationApprovals',
        'applicationApprovals',
      )
      .leftJoinAndSelect(
        'application.applicationApprovalCheckLists',
        'applicationApprovalCheckLists',
      )
      .getOne();

    if (!application) {
      throw new NotFoundException(
        `No application found with name ${applicationName}`,
      );
    }

    return application;
  }
  async searchApplicationByApplicationNameInAgency(
    applicationName: any,
    agencyId: any,
  ) {
    const application = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', {
        agencyId,
      })
      .andWhere('application.applicationName = :applicationName', {
        applicationName,
      })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect(
        'application.applicationApprovals',
        'applicationApprovals',
      )
      .leftJoinAndSelect(
        'application.applicationApprovalCheckLists',
        'applicationApprovalCheckLists',
      )
      .getOne();

    if (!application) {
      throw new NotFoundException(
        `No application found with name ${applicationName} in this agency`,
      );
    }

    return application;
  }

  async generalSearchApplications(params: {
    agencyId?: string;
    applicationName?: string;
    UPI?: string;
    applicationStatusId?: string;
    permitTypeId?: string;
    categoryTypeId?: string;
    buildTypeId?: string;
  }) {
    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .leftJoinAndSelect(
        'application.applicationApprovals',
        'applicationApprovals',
      )
      .leftJoinAndSelect(
        'application.applicationApprovalCheckLists',
        'applicationApprovalCheckLists',
      );

    if (params.agencyId) {
      query.andWhere('application.agencyId = :agencyId', {
        agencyId: params.agencyId,
      });
    }
    if (params.applicationName) {
      query.andWhere('application.applicationName = :applicationName', {
        applicationName: params.applicationName,
      });
    }
    if (params.UPI) {
      query.andWhere('application.upi = :UPI', { UPI: params.UPI });
    }
    if (params.applicationStatusId) {
      query.andWhere('application.applicationStatusId = :applicationStatusId', {
        applicationStatusId: params.applicationStatusId,
      });
    }
    if (params.permitTypeId) {
      query.andWhere('application.permitTypesId = :permitTypeId', {
        permitTypeId: params.permitTypeId,
      });
    }
    if (params.categoryTypeId) {
      query.andWhere('application.categoryTypesId = :categoryTypeId', {
        categoryTypeId: params.categoryTypeId,
      });
    }
    if (params.buildTypeId) {
      query.andWhere('application.buildTypesId = :buildTypeId', {
        buildTypeId: params.buildTypeId,
      });
    }

    const applications = await query.getMany();
    if (!applications || applications.length === 0) {
      throw new NotFoundException(
        'No applications found matching the criteria.',
      );
    }

    return applications;
  }

  // async findAllApplicationsWithDetailsByAgencyId(agencyId: any) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')

  //     .getMany();

  //   if (applications.length === 0) {
  //     throw new NotFoundException(
  //       `No applications found in this agency id ${agencyId}`,
  //     );
  //   }

  //   return applications;
  // }

  // with pagination
  async findAllApplicationsWithDetailsByAgencyId(
    agencyId: any,
    page: number = 1,
    limit: number = 100,
  ) {
    const skip = (page - 1) * limit;

    const [applications, total] = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .orderBy('application.submittedDate', 'ASC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationsByAgencyId(agencyId: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .getMany();

  //   return applications;
  // }
  async findAllApplicationsByAgencyId(agencyId: string) {
    const agency = await this.checkAgencyDataById(agencyId);
    if (!agency) {
      throw new NotFoundException(`Agency with ID ${agencyId} not found`);
    }
    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus');
    if (agency.code === 'COK') {
      query.andWhere('application.permitTypeCode != :code1', { code1: 'FINS' });
    }
    const applications = await query.getMany();

    return applications;
  }
  // async findAllApplicationsByAgencyId(agencyId: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .getMany();

  //   if (applications.length === 0) {
  //     throw new NotFoundException(
  //       `No applications found in this agency id ${agencyId}`,
  //     );
  //   }

  //   return applications;
  // }

  // with pagination
  async findAllApplicationsByAgencyIdForInspection(
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('application.permitTypeCode IN (:...codes)', {
        codes: ['FINS', 'OCP'],
      })
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .orderBy('application.updated_at', 'DESC');

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationsByAgencyIdForInspection(agencyId: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     // .andWhere('permitTypes.code IN (:...code)', { code: ['FINS', 'OCP'] })
  //     .andWhere('application.permitTypeCode IN (:...codes)', {
  //       codes: ['FINS', 'OCP'],
  //     })
  //     // .andWhere('permitTypes.code = :code1 OR permitTypes.code = :code2', {
  //     //   code1: 'FINS',
  //     //   code2: 'OCP',
  //     // })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')

  //     .getMany();

  //   return applications;
  // }

  // // revieweers
  // async findAllApplicationsByAgencyIdForInspectionReviewer(agencyId: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     // .andWhere('permitTypes.code IN (:...code)', { code: ['FINS', 'OCP'] })
  //     .andWhere('application.permitTypeCode IN (:...codes)', {
  //       codes: ['FINS', 'OCP'],
  //     })
  //     // .andWhere('permitTypes.code = :code1 OR permitTypes.code = :code2', {
  //     //   code1: 'FINS',
  //     //   code2: 'OCP',
  //     // })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')

  //     .getMany();

  //   return applications;
  // }

  // with pagination
  async findAllApplicationsByAgencyIdForInspectionReviewer(
    userId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const [reviewers, total] = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('reviewers.userId = :userId', { userId })
      .andWhere('reviewers.status = :status', { status: 0 })
      .leftJoinAndSelect('reviewers.applications', 'applications')
      .andWhere('applications.permitTypeCode IN (:...codes)', {
        codes: ['FINS', 'OCP'],
      })
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .leftJoinAndSelect('applications.invoices', 'invoices')
      .leftJoinAndSelect('applications.certificates', 'certificates')
      .leftJoinAndSelect('applications.projects', 'projects')
      .andWhere('applicationStatus.code = :code', { code: 'UNRV' })
      .orderBy('applications.updated_at', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const applications = reviewers.flatMap((reviewer) => {
      const apps = Array.isArray(reviewer.applications)
        ? reviewer.applications
        : [reviewer.applications];

      return apps.map((application) => ({
        id: application.id,
        upi: application.upi,
        isLocked: application.isLocked,
        isResubmitted: application.isResubmitted,
        combiningPlotSize: application.combiningPlotSize,
        isNonObjection: application.isNonObjection,
        isInspected: application.isInspected,
        isAssociatedUpi: application.isAssociatedUpi,
        isEIAVerified: application.isEIAVerified,
        buildUpArea: application.buildUpArea,
        numberOfFloor: application.numberOfFloor,
        grossFloorArea: application.grossFloorArea,
        numberOfParkingSpace: application.numberOfParkingSpace,
        priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
        capacityInformation: application.capacityInformation,
        numberOfDwellingUnits: application.numberOfDwellingUnits,
        DescriptionOfOperation: application.DescriptionOfOperation,
        percentageSpaceUse: application.percentageSpaceUse,
        waterConsumption: application.waterConsumption,
        electricityConsumption: application.electricityConsumption,
        DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
        ProjectCostInUSD: application.ProjectCostInUSD,
        ProjectCostInRwf: application.ProjectCostInRwf,
        agencyId: application.agencyId,
        userId: application.userId,
        submittedByUserId: application.submittedByUserId,
        certificateNumberEIA: application.certificateNumberEIA,
        applicationName: application.applicationName,
        permitTypeCode: application.permitTypes?.code,
        agencyCode: application.agencyCode,
        created_at: application.created_at,
        updated_at: application.updated_at,
        assignUsersFoReview: application.assignUsersFoReview,
        other: application.other,
        projects: application.projects,
        permitTypes: application.permitTypes,
        applicationStatus: application.applicationStatus,
        buildTypes: application.buildTypes,
        categoryTypes: application.categoryTypes,
      }));
    });

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationsByAgencyIdForInspectionReviewer(userId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .andWhere('reviewers.status = :status', { status: 0 })
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .andWhere('applications.permitTypeCode IN (:...codes)', {
  //       codes: ['FINS', 'OCP'],
  //     })
  //     .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .andWhere('applicationStatus.code = :code', { code: 'UNRV' })
  //     .getMany();

  //   // Check if applications is an array, if not, wrap it as an array
  //   const applications = reviewers.flatMap((reviewer) => {
  //     const applications = Array.isArray(reviewer.applications)
  //       ? reviewer.applications
  //       : [reviewer.applications]; // Wrap single object in an array

  //     return applications.map((application) => ({
  //       id: application.id,
  //       upi: application.upi,
  //       isLocked: application.isLocked,
  //       isResubmitted: application.isResubmitted,
  //       combiningPlotSize: application.combiningPlotSize,
  //       isNonObjection: application.isNonObjection,
  //       isInspected: application.isInspected,
  //       isAssociatedUpi: application.isAssociatedUpi,
  //       isEIAVerified: application.isEIAVerified,
  //       buildUpArea: application.buildUpArea,
  //       numberOfFloor: application.numberOfFloor,
  //       grossFloorArea: application.grossFloorArea,
  //       numberOfParkingSpace: application.numberOfParkingSpace,
  //       priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
  //       capacityInformation: application.capacityInformation,
  //       numberOfDwellingUnits: application.numberOfDwellingUnits,
  //       DescriptionOfOperation: application.DescriptionOfOperation,
  //       percentageSpaceUse: application.percentageSpaceUse,
  //       waterConsumption: application.waterConsumption,
  //       electricityConsumption: application.electricityConsumption,
  //       DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
  //       ProjectCostInUSD: application.ProjectCostInUSD,
  //       ProjectCostInRwf: application.ProjectCostInRwf,
  //       agencyId: application.agencyId,
  //       userId: application.userId,
  //       submittedByUserId: application.submittedByUserId,
  //       certificateNumberEIA: application.certificateNumberEIA,
  //       applicationName: application.applicationName,
  //       permitTypeCode: application.permitTypes?.code,
  //       agencyCode: application.agencyCode,
  //       created_at: application.created_at,
  //       updated_at: application.updated_at,
  //       assignUsersFoReview: application.assignUsersFoReview,
  //       other: application.other,
  //       projects: application.projects,
  //       permitTypes: application.permitTypes,
  //       applicationStatus: application.applicationStatus,
  //       buildTypes: application.buildTypes,
  //       categoryTypes: application.categoryTypes,
  //     }));
  //   });

  //   return applications;
  // }

  async findAllApplicationsByAgencyIdForInspectionFoundation(agencyId: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .andWhere('permitTypes.code = :code', { code: 'FINS' })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();

    return applications;
  }

  async findAllApplicationsByAgencyIdForInspectionOccupancy(agencyId: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .andWhere('permitTypes.code = :code', { code: 'OCP' })
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();

    // if (applications.length === 0) {
    //   throw new NotFoundException(
    //     `No occupancy applications found with agencyId ${agencyId}`,
    //   );
    // }

    return applications;
  }

  // async findOneApplication(id: string) {
  //   return this.applicationRepository.findOne({ id });
  // }

  async findOneApplication(id: string) {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('applications')
      .where('applications.id = :id', { id })
      .leftJoinAndSelect('applications.projects', 'projects')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .select([
        'applications',
        'projects',
        'permitTypes.id',
        'permitTypes.code',
        'permitTypes.name',
        'categoryTypes.code',
        'categoryTypes.name',
        'buildTypes.code',
        'buildTypes.name',
      ])
      .getOne();
  }

  async findOneApplicationWithAllDetails(id: string) {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('applications')
      .where('applications.id = :id', { id })
      .leftJoinAndSelect('applications.projects', 'projects')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .getOne();
  }

  // async findOneApplicationAllDetails(id: string) {
  //   return this.applicationEntityManagerRepository
  //     .createQueryBuilder('applications')
  //     .where('applications.id = :id', { id })
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .getMany();
  // }

  async findOneApplicationAllDetails(id: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.id = :id', { id })
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();

    const transformedApplications = await Promise.all(
      applications.map(async (application: Application) => {
        const project = application.projects;

        // Initialize reviewersOnApplications as an empty array
        let reviewersWithNames = [];

        // Check if there are any reviewers for this application
        const reviewers = await this.findReviewersOnApplication(application.id);

        if (reviewers && reviewers.length > 0) {
          // Add firstName, lastName, and checklistStatus to each reviewer
          reviewersWithNames = await Promise.all(
            reviewers.map(async (reviewer) => {
              try {
                const userDetails = (await this.checkUser(reviewer.userId)) || {
                  firstName: 'Unknown',
                  lastName: 'User',
                };

                const checklistStatus =
                  reviewer.status === '0' ? 'Pending' : 'Reviewed';
                return {
                  ...reviewer,
                  firstName: userDetails.firstName,
                  lastName: userDetails.lastName,
                  checklistStatus,
                };
              } catch (error) {
                // Gracefully handle errors in checkUser
                console.error(
                  `Error fetching user details for reviewer: ${reviewer.userId}`,
                  error,
                );
                return {
                  ...reviewer,
                  firstName: 'Unknown',
                  lastName: 'User',
                  checklistStatus: 'Pending',
                };
              }
            }),
          );
        }

        // Assign reviewersWithNames (which could be an empty array) to application
        application.reviewersOnApplications = reviewersWithNames;

        if (project) {
          try {
            const userDetails = (await this.checkUser(project.userId)) || {
              firstName: 'Unknown',
              lastName: 'User',
            };

            const senderDetails = (await this.checkUser(
              application.submittedByUserId,
            )) || {
              firstName: 'Unknown',
              lastName: 'User',
            };

            // Handle userDetails and senderDetails null cases and omit passwords
            const { ...userDetailsWithoutPassword } = userDetails;
            const { ...senderDetailsWithoutPassword } = senderDetails;

            const applicationWithUserDetails: ApplicationWithUserDetails = {
              ...application,
              userDetails: userDetailsWithoutPassword,
              senderDetails: senderDetailsWithoutPassword,
            };
            return applicationWithUserDetails;
          } catch (error) {
            // Gracefully handle errors when fetching project or sender details
            console.error(
              `Error fetching details for project or sender`,
              error,
            );
            return {
              ...application,
              userDetails: { firstName: 'Unknown', lastName: 'User' },
              senderDetails: { firstName: 'Unknown', lastName: 'User' },
            };
          }
        }

        return application;
      }),
    );

    return transformedApplications;
  }

  // Helper method to find reviewers associated with an application
  async findReviewersOnApplication(applicationId: string) {
    return this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('"reviewers"."applicationsId" = :applicationId', { applicationId }) // Use double quotes
      .getMany();
  }

  async findReviewersOnApplication2(applicationId: string) {
    return this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('"reviewers"."applicationsId" = :applicationId', { applicationId }) // Use double quotes
      .getMany();
  }

  // // without reviewers,
  // async findOneApplicationAllDetails(id: string) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.id = :id', { id })
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .getMany();

  //   const transformedApplications = await Promise.all(
  //     applications.map(async (application: Application) => {
  //       const project = application.projects;
  //       if (project) {
  //         const userDetails = await this.checkUser(project.userId);
  //         // Omit password from userDetails
  //         // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //         const { password, ...userDetailsWithoutPassword } = userDetails;
  //         const applicationWithUserDetails: ApplicationWithUserDetails = {
  //           ...application,
  //           userDetails: userDetailsWithoutPassword,
  //         };
  //         return applicationWithUserDetails;
  //       }
  //       return application;
  //     }),
  //   );

  //   return transformedApplications;
  // }

  async findOneApplicationAllDetailsByApplicationId(applicationNumber: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.applicationNumber = :applicationNumber', {
        applicationNumber,
      })
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .getMany();

    const transformedApplications = await Promise.all(
      applications.map(async (application: Application) => {
        const project = application.projects;

        if (project) {
          // Fetch userDetails and handle missing users with default values
          const userDetails = await this.checkUser(project.userId);
          const senderDetails = await this.checkUser(
            application.submittedByUserId,
          );

          // Provide default values if userDetails or senderDetails is null/undefined
          const userDetailsSafe = userDetails
            ? { ...userDetails }
            : { firstName: 'Unknown', lastName: 'User', password: null };

          const senderDetailsSafe = senderDetails
            ? { ...senderDetails }
            : { firstName: 'Unknown', lastName: 'User', password: null };

          // Omit password from both userDetails and senderDetails
          const { ...userDetailsWithoutPassword } = userDetailsSafe;
          const { ...senderDetailsWithoutPassword } = senderDetailsSafe;

          const applicationWithUserDetails: ApplicationWithUserDetails = {
            ...application,
            userDetails: userDetailsWithoutPassword || null,
            senderDetails: senderDetailsWithoutPassword || null,
          };

          return applicationWithUserDetails;
        }

        return application;
      }),
    );

    return transformedApplications;
  }

  // async findOneApplicationAllDetailsByApplicationId(applicationName: string) {
  //   return this.applicationRepository.findOne({ applicationName });
  // }

  // all
  // async getApplicationsByProjectAndPermitType(
  //   projectId: string,
  //   permitTypeId: string,
  // ) {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('projects.projectStatus', 'projectStatus')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
  //     .where('projects.id = :projectId', { projectId })
  //     .andWhere('permitTypes.id = :permitTypeId', { permitTypeId })
  //     .getMany();

  //   // if (applications.length === 0) {
  //   //   throw new NotFoundException(
  //   //     `No applications found for project id ${projectId} and permit type id ${permitTypeId}`,
  //   //   );
  //   // }

  //   return applications;
  // }

  // without cancelled application
  async getApplicationsByProjectAndPermitType(
    projectId: string,
    permitTypeId: string,
  ) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('projects.projectStatus', 'projectStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .where('projects.id = :projectId', { projectId })
      .andWhere('permitTypes.id = :permitTypeId', { permitTypeId })
      .andWhere('(applicationStatus.code != :code1)', {
        code1: 'CXL',
      })
      .getMany();

    return applications;
  }

  // // save with the applicationName while submitting

  // async updateApplicationSubmit(
  //   id: string,
  //   applicationDtoUpdate: ApplicationDtoUpdate,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     const clientInfo = this.parseUserAgent(userAgent);
  //     const projectData = await this.projectsRepository.findOne({
  //       id: applicationDtoUpdate.projectId,
  //     });
  //     if (!projectData) {
  //       throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationStatusData =
  //       await this.applicationStatusRepository.findOne({
  //         id: applicationDtoUpdate.applicationStatusId,
  //       });
  //     if (!applicationStatusData) {
  //       throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const permitType = await this.permitTypesRepository.findOne({
  //       id: applicationDtoUpdate.permitTypeId,
  //     });
  //     if (!permitType) {
  //       throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;
  //     const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

  //     // Update application and submitter data
  //     await this.applicationEntityManagerRepository
  //       .createQueryBuilder()
  //       .update(Application)
  //       .set({
  //         applicationStatus: applicationStatusData,
  //         submittedByUserId: applicationDtoUpdate.userId,
  //         submittedDate: new Date(),
  //         isCountingActive: true,
  //       })
  //       .where('id = :id', { id })
  //       .execute();

  //     // Retrieve the updated application entity
  //     const savedApplication = await this.applicationRepository.findOne({ id });
  //     if (!savedApplication) {
  //       throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
  //     }

  //     // Generate applicationName if submitting application for the first time
  //     if (!savedApplication.applicationName) {
  //       const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  //       let alphabeticPart = '';
  //       for (let i = 0; i < 4; i++) {
  //         alphabeticPart += alphabet.charAt(
  //           Math.floor(Math.random() * alphabet.length),
  //         );
  //       }
  //       const timestamp = Date.now().toString().slice(-5);
  //       const numericPart = Math.floor(Math.random() * 100000);
  //       const numericString = `${numericPart}-${timestamp}`.padStart(10, '0');
  //       savedApplication.applicationName = `${applicationDtoUpdate.agencyCode}-${applicationDtoUpdate.permitTypeCode}-${alphabeticPart}-${numericString}`;

  //       await this.applicationRepository.create(savedApplication);
  //     }

  //     // Create and save submission log
  //     const submissionLog = new SubmissionLog({
  //       application: savedApplication,
  //       userId: applicationDtoUpdate.userId,
  //       applicationStatusId: applicationDtoUpdate.applicationStatusId,
  //       ipAddress: clientIp,
  //       browser: clientInfo.browser,
  //       operatingSystem: clientInfo.operatingSystem,
  //     });

  //     await this.submissionLogRepository.save(submissionLog);

  //     // Notify if application is submitted
  //     if (applicationStatusData.code === 'SUB') {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: `${config.notification.senderEmail}`,
  //         // sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetail.email,
  //         subject: 'Kubaka Notification Email',
  //         message: `
  //           Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //           Thank you for submitting your application! <br>
  //           Your application has been received and assigned a reference number: ${savedApplication.applicationName}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const [emailResponse, smsResponse] = await Promise.all([
  //           axios.post(
  //             `${config.notification.emailAPI}/email/send/`,
  //             // 'https://notification.kubaka.gov.rw/email/send/',
  //             requestData,
  //           ),
  //           axios.post(
  //             `${config.notification.smsAPI}/sms/send/`,
  //             // 'https://notification.kubaka.gov.rw/sms/send',
  //             {
  //               msisdn: userDetail.phoneNumber,
  //               message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number: ${savedApplication.applicationName}\n\nBest regards,\nKUBAKA Team`,
  //             },
  //           ),
  //         ]);

  //         console.log('Email sent successfully:', emailResponse);
  //         console.log('SMS sent successfully:', smsResponse);

  //         return savedApplication;
  //       } catch (error) {
  //         console.error('Error sending email:', error);
  //         throw new HttpException(
  //           'Email not sent',
  //           HttpStatus.INTERNAL_SERVER_ERROR,
  //         );
  //       }
  //     }

  //     return savedApplication;
  //   } catch (err) {
  //     console.error('Error creating application or submission log:', err);
  //     throw err;
  //   }
  // }
  // save with the applicationName while submitting

  async updateApplicationSubmit(
    id: string,
    applicationDtoUpdate: ApplicationDtoUpdate,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      const clientInfo = this.parseUserAgent(userAgent);
      const projectData = await this.projectsRepository.findOne({
        id: applicationDtoUpdate.projectId,
      });
      if (!projectData) {
        throw new HttpException(
          'Project Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      const applicationStatusData =
        await this.applicationStatusRepository.findOne({
          id: applicationDtoUpdate.applicationStatusId,
        });
      if (!applicationStatusData) {
        throw new HttpException(
          'Status Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      const permitType = await this.permitTypesRepository.findOne({
        id: applicationDtoUpdate.permitTypeId,
      });
      if (!permitType) {
        throw new HttpException(
          'Permit Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      // const agencyData = await this.checkAgencyDataById(
      //   applicationDtoUpdate.agencyId,
      // );

      const agencyData = await this.checkAgencyDataById(projectData.agencyId);

      if (!agencyData) {
        throw new HttpException(
          'Agency Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      const userDetailLandOwner = await this.checkUserData(
        applicationDtoUpdate.userId,
      );
      const userDetailSubmitted = await this.checkUserData(
        applicationDtoUpdate.userId,
      );

      // Update application and submitter data
      await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({
          applicationStatus: applicationStatusData,
          submittedByUserId: applicationDtoUpdate.userId,
          submittedDate: new Date(),
          isCountingActive: true,
          upi: projectData.upi,
          agencyCode: agencyData.code,
        })
        .where('id = :id', { id })
        .execute();

      // Retrieve the updated application entity
      const savedApplication = await this.applicationRepository.findOne({ id });
      if (!savedApplication) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Generate applicationName if submitting application for the first time
      if (!savedApplication.applicationName) {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let alphabeticPart = '';
        for (let i = 0; i < 4; i++) {
          alphabeticPart += alphabet.charAt(
            Math.floor(Math.random() * alphabet.length),
          );
        }
        const timestamp = Date.now().toString().slice(-5);
        const numericPart = Math.floor(Math.random() * 100000);
        const numericString = `${numericPart}-${timestamp}`.padStart(10, '0');
        // savedApplication.applicationName = `${applicationDtoUpdate.agencyCode}-${applicationDtoUpdate.permitTypeCode}-${alphabeticPart}-${numericString}`;
        savedApplication.applicationName = `${agencyData.code}-${permitType.code}-${alphabeticPart}-${numericString}`;

        await this.applicationRepository.create(savedApplication);
      }

      // Create and save submission log
      const submissionLog = new SubmissionLog({
        application: savedApplication,
        userId: applicationDtoUpdate.userId,
        applicationStatusId: applicationDtoUpdate.applicationStatusId,
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,
      });

      await this.submissionLogRepository.save(submissionLog);

      // Notify if application is submitted
      if (applicationStatusData.code === 'SUB') {
        const requestData = {
          sender_name: 'KUBAKA MIS',
          sender_email: `${config.notification.senderEmail}`,
          // sender_email: '<EMAIL>',
          receiver_name: 'KUBAKA User',
          receiver_email: userDetailSubmitted.email,
          subject: 'Kubaka Notification Email',
          message: `
            Dear ${userDetailSubmitted.firstName} ${userDetailSubmitted.lastName}, <br><br>
            Thank you for submitting your application! <br>
            Your application has been received and assigned a reference number: ${savedApplication.applicationName}<br><br>
            Best regards, <br>
            KUBAKA Team
          `,
        };

        try {
          const [emailResponse, smsResponse] = await Promise.all([
            // message to the land owner
            axios.post(`${config.notification.smsAPI}/sms/send/`, {
              msisdn: userDetailLandOwner.phoneNumber,
              message: `Dear ${userDetailLandOwner.firstName} ${userDetailLandOwner.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number: ${savedApplication.applicationName}\n\nBest regards,\nKUBAKA Team`,
            }),

            // email to the user who submitted the application
            axios.post(
              `${config.notification.emailAPI}/email/send/`,
              requestData,
            ),
            // sms to the user who submitted the application
            axios.post(`${config.notification.smsAPI}/sms/send/`, {
              msisdn: userDetailSubmitted.phoneNumber,
              message: `Dear ${userDetailSubmitted.firstName} ${userDetailSubmitted.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number: ${savedApplication.applicationName}\n\nBest regards,\nKUBAKA Team`,
            }),
          ]);

          console.log('Email sent successfully:', emailResponse);
          console.log('SMS sent successfully:', smsResponse);

          return savedApplication;
        } catch (error) {
          console.error('Error sending email:', error);
          throw new HttpException(
            'Email not sent',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }

      return savedApplication;
    } catch (err) {
      console.error('Error creating application or submission log:', err);
      throw err;
    }
  }

  // // save with submission logs
  // async updateApplicationSubmit(
  //   id: string,
  //   applicationDtoUpdate: ApplicationDtoUpdate,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     const clientInfo = this.parseUserAgent(userAgent);
  //     const projectData = await this.projectsRepository.findOne({
  //       id: applicationDtoUpdate.projectId,
  //     });
  //     if (!projectData) {
  //       throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationStatusData =
  //       await this.applicationStatusRepository.findOne({
  //         id: applicationDtoUpdate.applicationStatusId,
  //       });
  //     if (!applicationStatusData) {
  //       throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const permitType = await this.permitTypesRepository.findOne({
  //       id: applicationDtoUpdate.permitTypeId,
  //     });
  //     if (!permitType) {
  //       throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;

  //     const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

  //     // Update the application status and submitter
  //     await this.applicationEntityManagerRepository
  //       .createQueryBuilder()
  //       .update(Application)
  //       .set({
  //         applicationStatus: applicationStatusData,
  //         submittedByUserId: applicationDtoUpdate.userId,
  //       })
  //       .where('id = :id', { id: id })
  //       .execute();

  //     // Retrieve the updated application entity from the database
  //     const savedApplication = await this.applicationRepository.findOne({ id });
  //     if (!savedApplication) {
  //       throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
  //     }

  //     // Create and save submission log with the updated application entity
  //     const submissionLog = new SubmissionLog({
  //       application: savedApplication,
  //       userId: applicationDtoUpdate.userId,
  //       applicationStatusId: applicationDtoUpdate.applicationStatusId,
  //       ipAddress: clientIp,
  //       browser: clientInfo.browser,
  //       operatingSystem: clientInfo.operatingSystem,
  //     });

  //     await this.submissionLogRepository.save(submissionLog);

  //     if (applicationStatusData.code === 'SUB') {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetail.email,
  //         subject: 'Kubaka Notification Email',
  //         message: `
  //           Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //           Thank you for submitting your application! <br>
  //           Your application has been received and assigned a reference number: ${savedApplication.applicationName}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const [emailResponse, smsResponse] = await Promise.all([
  //           axios.post(
  //             'https://notification.kubaka.gov.rw/email/send/',
  //             requestData,
  //           ),
  //           axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //             msisdn: userDetail.phoneNumber,
  //             message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number: ${savedApplication.applicationName}\n\nBest regards,\nKUBAKA Team`,
  //           }),
  //         ]);

  //         console.log('Email sent successfully:', emailResponse);
  //         console.log('SMS sent successfully:', smsResponse);

  //         return savedApplication;
  //       } catch (error) {
  //         console.error('Error sending email:', error);
  //         throw new HttpException(
  //           'Email not sent',
  //           HttpStatus.INTERNAL_SERVER_ERROR,
  //         );
  //       }
  //     }

  //     return savedApplication;
  //   } catch (err) {
  //     console.error('Error creating application or submission log:', err);
  //     throw err;
  //   }
  // }

  // async updateApplicationSubmit(
  //   id: string,
  //   applicationDtoUpdate: ApplicationDtoUpdate,
  // ) {
  //   const projectData = await this.projectsRepository.findOne({
  //     id: applicationDtoUpdate.projectId,
  //   });
  //   if (!projectData)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

  //   const applicationStatusData =
  //     await this.applicationStatusRepository.findOne({
  //       id: applicationDtoUpdate.applicationStatusId,
  //     });
  //   if (!applicationStatusData)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   const permitType = await this.permitTypesRepository.findOne({
  //     id: applicationDtoUpdate.permitTypeId,
  //   });
  //   if (!permitType)
  //     throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;

  //   //Get user information
  //   const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

  //   // Update project entity
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     // .set({ applicationStatus: applicationStatusData })
  //     // .set({ submittedByUserId: applicationDtoUpdate.userId })
  //     .set({
  //       applicationStatus: applicationStatusData,
  //       submittedByUserId: applicationDtoUpdate.userId,
  //     })
  //     .where('id = :id', { id: id })
  //     .execute();
  //   console.log(applicationUpdate);

  //   const savedData = await this.applicationRepository.findOne({ id });
  //   console.log(savedData);
  //   console.log(applicationStatusData.code);

  //   if (applicationStatusData.code === 'SUB') {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetail.email,
  //       subject: 'Kubaka Notification Email',
  //       message: `
  //         Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //         Thank you for submitting your application! <br>
  //         Your application has been received and assigned a reference number : ${savedData.applicationName}<br><br>
  //         Best regards, <br>
  //         KUBAKA Team
  //         `,
  //     };

  //     try {
  //       const [emailResponse, smsResponse] = await Promise.all([
  //         // sending email responses
  //         axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         ),
  //         // sending sms responses
  //         axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //           msisdn: userDetail.phoneNumber,
  //           message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number : ${savedData.applicationName}\n\nBest regards,\nKUBAKA Team`,
  //         }),
  //       ]);

  //       console.log('Email sent successfully:', emailResponse);
  //       console.log('SMS sent successfully:', smsResponse);

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   }
  // }

  // resubmit with submission logs
  async updateApplicationResubmit(
    id: string,
    applicationDtoUpdate: ApplicationDtoUpdate,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      const clientInfo = this.parseUserAgent(userAgent);
      const projectData = await this.projectsRepository.findOne({
        id: applicationDtoUpdate.projectId,
      });
      if (!projectData)
        throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

      const applicationStatusData =
        await this.applicationStatusRepository.findOne({
          id: applicationDtoUpdate.applicationStatusId,
        });
      if (!applicationStatusData)
        throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

      const permitType = await this.permitTypesRepository.findOne({
        id: applicationDtoUpdate.permitTypeId,
      });
      if (!permitType)
        throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;

      //Get user information
      const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

      // Update project entity
      const applicationUpdate = await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({
          applicationStatus: applicationStatusData,
          submittedByUserId: applicationDtoUpdate.userId,
          isResubmitted: true,
          // submittedDate: new Date(),
          resubmittedDate: new Date(),
          isCountingActive: true,
        })
        .where('id = :id', { id: id })
        .execute();
      console.log(applicationUpdate);

      const savedData = await this.applicationRepository.findOne({ id });
      if (!savedData) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }
      // Create and save submission log with the updated application entity
      const submissionLog = new SubmissionLog({
        application: savedData,
        userId: applicationDtoUpdate.userId,
        applicationStatusId: applicationDtoUpdate.applicationStatusId,
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,
      });

      await this.submissionLogRepository.save(submissionLog);

      if (applicationStatusData.code === 'RSMB') {
        const requestData = {
          sender_name: 'KUBAKA MIS',
          sender_email: `${config.notification.senderEmail}`,
          // sender_email: '<EMAIL>',
          receiver_name: 'KUBAKA User',
          receiver_email: userDetail.email,
          subject: 'Kubaka Notification Email',
          message: `
            Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
            Thank you for resubmitting your application! <br>
            Your application has been resubmitted. This is a reference number : ${savedData.applicationName}<br><br>
            Best regards, <br>
            KUBAKA Team
            `,
        };

        try {
          const [emailResponse, smsResponse] = await Promise.all([
            // sending email responses
            axios.post(
              `${config.notification.emailAPI}/email/send/`,
              // 'https://notification.kubaka.gov.rw/email/send/',
              requestData,
            ),
            // sending sms responses
            axios.post(
              `${config.notification.smsAPI}/sms/send/`,
              // 'https://notification.kubaka.gov.rw/sms/send',
              {
                msisdn: userDetail.phoneNumber,
                message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for resubmitting your application!\n\nYour application has been resubmitted with a reference number : ${savedData.applicationName}\n\nBest regards,\nKUBAKA Team`,
              },
            ),
          ]);

          console.log('Email sent successfully:', emailResponse);
          console.log('SMS sent successfully:', smsResponse);

          return savedData;
        } catch (error) {
          console.error('Error sending email:', error);
          throw new HttpException(
            'Email not sent',
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }

      return savedData;
    } catch (err) {
      console.error('Error creating application or submission log:', err);
      throw err;
    }
  }
  // resumit on irembohub

  async ApplicationResubmitIrembo(
    id: string,
    applicationDto: ApplicationResubmitIremboDto,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      const clientInfo = this.parseUserAgent(userAgent);

      const existingApplication = await this.applicationEntityManagerRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.projects', 'projects')
        .leftJoinAndSelect('application.permitTypes', 'permitTypes')
        .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
        .leftJoinAndSelect('application.buildTypes', 'buildTypes')
        .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
        .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
        .where('application.id = :id', { id })
        .getOne();

      if (!existingApplication) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      // Validate related data only if IDs are provided
      const [
        project,
        permitType,
        categoryType,
        buildType,
        applicationStatus,
        technologySurvey,
      ] = await Promise.all([
        applicationDto.projectId
          ? this.projectsRepository.findOne(
              { id: applicationDto.projectId },
              undefined,
              'Project',
            )
          : null,

        applicationDto.permitTypeId
          ? this.permitTypesRepository.findOne(
              { id: applicationDto.permitTypeId },
              undefined,
              'Permit type',
            )
          : null,
        applicationDto.categoryTypeId
          ? this.categoryTypesRepository.findOne(
              { id: applicationDto.categoryTypeId },
              undefined,
              'Category type',
            )
          : null,
        applicationDto.buildTypeId
          ? this.buildTypesRepository.findOne(
              { id: applicationDto.buildTypeId },
              undefined,
              'Build type',
            )
          : null,
        applicationDto.applicationStatusId
          ? this.applicationStatusRepository.findOne(
              { id: applicationDto.applicationStatusId },
              undefined,
              'Application status',
            )
          : null,
        applicationDto.technologySurveyId
          ? this.technologySurveysRepository.findOne(
              { id: applicationDto.technologySurveyId },
              undefined,
              'Technology survey',
            )
          : null,
      ]);
      // console.log(project);

      // // Check required referenced data
      if (applicationDto.projectId && !project)
        throw new HttpException('Project not found', HttpStatus.BAD_REQUEST);
      if (applicationDto.permitTypeId && !permitType)
        throw new HttpException(
          'Permit type not found',
          HttpStatus.BAD_REQUEST,
        );
      if (!applicationDto.categoryTypeId && !categoryType)
        throw new HttpException(
          'Category type not found',
          HttpStatus.BAD_REQUEST,
        );
      if (applicationDto.buildTypeId && !buildType)
        throw new HttpException('Build type not found', HttpStatus.BAD_REQUEST);
      if (applicationDto.applicationStatusId && !applicationStatus)
        throw new HttpException(
          'Application status not found',
          HttpStatus.BAD_REQUEST,
        );
      if (applicationDto.technologySurveyId && !technologySurvey)
        throw new HttpException(
          'Technology survey not found',
          HttpStatus.BAD_REQUEST,
        );

      const agencyData = await this.checkAgencyDataById(
        project?.agencyId || existingApplication.projects?.agencyId,
      );

      if (!agencyData) {
        throw new HttpException(
          'Agency Data Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      // // Generate applicationName if permitType changed
      if (
        applicationDto.permitTypeId &&
        applicationDto.permitTypeId !== existingApplication.permitTypes.id
      ) {
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let alphabeticPart = '';
        for (let i = 0; i < 4; i++) {
          alphabeticPart += alphabet.charAt(
            Math.floor(Math.random() * alphabet.length),
          );
        }
        const timestamp = Date.now().toString().slice(-5);
        const numericPart = Math.floor(Math.random() * 100000);
        const numericString = `${numericPart}-${timestamp}`.padStart(10, '0');
        applicationDto.applicationName = `${agencyData.code}-${permitType?.code ?? 'XXX'}-${alphabeticPart}-${numericString}`;
      }

      const resubmittedStatus = await this.applicationStatusRepository.findOne({
        code: 'RSMB',
      });

      if (!resubmittedStatus) {
        throw new HttpException(
          'Resubmitted status not found',
          HttpStatus.BAD_REQUEST,
        );
      }

      // // Dynamically build updateData from the cleaned DTO
      const updateData: any = { ...applicationDto };

      if (project) {
        updateData.projects = { id: project.id };
        delete updateData.projectId;
      }

      if (permitType) {
        updateData.permitTypes = { id: permitType.id };
        delete updateData.permitTypeId;
      }

      if (categoryType) {
        updateData.categoryTypes = { id: categoryType.id };
        delete updateData.categoryTypeId;
      }

      if (buildType) {
        updateData.buildTypes = { id: buildType.id };
        delete updateData.buildTypeId;
      }

      if (technologySurvey) {
        updateData.technologySurveys = { id: technologySurvey.id };
        delete updateData.technologySurveyId;
      }

      // // Always set resubmitted status
      updateData.applicationStatus = { id: resubmittedStatus.id };
      delete updateData.applicationStatusId;

      updateData.isResubmitted = true;
      updateData.resubmittedDate = new Date();
      updateData.isCountingActive = true;

      // // Perform the update
      await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set(updateData)
        .where('id = :id', { id })
        .execute();

      // // Handle other info
      if (
        applicationDto.other &&
        Object.values(applicationDto.other).some(
          (value) => value !== null && value !== '' && value !== undefined,
        )
      ) {
        // using  quey builder to find and update or create other info
        // Check if other info already exists for this application
        const existingOtherInfo =
          await this.otherInfoApplicationEntityManagerRepository
            .createQueryBuilder('otherInfoApplication')
            .leftJoinAndSelect(
              'otherInfoApplication.applications',
              'applications',
            )
            .where('applications.id = :id', { id })
            .getOne();
        // const existingOtherInfo =
        //   await this.otherInfoApplicationRepository.findOne({
        //     applications: { id: id } as any,
        //   });

        if (existingOtherInfo) {
          await this.otherInfoApplicationRepository.findOneAndUpdate(
            { id: existingOtherInfo.id },
            applicationDto.other,
          );
        } else {
          const otherInfo = new OtherInfoApplication({
            ...applicationDto.other,
            applications: { id: id } as any,
            userId: applicationDto.userId,
          });
          await this.otherInfoApplicationRepository.create(otherInfo);
        }
      }

      // // Log submission
      const submissionLog = new SubmissionLog({
        application: existingApplication,
        userId: applicationDto.userId,
        applicationStatusId: applicationDto.applicationStatusId,
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,
      });

      await this.submissionLogRepository.save(submissionLog);

      // Notify user
      try {
        const userDetails = await this.checkUserData(applicationDto.userId);
        if (userDetails) {
          const requestData = {
            sender_name: 'KUBAKA MIS',
            sender_email: `${config.notification.senderEmail}`,
            receiver_name: 'KUBAKA User',
            receiver_email: userDetails.email,
            subject: 'Kubaka Notification Email',
            message: `
              Dear ${userDetails.firstName} ${userDetails.lastName}, <br><br>
              Thank you for resubmitting your application! <br>
              Your application has been resubmitted. This is a reference number: ${existingApplication.applicationName}<br><br>
              Best regards, <br>
              KUBAKA Team
            `,
          };

          await Promise.all([
            axios.post(
              `${config.notification.emailAPI}/email/send/`,
              requestData,
            ),
            axios.post(`${config.notification.smsAPI}/sms/send/`, {
              msisdn: userDetails.phoneNumber,
              message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nThank you for resubmitting your application!\n\nYour application has been resubmitted with a reference number: ${existingApplication.applicationName}\n\nBest regards,\nKUBAKA Team`,
            }),
          ]);
        }
      } catch (notificationError) {
        console.error('Notification error:', notificationError);
      }

      const updatedApplication = await this.applicationEntityManagerRepository
        .createQueryBuilder('application')
        .leftJoinAndSelect('application.projects', 'projects')
        .leftJoinAndSelect('application.permitTypes', 'permitTypes')
        .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
        .leftJoinAndSelect('application.buildTypes', 'buildTypes')
        .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
        .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
        .where('application.id = :id', { id })
        .getOne();

      return {
        message: 'Application resubmitted successfully',
        data: updatedApplication,
      };
      // return 'Application resubmitted successfully';
    } catch (err) {
      console.error('Error resubmitting application:', err);
      throw err;
    }
  }

  // async updateApplicationResubmit(
  //   id: string,
  //   applicationDtoUpdate: ApplicationDtoUpdate,
  // ) {
  //   const projectData = await this.projectsRepository.findOne({
  //     id: applicationDtoUpdate.projectId,
  //   });
  //   if (!projectData)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

  //   const applicationStatusData =
  //     await this.applicationStatusRepository.findOne({
  //       id: applicationDtoUpdate.applicationStatusId,
  //     });
  //   if (!applicationStatusData)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   const permitType = await this.permitTypesRepository.findOne({
  //     id: applicationDtoUpdate.permitTypeId,
  //   });
  //   if (!permitType)
  //     throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;

  //   //Get user information
  //   const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

  //   // Update project entity
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     // .set({ applicationStatus: applicationStatusData })
  //     // .set({ submittedByUserId: applicationDtoUpdate.userId })
  //     .set({
  //       applicationStatus: applicationStatusData,
  //       submittedByUserId: applicationDtoUpdate.userId,
  //     })
  //     .where('id = :id', { id: id })
  //     .execute();
  //   console.log(applicationUpdate);

  //   const savedData = await this.applicationRepository.findOne({ id });
  //   console.log(savedData);
  //   console.log(applicationStatusData.code);

  //   if (applicationStatusData.code === 'RSMB') {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetail.email,
  //       subject: 'Kubaka Notification Email',
  //       message: `
  //         Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //         Thank you for resubmitting your application! <br>
  //         Your application has been resubmitted. This is a reference number : ${savedData.applicationName}<br><br>
  //         Best regards, <br>
  //         KUBAKA Team
  //         `,
  //     };

  //     try {
  //       const [emailResponse, smsResponse] = await Promise.all([
  //         // sending email responses
  //         axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         ),
  //         // sending sms responses
  //         axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //           msisdn: userDetail.phoneNumber,
  //           message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for resubmitting your application!\n\nYour application has been resubmitted with a reference number : ${savedData.applicationName}\n\nBest regards,\nKUBAKA Team`,
  //         }),
  //       ]);

  //       console.log('Email sent successfully:', emailResponse);
  //       console.log('SMS sent successfully:', smsResponse);

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   }
  // }

  // async updateApplicationSubmit(
  //   id: string,
  //   applicationDtoUpdate: ApplicationDtoUpdate,
  // ) {
  //   const projectData = await this.projectsRepository.findOne({
  //     id: applicationDtoUpdate.projectId,
  //   });
  //   if (!projectData)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

  //   const applicationStatus = await this.applicationStatusRepository.findOne({
  //     id: applicationDtoUpdate.applicationStatusId,
  //   });
  //   if (!applicationStatus)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   const permitType = await this.permitTypesRepository.findOne({
  //     id: applicationDtoUpdate.permitTypeId,
  //   });
  //   if (!permitType)
  //     throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { projectId, permitTypeId, ...updateData } = applicationDtoUpdate;

  //   //Get user information
  //   const userDetail = await this.checkUserData(applicationDtoUpdate.userId);

  //   // Update project entity
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ applicationStatus: applicationStatus })
  //     .where('id = :id', { id: id })
  //     .execute();
  //   console.log(applicationUpdate);

  //   const savedData = await this.applicationRepository.findOne({ id });
  //   console.log(savedData);
  //   console.log(applicationStatus.code);

  //   if (applicationStatus.code === 'SBM') {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetail.email,
  //       subject: 'Kubaka Notification Email',
  //       message: `
  //         Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //         Thank you for submitting your application! <br>
  //         Your application has been received and assigned a reference number : ${savedData.applicationName}<br><br>
  //         Best regards, <br>
  //         KUBAKA Team
  //         `,
  //     };

  //     try {
  //       const [emailResponse, smsResponse] = await Promise.all([
  //         // sending email responses
  //         axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         ),
  //         // sending sms responses
  //         axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //           msisdn: userDetail.phoneNumber,
  //           message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for submitting your application!\n\nYour application has been assigned a reference number : ${savedData.applicationName}\n\nBest regards,\nKUBAKA Team`,
  //         }),
  //       ]);

  //       console.log('Email sent successfully:', emailResponse);
  //       console.log('SMS sent successfully:', smsResponse);

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // async findOneApplicationByProjectId(projectId: string) {
  //   return this.applicationEntityManagerRepository
  //     .createQueryBuilder('applications')
  //     .where('applications.projects = :id', { projectId })
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .getMany();
  // }

  // async updateApplication2(
  //   id: string,
  //   updateApplicationDto: FindUpdateApplicationDto,
  // ) {
  //   //Get user information
  //   const userDetails = await this.checkUser(updateApplicationDto.userId);
  //   console.log(userDetails);

  //   const applicationStatus = await this.applicationStatusRepository.findOne({
  //     id: updateApplicationDto.applicationStatusId,
  //   });

  //   const projectData = await this.projectsRepository.findOne({
  //     id: updateAssigneeDto.projectId,
  //   });

  //   if (!projectData)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);
  //   // Check if project status with provided ID exists

  //   const projectStatusData = await this.projectStatusRepository.findOne({
  //     id: updateAssigneeDto.projectStatusId,
  //   });
  //   if (!projectStatusData)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { projectId, ...updateData } = updateAssigneeDto;

  //   // Update the Assignee using QueryBuilder
  //   const updateResult = await this.assigneeEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Assignee) // Assuming Assignee is your entity
  //     .set(updateData)
  //     .where('id = :id', { id })
  //     .execute();

  //   // Update project entity
  //   const projectUpdate = await this.projectEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Project)
  //     .set({ projectStatus: projectStatusData })
  //     .where('id = :projectId', { projectId: updateAssigneeDto.projectId })
  //     .execute();
  //   console.log(projectUpdate);

  //   console.log(updateResult);
  //   // Retrieve the updated Assignee
  //   const updatedAssignee = await this.assigneeRepository.findOne({ id });

  //   return updatedAssignee;
  // }

  // async updateApplication(
  //   id: string,
  //   updateApplicationDto: FindUpdateApplicationDto,
  // ) {
  //   //Get user information
  //   const userDetails = await this.checkUser(updateApplicationDto.userId);
  //   console.log(userDetails);

  //   const applicationStatus = await this.applicationStatusRepository.findOne({
  //     id: updateApplicationDto.applicationStatusId,
  //   });

  //   console.log(applicationStatus);
  //   const savedData = await this.applicationRepository.findOneAndUpdate(
  //     { id },
  //     updateApplicationDto,
  //   );

  //   console.log(savedData);

  //   if (applicationStatus.code === 'PND' || applicationStatus.code === 'SBM') {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetails.email,
  //       subject: 'Kubaka Notification Email',
  //       message: `
  //         Hello ${userDetails.firstName} ${userDetails.lastName}, <br>
  //         Thank you for submitting your application! <br><br>
  //         You have submitted an application with the following number : ${savedData.applicationName}<br><br>
  //         Best regards, <br>
  //         KUBAKA Team
  //         `,
  //     };

  //     try {
  //       const response = await axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     console.log(applicationStatus.code);
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // return this.applicationRepository.findOneAndUpdate(
  //   { id },
  //   updateApplicationDto,
  // );

  // async updateApplication(id: string, updateApplicationDto: ApplicationDto) {
  //   return this.applicationRepository.findOneAndUpdate(
  //     { id },
  //     updateApplicationDto,
  //   );
  // }

  // Resubmit the application on modification without changing the applicationNumber
  async updateApplicationToResubmit(
    id: string,
    applicationDtoUpdate: ApplicationDto,
  ) {
    const projectData = await this.projectsRepository.findOne({
      id: applicationDtoUpdate.projectId,
    });
    if (!projectData)
      throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

    const applicationStatus = await this.applicationStatusRepository.findOne({
      code: 'RSMB',
    });
    if (!applicationStatus)
      throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

    const permitTypeData = await this.permitTypesRepository.findOne({
      id: applicationDtoUpdate.permitTypeId,
    });
    if (!permitTypeData)
      throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

    const categoryTypeData = await this.categoryTypesRepository.findOne({
      id: applicationDtoUpdate.categoryTypeId,
    });
    if (!categoryTypeData)
      throw new HttpException('Category Data Not Found', HttpStatus.NOT_FOUND);

    const buildTypeData = await this.buildTypesRepository.findOne({
      id: applicationDtoUpdate.buildTypeId,
    });
    if (!buildTypeData)
      throw new HttpException('Build Data Not Found', HttpStatus.NOT_FOUND);

    const technologySurveyData = await this.technologySurveysRepository.findOne(
      {
        id: applicationDtoUpdate.technologySurveyId,
      },
    );
    if (!technologySurveyData)
      throw new HttpException(
        'Technology survey Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    const {
      permitTypeCode,
      agencyCode,
      userId,
      agencyId,
      waterConsumption,
      electricityConsumption,
      DistanceToTheNearestLandIn,
      ProjectCostInUSD,
      ProjectCostInRwf,
      buildUpArea,
      numberOfFloor,
      grossFloorArea,
      numberOfParkingSpace,
      priceOfDwellingUnitRwf,
      capacityInformation,
      numberOfDwellingUnits,
      DescriptionOfOperation,
      percentageSpaceUse,
      certificateNumberEIA,
    } = applicationDtoUpdate;

    //Get user information
    const userDetail = await this.checkUserData(applicationDtoUpdate.userId);
    const application = await this.applicationRepository.findOne({ id });

    // Update application entity
    if (!application) {
      throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
    }

    const applicationName = await this.updateApplicationName(
      application.applicationName,
      permitTypeCode,
    );

    application.applicationStatus = applicationStatus;
    application.submittedByUserId = userId;
    application.submittedDate = new Date();
    application.isCountingActive = true;
    application.projects = projectData;
    application.permitTypes = permitTypeData;
    application.buildTypes = buildTypeData;
    application.technologySurveys = technologySurveyData;
    application.applicationName = applicationName;
    application.userId = userId;
    application.agencyId = agencyId;
    application.agencyCode = agencyCode;
    application.permitTypeCode = permitTypeCode;
    application.waterConsumption = waterConsumption;
    application.electricityConsumption = electricityConsumption;
    application.DistanceToTheNearestLandIn = DistanceToTheNearestLandIn;
    application.ProjectCostInUSD = ProjectCostInUSD;
    application.ProjectCostInRwf = ProjectCostInRwf;
    application.buildUpArea = buildUpArea;
    application.numberOfFloor = numberOfFloor;
    application.grossFloorArea = grossFloorArea;
    application.numberOfParkingSpace = numberOfParkingSpace;
    application.numberOfDwellingUnits = numberOfDwellingUnits;
    application.priceOfDwellingUnitRwf = priceOfDwellingUnitRwf;
    application.capacityInformation = capacityInformation;
    application.DescriptionOfOperation = DescriptionOfOperation;
    application.percentageSpaceUse = percentageSpaceUse;
    application.certificateNumberEIA = certificateNumberEIA;

    await this.applicationEntityManagerRepository.save(application);

    const requestData = {
      sender_name: 'KUBAKA MIS',
      sender_email: `${config.notification.senderEmail}`,
      // sender_email: '<EMAIL>',
      receiver_name: 'KUBAKA User',
      receiver_email: userDetail.email,
      subject: 'Kubaka Notification Email',
      message: `
        Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
        Thank you for resubmitting your application! <br>
        Your application has been updated. ${applicationName}<br><br>
        Best regards, <br>
        KUBAKA Team
        `,
    };

    try {
      await Promise.all([
        // sending email responses
        axios.post(
          `${config.notification.emailAPI}/email/send/`,
          // 'https://notification.kubaka.gov.rw/email/send/',
          requestData,
        ),
        // sending sms responses
        axios.post(
          `${config.notification.smsAPI}/sms/send/`,
          // 'https://notification.kubaka.gov.rw/sms/send',
          {
            msisdn: userDetail.phoneNumber,
            message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThank you for resubmitting your application! \n\nBest regards,\nKUBAKA Team`,
          },
        ),
      ]);

      return application;
    } catch (error) {
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async updateApplicationName(
    applicationName: string,
    newPermitTypeCode: string,
  ): Promise<string> {
    if (!applicationName) return '';
    const parts = applicationName.split('-');
    if (parts.length >= 3) {
      parts[1] = newPermitTypeCode;
      return parts.join('-');
    }
    return applicationName;
  }

  // Resubmit the application on modification without changing the applicationNumber Only data without message
  async updateApplicationToResubmitOnlyData(
    id: string,
    applicationDtoUpdate: ApplicationDto,
  ) {
    const projectData = await this.projectsRepository.findOne({
      id: applicationDtoUpdate.projectId,
    });
    if (!projectData)
      throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

    const applicationStatus = await this.applicationStatusRepository.findOne({
      id: applicationDtoUpdate.applicationStatusId,
    });
    if (!applicationStatus)
      throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

    const permitTypeData = await this.permitTypesRepository.findOne({
      id: applicationDtoUpdate.permitTypeId,
    });
    if (!permitTypeData)
      throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);

    const categoryTypeData = await this.categoryTypesRepository.findOne({
      id: applicationDtoUpdate.categoryTypeId,
    });
    if (!categoryTypeData)
      throw new HttpException('Category Data Not Found', HttpStatus.NOT_FOUND);

    const buildTypeData = await this.buildTypesRepository.findOne({
      id: applicationDtoUpdate.buildTypeId,
    });
    if (!buildTypeData)
      throw new HttpException('Build Data Not Found', HttpStatus.NOT_FOUND);

    const technologySurveyData = await this.technologySurveysRepository.findOne(
      {
        id: applicationDtoUpdate.technologySurveyId,
      },
    );
    if (!technologySurveyData)
      throw new HttpException(
        'Technology survey Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    // Destructure the DTO to get the fields to update
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const {
      permitTypeCode,
      agencyCode,
      applicationName,
      userId,
      agencyId,
      waterConsumption,
      electricityConsumption,
      DistanceToTheNearestLandIn,
      ProjectCostInUSD,
      ProjectCostInRwf,
      buildUpArea,
      numberOfFloor,
      grossFloorArea,
      numberOfParkingSpace,
      priceOfDwellingUnitRwf,
      capacityInformation,
      numberOfDwellingUnits,
      DescriptionOfOperation,
      percentageSpaceUse,
      certificateNumberEIA,
    } = applicationDtoUpdate;

    // Update application entity
    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      // .set({ applicationStatus: applicationStatus })
      .set({
        applicationStatus: applicationStatus,
        submittedByUserId: applicationDtoUpdate.userId,
        submittedDate: new Date(),
        isCountingActive: true,

        projects: projectData,
        permitTypes: permitTypeData,
        categoryTypes: categoryTypeData,
        buildTypes: buildTypeData,
        technologySurveys: technologySurveyData,
        applicationName: applicationName,
        userId: userId,
        agencyId: agencyId,
        agencyCode: agencyCode,
        permitTypeCode: permitTypeCode,
        waterConsumption: waterConsumption,
        electricityConsumption: electricityConsumption,
        DistanceToTheNearestLandIn: DistanceToTheNearestLandIn,
        ProjectCostInUSD: ProjectCostInUSD,
        ProjectCostInRwf: ProjectCostInRwf,
        buildUpArea: buildUpArea,
        numberOfFloor: numberOfFloor,
        grossFloorArea: grossFloorArea,
        numberOfParkingSpace: numberOfParkingSpace,
        numberOfDwellingUnits: numberOfDwellingUnits,
        priceOfDwellingUnitRwf: priceOfDwellingUnitRwf,
        capacityInformation: capacityInformation,
        DescriptionOfOperation: DescriptionOfOperation,
        percentageSpaceUse: percentageSpaceUse,
        certificateNumberEIA: certificateNumberEIA,
      })
      .where('id = :id', { id: id })
      .execute();
    console.log(applicationUpdate);

    const savedData = await this.applicationRepository.findOne({ id });
    console.log(savedData);

    return savedData;
  }

  async removeApplication(id: string) {
    return this.applicationRepository.findOneAndDelete({ id });
  }

  // async deleteDraftApplication(id: string) {
  //   const application = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .where('application.id = :id', { id })
  //     .andWhere('applicationStatus.code = :code', { code: 'PND' })
  //     .getOne();

  //   if (!application) {
  //     throw new HttpException(
  //       'Application not found or not in Draft status',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .delete()
  //     .from('application')
  //     .where('id = :id', { id })
  //     .execute();

  //   return { message: 'Application deleted successfully' };
  // }

  // delete in the submission logs before deleting the application
  async deleteDraftApplication(id: string) {
    const application = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .where('application.id = :id', { id })
      .andWhere('applicationStatus.code = :code', { code: 'PND' })
      .getOne();

    if (!application) {
      throw new HttpException(
        'Application not found or not in Draft status',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.submissionLogRepository
      .createQueryBuilder()
      .delete()
      .from('submission_log')
      .where('applicationId = :id', { id })
      .execute();

    await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .delete()
      .from('application')
      .where('id = :id', { id })
      .execute();

    return { message: 'Application deleted successfully' };
  }

  // locking application
  async lockApplication(
    id: string,
    lockApplicationDtoUpdate: LockApplicationDtoUpdate,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      const savedData = await this.applicationRepository.findOne({ id });
      if (!savedData) {
        throw new HttpException('Application not found', HttpStatus.NOT_FOUND);
      }

      const clientInfo = this.parseUserAgent(userAgent);

      // Update locking status on an entity
      const applicationUpdate = await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({
          isLocked: true,
        })
        .where('id = :id', { id: id })
        .execute();
      console.log(applicationUpdate);

      // Create and save submission log with the updated application entity
      const submissionLog = new SubmissionLog({
        application: savedData,
        userId: lockApplicationDtoUpdate.userId,
        applicationStatusId: lockApplicationDtoUpdate.applicationStatusId,
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,
      });

      await this.submissionLogRepository.save(submissionLog);

      return savedData;
    } catch (err) {
      console.error('Error creating application or submission log:', err);
      throw err;
    }
  }

  // setting
  // permit type
  async createPermitType(permitTypeDto: PermitTypeDto) {
    const permitType = new PermitType({
      ...permitTypeDto,
    });

    // const emailTo = '<EMAIL>';
    // this.notificationsServiceService.emit('notify_email', {
    //   emailTo,
    //   text: `Your payment of $${permitTypeDto.name} has completed successfully.`,
    // });

    return this.permitTypesRepository.create(permitType);
  }

  async findAllPermitTypes() {
    const permitTypes = await this.permitTypesRepository.find({});

    const enrichedPermitTypes = await Promise.all(
      permitTypes.map(async (permit) => {
        let createdByName: string | null = null;
        let updatedByName: string | null = null;

        if (permit.createdBy) {
          try {
            const creator = await this.checkUserData(permit.createdBy);
            createdByName = creator
              ? `${creator.firstName} ${creator.lastName}`
              : null;
          } catch (error) {
            console.error(
              `Failed to fetch user for createdBy ID ${permit.createdBy}`,
              error,
            );
          }
        }

        if (permit.updatedBy) {
          try {
            const updater = await this.checkUserData(permit.updatedBy);
            updatedByName = updater
              ? `${updater.firstName} ${updater.lastName}`
              : null;
          } catch (error) {
            console.error(
              `Failed to fetch user for updatedBy ID ${permit.updatedBy}`,
              error,
            );
          }
        }

        return {
          ...permit,
          createdByName,
          updatedByName,
        };
      }),
    );

    return enrichedPermitTypes;
  }

  // async findAllPermitTypes() {
  //   return this.permitTypesRepository.find({});
  // }

  async findOnePermitTypeByName(name: string) {
    console.log(name);
  }

  async findOnePermitType(id: string) {
    return this.permitTypesRepository.findOne({ id });
  }

  // async findOnePermitTypeByCode(code: string) {
  //   console.log('sdfaf');
  //   const allData = this.permitTypesRepository.find({});
  //   console.log(allData);
  //   return (await allData).find((data) => data.code === code);
  // }

  async findOnePermitTypeByCode(code: string) {
    console.log(code);
    return this.permitTypesRepository.findOne({ code });
  }

  // async findOnePermitTypeByCode(code: string) {
  //   return this.permitTypeEntityManagerRepository.findBy({
  //     code: Equal(code),
  //   });
  // }
  // async findOnePermitTypeByCode(code: string) {
  //   const permitType = this.permitTypeEntityManagerRepository
  //     .createQueryBuilder('PermitType')
  //     .where('PermitType.code = :code', { code: code })
  //     .getOne();
  //   return permitType;
  // }

  async updatePermitType(id: string, updatePermitTypeDto: PermitTypeDto) {
    return this.permitTypesRepository.findOneAndUpdate(
      { id },
      updatePermitTypeDto,
    );
  }

  async removePermitType(id: string) {
    return this.permitTypesRepository.findOneAndDelete({ id });
  }

  // category rule
  async createCategoryRule(categoryRuleDto: CategoryRuleDto) {
    const categoryRule = new CategoryRule({
      ...categoryRuleDto,
    });
    return this.categoryRulesRepository.create(categoryRule);
  }

  async findAllCategoryRules() {
    return this.categoryRulesRepository.find({});
  }

  async findOneCategoryRule(id: string) {
    return this.categoryRulesRepository.findOne({ id });
  }

  async updateCategoryRule(id: string, updateCategoryRuleDto: CategoryRuleDto) {
    return this.categoryRulesRepository.findOneAndUpdate(
      { id },
      updateCategoryRuleDto,
    );
  }

  async removeCategoryRule(id: string) {
    return this.categoryRulesRepository.findOneAndDelete({ id });
  }

  // category type
  async createCategoryType(categoryTypeDto: CategoryTypeDto) {
    const dataFromDb = await this.categoryRulesRepository.findOne({
      id: categoryTypeDto.categoryRuleId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    const categoryType = new CategoryType({
      ...categoryTypeDto,
      categoryRules: (categoryTypeDto.categoryRuleId = {
        id: dataFromDb.id,
      } as any),
    });
    return this.categoryTypesRepository.create(categoryType);
  }

  async findAllCategoryTypes() {
    return this.categoryTypesRepository.findAll({
      relations: { categoryRules: true },
    });
  }

  async findOneCategoryType(id: string) {
    return this.categoryTypesRepository.findOne({ id });
  }

  async updateCategoryType(id: string, updateCategoryTypeDto: CategoryTypeDto) {
    return this.categoryTypesRepository.findOneAndUpdate(
      { id },
      updateCategoryTypeDto,
    );
  }

  async removeCategoryType(id: string) {
    return this.categoryTypesRepository.findOneAndDelete({ id });
  }

  // build type
  async createBuildType(buildTypeDto: BuildTypeDto) {
    const buildType = new BuildType({
      ...buildTypeDto,
    });
    return this.buildTypesRepository.create(buildType);
  }

  async findAllBuildTypes() {
    return this.buildTypesRepository.find({});
  }

  async findOneBuildType(id: string) {
    return this.buildTypesRepository.findOne({ id });
  }

  async updateBuildType(id: string, updateBuildTypeDto: BuildTypeDto) {
    return this.buildTypesRepository.findOneAndUpdate(
      { id },
      updateBuildTypeDto,
    );
  }

  async removeBuildType(id: string) {
    return this.buildTypesRepository.findOneAndDelete({ id });
  }

  // // document type
  async createDocumentType(documentTypeDto: DocumentTypeDto) {
    const documentType = new DocumentType({
      ...documentTypeDto,
    });
    return this.documentTypesRepository.create(documentType);
  }

  async findAllDocumentTypes() {
    return this.documentTypesRepository.find({});
  }

  async findOneDocumentType(id: string) {
    return this.documentTypesRepository.findOne({ id });
  }

  async updateDocumentType(id: string, updateCategoryTypeDto: DocumentTypeDto) {
    return this.documentTypesRepository.findOneAndUpdate(
      { id },
      updateCategoryTypeDto,
    );
  }

  async removeDocumentType(id: string) {
    return this.documentTypesRepository.findOneAndDelete({ id });
  }

  // required document
  async createRequiredDocument(requiredDocumentDto: RequiredDocumentDto) {
    const dataFromDb = await this.documentTypesRepository.findOne({
      id: requiredDocumentDto.documentTypeId,
    });
    if (!dataFromDb)
      throw new HttpException('Data Not Found', HttpStatus.NOT_FOUND);
    const dataFromDb2 = await this.permitTypesRepository.findOne({
      id: requiredDocumentDto.permitTypeId,
    });
    if (!dataFromDb2)
      throw new HttpException('Data Not Found', HttpStatus.NOT_FOUND);
    const dataFromDb3 = await this.categoryTypesRepository.findOne({
      id: requiredDocumentDto.categoryTypeId,
    });
    if (!dataFromDb3)
      throw new HttpException('Data Not Found', HttpStatus.NOT_FOUND);

    const requiredDocument = new RequiredDocument({
      ...requiredDocumentDto,
      documentTypes: (requiredDocumentDto.documentTypeId = {
        id: dataFromDb.id,
      } as any),
      permitTypes: (requiredDocumentDto.permitTypeId = {
        id: dataFromDb2.id,
      } as any),
      categoryTypes: (requiredDocumentDto.categoryTypeId = {
        id: dataFromDb3.id,
      } as any),
    });
    return this.requiredDocumentsRepository.create(requiredDocument);
  }

  // async findAllRequiredDocuments() {
  //   return this.requiredDocumentsRepository.find({
  //     // documentTypes: true,
  //     permitTypes: true,
  //     categoryTypes: true,
  //   });
  // }

  // async findAllRequiredDocuments() {
  //   return this.requiredDocumentsRepository.findAll({
  //     relations: ['documentTypes', 'permitTypes', 'categoryTypes'],
  //   });
  // }

  async findAllRequiredDocuments() {
    return this.requiredDocumentsRepository.findAll({
      relations: ['documentTypes', 'permitTypes', 'categoryTypes'],
      order: {
        categoryTypes: {
          code: 'ASC', // Sorts the categories in alphabetical order
        },
      },
    });
  }

  // async findOneRequiredDocument(id: string) {
  //   return this.requiredDocumentsRepository.findOne({
  //     id,
  //     permitTypes: true,
  //     categoryTypes: true,
  //     documentTypes: true,
  //   });
  // }

  async findOneRequiredDocument(id: string) {
    return this.requiredDocumentEntityManagerRepository
      .createQueryBuilder('requireDocument')
      .where('requireDocument.id = :id', {
        id,
      })
      .leftJoinAndSelect('requireDocument.permitTypes', 'permitTypes')
      .leftJoinAndSelect('requireDocument.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('requireDocument.documentTypes', 'documentTypes')
      .getOne();
  }

  // async updateRequiredDocument(
  //   id: string,
  //   updateRequiredDocumentDto: RequiredDocumentDto,
  // ) {
  //   return this.requiredDocumentsRepository.findOneAndUpdate(
  //     { id },
  //     updateRequiredDocumentDto,
  //   );
  // }

  async updateRequiredDocument(
    id: string,
    updateRequiredDocumentDto: RequiredDocumentDto,
  ) {
    const docTypeData = await this.documentTypesRepository.findOne({
      id: updateRequiredDocumentDto.documentTypeId,
    });
    if (!docTypeData) {
      throw new HttpException('Doc Type Data Not Found', HttpStatus.NOT_FOUND);
    }

    const categoryTypeData = await this.categoryTypesRepository.findOne({
      id: updateRequiredDocumentDto.categoryTypeId,
    });
    if (!categoryTypeData) {
      throw new HttpException('Category Data Not Found', HttpStatus.NOT_FOUND);
    }

    const permitTypeData = await this.permitTypesRepository.findOne({
      id: updateRequiredDocumentDto.permitTypeId,
    });
    if (!permitTypeData) {
      throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);
    }

    // Destructure the DTO to get the fields to update
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { categoryTypeId, documentTypeId, permitTypeId, name, code } =
      updateRequiredDocumentDto;

    // Update document required entity
    const documentRequiredUpdate =
      await this.requiredDocumentEntityManagerRepository
        .createQueryBuilder()
        .update(RequiredDocument)
        .set({
          documentTypes: docTypeData,
          categoryTypes: categoryTypeData,
          permitTypes: permitTypeData,
          name: name, // Ensure the name field is included in the update
          code: code,
        })
        .where('id = :id', { id: id })
        .execute();

    console.log(documentRequiredUpdate);
  }

  async removeRequiredDocument(id: string) {
    return this.requiredDocumentsRepository.findOneAndDelete({ id });
  }

  async findRequiredDocumentByPermitTypeId(
    permitTypesId: string,
  ): Promise<RequiredDocument[]> {
    return this.requiredDocumentEntityManagerRepository
      .createQueryBuilder('requireddocument')
      .where('requireddocument.permitTypesId = :permitTypesId', {
        permitTypesId,
      })
      .leftJoinAndSelect('requireddocument.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('requireddocument.documentTypes', 'documentTypes')
      .leftJoinAndSelect('requireddocument.permitTypes', 'permitTypes')
      .getMany();
  }

  async findAllRequiredDocumentByPermitTypeAndCategory(
    permitTypesId: string,
    categoryTypesId: string,
  ): Promise<RequiredDocument[]> {
    return this.requiredDocumentEntityManagerRepository
      .createQueryBuilder('requireddocument')
      .where('requireddocument.permitTypesId = :permitTypesId', {
        permitTypesId,
      })
      .andWhere('requireddocument.categoryTypesId = :categoryTypesId', {
        categoryTypesId,
      })
      .leftJoinAndSelect('requireddocument.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('requireddocument.permitTypes', 'permitTypes')
      .leftJoinAndSelect('requireddocument.documentTypes', 'documentTypes')
      .getMany();
  }

  // technology survey
  async createTechnologySurvey(technologySurveyDto: TechnologySurveyDto) {
    const technologySurvey = new TechnologySurvey({
      ...technologySurveyDto,
    });
    return this.technologySurveysRepository.create(technologySurvey);
  }

  async findAllTechnologySurveys() {
    return this.technologySurveysRepository.find({});
  }

  async findOneTechnologySurvey(id: string) {
    return this.technologySurveysRepository.findOne({ id });
  }

  async updateTechnologySurvey(
    id: string,
    updateTechnologySurveyDto: TechnologySurveyDto,
  ) {
    return this.technologySurveysRepository.findOneAndUpdate(
      { id },
      updateTechnologySurveyDto,
    );
  }

  async removeTechnologySurvey(id: string) {
    return this.technologySurveysRepository.findOneAndDelete({ id });
  }

  // Application Status
  async createApplicationStatus(applicationStatusDto: ApplicationStatusDto) {
    const applicationStatus = new ApplicationStatus({
      ...applicationStatusDto,
    });
    return this.applicationStatusRepository.create(applicationStatus);
  }

  async findAllApplicationStatus() {
    return this.applicationStatusRepository.find({});
  }

  async findOneApplicationStatus(id: string) {
    return this.applicationStatusRepository.findOne({ id });
  }

  async updateApplicationStatus(
    id: string,
    applicationStatusDto: ApplicationStatusDto,
  ) {
    return this.applicationStatusRepository.findOneAndUpdate(
      { id },
      applicationStatusDto,
    );
  }

  async removeApplicationStatus(id: string) {
    return this.applicationStatusRepository.findOneAndDelete({ id });
  }

  // Project Status
  async createProjectStatus(projectStatusDto: ProjectStatusDto) {
    const projectStatus = new ProjectStatus({
      ...projectStatusDto,
    });
    return this.projectStatusRepository.create(projectStatus);
  }

  async findAllProjectStatus() {
    return this.projectStatusRepository.find({});
  }

  async findOneProjectStatus(id: string) {
    return this.projectStatusRepository.findOne({ id });
  }

  async updateProjectStatus(id: string, projectStatusDto: ProjectStatusDto) {
    return this.projectStatusRepository.findOneAndUpdate(
      { id },
      projectStatusDto,
    );
  }

  async removeProjectStatus(id: string) {
    return this.projectStatusRepository.findOneAndDelete({ id });
  }

  // // Assignee
  // async createAssignee(assigneeDto: AssigneeDto) {
  //   const dataFromDb = await this.projectsRepository.findOne({
  //     id: assigneeDto.projectId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

  //   const dataFromDb2 = await this.projectStatusRepository.findOne({
  //     id: assigneeDto.projectStatusId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   const assignee = new Assignee({
  //     ...assigneeDto,
  //     projects: (assigneeDto.projectId = {
  //       id: dataFromDb.id,
  //     } as any),
  //   });

  //   // Update project entity
  //   const projectUpdate = await this.projectEntityManagerRepository
  //     .createQueryBuilder('project')
  //     .update(Project)
  //     .set({ projectStatus: dataFromDb2.id }) // Assuming projectStatusId is the correct field to update
  //     .where('project.id = :projectId', { projectId: assigneeDto.projectId }) // Assuming projectId is the correct field to match
  //     .execute();
  //   console.log(projectUpdate);

  //   return this.assigneeRepository.create(assignee);
  // }

  // Assignee

  async createAssignee(assigneeDto: AssigneeDto) {
    // Check if the project already exists with the same licenseNumber
    const existingAssignee = await this.assigneeEntityManagerRepository
      .createQueryBuilder('assignee')
      .where('assignee.projectsId = :projectId', {
        projectId: assigneeDto.projectId,
      })
      .andWhere('assignee.licenseNumber = :licenseNumber', {
        licenseNumber: assigneeDto.licenseNumber,
      })
      .getOne();

    // const existingAssignee = await this.assigneeRepository.findOne({
    //   where: {
    //     projectId: assigneeDto.projectId,
    //     licenseNumber: assigneeDto.licenseNumber,
    //   },
    // });

    if (existingAssignee) {
      throw new HttpException(
        'The project is already assigned to this engineer.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate project existence
    const projectData = await this.projectsRepository.findOne({
      id: assigneeDto.projectId,
    });

    if (!projectData)
      throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);

    // Validate project status existence
    const projectStatusData = await this.projectStatusRepository.findOne({
      id: assigneeDto.projectStatusId,
    });

    if (!projectStatusData)
      throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

    // Create an Assignee instance
    const assignee = new Assignee({
      ...assigneeDto,
      projects: projectData,
    });

    await this.assigneeRepository.create(assignee);

    // Update project entity
    const projectUpdate = await this.projectEntityManagerRepository
      .createQueryBuilder()
      .update(Project)
      .set({ projectStatus: projectStatusData })
      .where('id = :projectId', { projectId: assigneeDto.projectId })
      .execute();
    console.log(projectUpdate);

    // Email to engineer
    const userDetail = await this.checkUserData(assigneeDto.userIdForAssignee);
    const requestData = {
      sender_name: 'KUBAKA MIS',
      sender_email: `${config.notification.senderEmail}`,
      // sender_email: '<EMAIL>',
      receiver_name: 'KUBAKA User',
      receiver_email: userDetail.email,
      subject: 'Kubaka Notification Email',
      message: `
        Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
          The applicant has sent you an invitation for a construction permit.
          Go into the system, check pending requests, and accept or reject it.
          <br>
        Best regards, <br>
        KUBAKA Team
        `,
    };

    try {
      const [emailResponse, smsResponse] = await Promise.all([
        // sending email responses
        axios.post(
          `${config.notification.emailAPI}/email/send/`,
          // 'https://notification.kubaka.gov.rw/email/send/',
          requestData,
        ),
        // sending sms responses
        axios.post(
          `${config.notification.smsAPI}/sms/send/`,
          // 'https://notification.kubaka.gov.rw/sms/send',
          {
            msisdn: userDetail.phoneNumber,
            message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\n The applicant has sent you an invitation for a construction permit.\n Go into the system, check pending requests, and accept or reject it.\n\nBest regards,\nKUBAKA Team`,
          },
        ),
      ]);

      console.log('Email sent successfully:', emailResponse.data);
      console.log('SMS sent successfully:', smsResponse.data);

      return assignee;
    } catch (error) {
      console.error('Error sending email:', error);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async createAssignee(assigneeDto: AssigneeDto) {
  //   const projectData = await this.projectsRepository.findOne({
  //     id: assigneeDto.projectId,
  //   });

  //   if (!projectData)
  //     throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);
  //   // Check if project status with provided ID exists

  //   const projectStatusData = await this.projectStatusRepository.findOne({
  //     id: assigneeDto.projectStatusId,
  //   });
  //   if (!projectStatusData)
  //     throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

  //   // Creating Assignee instance
  //   const assignee = new Assignee({
  //     ...assigneeDto,
  //     projects: projectData,
  //   });

  //   this.assigneeRepository.create(assignee);

  //   // Update project entity
  //   const projectUpdate = await this.projectEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Project)
  //     .set({ projectStatus: projectStatusData })
  //     .where('id = :projectId', { projectId: assigneeDto.projectId })
  //     .execute();
  //   console.log(projectUpdate);

  //   // Email to engineer
  //   const userDetail = await this.checkUserData(assigneeDto.userIdForAssignee);
  //   const requestData = {
  //     sender_name: 'KUBAKA MIS',
  //     sender_email: '<EMAIL>',
  //     receiver_name: 'KUBAKA User',
  //     receiver_email: userDetail.email,
  //     subject: 'Kubaka Notification Email',
  //     message: `
  //       Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //         The applicant has sent you an invitation for a construction permit.
  //         Go into the system, check pending requests, and accept or reject it.
  //         <br>
  //       Best regards, <br>
  //       KUBAKA Team
  //       `,
  //   };
  //   try {
  //     const [emailResponse, smsResponse] = await Promise.all([
  //       // sending email responses
  //       axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       ),
  //       // sending sms responses
  //       axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //         msisdn: userDetail.phoneNumber,
  //         message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\n The applicant has sent you an invitation for a construction permit.\n Go into the system, check pending requests, and accept or reject it .\n\nBest regards,\nKUBAKA Team`,
  //       }),
  //     ]);

  //     console.log('Email sent successfully:', emailResponse);
  //     console.log('SMS sent successfully:', smsResponse);

  //     return projectUpdate;
  //   } catch (error) {
  //     console.error('Error sending email:', error);
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }

  //   // return this.assigneeRepository.create(assignee);
  // }

  async findAllAssignee() {
    return this.assigneeRepository.findAll({
      relations: {
        projects: true,
      },
    });
  }
  // async findAllAssignee() {
  //   return this.assigneeRepository.find({
  //     projects: true,
  //     // projectStatus: true,
  //   });
  // }

  async findOneAssignee(id: string) {
    return this.assigneeRepository.findOne({ id });
  }

  async findAssigneeOfTheProject(projectId: string) {
    const existingAssignee = await this.assigneeEntityManagerRepository
      .createQueryBuilder('assignee')
      .where('assignee.projectsId = :projectId', {
        projectId: projectId,
      })
      .getMany();
    // .getOne();
    return existingAssignee;
  }

  // async updateAssignee(id: string, updateAssigneeDto: UpdateAssigneeDto) {
  //   return this.assigneeRepository.findOneAndUpdate({ id }, updateAssigneeDto);
  // }

  async updateAssignee(id: string, updateAssigneeDto: UpdateAssigneeDto) {
    const projectData = await this.projectsRepository.findOne({
      id: updateAssigneeDto.projectId,
    });

    if (!projectData)
      throw new HttpException('Project Data Not Found', HttpStatus.NOT_FOUND);
    // Check if project status with provided ID exists

    const projectStatusData = await this.projectStatusRepository.findOne({
      id: updateAssigneeDto.projectStatusId,
    });
    if (!projectStatusData)
      throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectId, ...updateData } = updateAssigneeDto;

    // Update the Assignee using QueryBuilder
    const updateResult = await this.assigneeEntityManagerRepository
      .createQueryBuilder()
      .update(Assignee)
      .set(updateData)
      .where('id = :id', { id })
      .execute();

    // Update project entity
    const projectUpdate = await this.projectEntityManagerRepository
      .createQueryBuilder()
      .update(Project)
      .set({ projectStatus: projectStatusData })
      .where('id = :projectId', { projectId: updateAssigneeDto.projectId })
      .execute();
    console.log(projectUpdate);

    console.log(updateResult);
    // Retrieve the updated Assignee
    const updatedAssignee = await this.assigneeRepository.findOne({ id });

    return updatedAssignee;
  }

  //delete an Engineer On a Project
  async deleteEngineerOnProject(
    deleteEngineerOnProjectDto: DeleteEngineerOnProjectDto,
  ) {
    // Check if the project already exists with the same licenseNumber
    const existingAssignee = await this.assigneeEntityManagerRepository
      .createQueryBuilder('assignee')
      .where('assignee.projectsId = :projectId', {
        projectId: deleteEngineerOnProjectDto.projectId,
      })
      .andWhere('assignee.licenseNumber = :licenseNumber', {
        licenseNumber: deleteEngineerOnProjectDto.licenseNumber,
      })
      .getOne();

    if (!existingAssignee) {
      throw new HttpException(
        'The engineer is not found on this project',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate the timeline vs created_at
    const { timeLineDays, created_at } = existingAssignee;
    const currentDate = new Date();
    const createdAtDate = new Date(created_at);

    // Calculate the difference in days
    const daysDifference = Math.floor(
      (currentDate.getTime() - createdAtDate.getTime()) / (1000 * 60 * 60 * 24),
    );

    const projectStatusData = this.findOneProjectStatus(
      existingAssignee.projectStatusId,
    );

    if (
      (await projectStatusData).code === 'PAPRV' &&
      daysDifference <= parseInt(timeLineDays, 10)
    ) {
      throw new HttpException(
        `Cannot delete the record. Minimum timeline of ${timeLineDays} days not yet exceeded.`,
        HttpStatus.BAD_REQUEST,
      );
    }
    // if (daysDifference <= parseInt(timeLineDays, 10)) {
    //   throw new HttpException(
    //     `Cannot delete the record. Minimum timeline of ${timeLineDays} days not yet exceeded.`,
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    const projectData = this.findOneProject(
      deleteEngineerOnProjectDto.projectId,
    );
    const projectIdForUpdate = (await projectData).id;

    // Delete the record
    await this.assigneeEntityManagerRepository.delete({
      id: existingAssignee.id,
    });

    // Get the projectStatus of created
    const allProjectStatuses = await this.findAllProjectStatus();
    const createdStatusData = allProjectStatuses.find(
      (status) => status.code === 'PCRTD',
    );

    // Update project entity
    const updateProject = await this.projectEntityManagerRepository
      .createQueryBuilder()
      .update(Project)
      .set({ projectStatus: createdStatusData })
      .where('id = :id', { id: projectIdForUpdate })
      .execute();
    console.log(updateProject);

    return {
      message: 'Engineer successfully removed from the project.',
    };
  }

  async removeAssignee(id: string) {
    return this.assigneeRepository.findOneAndDelete({ id });
  }

  // updating assigneeStatus
  async updateAssignStatus(assigneeId: string): Promise<Assignee> {
    const assignee = await this.assigneeEntityManagerRepository.findOne({
      where: { id: assigneeId },
    });
    if (!assignee) {
      throw new HttpException('Assignee not found', HttpStatus.NOT_FOUND);
    }
    assignee.assigneeStatusId = '1';
    return await this.assigneeEntityManagerRepository.save(assignee);
  }

  // updating assigneeStatus / re assignment to the engineer / architect
  async reaAssigneeStatus(assigneeId: string): Promise<Assignee> {
    const assignee = await this.assigneeEntityManagerRepository.findOne({
      where: { id: assigneeId },
    });
    if (!assignee) {
      throw new HttpException('Assignee not found', HttpStatus.NOT_FOUND);
    }
    assignee.assigneeStatusId = '0';
    return await this.assigneeEntityManagerRepository.save(assignee);
  }

  // // assignee reject project
  // async rejectProjectAsAssignee(
  //   projectUserId: string,
  //   licenseNumber: string,
  //   projectId: string,
  // ) {
  //   // get project Status
  //   const allProjectStatuses = await this.findAllProjectStatus();
  //   const projectStatusData = allProjectStatuses.find(
  //     (status) => status.code === 'PRJCT',
  //   );
  //   if (!projectStatusData) {
  //     throw new NotFoundException('Project status "PRJCT" not found');
  //   }

  //   // Update project entity
  //   const applicationUpdate = await this.projectEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Project)
  //     .set({ projectStatus: projectStatusData })
  //     // .set({ submittedByUserId: applicationDtoUpdate.userId })
  //     // .where('id = :id', { id: projectId })
  //     .where('id = :projectId', { projectId: projectId })
  //     .execute();
  //   console.log(applicationUpdate);

  //   //Get user information
  //   const userDetail = await this.checkUserData(projectUserId);

  //   const requestData = {
  //     sender_name: 'KUBAKA MIS',
  //     sender_email: '<EMAIL>',
  //     receiver_name: 'KUBAKA User',
  //     receiver_email: userDetail.email,
  //     subject: 'Kubaka Notification Email',
  //     message: `
  //       Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
  //       The Engineer / Architect with this license number ${licenseNumber} rejected your proposal! Assign to others in order to submit your application.<br>
  //       Best regards, <br>
  //       KUBAKA Team
  //       `,
  //   };

  //   try {
  //     const [emailResponse, smsResponse] = await Promise.all([
  //       // sending email responses
  //       axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       ),
  //       // sending sms responses
  //       axios.post('https://notification.kubaka.gov.rw/sms/send', {
  //         msisdn: userDetail.phoneNumber,
  //         message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThe Engineer / Architect with this license number ${licenseNumber} rejected your proposal! Assign to others in order to submit your application.\n\nBest regards,\nKUBAKA Team`,
  //       }),
  //     ]);

  //     console.log('Email sent successfully:', emailResponse);
  //     console.log('SMS sent successfully:', smsResponse);

  //     return applicationUpdate;
  //     // return userDetail.lastName;
  //   } catch (error) {
  //     console.error('Error sending email:', error);
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
  // assignee reject project
  async rejectProjectAsAssignee(rejectProjectDto: RejectProjectDto) {
    // Get all project statuses
    const allProjectStatuses = await this.findAllProjectStatus();
    const projectStatusData = allProjectStatuses.find(
      (status) => status.code === 'PRJCT',
    );
    if (!projectStatusData) {
      throw new NotFoundException('Project status "PRJCT" not found');
    }

    // Update project entity
    const applicationUpdate = await this.projectEntityManagerRepository
      .createQueryBuilder()
      .update(Project)
      .set({ projectStatus: projectStatusData })
      .where('id = :projectId', { projectId: rejectProjectDto.projectId })
      .execute();
    console.log('Project status updated:', applicationUpdate);
    if (!applicationUpdate) {
      throw new HttpException(
        'Project status update failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Get user information
    const userDetail = await this.checkUserData(rejectProjectDto.projectUserId);

    // Prepare request data for notifications
    const requestData = {
      sender_name: 'KUBAKA MIS',
      sender_email: `${config.notification.senderEmail}`,
      // sender_email: '<EMAIL>',
      receiver_name: 'KUBAKA User',
      receiver_email: userDetail.email,
      subject: 'Kubaka Notification Email',
      message: `
        Dear ${userDetail.firstName} ${userDetail.lastName}, <br><br>
        The Engineer / Architect with this license number ${rejectProjectDto.licenseNumber} rejected your proposal! Assign to others in order to submit your application.<br>
        Best regards, <br>
        KUBAKA Team
        `,
    };

    try {
      const [emailResponse, smsResponse] = await Promise.all([
        // Sending email response
        axios.post(
          `${config.notification.emailAPI}/email/send/`,
          // 'https://notification.kubaka.gov.rw/email/send/',
          requestData,
        ),
        // Sending SMS response
        axios.post(
          `${config.notification.smsAPI}/sms/send/`,
          // 'https://notification.kubaka.gov.rw/sms/send',
          {
            msisdn: userDetail.phoneNumber,
            message: `Dear ${userDetail.firstName} ${userDetail.lastName},\n\nThe Engineer / Architect with this license number ${rejectProjectDto.licenseNumber} rejected your proposal! Assign to others in order to submit your application.\n\nBest regards,\nKUBAKA Team`,
          },
        ),
      ]);

      console.log('Email sent successfully:', emailResponse);
      console.log('SMS sent successfully:', smsResponse);
      // get update information

      const applicationUpdateed =
        await this.projectEntityManagerRepository.findOne({
          where: { id: rejectProjectDto.projectId },
          relations: { projectStatus: true },
        });

      return applicationUpdateed;
    } catch (error) {
      console.error('Error sending notifications:', error);
      throw new HttpException(
        'Notifications not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // assign reviewers and check if it not there already with logs
  // assign with logs
  async assignUsersForReview(
    applicationId: string,
    userIds: string[],
    clientIp: string,
    userAgent: string,
  ): Promise<Application> {
    try {
      const clientInfo = this.parseUserAgent(userAgent);
      const application = await this.applicationRepository.findOne({
        id: applicationId,
      });

      if (!application) {
        throw new NotFoundException('Application not found');
      }

      // Ensure userIds is an array of strings
      if (
        !Array.isArray(userIds) ||
        userIds.some((id) => typeof id !== 'string')
      ) {
        throw new BadRequestException('Invalid user IDs provided');
      }

      // Check for duplicate users in the incoming list
      const uniqueUserIds = [...new Set(userIds)];
      if (uniqueUserIds.length !== userIds.length) {
        throw new BadRequestException(
          'Duplicate user IDs found in the provided list.',
        );
      }

      // Check if any of the provided user IDs are already assigned
      const alreadyAssignedUsers = uniqueUserIds.filter(
        (userId) =>
          Array.isArray(application.assignUsersFoReview) &&
          application.assignUsersFoReview.includes(userId),
      );

      if (alreadyAssignedUsers.length > 0) {
        throw new BadRequestException(
          `The following users are already assigned to this application: ${alreadyAssignedUsers.join(
            ', ',
          )}`,
        );
      }

      // Assign unique user IDs to assignUsersFoReview property
      application.assignUsersFoReview = [
        ...(application.assignUsersFoReview || []),
        ...uniqueUserIds,
      ];

      // Retrieve application status data
      const allApplicationStatuses = await this.findAllApplicationStatus();

      // Determine the appropriate status code based on isNonObjection
      const statusCode = application.isNonObjection === '1' ? 'NOUNRV' : 'UNRV';
      const applicationStatus = allApplicationStatuses.find(
        (status) => status.code === statusCode,
      );

      if (!applicationStatus) {
        throw new NotFoundException(
          `Application status "${statusCode}" not found`,
        );
      }

      // Update application status
      application.applicationStatus = applicationStatus;
      await this.applicationRepository.create(application);

      // Create and save ReviewersOnApplication entities
      const reviewerPromises = uniqueUserIds.map((userId) => {
        const reviewerDto = new ReviewersOnApplicationDto();
        reviewerDto.userId = userId;
        reviewerDto.applicationId = applicationId;
        reviewerDto.ipAddress = clientIp;
        reviewerDto.browser = clientInfo.browser;
        reviewerDto.operatingSystem = clientInfo.operatingSystem;
        reviewerDto.status = '0';
        return this.createReviewersOnApplication(reviewerDto);
      });

      await Promise.all(reviewerPromises);

      return application;
    } catch (error) {
      console.error('Error in saving:', error);

      // Re-throw BadRequestException or NotFoundException without wrapping
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      console.log(error);
      // Generic fallback for unexpected errors
      throw new HttpException(
        'Error on server',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  // // assign reviewers and check if it not there already with logs
  // // assign with logs
  // async assignUsersForReview(
  //   applicationId: string,
  //   userIds: string[],
  //   clientIp: string,
  //   userAgent: string,
  // ): Promise<Application> {
  //   try {
  //     const clientInfo = this.parseUserAgent(userAgent);
  //     const application = await this.applicationRepository.findOne({
  //       id: applicationId,
  //     });

  //     if (!application) {
  //       throw new NotFoundException('Application not found');
  //     }

  //     // Ensure userIds is an array of strings
  //     if (
  //       !Array.isArray(userIds) ||
  //       userIds.some((id) => typeof id !== 'string')
  //     ) {
  //       throw new BadRequestException('Invalid user IDs provided');
  //     }

  //     // Check for duplicate users in the incoming list
  //     const uniqueUserIds = [...new Set(userIds)];
  //     if (uniqueUserIds.length !== userIds.length) {
  //       throw new BadRequestException(
  //         'Duplicate user IDs found in the provided list.',
  //       );
  //     }

  //     // Check if any of the provided user IDs are already assigned
  //     const alreadyAssignedUsers = uniqueUserIds.filter((userId) =>
  //       application.assignUsersFoReview.includes(userId),
  //     );

  //     if (alreadyAssignedUsers.length > 0) {
  //       throw new BadRequestException(
  //         `The following users are already assigned to this application: ${alreadyAssignedUsers.join(
  //           ', ',
  //         )}`,
  //       );
  //     }

  //     // Assign unique user IDs to assignUsersFoReview property
  //     application.assignUsersFoReview = [
  //       ...application.assignUsersFoReview,
  //       ...uniqueUserIds,
  //     ];

  //     // Retrieve application status data
  //     const allApplicationStatuses = await this.findAllApplicationStatus();

  //     // Determine the appropriate status code based on isNonObjection
  //     const statusCode = application.isNonObjection === '1' ? 'NOUNRV' : 'UNRV';
  //     const applicationStatus = allApplicationStatuses.find(
  //       (status) => status.code === statusCode,
  //     );

  //     if (!applicationStatus) {
  //       throw new NotFoundException(
  //         `Application status "${statusCode}" not found`,
  //       );
  //     }

  //     // Update application status
  //     application.applicationStatus = applicationStatus;
  //     await this.applicationRepository.create(application);

  //     // Create and save ReviewersOnApplication entities
  //     const reviewerPromises = uniqueUserIds.map((userId) => {
  //       const reviewerDto = new ReviewersOnApplicationDto();
  //       reviewerDto.userId = userId;
  //       reviewerDto.applicationId = applicationId;
  //       reviewerDto.ipAddress = clientIp;
  //       reviewerDto.browser = clientInfo.browser;
  //       reviewerDto.operatingSystem = clientInfo.operatingSystem;
  //       reviewerDto.status = '0';
  //       return this.createReviewersOnApplication(reviewerDto);
  //     });

  //     await Promise.all(reviewerPromises);

  //     return application;
  //   } catch (error) {
  //     console.error('Error in saving:', error);

  //     // Re-throw BadRequestException or NotFoundException without wrapping
  //     if (
  //       error instanceof BadRequestException ||
  //       error instanceof NotFoundException
  //     ) {
  //       throw error;
  //     }
  //     console.log(error);
  //     // Generic fallback for unexpected errors
  //     throw new HttpException(
  //       'Error on server',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // // assign with logs
  // async assignUsersForReview(
  //   applicationId: string,
  //   userIds: string[],
  //   clientIp: string,
  //   userAgent: string,
  // ): Promise<Application> {
  //   try {
  //     const clientInfo = this.parseUserAgent(userAgent);
  //     const application = await this.applicationRepository.findOne({
  //       id: applicationId,
  //     });

  //     if (!application) {
  //       throw new NotFoundException('Application not found');
  //     }

  //     // Ensure userIds is an array of strings
  //     if (
  //       !Array.isArray(userIds) ||
  //       userIds.some((id) => typeof id !== 'string')
  //     ) {
  //       throw new BadRequestException('Invalid user IDs provided');
  //     }

  //     // Assign user IDs directly to assignUsersFoReview property
  //     application.assignUsersFoReview = userIds;

  //     // Retrieve application status data
  //     const allApplicationStatuses = await this.findAllApplicationStatus();

  //     // Determine the appropriate status code based on isNonObjection
  //     const statusCode = application.isNonObjection === '1' ? 'NOUNRV' : 'UNRV';
  //     const applicationStatus = allApplicationStatuses.find(
  //       (status) => status.code === statusCode,
  //     );

  //     if (!applicationStatus) {
  //       throw new NotFoundException(
  //         `Application status "${statusCode}" not found`,
  //       );
  //     }

  //     // Update application status
  //     application.applicationStatus = applicationStatus;
  //     await this.applicationRepository.create(application);

  //     // Create and save ReviewersOnApplication entities
  //     const reviewerPromises = userIds.map((userId) => {
  //       const reviewerDto = new ReviewersOnApplicationDto();
  //       reviewerDto.userId = userId;
  //       reviewerDto.applicationId = applicationId;
  //       reviewerDto.ipAddress = clientIp;
  //       reviewerDto.browser = clientInfo.browser;
  //       reviewerDto.operatingSystem = clientInfo.operatingSystem;
  //       reviewerDto.status = '0';
  //       return this.createReviewersOnApplication(reviewerDto);
  //     });

  //     await Promise.all(reviewerPromises);

  //     return application;
  //   } catch (error) {
  //     console.error('Error in saving:', error);
  //     throw new HttpException(
  //       'Error on server ',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // async assignUsersForReview(
  //   applicationId: string,
  //   userIds: string[],
  // ): Promise<Application> {
  //   const application = await this.applicationRepository.findOne({
  //     id: applicationId,
  //   });

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   // Ensure userIds is an array of strings
  //   if (
  //     !Array.isArray(userIds) ||
  //     userIds.some((id) => typeof id !== 'string')
  //   ) {
  //     throw new BadRequestException('Invalid user IDs provided');
  //   }

  //   // Assign user IDs directly to assignUsersFoReview property
  //   application.assignUsersFoReview = userIds;

  //   // Retrieve application status data
  //   const allApplicationStatuses = await this.findAllApplicationStatus();

  //   // Determine the appropriate status code based on isNonObjection
  //   const statusCode = application.isNonObjection === '1' ? 'NOUNRV' : 'UNRV';
  //   const applicationStatus = allApplicationStatuses.find(
  //     (status) => status.code === statusCode,
  //   );

  //   if (!applicationStatus) {
  //     throw new NotFoundException(
  //       `Application status "${statusCode}" not found`,
  //     );
  //   }

  //   // Update application status
  //   application.applicationStatus = applicationStatus;
  //   await this.applicationRepository.create(application);

  //   // Create and save ReviewersOnApplication entities
  //   const reviewerPromises = userIds.map((userId) => {
  //     const reviewerDto = new ReviewersOnApplicationDto();
  //     reviewerDto.userId = userId;
  //     reviewerDto.applicationId = applicationId;
  //     reviewerDto.status = '0';
  //     return this.createReviewersOnApplication(reviewerDto);
  //   });

  //   await Promise.all(reviewerPromises);

  //   return application;
  // }

  // async assignUsersForReview(
  //   applicationId: string,
  //   userIds: string[],
  // ): Promise<Application> {
  //   const application = await this.applicationRepository.findOne({
  //     id: applicationId,
  //   });

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   // Ensure userIds is an array of strings
  //   if (
  //     !Array.isArray(userIds) ||
  //     userIds.some((id) => typeof id !== 'string')
  //   ) {
  //     throw new BadRequestException('Invalid user IDs provided');
  //   }

  //   // Assign user IDs directly to assignUsersFoReview property
  //   application.assignUsersFoReview = userIds;

  //   // Retrieve application status data
  //   const allApplicationStatuses = await this.findAllApplicationStatus();
  //   const applicationStatus = allApplicationStatuses.find(
  //     (status) => status.code === 'UNRV',
  //   );
  //   if (!applicationStatus) {
  //     throw new NotFoundException('Application status "UNRV" not found');
  //   }

  //   // Update application status
  //   application.applicationStatus = applicationStatus;
  //   await this.applicationRepository.create(application);

  //   // Create and save ReviewersOnApplication entities
  //   const reviewerPromises = userIds.map((userId) => {
  //     const reviewerDto = new ReviewersOnApplicationDto();
  //     reviewerDto.userId = userId;
  //     reviewerDto.applicationId = applicationId;
  //     reviewerDto.status = '0';
  //     return this.createReviewersOnApplication(reviewerDto);
  //   });

  //   await Promise.all(reviewerPromises);

  //   return application;
  // }

  //

  // // assignUsersFoReview
  // async assignUsersForReview(
  //   applicationId: string,
  //   userIds: string[],
  // ): Promise<Application> {
  //   const application = await this.applicationRepository.findOne({
  //     id: applicationId,
  //   });

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   // Ensure userIds is an array of strings
  //   if (
  //     !Array.isArray(userIds) ||
  //     userIds.some((id) => typeof id !== 'string')
  //   ) {
  //     throw new BadRequestException('Invalid user IDs provided');
  //   }

  //   // Assign user IDs directly to assignUsersFoReview property
  //   application.assignUsersFoReview = userIds;

  //   return this.applicationRepository.create(application);
  // }
  // get Reviews by applicationId
  async getReviewUserIdsByApplicationId(
    applicationId: string,
  ): Promise<string[]> {
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // return application.assignUsersFoReview || [];
    const reviewUserIds = application.assignUsersFoReview || [];
    const userPromises = reviewUserIds.map((userId) => this.checkUser(userId));

    return Promise.all(userPromises);
  }

  // get all applications for assignUsersFoReview
  async findAllWithAssignedUsers(): Promise<Application[]> {
    return this.applicationEntityManagerRepository.find({
      relations: { projects: true },
    });
  }

  //get reviewers by application Id
  async findReviewersByApplicationId(applicationId: string) {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('reviewersOnApplication')
      .where('reviewersOnApplication.applicationsId = :applicationId', {
        applicationId,
      })
      .getMany();
  }

  // get my application for review
  async findAllByAssignUsersForReview(userId: string): Promise<Application[]> {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .where(
        `',' || application.assignUsersFoReview || ',' LIKE '%,${userId},%'`,
      )
      .getMany();
  }

  // get application for review by application Id
  async findAllReviewerByApplicationId(
    applicationId: string,
  ): Promise<Application[]> {
    return this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .where(
        `',' || application.assignUsersFoReview || ',' LIKE '%,${applicationId},%'`,
      )
      .getMany();
  }

  // remove a user in reviewers
  // async removeUserFromReview(
  //   applicationId: string,
  //   userId: string,
  // ): Promise<Application> {
  //   const application = await this.applicationRepository.findOne({
  //     id: applicationId,
  //   });

  //   if (!application) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   if (
  //     !application.assignUsersFoReview ||
  //     !application.assignUsersFoReview.includes(userId)
  //   ) {
  //     throw new NotFoundException('User ID not found in assignUsersFoReview');
  //   }

  //   application.assignUsersFoReview = application.assignUsersFoReview.filter(
  //     (id) => id !== userId,
  //   );

  //   return this.applicationRepository.create(application);
  // }

  async removeUserFromReview(applicationId: string, userId: string) {
    // Fetch application
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    // Check if application exists
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Ensure assignUsersFoReview is an array and check if userId exists
    if (
      !Array.isArray(application.assignUsersFoReview) ||
      !application.assignUsersFoReview.includes(userId)
    ) {
      throw new NotFoundException('User ID not found in assignUsersFoReview');
    }

    // Remove userId from assignUsersFoReview
    application.assignUsersFoReview = application.assignUsersFoReview.filter(
      (id) => id !== userId,
    );

    // Save updated application
    const updatedApplication =
      await this.applicationRepository.create(application);

    // return updatedApplication;
    console.log(updatedApplication);
    return {
      statusCode: HttpStatus.OK,
      message: 'User removed successfully on this application',
    };
  }

  // Re assign a user in reviewers
  async reassignUserFromReviewRHAConsider(
    applicationId: string,
    userId: string,
    doneByUserId: string,
  ) {
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    if (
      !application.assignUsersFoReview ||
      !application.assignUsersFoReview.includes(userId)
    ) {
      throw new NotFoundException('User ID not found in assignUsersFoReview');
    }

    // Update the status of reviewer in reviewers table
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder()
      .update(ReviewersOnApplication)
      .set({
        status: '0',
      })
      .where('userId = :userId', { userId: userId })
      .andWhere('applicationsId = :applicationId', { applicationId })
      .execute();

    // Check the agency
    const agencyData = await this.checkUserData(doneByUserId);
    console.log(agencyData);

    // Get all application statuses
    const allApplicationStatuses = await this.findAllApplicationStatus();

    // Determine application status based on agency
    let applicationStatusData;

    // if (agencyData.agency.code === 'RHA') {
    //   applicationStatusData = allApplicationStatuses.find(
    //     (status) => status.code === 'NOUNRV',
    //   );
    // } else {
    //   applicationStatusData = allApplicationStatuses.find(
    //     (status) => status.code === 'UNRV',
    //   );
    // }
    if (agencyData?.agency?.code === 'RHA') {
      applicationStatusData = allApplicationStatuses.find(
        (status) => status.code === 'NOUNRV',
      );
    } else {
      applicationStatusData = allApplicationStatuses.find(
        (status) => status.code === 'UNRV',
      );
    }

    // Now you can use applicationStatusData to update the application status

    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({ applicationStatus: applicationStatusData })
      .where('id = :applicationId', { applicationId: applicationId })
      .execute();

    console.log('Update Result:', applicationUpdate);

    // return reviewer;
    console.log(reviewer);
    return {
      statusCode: HttpStatus.OK,
      message: 'User re-assigned successfully to this application',
    };
  }

  // // Re assign a user in reviewers
  async reassignUserFromReview(applicationId: string, userId: string) {
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    if (
      !application.assignUsersFoReview ||
      !application.assignUsersFoReview.includes(userId)
    ) {
      throw new NotFoundException('User ID not found in assignUsersFoReview');
    }

    // Update the status of reviewer in reviewers table
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder()
      .update(ReviewersOnApplication)
      .set({
        status: '0',
      })
      .where('userId = :userId', { userId: userId })
      .andWhere('applicationsId = :applicationId', { applicationId })
      .execute();

    // Update application status

    const allApplicationStatuses = await this.findAllApplicationStatus();

    const applicationStatusData = allApplicationStatuses.find(
      (status) => status.code === 'UNRV',
    );

    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({ applicationStatus: applicationStatusData })
      .where('id = :applicationId', { applicationId: applicationId })
      .execute();

    console.log('Update Result:', applicationUpdate);

    // return reviewer;
    console.log(reviewer);
    return {
      statusCode: HttpStatus.OK,
      message: 'User re-assigned successfully to this application',
    };
  }

  // remove a user in reviewers
  async removeUserFromReview2(applicationId: string, userId: string) {
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    if (
      !application.assignUsersFoReview ||
      !application.assignUsersFoReview.includes(userId)
    ) {
      throw new NotFoundException('User ID not found in assignUsersFoReview');
    }

    // Update the status of reviewer in reviewers table
    await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder()
      .update(ReviewersOnApplication)
      .set({
        status: '3',
      })
      .where('userId = :userId', { userId: userId })
      .andWhere('applicationsId = :applicationId', { applicationId })
      .execute();

    // return reviewer;
    return {
      statusCode: HttpStatus.OK,
      message: 'Reviewer removed successfully to this application',
    };
  }

  // adding another userId into assignUsersFoReview
  async addUserToReview(
    applicationId: string,
    userId: string,
  ): Promise<Application> {
    const application = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    if (!application.assignUsersFoReview) {
      application.assignUsersFoReview = [];
    }

    if (application.assignUsersFoReview.includes(userId)) {
      throw new NotFoundException(
        'User ID already exists in assignUsersFoReview',
      );
    }

    application.assignUsersFoReview.push(userId);

    return this.applicationRepository.create(application);
  }

  // update application after approval

  // async updateApplicationAfterApproval(applicationId: any) {
  //   const applicationStatusId = '2a9c2861-1902-47eb-a9a0-72cb90ee81d8';

  //   const inputObject = applicationId;
  //   const uuid = this.extractUUID(inputObject);
  //   console.log(uuid);

  //   // Retrieve application status data
  //   const applicationStatusData =
  //     await this.applicationStatusRepository.findOne({
  //       id: applicationStatusId,
  //     });

  //   if (!applicationStatusData) {
  //     throw new NotFoundException('Status not found');
  //   }
  //   console.log(applicationStatusData);

  //   // Retrieve application
  //   const applicationData = await this.applicationRepository.findOne({
  //     id: uuid,
  //   });

  //   if (!applicationData) {
  //     throw new NotFoundException('Application not found');
  //   }
  //   console.log(applicationData);

  //   // Update application status
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ applicationStatus: applicationStatusData })
  //     .where('"id" = :applicationId', { applicationId: uuid })
  //     .execute();

  //   console.log(applicationUpdate);

  //   return applicationUpdate;
  // }

  async updateApplicationAfterApproval(
    applicationId: any,
    applicationStatusId: string,
  ) {
    const uuidApplication = this.extractUUID(applicationId);
    console.log(uuidApplication);

    // Retrieve application status data
    const applicationStatusData =
      await this.applicationStatusRepository.findOne({
        id: applicationStatusId,
      });

    if (!applicationStatusData) {
      throw new HttpException('Status Data Not Found', HttpStatus.NOT_FOUND);
    }

    // Retrieve application
    const applicationData = await this.applicationRepository.findOne({
      id: uuidApplication,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }
    console.log(applicationData);

    // Calculate number of days from the correct start date
    let baseDate = applicationData.submittedDate;

    if (
      applicationData.resubmittedDate &&
      new Date(applicationData.resubmittedDate) >
        new Date(applicationData.submittedDate)
    ) {
      baseDate = applicationData.resubmittedDate;
    }

    const today = new Date();
    const startDate = new Date(baseDate);
    const timeDiff = today.getTime() - startDate.getTime();
    const numberOfDayItTakes = Math.floor(timeDiff / (1000 * 3600 * 24));

    // Check if application status code allows updating the submittedDate
    if (
      applicationStatusData.code === 'UNCRN' ||
      applicationStatusData.code === 'CTFD' ||
      applicationStatusData.code === 'CXL'
    ) {
      // update submittedDate to today's date
      await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({
          applicationStatus: applicationStatusData,
          isCountingActive: false,
          numberOfDayItTakes: numberOfDayItTakes,
        })
        .where('"id" = :applicationId', { applicationId: uuidApplication })
        .execute();
    } else {
      // If the status code is one of the restricted ones, only update the application status
      await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({ applicationStatus: applicationStatusData })
        .where('"id" = :applicationId', { applicationId: uuidApplication })
        .execute();
    }

    console.log('Application updated');

    return {
      statusCode: HttpStatus.OK,
      message: 'Application updated',
    };
  }

  // // without submiitedDate
  // async updateApplicationAfterApproval(
  //   applicationId: any,
  //   applicationStatusId: string,
  // ) {
  //   // const inputObjectApplicationStatus = applicationStatusId;
  //   // const uuidApplicationStatus = this.extractUUID(
  //   //   inputObjectApplicationStatus,
  //   // );
  //   // console.log(uuidApplicationStatus);

  //   const inputObject = applicationId;
  //   const uuidApplication = this.extractUUID(inputObject);
  //   console.log(uuidApplication);

  //   // Retrieve application status data
  //   const applicationStatusData =
  //     await this.applicationStatusRepository.findOne({
  //       id: applicationStatusId,
  //     });
  //   // const applicationStatusData =
  //   //   await this.applicationStatusRepository.findOne({
  //   //     id: uuidApplicationStatus,
  //   //   });
  //   // if (!applicationStatusData) {
  //   //   throw new NotFoundException('Status not found');
  //   // }
  //   // console.log(applicationStatusData);

  //   // Retrieve application
  //   const applicationData = await this.applicationRepository.findOne({
  //     id: uuidApplication,
  //   });

  //   if (!applicationData) {
  //     throw new NotFoundException('Application not found');
  //   }
  //   console.log(applicationData);

  //   // Update application status
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ applicationStatus: applicationStatusData })
  //     .where('"id" = :applicationId', { applicationId: uuidApplication })
  //     .returning('*')
  //     .execute();

  //   console.log(applicationUpdate);

  //   return {
  //     statusCode: HttpStatus.OK,
  //     message: 'Application updated',
  //   };
  //   // return applicationUpdate;
  // }

  // for non-objection only
  async updateApplicationAfterApprovalNORHA(
    applicationId: any,
    applicationStatusId: string,
  ) {
    // const inputObjectApplicationStatus = applicationStatusId;
    // const uuidApplicationStatus = this.extractUUID(
    //   inputObjectApplicationStatus,
    // );
    // console.log(uuidApplicationStatus);

    const inputObject = applicationId;
    const uuidApplication = this.extractUUID(inputObject);
    console.log(uuidApplication);

    // Retrieve application status data
    const applicationStatusData =
      await this.applicationStatusRepository.findOne({
        id: applicationStatusId,
      });
    // const applicationStatusData =
    //   await this.applicationStatusRepository.findOne({
    //     id: uuidApplicationStatus,
    //   });
    // if (!applicationStatusData) {
    //   throw new NotFoundException('Status not found');
    // }
    // console.log(applicationStatusData);

    // Retrieve application
    const applicationData = await this.applicationRepository.findOne({
      id: uuidApplication,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }
    console.log(applicationData);

    // Update application status & agency is hardcoded for now
    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      // .set({
      //   applicationStatus: applicationStatusData,
      //   isNonObjection: '1',
      //   agencyId: 'b4c3c778-d9b9-44f5-b13c-4e77e53c3430',
      // })
      .set({
        applicationStatus: applicationStatusData,
        isNonObjection: '1',
        isNonObjectionRequested: new Date(),
      })
      // .set({ applicationStatus: applicationStatusData })
      // .set({ isNonObjection: '1' })
      //Prod RHA agency
      // .set({ agencyId: 'b4c3c778-d9b9-44f5-b13c-4e77e53c3430' })
      //Dev RHA agency
      // .set({ agencyId: 'd9b59b94-bc89-4d53-b2be-8d3376e7414a' })
      .where('"id" = :applicationId', { applicationId: uuidApplication })
      .returning('*')
      .execute();

    console.log(applicationUpdate);

    return {
      statusCode: HttpStatus.OK,
      message: 'Application updated',
    };
    // return applicationUpdate;
  }

  // for non-objection only when returned
  async updateApplicationAfterApprovalRTNNO(
    applicationId: any,
    applicationStatusId: string,
  ) {
    const inputObject = applicationId;
    const uuidApplication = this.extractUUID(inputObject);
    console.log(uuidApplication);

    // Retrieve application status data
    const applicationStatusData =
      await this.applicationStatusRepository.findOne({
        id: applicationStatusId,
      });

    // Retrieve application
    const applicationData = await this.applicationRepository.findOne({
      id: uuidApplication,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }
    console.log(applicationData);

    // Update application status & agency is hardcoded for now
    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({
        applicationStatus: applicationStatusData,
        isNonObjectionReturned: '1',
        isNonObjectionReturnedDate: new Date(),
      })
      .where('"id" = :applicationId', { applicationId: uuidApplication })
      .returning('*')
      .execute();

    console.log(applicationUpdate);

    return {
      statusCode: HttpStatus.OK,
      message: 'Application updated',
    };
  }

  // for non-objection only when returned
  // async updateApplicationNumberOfDaysItTakes(applicationId: any) {
  //   const uuidApplication = this.extractUUID(applicationId);
  //   console.log('Application UUID:', uuidApplication);

  //   const applicationData = await this.applicationRepository.findOne({
  //     id: uuidApplication,
  //   });

  //   if (!applicationData) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   const baseDate =
  //     applicationData.resubmittedDate &&
  //     new Date(applicationData.resubmittedDate) >
  //       new Date(applicationData.submittedDate)
  //       ? applicationData.resubmittedDate
  //       : applicationData.submittedDate;

  //   const today = new Date();
  //   const startDate = new Date(baseDate);
  //   const timeDiff = today.getTime() - startDate.getTime();
  //   const numberOfDayItTakes = Math.floor(timeDiff / (1000 * 3600 * 24));

  //   const result = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ numberOfDayItTakes })
  //     .where('"id" = :applicationId', { applicationId: uuidApplication })
  //     .returning('*')
  //     .execute();

  //   console.log('Application updated with numberOfDayItTakes:', result);

  //   return result; // optional, or just let it finish without return
  // }

  // for update number of days it takes and return void
  async updateApplicationNumberOfDaysItTakes(
    applicationId: any,
  ): Promise<void> {
    const uuidApplication = this.extractUUID(applicationId);
    console.log(
      'Application UUID updateApplicationNumberOfDaysItTakes :',
      uuidApplication,
    );

    const applicationData = await this.applicationRepository.findOne({
      id: uuidApplication,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }

    const baseDate =
      applicationData.resubmittedDate &&
      new Date(applicationData.resubmittedDate) >
        new Date(applicationData.submittedDate)
        ? applicationData.resubmittedDate
        : applicationData.submittedDate;

    const today = new Date();
    const startDate = new Date(baseDate);
    const timeDiff = today.getTime() - startDate.getTime();
    const numberOfDayItTakesToApproval = Math.floor(
      timeDiff / (1000 * 3600 * 24),
    );

    await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({
        numberOfDayItTakes: numberOfDayItTakesToApproval,
      })
      .where('"id" = :applicationId', { applicationId: uuidApplication })
      .execute();

    console.log('numberOfDayItTakes updated for application:', uuidApplication);
  }

  // async updateApplicationAfterReviewerComment(applicationId: any) {
  //   const inputObject = applicationId;
  //   const uuidApplication = this.extractUUID(inputObject);
  //   console.log(uuidApplication);

  //   // Retrieve application status data
  //   const allApplicationStatuses = await this.findAllApplicationStatus();
  //   const applicationStatus = allApplicationStatuses.find(
  //     (status) => status.code === 'ACPD',
  //   );

  //   // Retrieve application
  //   const applicationData = await this.applicationRepository.findOne({
  //     id: uuidApplication,
  //   });

  //   if (!applicationData) {
  //     throw new NotFoundException('Application not found');
  //   }
  //   console.log(applicationData);

  //   // Update application status
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ applicationStatus: applicationStatus })
  //     .where('"id" = :applicationId', { applicationId: uuidApplication })
  //     .returning('*')
  //     .execute();

  //   console.log(applicationUpdate);

  //   return applicationUpdate;
  // }

  async updateApplicationAfterReviewerComment(applicationId: any) {
    console.log('UUID:', applicationId);

    // Retrieve application status data for 'RVW' and 'UNRV'
    const allApplicationStatuses = await this.findAllApplicationStatus();
    const rvwStatusData = allApplicationStatuses.find(
      (status) => status.code === 'RVW',
    );
    const unrvStatusData = allApplicationStatuses.find(
      (status) => status.code === 'UNRV',
    );

    if (!rvwStatusData || !unrvStatusData) {
      throw new NotFoundException(
        'Required application statuses "RVW" or "UNRV" not found',
      );
    }

    console.log('RVW Status Data:', rvwStatusData);
    console.log('UNRV Status Data:', unrvStatusData);

    // Retrieve application
    const applicationData = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }

    console.log('Application Data:', applicationData);

    // Retrieve all reviewers on the application
    const reviewersOnApplication =
      await this.findAllReviewersOnApplicationByApplicationId(applicationId);
    const hasReviewerWithStatusZero = reviewersOnApplication.some(
      (reviewer) => reviewer.status === '0',
    );

    // Determine the application status based on reviewers' statuses
    const applicationStatusData = hasReviewerWithStatusZero
      ? unrvStatusData
      : rvwStatusData;

    // Update application status
    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({ applicationStatus: applicationStatusData })
      .where('id = :applicationId', { applicationId: applicationId })
      .execute();

    console.log('Update Result:', applicationUpdate);

    return applicationUpdate;
  }

  // async updateApplicationAfterReviewerComment(applicationId: any) {
  //   console.log('UUID:', applicationId);

  //   // Retrieve application status data
  //   const allApplicationStatuses = await this.findAllApplicationStatus();
  //   const applicationStatusData = allApplicationStatuses.find(
  //     (status) => status.code === 'RVW',
  //   );

  //   if (!applicationStatusData) {
  //     throw new NotFoundException('Application status "RVW" not found');
  //   }

  //   console.log('Application Status Data:', applicationStatusData);

  //   // Retrieve application
  //   const applicationData = await this.applicationRepository.findOne({
  //     id: applicationId,
  //   });

  //   if (!applicationData) {
  //     throw new NotFoundException('Application not found');
  //   }

  //   console.log('Application Data:', applicationData);

  //   // Update application status
  //   const applicationUpdate = await this.applicationEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Application)
  //     .set({ applicationStatus: applicationStatusData })
  //     .where('id = :applicationId', { applicationId: applicationId })
  //     .execute();

  //   console.log('Update Result:', applicationUpdate);

  //   return applicationUpdate;
  // }

  async updateApplicationAfterReviewerCommentRHA(applicationId: any) {
    console.log('UUID:', applicationId);

    // Retrieve application status data
    const allApplicationStatuses = await this.findAllApplicationStatus();
    const applicationStatusData = allApplicationStatuses.find(
      (status) => status.code === 'NORVW',
    );

    if (!applicationStatusData) {
      throw new NotFoundException('Application status "NORVW" not found');
    }

    console.log('Application Status Data NORVW:', applicationStatusData);

    // Retrieve application
    const applicationData = await this.applicationRepository.findOne({
      id: applicationId,
    });

    if (!applicationData) {
      throw new NotFoundException('Application not found');
    }

    console.log('Application Data:', applicationData);

    // Update application status
    const applicationUpdate = await this.applicationEntityManagerRepository
      .createQueryBuilder()
      .update(Application)
      .set({ applicationStatus: applicationStatusData })
      .where('id = :applicationId', { applicationId: applicationId })
      .execute();

    console.log('Update Result:', applicationUpdate);

    return applicationUpdate;
  }

  extractUUID(input: { id: string }): string {
    return input.id;
  }

  // setting
  // QuestionCategory
  async createQuestionCategory(questionCategoryDto: QuestionCategoryDto) {
    const questionCategory = new QuestionCategory({
      ...questionCategoryDto,
    });

    return this.questionCategoryRepository.create(questionCategory);
  }

  async findAllQuestionCategorys() {
    return this.questionCategoryRepository.find({});
  }

  async findOneQuestionCategory(id: string) {
    return this.questionCategoryRepository.findOne({ id });
  }

  async updateQuestionCategory(
    id: string,
    questionCategoryDto: QuestionCategoryDto,
  ) {
    return this.questionCategoryRepository.findOneAndUpdate(
      { id },
      questionCategoryDto,
    );
  }

  async removeQuestionCategory(id: string) {
    return this.questionCategoryRepository.findOneAndDelete({ id });
  }

  // // ConstructionMethod
  // async createConstructionMethod(constructionMethodDto: ConstructionMethodDto) {
  //   const constructionMethod = new ConstructionMethod({
  //     ...constructionMethodDto,
  //   });

  //   return this.constructionMethodRepository.create(constructionMethod);
  // }

  // async findAllConstructionMethods() {
  //   return this.constructionMethodRepository.find({});
  // }

  // async findOneConstructionMethod(id: string) {
  //   return this.equipmentCapacityRepository.findOne({ id });
  // }

  // async updateConstructionMethod(
  //   id: string,
  //   constructionMethodDto: ConstructionMethodDto,
  // ) {
  //   return this.constructionMethodRepository.findOneAndUpdate(
  //     { id },
  //     constructionMethodDto,
  //   );
  // }

  // async removeConstructionMethod(id: string) {
  //   return this.constructionMethodRepository.findOneAndDelete({ id });
  // }

  // // EquipmentCapacity
  // async createEquipmentCapacity(equipmentCapacityDto: EquipmentCapacityDto) {
  //   const equipmentCapacity = new EquipmentCapacity({
  //     ...equipmentCapacityDto,
  //   });

  //   return this.equipmentCapacityRepository.create(equipmentCapacity);
  // }

  // async findAllEquipmentCapacitys() {
  //   return this.equipmentCapacityRepository.find({});
  // }

  // async findOneEquipmentCapacity(id: string) {
  //   return this.equipmentCapacityRepository.findOne({ id });
  // }

  // async updateEquipmentCapacity(
  //   id: string,
  //   equipmentCapacityDto: EquipmentCapacityDto,
  // ) {
  //   return this.equipmentCapacityRepository.findOneAndUpdate(
  //     { id },
  //     equipmentCapacityDto,
  //   );
  // }

  // async removeEquipmentCapacity(id: string) {
  //   return this.equipmentCapacityRepository.findOneAndDelete({ id });
  // }

  // other info application
  async createOtherInfoApplication(
    otherInfoApplicationDto: OtherInfoApplicationDto,
  ) {
    const dataFromDb = await this.permitTypesRepository.findOne({
      id: otherInfoApplicationDto.permitTypeId,
    });
    if (!dataFromDb)
      throw new HttpException('Permit Type Not Found', HttpStatus.BAD_REQUEST);

    const dataFromDb2 = await this.applicationRepository.findOne({
      id: otherInfoApplicationDto.applicationId,
    });
    if (!dataFromDb2)
      throw new HttpException('Application Not Found', HttpStatus.BAD_REQUEST);

    const otherInfoApplication = new OtherInfoApplication({
      ...otherInfoApplicationDto,
      permitTypes: (otherInfoApplicationDto.permitTypeId = {
        id: dataFromDb.id,
      } as any),
      applications: (otherInfoApplicationDto.applicationId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.otherInfoApplicationRepository.create(otherInfoApplication);
  }

  async findAllOtherInfoApplication() {
    return this.otherInfoApplicationRepository.findAll({
      relations: {
        permitTypes: true,
        applications: true,
      },
    });
  }

  async findOneOtherInfoApplication(id: string) {
    return this.otherInfoApplicationRepository.findOne({ id });
  }

  async updateOtherInfoApplication(
    id: string,
    otherInfoApplicationDto: OtherInfoApplicationDto,
  ) {
    return this.otherInfoApplicationRepository.findOneAndUpdate(
      { id },
      otherInfoApplicationDto,
    );
  }

  async removeOtherInfoApplication(id: string) {
    return this.otherInfoApplicationRepository.findOneAndDelete({ id });
  }

  // other info application
  async createPermitQuestion(permitQuestionDto: PermitQuestionDto) {
    const dataFromDb = await this.permitTypesRepository.findOne({
      id: permitQuestionDto.permitTypeId,
    });
    if (!dataFromDb)
      throw new HttpException('Permit Type Not Found', HttpStatus.BAD_REQUEST);

    const dataFromDb2 = await this.questionCategoryRepository.findOne({
      id: permitQuestionDto.questionCategoryId,
    });
    if (!dataFromDb2)
      throw new HttpException(
        'Question Category Not Found',
        HttpStatus.BAD_REQUEST,
      );

    const permitQuestion = new PermitQuestion({
      ...permitQuestionDto,
      permitTypes: (permitQuestionDto.permitTypeId = {
        id: dataFromDb.id,
      } as any),
      questionCategorys: (permitQuestionDto.questionCategoryId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.permitQuestionRepository.create(permitQuestion);
  }

  async findAllPermitQuestion() {
    return this.permitQuestionRepository.findAll({
      relations: {
        permitTypes: true,
        questionCategorys: true,
        answers: true,
      },
    });
  }

  async findOnePermitQuestion(id: string) {
    return this.permitQuestionRepository.findOne({ id });
  }

  async findAllPermitQuestionByPermitType(
    permitTypeId: string,
  ): Promise<PermitQuestion[]> {
    return this.permitQuestionEntityManagerRepository
      .createQueryBuilder('permitQuestions')
      .where('permitQuestions.permitTypes = :permitTypeId', {
        permitTypeId,
      })
      .leftJoinAndSelect('permitQuestions.permitTypes', 'permitTypes')
      .leftJoinAndSelect(
        'permitQuestions.questionCategorys',
        'questionCategories',
      )
      .getMany();
  }

  async updatePermitQuestion(id: string, permitQuestionDto: PermitQuestionDto) {
    return this.permitQuestionRepository.findOneAndUpdate(
      { id },
      permitQuestionDto,
    );
  }

  async removePermitQuestion(id: string) {
    return this.permitQuestionRepository.findOneAndDelete({ id });
  }

  // Answer
  async createAnswer(answerDto: AnswerDto) {
    const dataFromDb = await this.permitQuestionRepository.findOne({
      id: answerDto.permitQuestionId,
    });
    if (!dataFromDb)
      throw new HttpException('Question Not Found', HttpStatus.BAD_REQUEST);

    const dataFromDb2 = await this.applicationRepository.findOne({
      id: answerDto.applicationId,
    });
    if (!dataFromDb2)
      throw new HttpException('Application Not Found', HttpStatus.BAD_REQUEST);

    const answer = new Answer({
      ...answerDto,
      permitQuestions: (answerDto.permitQuestionId = {
        id: dataFromDb.id,
      } as any),
      applications: (answerDto.applicationId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.answerRepository.create(answer);
  }

  async findAllAnswer() {
    return this.answerRepository.findAll({
      relations: {
        permitQuestions: true,
        applications: true,
      },
    });
  }
  async findOneAnswer(id: string) {
    return this.answerRepository.findOne({ id });
  }

  async updateAnswer(id: string, answerDto: AnswerDto) {
    return this.answerRepository.findOneAndUpdate({ id }, answerDto);
  }

  async removeAnswer(id: string) {
    return this.answerRepository.findOneAndDelete({ id });
  }

  // reviewersOnApplication
  async createReviewersOnApplication(
    reviewersOnApplicationDto: ReviewersOnApplicationDto,
  ) {
    const dataFromDb2 = await this.applicationRepository.findOne({
      id: reviewersOnApplicationDto.applicationId,
    });
    if (!dataFromDb2)
      throw new HttpException('Application Not Found', HttpStatus.BAD_REQUEST);

    const reviewersOnApplication = new ReviewersOnApplication({
      ...reviewersOnApplicationDto,
      applications: (reviewersOnApplicationDto.applicationId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.reviewersOnApplicationRepository.create(reviewersOnApplication);
  }

  async findAllReviewersOnApplication() {
    return this.reviewersOnApplicationRepository.findAll({
      relations: {
        applications: true,
      },
    });
  }
  async findOneReviewersOnApplication(id: string) {
    return this.reviewersOnApplicationRepository.findOne({ id });
  }

  async findAllReviewersOnApplicationByApplicationId(applicationId: string) {
    return await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewersOnApplication')
      .where('reviewersOnApplication.applications = :applicationId', {
        applicationId,
      })
      .getMany();
  }

  // async findAllReviewersOnApplicationByApplicationId(applicationId: string) {
  //   return await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .leftJoinAndSelect('reviewers.applications', 'application')
  //     .where('application.id = :id', { applicationId })
  //     .getMany();
  // }

  async findAllReviewersOnApplicationByApplicationId2(applicationId: string) {
    const reviewers = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewersOnApplication')
      .where('"reviewersOnApplication"."applicationsId" = :applicationId', {
        applicationId,
      })

      .getMany();

    if (reviewers.length === 0) {
      throw new NotFoundException('No reviewers found for this application.');
    }
    return reviewers;
  }
  // async findAllReviewersOnApplicationByApplicationId2(applicationId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewersOnApplication')
  //     .where('reviewersOnApplication.applications = :applicationId', {
  //       applicationId,
  //     })
  //     .getMany();

  //   if (reviewers.length === 0) {
  //     throw new NotFoundException('No reviewers found for this application.');
  //   }
  //   return reviewers;
  // }

  // async findOneReviewersOnApplicationByApplicationId(applicationId: string) {
  //   return await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .where('reviewers.applications = :applicationId', {
  //       applicationId,
  //     })
  //     .getOne();
  // }

  async findOneReviewersOnApplicationByApplicationId(applicationId: string) {
    return await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .leftJoinAndSelect('reviewers.applications', 'applications')
      .where('"reviewers"."applicationsId" = :applicationId', { applicationId })
      .getOne();
  }

  // async findOneReviewersOnApplicationByUserId(userASId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userASId })
  //     .getMany();

  //   if (reviewers.length === 0) {
  //     return [];
  //   }

  //   return reviewers;
  // }

  async findReviewersOnApplicationByUserId(userId: string) {
    const reviewers = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('reviewers.userId = :userId', { userId })
      .getMany();

    if (reviewers.length === 0) {
      return [];
    }

    return reviewers;
  }

  // count where status is 0 and applciation is under reviewer
  async countApplicationOfReviewer(
    userId: string,
  ): Promise<{ number: number }> {
    const count = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('reviewers.userId = :userId', { userId })
      .leftJoinAndSelect('reviewers.applications', 'applications')
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .andWhere('applicationStatus.code = :code', { code: 'UNRV' })
      .andWhere('reviewers.status = :status', { status: 0 })
      .getCount();

    return { number: count };
  }

  // async countApplicationOfReviewer(
  //   userId: string,
  // ): Promise<{ number: number }> {
  //   const count = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .andWhere('reviewers.status = :status', { status: 0 })
  //     .getCount();

  //   return { number: count };
  // }

  // with pagination
  async getApplicationsOfReviewer(
    userId: string,
    page: number = 1,
    limit: number = 100,
  ) {
    const skip = (page - 1) * limit;

    const [reviewers, total] = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('reviewers.userId = :userId', { userId })
      .andWhere('reviewers.status = :status', { status: 0 })
      .leftJoinAndSelect('reviewers.applications', 'applications')
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .leftJoinAndSelect('applications.invoices', 'invoices')
      .leftJoinAndSelect('applications.certificates', 'certificates')
      .leftJoinAndSelect('applications.projects', 'projects')
      .andWhere('applicationStatus.code = :code', { code: 'UNRV' })
      .orderBy('applications.submittedDate', 'ASC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const applications = reviewers.flatMap((reviewer) => {
      const apps = Array.isArray(reviewer.applications)
        ? reviewer.applications
        : [reviewer.applications];

      return apps.map((application) => ({
        id: application.id,
        upi: application.upi,
        isLocked: application.isLocked,
        isResubmitted: application.isResubmitted,
        combiningPlotSize: application.combiningPlotSize,
        isNonObjection: application.isNonObjection,
        isInspected: application.isInspected,
        isAssociatedUpi: application.isAssociatedUpi,
        isEIAVerified: application.isEIAVerified,
        buildUpArea: application.buildUpArea,
        numberOfFloor: application.numberOfFloor,
        grossFloorArea: application.grossFloorArea,
        numberOfParkingSpace: application.numberOfParkingSpace,
        priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
        capacityInformation: application.capacityInformation,
        numberOfDwellingUnits: application.numberOfDwellingUnits,
        DescriptionOfOperation: application.DescriptionOfOperation,
        percentageSpaceUse: application.percentageSpaceUse,
        waterConsumption: application.waterConsumption,
        electricityConsumption: application.electricityConsumption,
        DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
        ProjectCostInUSD: application.ProjectCostInUSD,
        ProjectCostInRwf: application.ProjectCostInRwf,
        agencyId: application.agencyId,
        userId: application.userId,
        submittedByUserId: application.submittedByUserId,
        certificateNumberEIA: application.certificateNumberEIA,
        applicationName: application.applicationName,
        permitTypeCode: application.permitTypes?.code,
        agencyCode: application.agencyCode,
        created_at: application.created_at,
        updated_at: application.updated_at,
        assignUsersFoReview: application.assignUsersFoReview,
        other: application.other,
        projects: application.projects,
        permitTypes: application.permitTypes,
        applicationStatus: application.applicationStatus,
        buildTypes: application.buildTypes,
        categoryTypes: application.categoryTypes,
      }));
    });

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async getApplicationsOfReviewer(userId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .andWhere('reviewers.status = :status', { status: 0 })
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .andWhere('applicationStatus.code = :code', { code: 'UNRV' })
  //     .orderBy('applications.submittedDate')
  //     .getMany();

  //   // Check if applications is an array, if not, wrap it as an array
  //   const applications = reviewers.flatMap((reviewer) => {
  //     const applications = Array.isArray(reviewer.applications)
  //       ? reviewer.applications
  //       : [reviewer.applications]; // Wrap single object in an array

  //     return applications.map((application) => ({
  //       id: application.id,
  //       upi: application.upi,
  //       isLocked: application.isLocked,
  //       isResubmitted: application.isResubmitted,
  //       combiningPlotSize: application.combiningPlotSize,
  //       isNonObjection: application.isNonObjection,
  //       isInspected: application.isInspected,
  //       isAssociatedUpi: application.isAssociatedUpi,
  //       isEIAVerified: application.isEIAVerified,
  //       buildUpArea: application.buildUpArea,
  //       numberOfFloor: application.numberOfFloor,
  //       grossFloorArea: application.grossFloorArea,
  //       numberOfParkingSpace: application.numberOfParkingSpace,
  //       priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
  //       capacityInformation: application.capacityInformation,
  //       numberOfDwellingUnits: application.numberOfDwellingUnits,
  //       DescriptionOfOperation: application.DescriptionOfOperation,
  //       percentageSpaceUse: application.percentageSpaceUse,
  //       waterConsumption: application.waterConsumption,
  //       electricityConsumption: application.electricityConsumption,
  //       DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
  //       ProjectCostInUSD: application.ProjectCostInUSD,
  //       ProjectCostInRwf: application.ProjectCostInRwf,
  //       agencyId: application.agencyId,
  //       userId: application.userId,
  //       submittedByUserId: application.submittedByUserId,
  //       certificateNumberEIA: application.certificateNumberEIA,
  //       applicationName: application.applicationName,
  //       permitTypeCode: application.permitTypes?.code,
  //       agencyCode: application.agencyCode,
  //       created_at: application.created_at,
  //       updated_at: application.updated_at,
  //       assignUsersFoReview: application.assignUsersFoReview,
  //       other: application.other,
  //       projects: application.projects,
  //       permitTypes: application.permitTypes,
  //       applicationStatus: application.applicationStatus,
  //       buildTypes: application.buildTypes,
  //       categoryTypes: application.categoryTypes,
  //     }));
  //   });

  //   return applications;
  // }

  // async getAllApplicationsOfReviewer(userId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .getMany();

  //   // Check if applications is an array, if not, wrap it as an array
  //   const applications = reviewers.flatMap((reviewer) => {
  //     const applications = Array.isArray(reviewer.applications)
  //       ? reviewer.applications
  //       : [reviewer.applications]; // Wrap single object in an array

  //     return applications.map((application) => ({
  //       id: application.id,
  //       upi: application.upi,
  //       isLocked: application.isLocked,
  //       combiningPlotSize: application.combiningPlotSize,
  //       isNonObjection: application.isNonObjection,
  //       isInspected: application.isInspected,
  //       isAssociatedUpi: application.isAssociatedUpi,
  //       isEIAVerified: application.isEIAVerified,
  //       buildUpArea: application.buildUpArea,
  //       numberOfFloor: application.numberOfFloor,
  //       grossFloorArea: application.grossFloorArea,
  //       numberOfParkingSpace: application.numberOfParkingSpace,
  //       priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
  //       capacityInformation: application.capacityInformation,
  //       numberOfDwellingUnits: application.numberOfDwellingUnits,
  //       DescriptionOfOperation: application.DescriptionOfOperation,
  //       percentageSpaceUse: application.percentageSpaceUse,
  //       waterConsumption: application.waterConsumption,
  //       electricityConsumption: application.electricityConsumption,
  //       DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
  //       ProjectCostInUSD: application.ProjectCostInUSD,
  //       ProjectCostInRwf: application.ProjectCostInRwf,
  //       agencyId: application.agencyId,
  //       userId: application.userId,
  //       submittedByUserId: application.submittedByUserId,
  //       certificateNumberEIA: application.certificateNumberEIA,
  //       applicationName: application.applicationName,
  //       permitTypeCode: application.permitTypes?.code,
  //       agencyCode: application.agencyCode,
  //       created_at: application.created_at,
  //       updated_at: application.updated_at,
  //       assignUsersFoReview: application.assignUsersFoReview,
  //       other: application.other,
  //       projects: application.projects,
  //       permitTypes: application.permitTypes,
  //       applicationStatus: application.applicationStatus,
  //       buildTypes: application.buildTypes,
  //       categoryTypes: application.categoryTypes,
  //     }));
  //   });

  //   return applications;
  // }

  // async getAllApplicationsOfReviewer(userId: string) {
  //   const reviewers = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .andWhere('reviewers.status = :status', { status: '0' })
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .andWhere(
  //       '(applicationStatus.code = :code1 OR applicationStatus.code = :code2)',
  //       {
  //         code1: 'UNRV',
  //         code2: 'NOUNRV',
  //       },
  //     )
  //     .orderBy('applications.updated_at')
  //     .getMany();

  //   // .orderBy('application.submittedDate')

  //   // Check if applications is an array, if not, wrap it as an array
  //   const applications = reviewers.flatMap((reviewer) => {
  //     const applications = Array.isArray(reviewer.applications)
  //       ? reviewer.applications
  //       : [reviewer.applications]; // Wrap single object in an array

  //     return applications.map((application) => ({
  //       id: application.id,
  //       upi: application.upi,
  //       isLocked: application.isLocked,
  //       isResubmitted: application.isResubmitted,
  //       combiningPlotSize: application.combiningPlotSize,
  //       isNonObjection: application.isNonObjection,
  //       isInspected: application.isInspected,
  //       isAssociatedUpi: application.isAssociatedUpi,
  //       isEIAVerified: application.isEIAVerified,
  //       buildUpArea: application.buildUpArea,
  //       numberOfFloor: application.numberOfFloor,
  //       grossFloorArea: application.grossFloorArea,
  //       numberOfParkingSpace: application.numberOfParkingSpace,
  //       priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
  //       capacityInformation: application.capacityInformation,
  //       numberOfDwellingUnits: application.numberOfDwellingUnits,
  //       DescriptionOfOperation: application.DescriptionOfOperation,
  //       percentageSpaceUse: application.percentageSpaceUse,
  //       waterConsumption: application.waterConsumption,
  //       electricityConsumption: application.electricityConsumption,
  //       DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
  //       ProjectCostInUSD: application.ProjectCostInUSD,
  //       ProjectCostInRwf: application.ProjectCostInRwf,
  //       agencyId: application.agencyId,
  //       userId: application.userId,
  //       submittedByUserId: application.submittedByUserId,
  //       certificateNumberEIA: application.certificateNumberEIA,
  //       applicationName: application.applicationName,
  //       permitTypeCode: application.permitTypes?.code,
  //       agencyCode: application.agencyCode,
  //       created_at: application.created_at,
  //       updated_at: application.updated_at,
  //       assignUsersFoReview: application.assignUsersFoReview,
  //       other: application.other,
  //       projects: application.projects,
  //       permitTypes: application.permitTypes,
  //       applicationStatus: application.applicationStatus,
  //       buildTypes: application.buildTypes,
  //       categoryTypes: application.categoryTypes,
  //     }));
  //   });

  //   return applications;
  // }

  // with pagination
  async getAllApplicationsOfReviewer(
    userId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const [reviewers, total] = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewers')
      .where('reviewers.userId = :userId', { userId })
      .andWhere('reviewers.status = :status', { status: '0' })
      .leftJoinAndSelect('reviewers.applications', 'applications')
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
      .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
      .leftJoinAndSelect('applications.invoices', 'invoices')
      .leftJoinAndSelect('applications.certificates', 'certificates')
      .leftJoinAndSelect('applications.projects', 'projects')
      .andWhere(
        '(applicationStatus.code = :code1 OR applicationStatus.code = :code2)',
        { code1: 'UNRV', code2: 'NOUNRV' },
      )
      .orderBy('applications.updated_at', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    const applications = reviewers.flatMap((reviewer) => {
      const apps = Array.isArray(reviewer.applications)
        ? reviewer.applications
        : [reviewer.applications];

      return apps.map((application) => ({
        id: application.id,
        upi: application.upi,
        isLocked: application.isLocked,
        isResubmitted: application.isResubmitted,
        combiningPlotSize: application.combiningPlotSize,
        isNonObjection: application.isNonObjection,
        isInspected: application.isInspected,
        isAssociatedUpi: application.isAssociatedUpi,
        isEIAVerified: application.isEIAVerified,
        buildUpArea: application.buildUpArea,
        numberOfFloor: application.numberOfFloor,
        grossFloorArea: application.grossFloorArea,
        numberOfParkingSpace: application.numberOfParkingSpace,
        priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
        capacityInformation: application.capacityInformation,
        numberOfDwellingUnits: application.numberOfDwellingUnits,
        DescriptionOfOperation: application.DescriptionOfOperation,
        percentageSpaceUse: application.percentageSpaceUse,
        waterConsumption: application.waterConsumption,
        electricityConsumption: application.electricityConsumption,
        DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
        ProjectCostInUSD: application.ProjectCostInUSD,
        ProjectCostInRwf: application.ProjectCostInRwf,
        agencyId: application.agencyId,
        userId: application.userId,
        submittedByUserId: application.submittedByUserId,
        certificateNumberEIA: application.certificateNumberEIA,
        applicationName: application.applicationName,
        permitTypeCode: application.permitTypes?.code,
        agencyCode: application.agencyCode,
        created_at: application.created_at,
        updated_at: application.updated_at,
        assignUsersFoReview: application.assignUsersFoReview,
        other: application.other,
        projects: application.projects,
        permitTypes: application.permitTypes,
        applicationStatus: application.applicationStatus,
        buildTypes: application.buildTypes,
        categoryTypes: application.categoryTypes,
      }));
    });

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // // without modification
  // async getApplicationsOfReviewer(userId: string) {
  //   const applications = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewers')
  //     .where('reviewers.userId = :userId', { userId })
  //     .andWhere('reviewers.status = :status', { status: 0 })
  //     .leftJoinAndSelect('reviewers.applications', 'applications')
  //     .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('applications.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('applications.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('applications.invoices', 'invoices')
  //     .leftJoinAndSelect('applications.certificates', 'certificates')
  //     .leftJoinAndSelect('applications.projects', 'projects')
  //     .getMany();
  //   return applications;
  // }

  async updateReviewersOnApplication(
    id: string,
    reviewersOnApplicationDto: ReviewersOnApplicationDto,
  ) {
    return this.reviewersOnApplicationRepository.findOneAndUpdate(
      { id },
      reviewersOnApplicationDto,
    );
  }

  // Updating ReviewersOnApplication
  // async updateReviewersOnApplicationStatus(applicationId: string) {
  //   const reviewer = await this.reviewersOnApplicationEntityRepository
  //     .createQueryBuilder('reviewersOnApplication')
  //     .where('reviewersOnApplication.applicationId = :applicationId', {
  //       applicationId,
  //     })
  //     .getOne();

  //   if (!reviewer) {
  //     throw new Error('Reviewer not found for the given applicant ID');
  //   }

  //   reviewer.status = '1';
  //   return await this.reviewersOnApplicationEntityRepository.save(reviewer);
  // }
  async updateReviewersOnApplicationStatusSpecificUser(
    applicationId: string,
    userId: string,
  ) {
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewer')
      .where('reviewer.applications = :applicationId', { applicationId })
      .andWhere('reviewer.userId = :userId', { userId })
      .getOne();

    if (!reviewer) {
      throw new HttpException(
        'Reviewer not found for the given application ID and user ID',
        HttpStatus.BAD_REQUEST,
      );
    }

    reviewer.status = '1';
    return await this.reviewersOnApplicationEntityRepository.save(reviewer);
  }

  // Find reviewe on a specific application
  async findTheReviewersOnSpecificApplication(
    applicationId: string,
    userId: string,
  ) {
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewer')
      .where('reviewer.applications = :applicationId', { applicationId })
      .andWhere('reviewer.userId = :userId', { userId })
      .getOne();

    if (!reviewer) {
      throw new HttpException(
        'Reviewer not found for the given application ID and user ID',
        HttpStatus.BAD_REQUEST,
      );
    }
    return await reviewer;
  }

  async updateReviewersOnApplicationStatus(applicationId: string) {
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewer')
      .where('reviewer.applications = :applicationId', { applicationId })
      .getOne();

    if (!reviewer) {
      throw new HttpException(
        'Reviewer not found for the given application ID and user ID',
        HttpStatus.BAD_REQUEST,
      );
    }

    reviewer.status = '1';
    return await this.reviewersOnApplicationEntityRepository.save(reviewer);
  }

  // async removeReviewersOnApplication(id: string) {
  //   return this.reviewersOnApplicationRepository.findOneAndDelete({ id });
  // }
  async removeReviewersOnApplication(id: string) {
    const reviewer = await this.reviewersOnApplicationRepository.findOne({
      id,
    });

    if (!reviewer) {
      throw new HttpException(
        'Reviewer not found for the given application ID and user ID',
        HttpStatus.BAD_REQUEST,
      );
    }

    reviewer.status = '3';

    return this.reviewersOnApplicationRepository.create(reviewer);
  }

  //Chat dashboard
  // async generateCharts(createChartDto: CreateChartDto) {
  //   // Set default year to the current year if not provided
  //   const year = createChartDto.year || new Date().getFullYear();
  //   const agencyId = createChartDto.agencyId;

  //   try {
  //     // Fetch applications based on the presence of agencyId
  //     const applications = agencyId
  //       ? await this.findAllApplicationsWithDetailsByAgency(agencyId)
  //       : await this.findAllApplications();

  //     console.log('Total Applications Fetched:', applications.length);

  //     // Filter applications by the specified or default year
  //     const filteredApplications = applications.filter(
  //       (app) => new Date(app.created_at).getFullYear() === year,
  //     );

  //     console.log(
  //       'Applications After Year Filter:',
  //       filteredApplications.length,
  //     );

  //     // Prepare data for bar and pie charts
  //     const barChartData = this.prepareBarChartData(filteredApplications);
  //     console.log('Bar Chart Data:', barChartData);

  //     const pieChartData = await this.preparePieChartData(filteredApplications);
  //     console.log('Pie Chart Data:', pieChartData);

  //     return {
  //       barChartData,
  //       pieChartData,
  //     };
  //   } catch (error) {
  //     console.error('Error in generateCharts:', error);
  //     throw new HttpException(
  //       'Error generating charts',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
  async generateCharts(dataChartDto: DataForChartDto) {
    // ✅ Log what the DTO received
    console.log(`Received DTO:`, dataChartDto);

    // ✅ Use the year directly from DTO, ensuring it's a number
    const year = Number(dataChartDto.year);
    const agencyId = dataChartDto.agencyId;

    if (isNaN(year) || year < 0) {
      console.error(`Invalid year provided: ${dataChartDto.year}`);
      throw new HttpException('Invalid year provided', HttpStatus.BAD_REQUEST);
    }

    console.log(
      `Fetching applications for agencyId: ${agencyId || 'All'} and year: ${year}`,
    );

    let applications = [];
    try {
      if (agencyId) {
        applications =
          await this.findAllApplicationsWithDetailsByAgencyForCharts(agencyId);
        // await this.findAllApplicationsWithDetailsByAgency(agencyId);
      } else {
        applications = await this.findAllApplicationsForCharts();
        // applications = await this.findAllApplications();
      }

      console.log(
        `Total Applications Before Filtering: ${applications.length}`,
      );
      console.log(`Filtering applications for the year: ${year}`);

      // 🔍 Debugging: Log each application's year before filtering
      applications.forEach((app) => {
        const appYear = new Date(app.created_at).getFullYear();
        console.log(`Application ID: ${app.id}, Created Year: ${appYear}`);
      });

      // ✅ Correctly filter by the provided year
      applications = applications.filter((app) => {
        const appYear = new Date(app.created_at).getFullYear();
        return appYear === year;
      });

      console.log(`Total Applications After Filtering: ${applications.length}`);

      if (applications.length === 0) {
        console.warn(`No applications found for year ${year}`);
        return { message: `No data found for the specified year: ${year}.` };
      }

      const barChartData = this.prepareBarChartData(applications);
      const pieChartData = await this.preparePieChartData(applications);

      return {
        barChartData,
        pieChartData,
      };
    } catch (error) {
      console.error(`Error fetching data:`, error);
      throw new HttpException(
        'Error generating charts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // async generateCharts(createChartDto: CreateChartDto) {
  //   // Extract year and agencyId from the request body
  //   const year = createChartDto.year || new Date().getFullYear();
  //   const agencyId = createChartDto.agencyId;

  //   let applications;

  //   // Fetch applications based on the presence of agencyId
  //   if (agencyId) {
  //     console.log(`Fetching applications for agencyId: ${agencyId}`);
  //     try {
  //       applications =
  //         await this.findAllApplicationsWithDetailsByAgency(agencyId);
  //     } catch (error) {
  //       console.error(`Error fetching data for agencyId: ${agencyId}`, error);
  //       return {
  //         message: `Error fetching data for agency ID: ${agencyId}. Please check the ID and try again.`,
  //       };
  //     }

  //     // Check if any applications were found for the given agencyId
  //     if (!applications || applications.length === 0) {
  //       console.warn(`No data found for agencyId: ${agencyId}`);
  //       return {
  //         message: `No data found for the specified agency ID: ${agencyId}. Please check if the agency ID is correct.`,
  //       };
  //     }
  //   } else {
  //     console.log('Fetching all applications');
  //     applications = await this.findAllApplications();
  //   }

  //   console.log(`Total Applications Fetched: ${applications.length}`);

  //   // Filter applications by the specified or default year
  //   const filteredApplications = applications.filter(
  //     (app) => new Date(app.created_at).getFullYear() === year,
  //   );

  //   // Check if any applications were found for the given year
  //   if (!filteredApplications || filteredApplications.length === 0) {
  //     console.warn(`No data found for the year: ${year}`);
  //     return {
  //       message: `No data found for the specified year: ${year}.`,
  //     };
  //   }

  //   console.log(
  //     `Applications After Year Filter: ${filteredApplications.length}`,
  //   );

  //   // Prepare data for bar and pie charts
  //   const barChartData = this.prepareBarChartData(filteredApplications);
  //   const pieChartData = await this.preparePieChartData(filteredApplications);

  //   return {
  //     barChartData,
  //     pieChartData,
  //   };
  // }

  private prepareBarChartData(applications: any[]) {
    const permitCounts = {};

    // Count applications by permit type
    applications.forEach((app) => {
      // Handle the case where permitTypes is a single object rather than an array
      if (Array.isArray(app.permitTypes)) {
        // If permitTypes is an array
        app.permitTypes.forEach((permit) => {
          const permitName = permit.name;
          if (!permitCounts[permitName]) {
            permitCounts[permitName] = 0;
          }
          permitCounts[permitName]++;
        });
      } else if (app.permitTypes && typeof app.permitTypes === 'object') {
        // If permitTypes is a single object
        const permitName = app.permitTypes.name;
        if (!permitCounts[permitName]) {
          permitCounts[permitName] = 0;
        }
        permitCounts[permitName]++;
      } else {
        console.warn(
          'Permit types is not an array or object:',
          app.permitTypes,
        );
      }
    });

    // Convert counts to the format for the bar chart
    const barChartData = Object.keys(permitCounts).map((permitName) => ({
      category: permitName,
      value: permitCounts[permitName],
    }));

    console.log('Prepared Bar Chart Data:', barChartData);

    return barChartData;
  }

  private async preparePieChartData(applications: any[]) {
    let maleCount = 0;
    let femaleCount = 0;

    for (const app of applications) {
      try {
        const userDetail = await this.checkUserData(app.userId);
        if (userDetail && userDetail.gender) {
          if (userDetail.gender.toLowerCase() === 'male') {
            maleCount++;
          } else if (userDetail.gender.toLowerCase() === 'female') {
            femaleCount++;
          }
        } else {
          console.warn('Missing gender in user data for application:', app);
        }
      } catch (error) {
        console.error(
          'Error fetching user data for userId:',
          app.userId,
          error,
        );
      }
    }

    // Calculate percentages
    const total = maleCount + femaleCount;
    const malePercentage = total > 0 ? (maleCount / total) * 100 : 0;
    const femalePercentage = total > 0 ? (femaleCount / total) * 100 : 0;

    return {
      series: [
        {
          name: 'Percentage',
          colorByPoint: true,
          data: [
            { name: 'Male', y: malePercentage },
            { name: 'Female', y: femalePercentage },
          ],
        },
      ],
    };
  }

  // // Occupancy Inspection
  // async createOccupancyInspection(
  //   occupancyInspectionDto: OccupancyInspectionDto,
  // ) {
  //   const dataFromDb = await this.applicationRepository.findOne({
  //     id: occupancyInspectionDto.applicationId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Application Not Found', HttpStatus.BAD_REQUEST);

  //   const occupancyInspection = new OccupancyInspection({
  //     ...occupancyInspectionDto,
  //     application: (occupancyInspectionDto.applicationId = {
  //       id: dataFromDb.id,
  //     } as any),
  //   });
  //   return this.occupancyInspectionRepository.create(occupancyInspection);
  // }

  // Occupancy Inspection with a file plus change the application status
  async createOccupancyInspection(
    occupancyInspectionDto: OccupancyInspectionDto,
    file: Express.Multer.File,
  ) {
    try {
      let fileBase64: string = null;

      // Convert file to base64 if provided
      if (file) {
        fileBase64 = file.buffer.toString('base64');
      }

      // Check if application exists
      const dataFromDb = await this.applicationRepository.findOne({
        id: occupancyInspectionDto.applicationId,
      });
      if (!dataFromDb) {
        throw new HttpException(
          'Application Not Found',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Get the list of reviewers for the application
      const reviewers = await this.findAllReviewersOnApplicationByApplicationId(
        occupancyInspectionDto.applicationId,
      );

      if (!reviewers || reviewers.length === 0) {
        throw new HttpException(
          'No reviewers found for the given application ID',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check if the current user is in the reviewers list
      const currentReviewer = reviewers.find(
        (reviewer) => reviewer.userId === occupancyInspectionDto.userId,
      );

      if (!currentReviewer) {
        throw new HttpException(
          'You are not authorized to review this application',
          HttpStatus.FORBIDDEN,
        );
      }

      // Check the status of the reviewer
      if (currentReviewer.status === '1') {
        throw new HttpException(
          'You have already reviewed this application',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Update the reviewer status to '1' (Reviewed)
      await this.updateReviewersOnApplicationStatusInspection(
        occupancyInspectionDto.applicationId,
        currentReviewer.userId,
      );

      // Create the occupancy inspection
      const occupancyInspection = new OccupancyInspection({
        ...occupancyInspectionDto,
        application: dataFromDb,
        fileBase64,
      });

      const savedEntity =
        await this.occupancyInspectionRepository.create(occupancyInspection);

      // Only call the RHA update logic if isNonObjection = '1'
      if (dataFromDb.isNonObjection === '1') {
        await this.updateApplicationAfterReviewerCommentInspectionRHA(
          occupancyInspectionDto.applicationId,
        );
      } else {
        // // Check if all reviewers have `status` = '1'
        // const allReviewersUpdated = reviewers.every(
        //   (reviewer) =>
        //     reviewer.userId === currentReviewer.userId ||
        //     reviewer.status === '1' ||
        //     reviewer.status === '3',
        // );

        // if (allReviewersUpdated) {
        //   // If all reviewers have reviewed, update the application status to 'RVW'
        //   await this.updateApplicationAfterReviewerCommentInspection(
        //     occupancyInspectionDto.applicationId,
        //   );
        // }

        await this.updateApplicationAfterReviewerCommentInspection(
          occupancyInspectionDto.applicationId,
        );
      }

      return savedEntity;
    } catch (error) {
      console.error('Error in create inspection:', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateReviewersOnApplicationStatusInspection(
    applicationId: string,
    userId: string,
  ) {
    const reviewer = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewer')
      .where('reviewer.applications = :applicationId', { applicationId })
      .andWhere('reviewer.userId = :userId', { userId })
      .getOne();

    if (!reviewer) {
      throw new Error(
        'Reviewer not found for the given application ID and user ID',
      );
    }

    reviewer.status = '1'; // Update status to reviewed
    return await this.reviewersOnApplicationEntityRepository.save(reviewer);
  }

  async updateApplicationAfterReviewerCommentInspection(applicationId: string) {
    console.log('UUID:', applicationId);

    // Retrieve application status data for 'RVW' and 'UNRV'
    const allApplicationStatuses = await this.findAllApplicationStatus();
    const rvwStatusData = allApplicationStatuses.find(
      (status) => status.code === 'RVW',
    );
    const unrvStatusData = allApplicationStatuses.find(
      (status) => status.code === 'UNRV',
    );

    if (!rvwStatusData || !unrvStatusData) {
      throw new NotFoundException(
        'Required application statuses "RVW" or "UNRV" not found',
      );
    }

    // isNonObjection: 1
    // isNonObjectionReturned: 1

    console.log('RVW Status Data:', rvwStatusData);
    console.log('UNRV Status Data:', unrvStatusData);

    // Retrieve all reviewers on the application
    const reviewersOnApplication =
      await this.findAllReviewersOnApplicationByApplicationId(applicationId);
    const hasReviewerWithStatusZero = reviewersOnApplication.some(
      (reviewer) => reviewer.status === '0',
    );

    // Determine the application status based on reviewers' statuses
    const applicationStatusData = hasReviewerWithStatusZero
      ? unrvStatusData
      : rvwStatusData;

    // Update application status only if all reviewers have reviewed
    if (!hasReviewerWithStatusZero) {
      const applicationUpdate = await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({ applicationStatus: applicationStatusData })
        // .set({ applicationStatus: rvwStatusData })
        .where('id = :applicationId', { applicationId })
        .execute();

      console.log('Update Result:', applicationUpdate);

      return applicationUpdate;
    }

    return null;
  }

  async updateApplicationAfterReviewerCommentInspectionRHA(
    applicationId: string,
  ) {
    console.log('UUID:', applicationId);

    // Retrieve application status data for 'NORVW' and 'NOUNRV'
    const allApplicationStatuses = await this.findAllApplicationStatus();
    const rvwStatusDataRHA = allApplicationStatuses.find(
      (status) => status.code === 'NORVW',
    );
    const unrvStatusDataRHA = allApplicationStatuses.find(
      (status) => status.code === 'NOUNRV',
    );

    if (!rvwStatusDataRHA || !unrvStatusDataRHA) {
      throw new NotFoundException(
        'Required application statuses "NORVW" or "NOUNRV" not found',
      );
    }

    // Retrieve all reviewers on the application
    const reviewersOnApplication =
      await this.findAllReviewersOnApplicationByApplicationId(applicationId);

    const hasStatusZero = reviewersOnApplication.some(
      (reviewer) => reviewer.status === '0',
    );

    const allStatusOneOrThree = reviewersOnApplication.every(
      (reviewer) => reviewer.status === '1' || reviewer.status === '3',
    );

    if (hasStatusZero) {
      // If any reviewer has status 0, set status to NOUNRV
      const result = await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({ applicationStatus: unrvStatusDataRHA })
        .where('id = :applicationId', { applicationId })
        .execute();

      console.log('Set to NOUNRV due to reviewer with 0:', result);
      return result;
    }

    if (allStatusOneOrThree) {
      // If all reviewers have status 1 or 3, set status to NORVW
      const result = await this.applicationEntityManagerRepository
        .createQueryBuilder()
        .update(Application)
        .set({ applicationStatus: rvwStatusDataRHA })
        .where('id = :applicationId', { applicationId })
        .execute();

      console.log('Set to NORVW (all 1 or 3):', result);
      return result;
    }

    // If neither condition matches, do nothing
    console.log('No status change applied.');
    return null;
  }

  // // Occupancy Inspection with a file
  // async createOccupancyInspection(
  //   occupancyInspectionDto: OccupancyInspectionDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     // Convert file to base64 if provided
  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const dataFromDb = await this.applicationRepository.findOne({
  //       id: occupancyInspectionDto.applicationId,
  //     });
  //     if (!dataFromDb)
  //       throw new HttpException(
  //         'Application Not Found',
  //         HttpStatus.BAD_REQUEST,
  //       );

  //     const occupancyInspection = new OccupancyInspection({
  //       ...occupancyInspectionDto,
  //       application: (occupancyInspectionDto.applicationId = {
  //         id: dataFromDb.id,
  //       } as any),

  //       fileBase64,
  //     });

  //     const savedEntity =
  //       await this.occupancyInspectionRepository.create(occupancyInspection);

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in createApplicationApprovalCheckList:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  async findAllOccupancyInspection() {
    return this.occupancyInspectionEntityRepository.find({
      relations: {
        application: true,
      },
    });
  }

  async findOneOccupancyInspection(id: string) {
    return this.occupancyInspectionEntityRepository.findOne({
      where: { id: id },
      relations: { application: true },
    });
  }

  async updateOccupancyInspection(
    id: string,
    occupancyInspectionDto: OccupancyInspectionDto,
  ) {
    return this.occupancyInspectionRepository.findOneAndUpdate(
      { id },
      occupancyInspectionDto,
    );
  }

  async removeOccupancyInspection(id: string) {
    return this.occupancyInspectionRepository.findOneAndDelete({ id });
  }

  // async findOneOccupancyInspectionByApplicationId(applicationId: string) {
  //   return await this.occupancyInspectionEntityRepository
  //     .createQueryBuilder('occupancyInspection')
  //     .where('occupancyInspection.applicationId = :applicationId', {
  //       applicationId,
  //     })
  //     .leftJoinAndSelect('occupancyInspection.application', 'application')
  //     .getMany();
  // }

  async findOneOccupancyInspectionByApplicationId(applicationId: string) {
    const inspections = await this.occupancyInspectionEntityRepository
      .createQueryBuilder('occupancyInspection')
      .where('occupancyInspection.applicationId = :applicationId', {
        applicationId,
      })
      .leftJoinAndSelect('occupancyInspection.application', 'application')
      .getMany();

    const inspectionsWithUser = await Promise.all(
      inspections.map(async (inspection) => {
        const user = await this.checkUser(inspection.userId);
        return {
          ...inspection,
          userFullName:
            user?.firstName && user?.lastName
              ? `${user.firstName} ${user.lastName}`
              : null,
        };
      }),
    );

    return inspectionsWithUser;
  }

  // // Foundation Inspection
  // async createFoundationInspection(
  //   foundationInspectionDto: FoundationInspectionDto,
  // ) {
  //   const dataFromDb = await this.applicationRepository.findOne({
  //     id: foundationInspectionDto.applicationId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Application Not Found', HttpStatus.BAD_REQUEST);

  //   const foundationInspection = new FoundationInspection({
  //     ...foundationInspectionDto,
  //     application: (foundationInspectionDto.applicationId = {
  //       id: dataFromDb.id,
  //     } as any),
  //   });
  //   return this.foundationInspectionRepository.create(foundationInspection);
  // }

  // Foundation Inspection with a file
  async createFoundationInspectionWithAFile(
    foundationInspectionDto: FoundationInspectionDto,
    file: Express.Multer.File,
  ) {
    try {
      let fileBase64: string = null;

      // Convert file to base64 if provided
      if (file) {
        fileBase64 = file.buffer.toString('base64');
      }

      // Check if application exists
      const dataFromDb = await this.applicationRepository.findOne({
        id: foundationInspectionDto.applicationId,
      });
      if (!dataFromDb)
        throw new HttpException(
          'Application Not Found',
          HttpStatus.BAD_REQUEST,
        );

      // Get the list of reviewers for the application
      const reviewers = await this.findAllReviewersOnApplicationByApplicationId(
        foundationInspectionDto.applicationId,
      );

      if (!reviewers || reviewers.length === 0) {
        throw new HttpException(
          'No reviewers found for the given application ID',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check if the current user is in the reviewers list
      const currentReviewer = reviewers.find(
        (reviewer) => reviewer.userId === foundationInspectionDto.userId,
      );

      if (!currentReviewer) {
        throw new HttpException(
          'You are not authorized to review this application',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Check the status of the reviewer
      if (currentReviewer.status === '1') {
        throw new HttpException(
          'You have already reviewed this application',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Update the reviewer status to '1' (Reviewed)
      await this.updateReviewersOnApplicationStatusInspection(
        foundationInspectionDto.applicationId,
        currentReviewer.userId,
      );

      // Create the foundation inspection

      const foundationInspection = new FoundationInspection({
        ...foundationInspectionDto,
        application: (foundationInspectionDto.applicationId = {
          id: dataFromDb.id,
        } as any),

        fileBase64,
      });

      const savedEntity =
        await this.foundationInspectionRepository.create(foundationInspection);

      // Check if all reviewers have `status` = '1'
      const allReviewersUpdated = reviewers.every(
        (reviewer) =>
          reviewer.userId === currentReviewer.userId ||
          reviewer.status === '1' ||
          reviewer.status === '3',
      );
      // const allReviewersUpdated = reviewers.every(
      //   (reviewer) =>
      //     reviewer.userId === currentReviewer.userId ||
      //     reviewer.status === '1' ||
      //     reviewer.status === '3',
      // );

      if (allReviewersUpdated) {
        // If all reviewers have reviewed, update the application status to 'RVW'
        await this.updateApplicationAfterReviewerCommentInspection(
          foundationInspectionDto.applicationId,
        );
      }

      return savedEntity;
    } catch (error) {
      console.error('Error in foundation :', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  // // Foundation Inspection with a file
  // async createFoundationInspectionWithAFile(
  //   foundationInspectionDto: FoundationInspectionDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     // Convert file to base64 if provided
  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const dataFromDb = await this.applicationRepository.findOne({
  //       id: foundationInspectionDto.applicationId,
  //     });
  //     if (!dataFromDb)
  //       throw new HttpException(
  //         'Application Not Found',
  //         HttpStatus.BAD_REQUEST,
  //       );

  //     const foundationInspection = new FoundationInspection({
  //       ...foundationInspectionDto,
  //       application: (foundationInspectionDto.applicationId = {
  //         id: dataFromDb.id,
  //       } as any),

  //       fileBase64,
  //     });

  //     const savedEntity =
  //       await this.foundationInspectionRepository.create(foundationInspection);

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in foundation :', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  async findAllFoundationInspection() {
    return this.foundationInspectionRepository.findAll({
      relations: {
        application: true,
      },
    });
  }

  async findOneFoundationInspection(id: string) {
    return this.foundationInspectionEntityRepository.findOne({
      where: { id: id },
      relations: { application: true },
    });
  }

  async updateFoundationInspection(
    id: string,
    foundationInspectionDto: FoundationInspectionDto,
  ) {
    return this.foundationInspectionRepository.findOneAndUpdate(
      { id },
      foundationInspectionDto,
    );
  }

  async removeFoundationInspection(id: string) {
    return this.foundationInspectionRepository.findOneAndDelete({ id });
  }

  // async findOneFoundationInspectionByApplicationId(applicationId: string) {
  //   return await this.foundationInspectionEntityRepository
  //     .createQueryBuilder('foundationInspection')
  //     .where('foundationInspection.applicationId = :applicationId', {
  //       applicationId,
  //     })
  //     .leftJoinAndSelect('foundationInspection.application', 'application')
  //     .getMany();
  // }

  async findOneFoundationInspectionByApplicationId(applicationId: string) {
    const inspections = await this.foundationInspectionEntityRepository
      .createQueryBuilder('foundationInspection')
      .where('foundationInspection.applicationId = :applicationId', {
        applicationId,
      })
      .leftJoinAndSelect('foundationInspection.application', 'application')
      .getMany();

    const inspectionsWithUser = await Promise.all(
      inspections.map(async (inspection) => {
        const user = await this.checkUser(inspection.userId);
        return {
          ...inspection,
          userFullName:
            user?.firstName && user?.lastName
              ? `${user.firstName} ${user.lastName}`
              : null,
        };
      }),
    );

    return inspectionsWithUser;
  }

  async countApplicationsForInspection(agencyId: string) {
    console.log(agencyId);
    const applicationsForInspection =
      await this.findAllApplicationsForInspection(agencyId);
    // console.log(applicationsData2.length);

    console.log(applicationsForInspection.length);

    if (applicationsForInspection.length === 0) {
      const countingNumber: { [status: string]: number } = {
        all: 0,
        pending: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      return countingNumber;
    } else {
      const countingNumber: { [status: string]: number } = {
        all: applicationsForInspection.length,
        pending: 0,
        // approved: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      applicationsForInspection.forEach((application) => {
        switch (application.applicationStatus.code) {
          case 'PND':
            countingNumber.pending++;
            break;
          // case 'SMB':
          //   countingBoard2.approved++;
          //   break;
          case 'RVW':
            countingNumber.review++;
            break;
          case 'CTFD':
            countingNumber.certified++;
            break;
          case 'CXL':
            countingNumber.rejected++;
            break;
          case 'NORHA':
            countingNumber.rha++;
            break;
          case 'SUB':
            countingNumber.submitted++;
            break;
          case 'UNRV':
            countingNumber.underReview++;
            break;
          case 'UNCRN':
            countingNumber.underCorrection++;
            break;
          case 'RSMB':
            countingNumber.reSubmitted++;
            break;
          case 'RTNNO':
            countingNumber.rhaReturned++;
            break;
          case 'NORVW':
            countingNumber.nonObjectionReviewed++;
            break;

          case 'NOUNRV':
            countingNumber.nonObjectionUnderReview++;
            break;
          case 'PAPRV':
            countingNumber.preApproval++;
            break;
          default:
            break;
        }
      });
      return countingNumber;
    }
  }
  // async countApplicationsForInspection(agencyId: string) {
  //   console.log('bbbbb');
  //   console.log(agencyId);
  //   const applicationsData2 =
  //     await this.findAllApplicationsForInspection(agencyId);

  //   if (applicationsData2.length === 0) {
  //     if (applicationsData2.length === 0) {
  //       const countingBoard2: { [status: string]: number } = {
  //         all: 0,
  //         pending: 0,
  //         certified: 0,
  //         review: 0,
  //         rejected: 0,
  //         rha: 0,
  //         submitted: 0,
  //         underReview: 0,
  //         underCorrection: 0,
  //         reSubmitted: 0,
  //         rhaReturned: 0,
  //         nonObjectionReviewed: 0,
  //         nonObjectionUnderReview: 0,
  //         preApproval: 0,
  //       };
  //       return countingBoard2;
  //     }
  //   }

  //   const countingBoard2: { [status: string]: number } = {
  //     all: applicationsData2.length,
  //     pending: 0,
  //     // approved: 0,
  //     certified: 0,
  //     review: 0,
  //     rejected: 0,
  //     rha: 0,
  //     submitted: 0,
  //     underReview: 0,
  //     underCorrection: 0,
  //     reSubmitted: 0,
  //     rhaReturned: 0,

  //     nonObjectionReviewed: 0,
  //     nonObjectionUnderReview: 0,
  //     preApproval: 0,
  //   };

  //   applicationsData2.forEach((application) => {
  //     switch (application.applicationStatus.code) {
  //       case 'PND':
  //         countingBoard2.pending++;
  //         break;
  //       // case 'SMB':
  //       //   countingBoard2.approved++;
  //       //   break;
  //       case 'RVW':
  //         countingBoard2.review++;
  //         break;
  //       case 'CTFD':
  //         countingBoard2.certified++;
  //         break;
  //       case 'CXL':
  //         countingBoard2.rejected++;
  //         break;
  //       case 'NORHA':
  //         countingBoard2.rha++;
  //         break;
  //       case 'SUB':
  //         countingBoard2.submitted++;
  //         break;
  //       case 'UNRV':
  //         countingBoard2.underReview++;
  //         break;
  //       case 'UNCRN':
  //         countingBoard2.underCorrection++;
  //         break;
  //       case 'RSMB':
  //         countingBoard2.reSubmitted++;
  //         break;
  //       case 'RTNNO':
  //         countingBoard2.rhaReturned++;
  //         break;
  //       case 'NORVW':
  //         countingBoard2.nonObjectionReviewed++;
  //         break;

  //       case 'NOUNRV':
  //         countingBoard2.nonObjectionUnderReview++;
  //         break;
  //       case 'PAPRV':
  //         countingBoard2.preApproval++;
  //         break;
  //       default:
  //         break;
  //     }
  //   });

  //   return countingBoard2;
  // }

  async findAllApplicationsForInspection(agencyId: string) {
    console.log('loveeeee');
    console.log(agencyId);

    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })

      // .andWhere(
      //   '(application.permitTypeCode = :code1 OR application.permitTypeCode = :code2)',
      //   {
      //     code1: 'FINS',
      //     code2: 'OCP',
      //   },
      // )
      .andWhere('application.permitTypeCode = :code1', { code1: 'FINS' })

      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .getMany();

    return applications;
  }

  // async findAllApplicationsForInspection(agencyId: string) {
  //   console.log('loveeeee');
  //   console.log(agencyId);

  //   const applicationsFINS = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere('application.permitTypeCode = :code', { code: 'FINS' })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .getMany();

  //   const applicationsOCP = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere('application.permitTypeCode = :code', { code: 'OCP' })
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.invoices', 'invoices')
  //     .leftJoinAndSelect('application.certificates', 'certificates')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .getMany();

  //   const combinedApplications = [...applicationsFINS, ...applicationsOCP];

  //   console.log(combinedApplications);

  //   return combinedApplications;
  // }

  // All application without INSP or OCP
  async dashboardApplicationsWithoutInspection(agencyId: string) {
    const applicationsWithoutInspection =
      await this.findAllApplicationsWithoutInspection(agencyId);
    if (applicationsWithoutInspection.length === 0) {
      const countingNumber: { [status: string]: number } = {
        all: 0,
        pending: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      return countingNumber;
    } else {
      const countingNumber: { [status: string]: number } = {
        all: applicationsWithoutInspection.length,
        pending: 0,
        // approved: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      applicationsWithoutInspection.forEach((application) => {
        switch (application.applicationStatus.code) {
          case 'PND':
            countingNumber.pending++;
            break;
          // case 'SMB':
          //   countingBoard2.approved++;
          //   break;
          case 'RVW':
            countingNumber.review++;
            break;
          case 'CTFD':
            countingNumber.certified++;
            break;
          case 'CXL':
            countingNumber.rejected++;
            break;
          case 'NORHA':
            countingNumber.rha++;
            break;
          case 'SUB':
            countingNumber.submitted++;
            break;
          case 'UNRV':
            countingNumber.underReview++;
            break;
          case 'UNCRN':
            countingNumber.underCorrection++;
            break;
          case 'RSMB':
            countingNumber.reSubmitted++;
            break;
          case 'RTNNO':
            countingNumber.rhaReturned++;
            break;
          case 'NORVW':
            countingNumber.nonObjectionReviewed++;
            break;

          case 'NOUNRV':
            countingNumber.nonObjectionUnderReview++;
            break;
          case 'PAPRV':
            countingNumber.preApproval++;
            break;
          default:
            break;
        }
      });
      return countingNumber;
    }
  }

  // All application without INSP or OCP removing application status with PND
  async findAllApplicationsWithoutInspection(agencyId: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere(
        '(application.permitTypeCode != :code1 AND application.permitTypeCode != :code2)',
        {
          code1: 'FINS',
          code2: 'OCP',
        },
      )
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .orderBy('application.submittedDate')
      .getMany();

    return applications;
  }

  // All application without INSP or OCP removed the draft
  async findAllApplicationsWithoutInspection2(agencyId: string) {
    const applications = await this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere(
        '(application.permitTypeCode != :code1 AND application.permitTypeCode != :code2)',
        {
          code1: 'FINS',
          code2: 'OCP',
        },
      )
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .andWhere('applicationStatus.code != :statusCode', { statusCode: 'PND' })
      .orderBy('application.submittedDate')
      .getMany();

    return applications;
  }

  // All application without INSP or OCP removing application status with PND
  async dashboardApplicationsWithoutInspection2(agencyId: string) {
    const applicationsWithoutInspection =
      await this.findAllApplicationsWithoutInspection2(agencyId);
    if (applicationsWithoutInspection.length === 0) {
      const countingNumber: { [status: string]: number } = {
        all: 0,
        pending: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      return countingNumber;
    } else {
      const countingNumber: { [status: string]: number } = {
        all: applicationsWithoutInspection.length,
        pending: 0,
        // approved: 0,
        certified: 0,
        review: 0,
        rejected: 0,
        rha: 0,
        submitted: 0,
        underReview: 0,
        underCorrection: 0,
        reSubmitted: 0,
        rhaReturned: 0,
        nonObjectionReviewed: 0,
        nonObjectionUnderReview: 0,
        preApproval: 0,
      };
      applicationsWithoutInspection.forEach((application) => {
        switch (application.applicationStatus.code) {
          case 'PND':
            countingNumber.pending++;
            break;
          // case 'SMB':
          //   countingBoard2.approved++;
          //   break;
          case 'RVW':
            countingNumber.review++;
            break;
          case 'CTFD':
            countingNumber.certified++;
            break;
          case 'CXL':
            countingNumber.rejected++;
            break;
          case 'NORHA':
            countingNumber.rha++;
            break;
          case 'SUB':
            countingNumber.submitted++;
            break;
          case 'UNRV':
            countingNumber.underReview++;
            break;
          case 'UNCRN':
            countingNumber.underCorrection++;
            break;
          case 'RSMB':
            countingNumber.reSubmitted++;
            break;
          case 'RTNNO':
            countingNumber.rhaReturned++;
            break;
          case 'NORVW':
            countingNumber.nonObjectionReviewed++;
            break;

          case 'NOUNRV':
            countingNumber.nonObjectionUnderReview++;
            break;
          case 'PAPRV':
            countingNumber.preApproval++;
            break;
          default:
            break;
        }
      });
      return countingNumber;
    }
  }

  // With pagination
  async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
    applicationStatusId: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere(
        '(application.permitTypeCode != :code1 AND application.permitTypeCode != :code2)',
        {
          code1: 'FINS',
          code2: 'OCP',
        },
      )
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId,
      })
      .orderBy('application.submittedDate', 'ASC');

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // With pagination withCode
  async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId2(
    applicationStatusCode: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    // // Retrieve applicationStatusId from code
    // const status = await this.applicationStatusRepository.findOne({
    //   code: applicationStatusCode,
    // });
    // if (!status) {
    //   throw new NotFoundException(
    //     `ApplicationStatus with code '${applicationStatusCode}' not found`,
    //   );
    // }

    // Get applicationStatus by code
    // const status = await this.applicationStatusEntityManagerRepository.findOne({
    //   where: { code: applicationStatusCode },
    // });

    const status = await this.applicationStatusEntityManagerRepository
      .createQueryBuilder('applicationStatus')
      .where('applicationStatus.code = :code', { code: applicationStatusCode })
      .getOne();

    if (!status) {
      throw new NotFoundException(
        `ApplicationStatus with code '${applicationStatusCode}' not found`,
      );
    }

    console.log(status);

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere(
        '(application.permitTypeCode != :code1 AND application.permitTypeCode != :code2)',
        {
          code1: 'FINS',
          code2: 'OCP',
        },
      )
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId: status.id,
      })
      .orderBy('application.submittedDate', 'ASC');

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    console.log(status);

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
  //   applicationStatusId: string,
  //   agencyId: string,
  // ): Promise<Application[]> {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     .andWhere(
  //       '(application.permitTypeCode != :code1 AND application.permitTypeCode != :code2)',
  //       {
  //         code1: 'FINS',
  //         code2: 'OCP',
  //       },
  //     )
  //     .andWhere('application.applicationStatus.id = :applicationStatusId', {
  //       applicationStatusId,
  //     })

  //     .orderBy('application.submittedDate')
  //     .getMany();

  //   return applications;
  // }

  // with pagination
  async findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
    applicationStatusId: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('application.permitTypeCode = :code1', { code1: 'FINS' })
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId,
      })
      .orderBy('application.submittedDate', 'DESC');

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // async findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
  //   applicationStatusId: string,
  //   agencyId: string,
  // ): Promise<Application[]> {
  //   const applications = await this.applicationEntityManagerRepository
  //     .createQueryBuilder('application')
  //     .leftJoinAndSelect('application.projects', 'projects')
  //     .leftJoinAndSelect('application.permitTypes', 'permitTypes')
  //     .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
  //     .leftJoinAndSelect('application.buildTypes', 'buildTypes')
  //     .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
  //     .where('application.agencyId = :agencyId', { agencyId })
  //     // .andWhere(
  //     //   '(application.permitTypeCode = :code1 OR application.permitTypeCode = :code2)',
  //     //   {
  //     //     code1: 'FINS',
  //     //     code2: 'OCP',
  //     //   },
  //     // )
  //     .andWhere('application.permitTypeCode = :code1', { code1: 'FINS' })
  //     .andWhere('application.applicationStatus.id = :applicationStatusId', {
  //       applicationStatusId,
  //     })
  //     .orderBy('application.submittedDate')
  //     .getMany();

  //   return applications;
  // }

  // with pagination
  async findAllApplicationWithInspectionByApplicationStatusCodeAndAgencyId(
    applicationStatusCode: string,
    agencyId: string,
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: Application[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const status = await this.applicationStatusEntityManagerRepository
      .createQueryBuilder('applicationStatus')
      .where('applicationStatus.code = :code', { code: applicationStatusCode })
      .getOne();

    if (!status) {
      throw new NotFoundException(
        `ApplicationStatus with code '${applicationStatusCode}' not found`,
      );
    }

    const query = this.applicationEntityManagerRepository
      .createQueryBuilder('application')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .where('application.agencyId = :agencyId', { agencyId })
      .andWhere('application.permitTypeCode = :code1', { code1: 'FINS' })
      .andWhere('application.applicationStatus.id = :applicationStatusId', {
        applicationStatusId: status.id,
      })
      .orderBy('application.submittedDate', 'DESC');

    const [applications, total] = await query
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: applications,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findReviewersOnApplicationsByUserIdInDateRange(
    reviewerReportDto: ReviewerReportDto,
  ) {
    const userId = reviewerReportDto.userId;
    const startDate = reviewerReportDto.startDate;
    const endDate = reviewerReportDto.endDate;
    const approvals = await this.reviewersOnApplicationEntityRepository
      .createQueryBuilder('reviewersOnApplication')
      .where('reviewersOnApplication.userId = :userId', { userId })
      .andWhere(
        'reviewersOnApplication.created_at BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      )
      .leftJoinAndSelect('reviewersOnApplication.applications', 'applications')
      .leftJoinAndSelect('applications.applicationStatus', 'applicationStatus')
      .getMany();

    if (approvals.length === 0) {
      throw new NotFoundException(
        `No data found for the given user and date range`,
      );
    }

    return approvals;
  }
}
