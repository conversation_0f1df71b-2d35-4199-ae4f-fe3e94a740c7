import { AbstractEntity } from '@app/common';
import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
} from 'typeorm';

@Entity()
export class Message extends AbstractEntity<Message> {
  @Column()
  senderId: string;

  @Column()
  receiverId: string;

  @Column('text')
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: false })
  isRead: boolean;

  @UpdateDateColumn({
    type: 'timestamptz',
    nullable: true,
    select: true,
  })
  public readAt: Date | null;
}

@Entity()
export class IssueCategory extends AbstractEntity<IssueCategory> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => ChatBoard, (chatBoard) => chatBoard.issueCategory)
  chatBoards: ChatBoard[];
}

@Entity()
export class ChatBoard extends AbstractEntity<ChatBoard> {
  @Column({ type: 'varchar', nullable: true })
  phoneNumber: string;

  @Column({ type: 'varchar', nullable: true })
  senderNames: string;

  @Column()
  agencyId: string;

  @Column('text')
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ default: false })
  isRead: boolean;

  @Column({ default: '0', nullable: true })
  isReply: string;

  @Column({ type: 'uuid', array: true, nullable: true })
  documentIds: string[];

  @UpdateDateColumn({
    type: 'timestamptz',
    nullable: true,
    select: true,
  })
  public readAt: Date | null;

  @ManyToOne(() => IssueCategory, (issueCategory) => issueCategory.chatBoards)
  issueCategory: IssueCategory;
}
