import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExternalPageLayoutComponent } from '../external-page-layout/external-page-layout.component';
import { VerifyCertificateComponent } from '../verify-certificate/verify-certificate.component';

export const externalRoutes: Routes = [
  {
    path: '', component: ExternalPageLayoutComponent,
    children: [
      { path: 'certificate/:id', component: VerifyCertificateComponent }
    ]
  },
]

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(externalRoutes)
  ]
})
export class ExternalPageModule { }
