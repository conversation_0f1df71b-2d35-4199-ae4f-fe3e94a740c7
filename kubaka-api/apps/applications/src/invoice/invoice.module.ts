import { forwardRef, Module } from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import { InvoiceController } from './invoice.controller';
import { DatabaseModule } from '@app/common';
import {
  InvoiceStatus,
  Price,
  InvoiceType,
  InvoiceItem,
  Invoice,
  Receipt,
} from '../entities/invoice.entity';
import { Certificate } from '../entities/certificate.entity';
import {
  InvoiceStatusRepository,
  PriceRepository,
  InvoiceTypeRepository,
  InvoiceItemRepository,
  InvoiceRepository,
  ReceiptRepository,
} from './invoice.repository';
import {
  GenericSearch,
  GenericSearchWithRelations,
} from './generic-search.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { AUTH_SERVICE } from '@app/common/constants';
import { ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { CertificateModule } from '../certificate/certificate.module';
import { ApplicationModule } from '../application/application.module';

@Module({
  imports: [
    DatabaseModule,
    HttpModule,
    DatabaseModule.forFeature([
      // invoice
      InvoiceStatus,
      Price,
      InvoiceType,
      InvoiceItem,
      Invoice,
      Receipt,
      // certificate
      Certificate,
    ]),

    // import app
    forwardRef(() => CertificateModule),
    ApplicationModule,

    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [InvoiceController],
  providers: [
    InvoiceService,
    InvoiceStatusRepository,
    PriceRepository,
    InvoiceTypeRepository,
    InvoiceItemRepository,
    InvoiceRepository,
    ReceiptRepository,

    GenericSearch,
    GenericSearchWithRelations,
  ],
  exports: [InvoiceService],
})
export class InvoiceModule {}
