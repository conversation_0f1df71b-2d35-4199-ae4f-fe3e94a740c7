import { Column, <PERSON>tity, ManyToOne, OneToMany } from 'typeorm';
import { District } from './district.entity';
import { AbstractEntity } from '@app/common';
import { Cell } from './cell.entity';
import { User } from '../user-management/user.entity';

@Entity()
export class Sector extends AbstractEntity<Sector> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;

  @Column()
  code: string;

  @ManyToOne(() => District, (district) => district.sectors)
  district: District;

  @OneToMany(() => Cell, (cell) => cell.sector)
  cells: Cell[];

  @OneToMany(() => User, (user) => user.sector)
  users: User[];
}
