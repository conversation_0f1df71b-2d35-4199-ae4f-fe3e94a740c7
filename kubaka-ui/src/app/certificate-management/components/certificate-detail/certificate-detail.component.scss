.pageborder{
    width: 100%;
    padding: 10mm;
    .page{
        margin: 0;
        overflow: hidden;
        box-sizing: border-box;
        position: relative;
        page-break-after: always;
        margin: 5mm auto;
        padding: 5mm;

    }
    .a4portrait{
        width: 210mm;
        height: 296mm;
    }
    .a4landscape{
        height: 210mm;
        width: 296mm;
    }
}
.app-gen-content{
  padding: 1.5rem 0;
}
@media screen {
    body{
        background-color: #d1d1d1;
    }
    .page{
        background-color: #fff;
        box-shadow: 0 .5mm 3mm rgba(0,0,0,.1);
        margin: 5mm auto;
    }
}

@page{
    size: A4;
    margin: 0;
}

@media print{
    .A4{
        // width: 210mm;
        // height: 296mm;
        // padding: 10mm;
        // page-break-inside: auto;
        // p:nth-child(1){
        //   color: rgb(42, 42, 190);
        // }
        // p:nth-child(2){
        //     color: rgb(153, 0, 255);
        //   }
        //   p:nth-child(3){
        //     color: rgb(0, 183, 255);
        //   }
        //   p:nth-child(4){
        //     color: rgb(164, 40, 168);
        //   }
        //   p:nth-child(5){
        //     color: rgb(185, 197, 22);
        //   }
        //   p:nth-child(6){
        //     color: rgb(51, 51, 155);
        //   }
        //   p:nth-child(7){
        //     color: rgb(255, 123, 0);
        //   }
        //   p:nth-child(8){
        //     color: rgb(26, 126, 40);
        //   }
        //   p:nth-child(9){
        //     color: rgb(129, 35, 35);
        //   }
          
    }
    .potrait{
        width: 210mm;
        height: 296mm;
        padding: 10mm;
    }
    .landscape{
        width: 296mm;
        height: 210mm;
        padding: 10mm;
    }
    p{
        // break-before: always;
        // break-inside: avoid;
    }
    h4{
        color: rgb(45, 125, 128);
        string-set: Chapter content(string);
    }

}
@page {
    // size: A4 portrait;
    // margin: 20%;
    @top-right {
        // content: "Page " counter(pageNumber);
        content: string(Chapter);
      }
}
@top-left-corner{
    content: "Top left corner";
}
@page:first{
    // margin-top: 30mm;
}
@page:left{
    // background-color: rgb(188, 164, 211);
    // margin-top: 60mm;
}
@page:right{
    // background-color: rgb(207, 186, 157);
    // margin-top: 20mm;
}