import { AbstractEntity } from '@app/common';
import {
  Column,
  Entity,
  // JoinColumn,
  // JoinTable,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import {
  Application,
  OtherInfoApplication,
  PermitQuestion,
  Project,
} from './application.entity';
import { PermitCheckList } from './approval.entity';
import { Certificate } from './certificate.entity';
import { Price } from './invoice.entity';
// import { PermitCheckList } from './approval.entity';

@Entity()
export class PermitType extends AbstractEntity<PermitType> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @Column({ default: 21, nullable: true })
  issuedDays: number;

  @OneToMany(
    () => RequiredDocument,
    (requiredDocument) => requiredDocument.permitTypes,
  )
  requiredDocuments: RequiredDocument[];

  @OneToMany(() => Price, (price) => price.permitTypes)
  price: Price[];

  @OneToMany(() => Application, (application) => application.projects)
  applications: Application[];

  @OneToMany(
    () => PermitCheckList,
    (permitCheckList) => permitCheckList.permitTypes,
  )
  permitCheckLists: PermitCheckList[];

  @OneToMany(() => Certificate, (certificate) => certificate.permitTypes)
  certificates: Certificate[];

  @OneToMany(
    () => PermitQuestion,
    (permitQuestion) => permitQuestion.permitTypes,
  )
  questions: PermitQuestion[];

  @OneToMany(
    () => OtherInfoApplication,
    (otherInfoApplication) => otherInfoApplication.permitTypes,
  )
  otherInfoApplications: OtherInfoApplication[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class CategoryRule extends AbstractEntity<CategoryRule> {
  @Column()
  GrossFloorArea: number;

  @Column()
  GPlus: number;

  @Column()
  NumberOfPeople: number;

  @OneToMany(() => CategoryType, (categoryType) => categoryType.categoryRules)
  categoryTypes: CategoryType[];
}

@Entity()
export class CategoryType extends AbstractEntity<CategoryType> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @ManyToOne(() => CategoryRule, (categoryRule) => categoryRule.categoryTypes)
  categoryRules: CategoryRule;

  @OneToMany(
    () => RequiredDocument,
    (requiredDocument) => requiredDocument.categoryTypes,
  )
  requiredDocuments: RequiredDocument[];

  @OneToMany(() => Application, (application) => application.projects)
  applications: Application[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class BuildType extends AbstractEntity<BuildType> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @Column({ type: 'varchar' })
  description: string;

  @OneToMany(() => Application, (application) => application.projects)
  applications: Application[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class DocumentType extends AbstractEntity<DocumentType> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(
    () => RequiredDocument,
    (requiredDocument) => requiredDocument.documentTypes,
  )
  requiredDocuments: RequiredDocument[];
}

@Entity()
export class RequiredDocument extends AbstractEntity<RequiredDocument> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', default: 0, nullable: true })
  code: string;

  @ManyToOne(
    () => DocumentType,
    (documentType) => documentType.requiredDocuments,
  )
  documentTypes: DocumentType;

  @ManyToOne(() => PermitType, (permitType) => permitType.requiredDocuments)
  permitTypes: PermitType;

  @ManyToOne(
    () => CategoryType,
    (categoryType) => categoryType.requiredDocuments,
  )
  categoryTypes: CategoryType;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class TechnologySurvey extends AbstractEntity<TechnologySurvey> {
  @Column({ type: 'varchar' })
  name: string;

  @OneToMany(() => Application, (application) => application.technologySurveys)
  applications: Application[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class ApplicationStatus extends AbstractEntity<ApplicationStatus> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => Application, (application) => application.applicationStatus)
  applications: Application[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}
@Entity()
export class ProjectStatus extends AbstractEntity<ProjectStatus> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => Project, (project) => project.projectStatus)
  projects: Project[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}

@Entity()
export class QuestionCategory extends AbstractEntity<QuestionCategory> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(
    () => PermitQuestion,
    (permitQuestion) => permitQuestion.questionCategorys,
  )
  questions: PermitQuestion[];
}

// @Entity()
// export class NonObjectionStatus extends AbstractEntity<NonObjectionStatus> {
//   @Column({ type: 'varchar' })
//   name: string;

//   @Column({ type: 'varchar' })
//   code: string;

//   @OneToMany(() => Application, (application) => application.applicationStatus)
//   applications: Application[];
// }

// @Entity()
// export class ConstructionMethod extends AbstractEntity<ConstructionMethod> {
//   @Column({ type: 'varchar' })
//   name: string;

//   @Column({ type: 'varchar' })
//   code: string;

//   @OneToMany(
//     () => OtherInfoApplication,
//     (otherInfoApplication) => otherInfoApplication.equipmentCapacitys,
//   )
//   otherInfoApplications: OtherInfoApplication[];
// }

// @Entity()
// export class EquipmentCapacity extends AbstractEntity<EquipmentCapacity> {
//   @Column({ type: 'varchar' })
//   name: string;

//   @Column({ type: 'varchar' })
//   code: string;

//   @OneToMany(
//     () => OtherInfoApplication,
//     (otherInfoApplication) => otherInfoApplication.equipmentCapacitys,
//   )
//   otherInfoApplications: OtherInfoApplication[];
// }
