import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';

@Injectable()
export class GenericSearch<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.andWhere(whereSearch);
    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

@Injectable()
export class GenericSearchWithRelations<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
    relations?: string[],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.where(whereSearch);

    // Include relations if specified
    if (relations && relations.length > 0) {
      for (const relationName of relations) {
        queryBuilder.leftJoinAndSelect(`alias.${relationName}`, relationName);
      }
    }

    const [items, totalCount] = await queryBuilder.getManyAndCount();
    console.log(items);
    return { items, totalCount };
  }
}
