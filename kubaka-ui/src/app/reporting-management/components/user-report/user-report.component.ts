import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
// import {
//   CellValueChangedEvent,
//   ColDef,
//   GridReadyEvent,
//   SelectionChangedEvent,
//   ValueFormatterParams,
//   GridApi, // Import GridApi
// } from 'ag-grid-community';
import {
  CellValueChangedEvent,
  ColDef,
  // ColumnApi,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  SelectionChangedEvent,
  ValueFormatterParams,
  CsvExportParams
} from 'ag-grid-community';
import { AgGridAngular } from 'ag-grid-angular';
import { environment } from 'src/environments/environment';
import { GlobalRefreshService } from 'src/app/shared/services/global-refresh.service';

@Component({
  selector: 'app-user-report',
  standalone: true,
  imports: [AgGridAngular],
  templateUrl: './user-report.component.html',
  styleUrls: ['./user-report.component.scss']
})
export class UserReportComponent implements OnInit, OnDestroy {

  // Variables to store the grid API and column API
  private gridApi!: GridApi;
  private refreshSub: any;

  rowData: any[] = [];
  colDefs: ColDef[] = [
    { field: 'firstName', width: 150, checkboxSelection: true },
    { field: 'lastName', width: 130 },
    { field: 'gender', width: 130 },
    { field: 'email', width: 225 },
    { field: 'createdAt', valueFormatter: this.dateFormatter },
    { field: 'role.name', width: 130 },
    { field: 'userType.name', width: 120 },
    { field: 'agency.name' },
  ];

  defaultColDef: ColDef = {
    filter: true,
    editable: true,
  };

  constructor(private http: HttpClient, private globalRefresh: GlobalRefreshService) { }

  ngOnInit(): void {
    this.refreshSub = this.globalRefresh.refreshInterval$.subscribe(() => {
      if (this.gridApi) {
        this.http
          .get<any[]>(environment.authUrl + 'user-management/users')
          .subscribe((data) => (this.rowData = data));
      }
    });
  }

  ngOnDestroy(): void {
    if (this.refreshSub) {
      this.refreshSub.unsubscribe();
    }
  }

  onGridReady(params: GridReadyEvent) {
    // Save the grid API reference
    this.gridApi = params.api;

    this.http
      .get<any[]>(environment.authUrl + 'user-management/users')
      .subscribe((data) => (this.rowData = data));
  }

  onSelectionChanged(event: SelectionChangedEvent) {

  }

  onCellValueChanged(event: CellValueChangedEvent) {
    console.log(`New Cell Value: ${event.value}`);
  }

  dateFormatter(params: ValueFormatterParams) {
    return new Date(params.value).toLocaleDateString('en-us', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Export to Excel using the grid API
  onBtnExport() {
    // const params = {
    //   fileName: 'user-report.xlsx',
    //   onlySelected: true, // Export only selected rows
    //   columnKeys: ['firstName', 'lastName', 'email'], // Export specific columns
    // };
    // // Use the grid API to export data as Excel
    // this.gridApi.exportDataAsExcel(params);
     const params: CsvExportParams = {
          fileName: 'users.csv',
          columnSeparator: ',',
        };
        this.gridApi.exportDataAsCsv(params);
  }
}
















// import { Component } from '@angular/core';
// import {
//   CellValueChangedEvent,
//   ColDef,
//   GridReadyEvent,
//   ICellRendererParams,
//   SelectionChangedEvent,
//   ValueFormatterParams,
//   GridApi
// } from 'ag-grid-community';
// import { AgGridAngular, ICellRendererAngularComp } from 'ag-grid-angular';
// import { HttpClient } from '@angular/common/http';
// import { environment } from 'src/environments/environment';


// export class MissionResultRenderer implements ICellRendererAngularComp {
//   // Init Cell Value
//   public value!: string;
//   agInit(params: ICellRendererParams): void {
//     this.value = params.value ? 'tick-in-circle' : 'cross-in-circle';
//   }

//   // Return Cell Value
//   refresh(params: ICellRendererParams): boolean {
//     this.value = params.value;
//     return true;
//   }
// }

// export class CompanyLogoRenderer implements ICellRendererAngularComp {
//   // Init Cell Value
//   public value!: string;
//   agInit(params: ICellRendererParams): void {
//     this.value = params.value;
//   }

//   // Return Cell Value
//   refresh(params: ICellRendererParams): boolean {
//     this.value = params.value;
//     return true;
//   }
// }

// @Component({
//   selector: 'app-user-report',
//   standalone: true,
//   imports: [AgGridAngular],
//   templateUrl: './user-report.component.html',
//   styleUrls: ['./user-report.component.scss']
// })
// export class UserReportComponent {
//   private gridApi!: GridApi;
//   // Return formatted date value
//   dateFormatter(params: ValueFormatterParams) {
//     return new Date(params.value).toLocaleDateString('en-us', {
//       weekday: 'long',
//       year: 'numeric',
//       month: 'short',
//       day: 'numeric',
//     });
//   }

//   // Row Data: The data to be displayed.
//   rowData: any[] = [];

//   // Column Definitions: Defines & controls grid columns.
//   colDefs: ColDef[] = [
//     {
//       field: 'firstName',
//       width: 150,
//       checkboxSelection: true,
//     },
//     {
//       field: 'lastName',
//       width: 130,
//       // cellRenderer: CompanyLogoRenderer,
//     },
//     {
//       field: 'gender',
//       width: 130,
//       // cellRenderer: CompanyLogoRenderer,
//     },

//     {
//       field: 'email',
//       width: 225,
//     },
//     {
//       field: 'createdAt',
//       valueFormatter: this.dateFormatter,
//     },
//     {
//       field: 'role.name',
//       width: 130,
//       // valueFormatter: (params) => {
//       //   return '£' + params.value.toLocaleString();
//       // },
//     },
//     {
//       field: 'userType.name',
//       width: 120,
//       // cellRenderer: MissionResultRenderer,
//     },
//     { field: 'agency.name' },
//   ];

//   // Default Column Definitions: Apply configuration across all columns
//   defaultColDef: ColDef = {
//     filter: true, // Enable filtering on all columns
//     editable: true, // Enable editing on all columns
//   };

//   // Load data into grid when ready
//   constructor(private http: HttpClient) { }
//   onGridReady(params: GridReadyEvent) {
//     this.gridApi = params.api;
//     this.http
//       .get<any[]>(
//         environment.authUrl + 'user-management/users'
//       )
//       .subscribe((data) => (this.rowData = data));
//   }

//   // Handle row selection changed event
//   onSelectionChanged = (event: SelectionChangedEvent) => {
//   };

//   // Handle cell editing event
//   onCellValueChanged = (event: CellValueChangedEvent) => {
//   };


//   onBtnExport() {
//     const params = {
//       fileName: 'user-report.xlsx',  // Set the file name
//       onlySelected: true,            // Export only selected rows if necessary
//       columnKeys: ['firstName', 'lastName', 'email']  // Export specific columns
//     };
//     // params.api.exportDataAsExcel(params);
//     this.gridApi.exportDataAsExcel(params);
//   }
// }
