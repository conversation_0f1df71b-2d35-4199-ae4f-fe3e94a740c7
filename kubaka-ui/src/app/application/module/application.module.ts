import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { AplicantApplicationsComponent } from '../components/aplicant-applications/aplicant-applications.component';
import { ApplicationDetailComponent } from '../components/application-detail/application-detail.component';
import { ApplicationRoadMapComponent } from '../components/application-road-map/application-road-map.component';
import { AssignedApplicationDetailComponent } from '../components/assignment-to-eng-architect/assigned-application-detail/assigned-application-detail.component';
import { AssignedApplicationsComponent } from '../components/assignment-to-eng-architect/assigned-applications/assigned-applications.component';
import { ProjectDetailContinueComponent } from '../components/assignment-to-eng-architect/project-detail-continue/project-detail-continue.component';
import { NewApplicationDetailComponent } from '../components/new-application/new-application-detail/new-application-detail.component';
import { NewApplicationComponent } from '../components/new-application/new-application/new-application.component';
import { DevelopmentDetailsComponent } from '../components/old-system-application/development-details/development-details.component';
import { ApplicationRoadMapOldSystemComponent } from '../components/old-system/application-road-map-old-system/application-road-map-old-system.component';
import { OldDevelopmentDetailsComponent } from '../components/old-system/old-development-details/old-development-details.component';
import { OldProjectDetailsComponent } from '../components/old-system/old-project-details/old-project-details.component';
import { OtherApplicationComponent } from '../components/other-related-application-components/other-application/other-application.component';
import { ProjectAttachmentComponent } from '../components/project-attachment/project-attachment.component';
import { ApplicationRoutingModule } from './application-routing.module';
import { PlotInfoComponent } from '../components/old-system-application/plot-info/plot-info.component';
import { ProjectDetailsComponent } from '../components/old-system-application/project-details/project-details.component';
import { OtherApplicationDocumentComponent } from '../components/other-related-application-components/other-application-document/other-application-document.component';
import { ProjectsComponent } from '../components/projects/projects.component';
import { ProjectModificationComponent } from '../components/new-application/project-modification/project-modification.component';
import { MyBoxComponent } from 'src/app/application-review-management/components/my-box/my-box.component';
import { OtherApplicationWithoutNcpComponent } from '../components/other-related-application-components/other-application-without-ncp/other-application-without-ncp.component';




@NgModule({
  declarations: [
    ApplicationRoadMapComponent,
    ApplicationRoadMapOldSystemComponent,
    OldDevelopmentDetailsComponent,
    OldProjectDetailsComponent,
    AplicantApplicationsComponent,
    PlotInfoComponent,
    ProjectDetailsComponent,
    DevelopmentDetailsComponent,
    ProjectAttachmentComponent,
    ApplicationDetailComponent,
    AssignedApplicationsComponent,
    OtherApplicationComponent,
    OtherApplicationDocumentComponent,
    AssignedApplicationDetailComponent,
    ProjectDetailContinueComponent,
    NewApplicationComponent,
    NewApplicationDetailComponent,
    ProjectsComponent,
    ProjectModificationComponent,
    MyBoxComponent,
    OtherApplicationWithoutNcpComponent
  ],
  imports: [
    CommonModule,
    ApplicationRoutingModule,
    SharedModule
  ]
})
export class ApplicationModule { }
