import { Component, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ngxCsv } from 'ngx-csv';
import { NgbdAdvancedSortableHeader } from "src/app/shared/directives/sortable.directive";
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-permit-checklist',
  templateUrl: './permit-checklist.component.html',
  styleUrls: ['./permit-checklist.component.scss']
})
export class PermitChecklistComponent {
  userForm!: UntypedFormGroup;
  submitted = false;
  lists: any[] = [];
  total!: number;
  @ViewChildren(NgbdAdvancedSortableHeader)
  headers!: QueryList<NgbdAdvancedSortableHeader>;
  page = 1;
  pageSize = 20;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  econtent: any = {};
  masterSelected!: boolean;
  outPutData: any = {};
  permitTypes: any[] = [];

  questionCategories: any[] = [];

  constructor(
    private modalService: NgbModal,
    public userService: UserMgtService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService
  ) {
    this.initiliazeForm();
    this.loadList();

    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => { this.permitTypes = data }
      )


    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.QUESTION_CATEGORIES)
      .subscribe(
        data => { this.questionCategories = data }
      )
  }


  ngOnDestroy() {
    this.userService.searchTerm = '';
  }

  loadList() {
    this.userService
      .findAllWithPath(environment.applicationUrl + APIURLPATH.PERMIT_QUESTION)
      .subscribe(
        (data) => {
          this.lists = data;
          this.content = data;
          setTimeout(() => {
            document.getElementById("elmLoader")?.classList.add("d-none");
          }, 1200);
          this.collectionSize = this.lists.length;
          this.total = this.lists.length;
          this.totalRecords = this.lists.length;
          this.startIndex = (this.page - 1) * this.pageSize + 1;
          this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
          if (this.endIndex > this.totalRecords) {
            this.endIndex = this.totalRecords;
          }
          this.lists = this.lists.slice(
            this.startIndex - 1,
            this.endIndex
          );
        });
  }


  ngOnInit(): void {
    this.outPutData.name = 'Permit Question';
  }


  initiliazeForm() {
    // this.userForm = this.formBuilder.group({
    //   id: [""],
    //   name: ['', [Validators.required]],
    //   code: ['', [Validators.required]],
    //   permitTypeId: ['', Validators.required],
    // });
    this.userForm = this.formBuilder.group({
      id: [""],
      code: ['', [Validators.required]],
      description: ['', [Validators.required]],
      comment: ['', [Validators.required]],
      permitTypeId: ['', Validators.required],
      questionCategoryId: ['', Validators.required],
      userId: ['']
    });
  }

  get form() {
    return this.userForm.controls;
  }



  openModal(content: any) {
    this.submitted = false;
    this.modalService.open(content, { size: "md", centered: true });
  }

  checkedValGet: any[] = [];
  onCheckboxChange(e: any) {
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }


  getPremiumData() {

    this.lists = this.content
      .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);

  }


  editDataGet(id: any, content: any) {
    this.submitted = false;

    this.modalService.open(content, { size: "md", centered: true });
    // var modelTitle = document.querySelector(".modal-title") as HTMLAreaElement;
    // modelTitle.innerHTML = "Edit User";
    // var updateBtn = document.getElementById("add-btn") as HTMLAreaElement;
    // updateBtn.innerHTML = "Update";

    this.userService.findOneWithPath(id, environment.applicationUrl + APIURLPATH.APPROVAL_STATUS).subscribe({
      next: (data: any) => {
        const users = data;
        this.econtent = users;
        this.userForm.controls['code'].setValue(this.econtent.code);
        this.userForm.controls['permitTypeId'].setValue(this.econtent.approvalLevelId);
        this.userForm.controls["description"].setValue(this.econtent.description);
        this.userForm.controls["comment"].setValue(this.econtent.comment);
        this.userForm.controls["questionCategoryId"].setValue(this.econtent.questionCategoryId);
        this.userForm.controls["id"].setValue(this.econtent.id);

        this.outPutData.type = 'edit';
        this.outPutData.name = 'Permit Question';
        // this.outPutData.isOpen = true;

      },
    });
  }


  saveUser() {
    if (this.userForm.invalid) {
    } else {
      if (this.userForm.get("id")?.value) {
        this.submitted = true;
        this.userService.patchAssetWithPath(this.userForm.value.id, this.userForm.value, environment.applicationUrl + APIURLPATH.PERMIT_QUESTION)
          .subscribe(
            (data: any) => {
              this.submitted = false;
            },)
      } else {
        // this.userService.saveAssetWithPath(this.userForm.value, environment.applicationUrl + APIURLPATH.CHECKLIST)
        this.userService.saveAssetWithPath(this.userForm.value, environment.applicationUrl + APIURLPATH.PERMIT_QUESTION)
          .subscribe(
            (data: any) => {
              this.utilService.showNotification(NOTIFICATION_COLOR.success, "Permit Question saved successfully", "bottom", "center");
              this.loadList();
              this.modalService.dismissAll();
            },
          )
      }
    }
  }


  deleteData(id: any) {
    if (id) {
      this.userService.deleteWithPath(id, environment.applicationUrl + APIURLPATH.PERMIT_QUESTION).subscribe({
        next: data => { this.loadList(); },
        error: err => {
        }
      });

    } else {

    }
  }


  deleteMultiple(content: any) {
    var checkboxes: any = document.getElementsByName("checkAll");
    var result;
    var checkedVal: any[] = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        result = checkboxes[i].value;
        checkedVal.push(result);
      }
    }
    if (checkedVal.length > 0) {
      this.modalService.open(content, { centered: true });
    } else {
      Swal.fire({
        text: "Please select at least one checkbox",
        confirmButtonColor: "#239eba",
      });
    }
    this.checkedValGet = checkedVal;
  }

  checkUncheckAll(ev: any) {
    this.lists.forEach((x: { state: any }) => (x.state = ev.target.checked));
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }


  saveValue(event: any) {
    this.loadList();
    this.modalService.dismissAll();
  }



  // Csv File Export
  csvFileExport() {
    var users = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalseparator: ".",
      showLabels: true,
      showTitle: true,
      title: "Check List",
      useBom: true,
      noDownload: false,
      headers: [
        "ID",
        "name",
        "code",
        "permitType",
        "questionCategory"
      ],
    };
    new ngxCsv(this.content, "checklist", users);
  }





}
