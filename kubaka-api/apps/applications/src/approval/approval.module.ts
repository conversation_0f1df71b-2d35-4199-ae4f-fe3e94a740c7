// import { PermitTypesRepository } from './../application.repository';
import { Module } from '@nestjs/common';
import { ApprovalService } from './approval.service';
import { ApprovalController } from './approval.controller';
import { DatabaseModule } from '@app/common';
import {
  ApprovalLevel,
  ApprovalStatus,
  PermitCheckList,
  ApplicationApprovalCheckList,
  ApplicationApproval,
  ApprovalDocument,
} from '../entities/approval.entity';
import {
  ApprovalLevelRepository,
  ApprovalStatusRepository,
  ApplicationApprovalRepository,
  PermitCheckListRepository,
  ApplicationApprovalCheckListRepository,
  ApprovalDocumentRepository,
} from './approval.repository';
import { GenericSearch } from './generic-search.service';
import {
  APPLICATIONS_SERVICE,
  AUTH_SERVICE,
  DOCUMENTS_SERVICE,
} from '@app/common/constants';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ApplicationModule } from '../application/application.module';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      // approval
      ApprovalLevel,
      ApprovalStatus,
      PermitCheckList,
      ApplicationApprovalCheckList,
      ApplicationApproval,
      ApprovalDocument,
    ]),

    // import app
    ApplicationModule,

    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },

      {
        name: APPLICATIONS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('APPLICATIONS_HOST'),
            port: configService.get('APPLICATIONS_PORT'),
          },
        }),
        inject: [ConfigService],
      },

      {
        name: DOCUMENTS_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('DOCUMENTS_HOST'),
            port: configService.get('DOCUMENTS_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [ApprovalController],
  providers: [
    ApprovalService,
    ApprovalLevelRepository,
    ApprovalStatusRepository,
    ApplicationApprovalRepository,
    PermitCheckListRepository,
    ApplicationApprovalCheckListRepository,
    ApprovalDocumentRepository,

    GenericSearch,
  ],
  exports: [ApprovalService],
})
export class ApprovalModule {}
