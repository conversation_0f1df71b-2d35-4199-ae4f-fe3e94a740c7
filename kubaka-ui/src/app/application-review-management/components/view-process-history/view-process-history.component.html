<!-- <p>view-process-history here!</p> -->
<div class="app-gen appl-details">
    <div class="app-gen-header genvline">
        <section class="kbk-x-s kbk-aend sp-2">
            <div class="land-info">
                <div class="land-info-item">
                    <h3>Project</h3>
                    <div class="aline">
                        <div class="form-out">
                            <label>{{applicationDetails?.application?.projects.projectName}}</label>
                            <span>{{applicationDetails?.application?.projects?.upi}}</span>
                        </div>
                        <div class="form-out">
                            <label>Application</label>
                            <span>{{applicationDetails?.application?.applicationName}}</span>
                        </div>
                        <div class="form-out">
                            <label>Agency</label>
                            <span> {{applicationDetails?.application?.agencyCode}}</span>
                        </div>
                        <div class="form-out">
                            <label>Permit Type</label>
                            <span>{{applicationDetails?.application?.permitTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Category Type</label>
                            <span>{{applicationDetails?.application?.categoryTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Build Type</label>
                            <span>{{applicationDetails?.application?.buildTypes?.name}}</span>
                        </div>
                        <div class="form-out">
                            <label>Submitted On</label>
                            <span>{{applicationDetails?.application?.submittedDate | date}}</span>
                        </div>
                    </div>
                </div>
                <div class="btns" *ngIf="!this.inputData?.isBackNotAllowed">
                    <button type="button" class="kbk-btn kbk-btn-sec"
                        [routerLink]="['/account/all-applications/lists']">Back</button>
                </div>
            </div>
        </section>
    </div>
</div>
<div class="app-dash">
    <div class="app-gen-header hide">
        <div class="genline">
            <div class="hide">
                <span class="hder" aria-label="header tittle">Project:
                    {{applicationDetails?.application?.projects.projectName}}</span><br />
                <span class="hder" aria-label="header tittle">Application:
                    {{applicationDetails?.application?.applicationName}}</span>
                <span class="hder-sm" aria-label="header tittle">UPI :
                    {{applicationDetails?.application?.projects?.upi}}</span>
                <span class="hder-sm" aria-label="header tittle">Agency :
                    {{applicationDetails?.application?.agencyCode}}</span>
                <span class="hder-sm" aria-label="header tittle">Permit Type :
                    {{applicationDetails?.application?.permitTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Category Type :
                    {{applicationDetails?.application?.categoryTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Build Type :
                    {{applicationDetails?.application?.buildTypes?.name}}</span>
                <span class="hder-sm" aria-label="header tittle">Application Submitted On :
                    {{applicationDetails?.application?.submittedDate | date}}</span>
            </div>
            <div class="btns hide" *ngIf="this.paramId">
                <button type="button" class="kbk-btn kbk-btn-sec"
                    [routerLink]="['/account/all-applications/lists']">Back</button>
            </div>
        </div>
    </div>

    <div class="dash-flex">
        <div class="app-l">
            <div class="process-steps">
                <ul class="step-list">
                    <!-- Create a flex component with h3 and download icon -->
                     <span class="process-steps-header">
                        <h3>Process History</h3>
                        <img src="assets/ikons/SVG/ikon-download.svg" alt="Download Icon" class="download-icon"
                            (click)="downloadProcessHistoryCSV()" />
                    </span>

                    <table class="process-history-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>User</th>
                                <th>User Type</th>
                                <th>Status</th>
                                <th>Duration</th>
                                <th>Activity</th>
                                <th class="action-column"></th>
                            </tr>
                        </thead>

                        <tbody>
                            <ng-container *ngFor="let row of processHistory; let i = index">
                                <tr class="process-row" [class.expanded]="isExpanded(row)" [class.odd]="i % 2 === 1"
                                    (click)="toggleExpanded(row)">
                                    <td class="timeline-cell">
                                        <div class="timeline-indicator"></div>
                                        <span>{{ row.date | date: 'short' }}</span>
                                    </td>
                                    <td>{{ row.user }}</td>
                                    <td>{{ row.userType }}</td>
                                    <td>
                                        <span class="status-badge" [class]="'status-' + row.status.toLowerCase()">
                                            {{ row.status }}
                                        </span>
                                    </td>
                                    <td>{{ row.timeDifference }}</td>
                                    <td>{{ row.activity }}</td>
                                    <td class="action-cell">
                                        <img [src]="isExpanded(row) ? 'assets/ikons/SVG/ikon-arrowUp.svg' : 'assets/ikons/SVG/ikon-arrowDown.svg'"
                                            alt="Toggle Arrow" class="toggle-icon" />
                                    </td>
                                </tr>
                                <tr *ngIf="isExpanded(row)" class="expanded-details">
                                    <td colspan="6">
                                        <div class="expanded-content">
                                            <div class="details-grid">
                                                <div class="detail-item">
                                                    <strong>PC Info:</strong>
                                                    <span>
                                                        <strong>IP Address:</strong> {{ row.ip }}
                                                    </span>
                                                    <span>
                                                        <strong>OS:</strong> {{ row.os }}
                                                    </span>
                                                    <span>
                                                        <strong>Browser:</strong> {{ row.browser }}
                                                    </span>
                                                </div>
                                                <div class="detail-item" *ngIf="row.other.checklist">
                                                    <strong>Checklist:</strong>
                                                    <ul class="checklist-list">
                                                        <li *ngFor="let item of getObjectEntries(row.other.checklist)">
                                                        <strong>{{ item.key | splitByCapital }}:</strong> {{ item.value || '-' }}   
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="detail-item" *ngIf="row.other.comment">
                                                    <strong>Comments:</strong>
                                                    <p>{{ row.other.comment }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>

                </ul>
            </div>
        </div>
    </div>




</div>