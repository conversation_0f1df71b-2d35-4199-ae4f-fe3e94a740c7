import { ChangeDetectorRef, Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AppConfig } from 'src/app/app.config';
import { StepperService } from '../../../services/stepper.service';

@Component({
  selector: 'app-plot-info',
  templateUrl: './plot-info.component.html',
  styleUrls: ['./plot-info.component.scss']
})
export class PlotInfoComponent {

  mapOptions: google.maps.MapOptions = {
    center: { lat: 38.9987208, lng: -77.2538699 },
    zoom: 14
  }
  marker = {
    position: { lat: 38.9987208, lng: -77.2538699 },
  }
  // mapOptions: google.maps.MapOptions = {
  //   center: { lat: 517996.67544586, lng: -4779396.55546599 },
  //   zoom: 25
  // }
  // marker = {
  //   position: { lat: 517996.67544586, lng: -4779396.55546599 },
  // }
  applicationDetail: any = {};
  userForm!: UntypedFormGroup;

  constructor(
    private cdr: ChangeDetectorRef,
    private stepperService: StepperService,
    private appConfig: AppConfig,
    private formBuilder: UntypedFormBuilder) {
    this.applicationDetail = JSON.parse(localStorage.getItem(this.appConfig.UPI_old_INFO) as any);
    if (this.applicationDetail?.isFromOldSystem) {
      this.applicationDetail.representative = {};
      this.applicationDetail.representative.address = {};
      this.applicationDetail.representative.address.province = {};
      this.applicationDetail.representative.address.district = {};
      this.applicationDetail.representative.address.sector = {};
      this.applicationDetail.representative.address.cell = {};
      this.applicationDetail.representative.address.village = {};

      if (this.applicationDetail.parcelLocation) {
        this.applicationDetail.parcelLocation.province = {};
        this.applicationDetail.parcelLocation.district = {};
        this.applicationDetail.parcelLocation.sector = {};
        this.applicationDetail.parcelLocation.cell = {};
        this.applicationDetail.parcelLocation.village = {};
      }
    }
  }

  ngOnInit(): void {
    if (this.applicationDetail?.isFromOldSystem) {
      this.applicationDetail = {}
      // this.applicationDetail.parcelLocation.province = {};
      // this.applicationDetail.representative = {};
      this.onNext();
      this.cdr?.detectChanges();
    } else if (!this.applicationDetail?.isFromOldSystem
      && this.applicationDetail?.bpmisProjectId
      && this.applicationDetail?.bpmisProjectId.toString().length > 0) {
      this.onNext();
      // this.cdr?.detectChanges();
    } else {
      this.userForm = this.formBuilder.group({
        upi: [this.applicationDetail?.upi, [Validators.required]],
      });
    }

  }

  onPrev(): void {
    // Disable Previous button when current step is 1
    if (this.stepperService.getCurrentStep() > 1) {
      this.stepperService.prevStep();
    }
  }

  onNext(): void {
    this.stepperService.nextStep();
  }

  isPrevButtonDisabled(): boolean {
    return this.stepperService.getCurrentStep() === 1;
  }

}
