import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { EmailDto, NotifyEmailDto } from './dto/notify-email.dto';
import { Repository } from 'typeorm';
import {
  ChatBoard,
  IssueCategory,
  Message,
} from './entities/notification.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateMessageDto } from './dto/create-message.dto';
import { UpdateReadStatusDto } from './dto/update-read-status.dto';
import {
  ChatBoardDto,
  ChatBoardReplyDto,
  IssueCategoryDto,
} from './dto/chat-board.dto';
import {
  ChatBoardRepository,
  IssueCategoryRepository,
} from './notifications.repository';

import axios from 'axios';

import config from '../config';
import { AUTH_SERVICE } from '@app/common/constants';
import { ClientProxy } from '@nestjs/microservices';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    private readonly issueCategoryRepository: IssueCategoryRepository,
    private readonly chatBoardRepository: ChatBoardRepository,

    @InjectRepository(ChatBoard)
    private chatBoardEntityRepository: Repository<ChatBoard>,

    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,
  ) {}

  private readonly transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      type: 'OAuth2',
      user: this.configService.get('SMTP_USER'),
      clientId: this.configService.get('GOOGLE_OAUTH_CLIENT_ID'),
      clientSecret: this.configService.get('GOOGLE_OAUTH_CLIENT_SECRET'),
      refreshToken: this.configService.get('GOOGLE_OAUTH_REFRESH_TOKEN'),
    },
  });

  async getAllUserByAgency(agencyId: string) {
    return this.authService
      .send<any>({ cmd: 'userData' }, agencyId)
      .toPromise();
  }

  async getAllUser(agencyId: string) {
    return this.authService
      .send<any>({ cmd: 'userData' }, agencyId)
      .toPromise();
  }

  async checkAgencyDataById(agencyId: string) {
    return this.authService
      .send<any>({ cmd: 'checkAgencyDataById' }, agencyId)
      .toPromise();
  }

  async notifyEmail({ email, text }: NotifyEmailDto) {
    await this.transporter.sendMail({
      from: this.configService.get('SMTP_USER'),
      to: email,
      subject: 'KUBAKA Notification',
      text,
    });
  }

  private readonly transporter2 = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    auth: {
      user: '<EMAIL>',
      pass: 'cgnmqxzsbuzqlktt',
    },
  });

  async notifyEmail2({ email, subject, text, html }: EmailDto) {
    await this.transporter.sendMail({
      from: '<EMAIL>',
      to: email,
      subject: subject,
      text,
      html,
    });
  }

  // Send a message
  async sendMessage(createMessageDto: CreateMessageDto): Promise<Message> {
    const message = this.messageRepository.create(createMessageDto);
    return this.messageRepository.save(message);
  }

  // Get conversation between two users
  async getConversation(userId1: string, userId2: string): Promise<Message[]> {
    return this.messageRepository.find({
      where: [
        { senderId: userId1, receiverId: userId2 },
        { senderId: userId2, receiverId: userId1 },
      ],
      order: { createdAt: 'ASC' },
    });
  }

  // get all messages
  async getAllConversation() {
    return this.messageRepository.createQueryBuilder('message').getMany();
  }

  // get unread messages
  async getConversationUnread(userId: string): Promise<{ number: number }> {
    const count = await this.messageRepository
      .createQueryBuilder('message')
      .where('message.senderId = :userId', { userId })
      .orWhere('message.receiverId = :userId', { userId })
      .andWhere('message.isRead = :isRead', { isRead: false })
      .getCount();

    return { number: count };
  }
  // get unread messages
  async getConversationUnreadReceiver(
    userId: string,
  ): Promise<{ number: number }> {
    const count = await this.messageRepository
      .createQueryBuilder('message')
      .where('message.receiverId = :userId', { userId })
      .andWhere('message.isRead = :isRead', { isRead: false })
      .getCount();

    return { number: count };
  }

  // Update message read status
  async updateReadStatus(
    updateReadStatusDto: UpdateReadStatusDto,
  ): Promise<Message> {
    const { messageId, isRead } = updateReadStatusDto;
    const message = await this.messageRepository.findOne({
      where: { id: messageId },
    });

    if (!message) {
      throw new Error('Message not found');
    }

    message.isRead = isRead;
    message.readAt = isRead ? new Date() : null;

    return this.messageRepository.save(message);
  }

  // Issue Category
  async createIssueCategory(issueCategoryDto: IssueCategoryDto) {
    const issueCategory = new IssueCategory({
      ...issueCategoryDto,
    });
    return this.issueCategoryRepository.create(issueCategory);
  }

  async findAllIssueCategory() {
    return this.issueCategoryRepository.find({});
  }

  async findOneIssueCategory(id: string) {
    return this.issueCategoryRepository.findOne({ id });
  }

  async updateIssueCategory(id: string, issueCategoryDto: IssueCategoryDto) {
    return this.issueCategoryRepository.findOneAndUpdate(
      { id },
      issueCategoryDto,
    );
  }

  async removeIssueCategory(id: string) {
    return this.issueCategoryRepository.findOneAndDelete({ id });
  }

  // ChatBoard
  async createChatBoard(chatBoardDto: ChatBoardDto) {
    const dataFromDb = await this.issueCategoryRepository.findOne({
      id: chatBoardDto.issueCategoryId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    const chatBoard = new ChatBoard({
      ...chatBoardDto,
      issueCategory: (chatBoardDto.issueCategoryId = {
        id: dataFromDb.id,
      } as any),
    });
    return this.chatBoardRepository.create(chatBoard);
  }

  // ChatBoard
  async createChatBoardAndSendEmail(chatBoardDto: ChatBoardDto) {
    const dataFromDb = await this.issueCategoryRepository.findOne({
      id: chatBoardDto.issueCategoryId,
    });

    if (!dataFromDb) {
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );
    }

    

    const chatBoard = new ChatBoard({
      ...chatBoardDto,
      issueCategory: { id: dataFromDb.id } as any,
    });

    // Save the chat board to the database
    const savedChatBoard = await this.chatBoardRepository.create(chatBoard);

    // After saving the chat board, prepare the email data
    const requestData = {
      sender_name: 'KUBAKA MIS',
      sender_email: `${config.notification.senderEmail}`,
      // sender_email: '<EMAIL>',
      receiver_name: 'KUBAKA User',
      receiver_email: '<EMAIL>',
      subject: 'Assistance Required for Issue on Kubaka MIS',
      message: `
        Dear Kubaka Team,
        <br><br>
        We have received a message from ${chatBoardDto.senderNames} ${chatBoardDto.phoneNumber}, regarding an issue they encountered on the Kubaka MIS system. <br><br>
        Here is the message : <br><br>
        ${chatBoardDto.content}<br><br>
        Best regards, <br>
        Kubaka Team
      `,
    };

    try {
      // Send the email after saving the chat board
      const emailResponse = await axios.post(
        // 'https://notification.kubaka.gov.rw/email/send/',
        `${config.notification.emailAPI}/email/send/`,

        requestData,
      );
      console.log('Email sent successfully:', emailResponse);
    } catch (error) {
      console.error('Error sending email:', error);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return savedChatBoard;
  }

  // async findAllChatBoard() {
  //   return this.chatBoardRepository.findAll({
  //     relations: { issueCategory: true },
  //   });
  // }

  async findAllChatBoard() {
    // Step 1: Fetch all chat boards with issue category
    const chatBoards = await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .getMany();

    // Step 2: Append agency name to each chat board
    const chatBoardsWithAgencyName = await Promise.all(
      chatBoards.map(async (chatBoard) => {
        if (chatBoard.agencyId) {
          try {
            const agency = await this.checkAgencyDataById(chatBoard.agencyId);
            return {
              ...chatBoard,
              agencyName: agency?.name || null,
            };
          } catch (error) {
            console.error(
              `Failed to fetch agency for ID ${chatBoard.agencyId}:`,
              error,
            );
            return {
              ...chatBoard,
              agencyName: null,
            };
          }
        } else {
          return {
            ...chatBoard,
            agencyName: null,
          };
        }
      }),
    );

    return chatBoardsWithAgencyName;
  }

  async findAllChatBoardDistinct() {
    // Await the result of the query
    const chatBoards = await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .distinctOn(['chatBoard.phoneNumber']) // Ensures uniqueness by phone number
      .orderBy('chatBoard.phoneNumber') // Ensures correct distinct behavior
      .addOrderBy('chatBoard.createdAt', 'DESC') // Gets the latest entry per phone number
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .getMany(); // This returns a Promise, so we must await it

    // Step 2: Append agency name to each chat board
    const chatBoardsWithAgencyName = await Promise.all(
      chatBoards.map(async (chatBoard) => {
        if (chatBoard.agencyId) {
          try {
            const agency = await this.checkAgencyDataById(chatBoard.agencyId);
            return {
              ...chatBoard,
              agencyName: agency?.name || null,
            };
          } catch (error) {
            console.error(
              `Failed to fetch agency for ID ${chatBoard.agencyId}:`,
              error,
            );
            return {
              ...chatBoard,
              agencyName: null,
            };
          }
        } else {
          return {
            ...chatBoard,
            agencyName: null,
          };
        }
      }),
    );

    return chatBoardsWithAgencyName;
  }

  // async findAllChatBoardDistinct() {
  //   return this.chatBoardEntityRepository
  //     .createQueryBuilder('chatBoard')
  //     .distinctOn(['chatBoard.phoneNumber']) // Ensures uniqueness by phone number
  //     .orderBy('chatBoard.phoneNumber') // Ensures correct distinct behavior
  //     .addOrderBy('chatBoard.createdAt', 'DESC') // Gets the latest entry per phone number
  //     .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
  //     .getMany();
  // }

  async findOneChatBoard(id: string) {
    return this.chatBoardRepository.findOne({ id });
  }

  async updateChatBoard(id: string, chatBoardDto: ChatBoardDto) {
    return this.chatBoardRepository.findOneAndUpdate({ id }, chatBoardDto);
  }

  // search on chatboard by phone number
  async findAllChatBoardByAPhone(phoneNumber: any) {
    const chatBoards = await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .where('chatBoard.phoneNumber = :phoneNumber', { phoneNumber })
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .orderBy('chatBoard.createdAt')
      .getMany();

    if (chatBoards.length === 0) {
      throw new HttpException('Phone number not found', HttpStatus.BAD_REQUEST);
    }

    return chatBoards;
  }

  // report on chatboard
  async findAllChatBoardByAgency(agencyId: any) {
    return await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .where('chatBoard.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .orderBy('chatBoard.createdAt')
      .getMany();
  }

  // report on chatboard distinct bu phone number
  async findAllChatBoardByAgencyDistinct(agencyId: any) {
    return await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .distinctOn(['chatBoard.phoneNumber']) // Ensures uniqueness by phone number
      .where('chatBoard.agencyId = :agencyId', { agencyId })
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .orderBy('chatBoard.phoneNumber', 'ASC') // Ensures correct distinct behavior
      .addOrderBy('chatBoard.createdAt', 'DESC') // Gets the latest entry per phone number
      .getMany();
  }

  async findAllChatBoardByIssueCategory(issueCategoryId: any) {
    return await this.chatBoardEntityRepository
      .createQueryBuilder('chatBoard')
      .where('chatBoard.issueCategoryId = :issueCategoryId', {
        issueCategoryId,
      })
      .leftJoinAndSelect('chatBoard.issueCategory', 'issueCategory')
      .orderBy('chatBoard.createdAt')
      .getMany();
  }

  // get all users by agency with the unread messages number
  // get all conversations and include unread messages for users
  // async getAllUserWithUnreadConversation(agencyId: string) {
  //   try {
  //     // Step 1: Fetch users by agency ID using the microservice event
  //     const usersResponse = await this.authService
  //       .send<any>({ cmd: 'getAllUserByAgency' }, agencyId)
  //       .toPromise();

  //     if (!usersResponse.success) {
  //       console.error('Failed to fetch users:', usersResponse.message);
  //       throw new Error('Failed to fetch users.');
  //     }

  //     const users = usersResponse.data;

  //     // If no users are found, return an empty list with 0 unread messages
  //     if (users.length === 0) {
  //       return {
  //         success: true,
  //         message: 'No users found for the specified agency ID.',
  //         data: [],
  //       };
  //     }

  //     // Step 2: Fetch unread messages
  //     const userIds = users.map((user) => user.id);

  //     const unreadMessages = await this.messageRepository
  //       .createQueryBuilder('message')
  //       .select(['message.senderId', 'message.receiverId'])
  //       .addSelect('COUNT(*)', 'unreadCount')
  //       .where('message.isRead = false')
  //       .andWhere(
  //         '(message.senderId IN (:...userIds) OR message.receiverId IN (:...userIds))',
  //         { userIds },
  //       )
  //       .groupBy('message.senderId, message.receiverId')
  //       .getRawMany();

  //     // Step 3: Map unread messages to users
  //     const usersWithUnreadMessages = users.map((user) => {
  //       // Count unread messages for this user as sender or receiver
  //       const unreadAsSender = unreadMessages.find(
  //         (msg) => msg.senderId === user.id,
  //       );
  //       const unreadAsReceiver = unreadMessages.find(
  //         (msg) => msg.receiverId === user.id,
  //       );

  //       const totalUnreadCount =
  //         (unreadAsSender ? parseInt(unreadAsSender.unreadCount, 10) : 0) +
  //         (unreadAsReceiver ? parseInt(unreadAsReceiver.unreadCount, 10) : 0);

  //       return {
  //         ...user,
  //         unreadMessageCount: totalUnreadCount,
  //       };
  //     });

  //     // Return the response
  //     return {
  //       success: true,
  //       message: 'Users with unread messages fetched successfully',
  //       data: usersWithUnreadMessages,
  //     };
  //   } catch (error) {
  //     console.error('Error in getAllConversation:', error);
  //     throw new Error('Failed to fetch conversations. Please try again later.');
  //   }
  // }
  async getAllUserWithUnreadConversation(agencyId: string) {
    try {
      // Step 1: Fetch users by agency ID using the microservice event
      const usersResponse = await this.authService
        .send<any>({ cmd: 'getAllUserByAgency' }, agencyId)
        .toPromise();

      if (!usersResponse.success) {
        console.error('Failed to fetch users:', usersResponse.message);
        throw new Error('Failed to fetch users.');
      }

      const users = usersResponse.data;

      if (users.length === 0) {
        return {
          success: true,
          message: 'No users found for the specified agency ID.',
          data: [],
        };
      }

      // Step 2: Fetch unread messages where the user is a receiver
      const userIds = users.map((user) => user.id);

      const unreadMessages = await this.messageRepository
        .createQueryBuilder('message')
        .select('message.receiverId', 'receiverId')
        .addSelect('COUNT(*)', 'unreadCount')
        .where('message.isRead = false')
        .andWhere('message.receiverId IN (:...userIds)', { userIds })
        .groupBy('message.receiverId')
        .getRawMany();

      // Step 3: Map unread messages to users
      const usersWithUnreadMessages = users.map((user) => {
        const unreadAsReceiver = unreadMessages.find(
          (msg) => msg.receiverId === user.id,
        );

        const totalUnreadCount = unreadAsReceiver
          ? parseInt(unreadAsReceiver.unreadCount, 10)
          : 0;

        return {
          ...user,
          unreadMessageCount: totalUnreadCount,
        };
      });

      // Return the response
      return {
        success: true,
        message: 'Users with unread messages fetched successfully',
        data: usersWithUnreadMessages,
      };
    } catch (error) {
      console.error('Error in getAllConversation:', error);
      throw new Error('Failed to fetch conversations. Please try again later.');
    }
  }
  async getAllApplicantWithUnreadMessage() {
    try {
      // Step 1: Fetch users using the event
      const usersResponse = await this.authService
        .send<any, any>({ cmd: 'getAllApplicants' }, {}) // Pass an empty object as the second argument
        .toPromise();

      if (!usersResponse.success) {
        console.error('Failed to fetch users:', usersResponse.message);
        throw new Error('Failed to fetch users.');
      }

      const users = usersResponse.data;

      if (users.length === 0) {
        return {
          success: true,
          message: 'No users found for the specified agency ID.',
          data: [],
        };
      }

      // Step 2: Fetch unread messages where the user is a receiver
      const userIds = users.map((user) => user.id);

      const unreadMessages = await this.messageRepository
        .createQueryBuilder('message')
        .select('message.receiverId', 'receiverId')
        .addSelect('COUNT(message.id)', 'unreadCount')
        .where('message.isRead = false')
        .andWhere('message.receiverId IN (:...userIds)', { userIds })
        .groupBy('message.receiverId')
        .getRawMany();

      // Step 3: Map unread messages to users
      const usersWithUnreadMessages = users.map((user) => {
        const unreadAsReceiver = unreadMessages.find(
          (msg) => msg.receiverId === user.id,
        );

        return {
          ...user,
          unreadMessageCount: unreadAsReceiver
            ? parseInt(unreadAsReceiver.unreadCount, 10)
            : 0,
        };
      });

      // Return the response
      return {
        success: true,
        message: 'Users with unread messages fetched successfully',
        data: usersWithUnreadMessages,
      };
    } catch (error) {
      console.error('Error in getAllConversation:', error);
      throw new Error('Failed to fetch conversations. Please try again later.');
    }
  }

  // ChatBoard without SMS
  async createChatBoardReply(chatBoardReplyDto: ChatBoardReplyDto) {
    const dataFromDb = await this.issueCategoryRepository.findOne({
      id: chatBoardReplyDto.issueCategoryId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    const chatBoard = new ChatBoard({
      ...chatBoardReplyDto,
      issueCategory: (chatBoardReplyDto.issueCategoryId = {
        id: dataFromDb.id,
      } as any),
    });

    const savedChatBoard = await this.chatBoardRepository.create(chatBoard);
    return {
      statusCode: HttpStatus.OK,
      message: 'Message created successfully',
      data: savedChatBoard,
    };
  }
  // ChatBoard
  async createChatBoardReplyWithSMS(chatBoardReplyDto: ChatBoardReplyDto) {
    const dataFromDb = await this.issueCategoryRepository.findOne({
      id: chatBoardReplyDto.issueCategoryId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );

    const chatBoard = new ChatBoard({
      ...chatBoardReplyDto,
      issueCategory: (chatBoardReplyDto.issueCategoryId = {
        id: dataFromDb.id,
      } as any),
    });

    const savedChatBoard = await this.chatBoardRepository.create(chatBoard);

    try {
      const [smsResponse] = await Promise.all([
        // sending sms responses
        axios.post(
          `${config.notification.smsAPI}/sms/send/`,
          // 'https://notification.kubaka.gov.rw/sms/send',
          {
            msisdn: chatBoardReplyDto.phoneNumber,
            message: `${chatBoardReplyDto.content} \nBest regards\nKUBAKA Team`,
          },
        ),
      ]);

      console.log('SMS sent successfully:', smsResponse);
      return {
        statusCode: HttpStatus.OK,
        message: 'Message created successfully',
        data: savedChatBoard,
      };
    } catch (error) {
      console.error('Error sending email:', error.response.data);
      throw new HttpException(
        'Email not sent',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
