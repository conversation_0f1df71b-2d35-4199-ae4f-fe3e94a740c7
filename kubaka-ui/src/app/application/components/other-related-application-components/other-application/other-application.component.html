<div class="stepper-content" *ngIf="!outputData.isDocumenting">
    <!-- <div class="stepper-header">
        <div class="upper-counter"> Step <span>1</span> / <span>1</span> </div>
    </div> -->
    <div class="stepper-body">
        <div>
            <!-- Step content goes here -->
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"otherApplication.projectDetails" | translate }}</h2>
                </div>

                <form [formGroup]="pageForm" autocomplete="off" *ngIf="currentUser.data.user.userType.code === 'LO'">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToDoItYouself"
                                        (change)="changeForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"otherApplication.doItYourself" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToAssignToEngineer"
                                        (change)="changeAssignForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"otherApplication.assignToEngineerArchitect" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                <form (ngSubmit)="onSubmit()" *ngIf="!outputData.isLoading" [formGroup]="userForm" autocomplete="off">

                    <section>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card">
                                <!-- <h3>{{"otherApplication.projectDetails" | translate }}</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.projectBrief" | translate }} </label>
                                        <span class="form-out_txtarea">{{projectDetails.projectDescription}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedMonthlyWaterM3" | translate }}</label>
                                        <!-- <span>{{projectDetails.waterConsumption ? projectDetails.waterConsumption :
                                            '0'}}</span> -->
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="waterConsumption" name="waterConsumption"
                                                    formControlName="waterConsumption" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.distanceToLandline" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="DistanceToTheNearestLandIn"
                                                    name="DistanceToTheNearestLandIn"
                                                    formControlName="DistanceToTheNearestLandIn" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.DistanceToTheNearestLandIn ?
                                            projectDetails.DistanceToTheNearestLandIn : '0'}}</span> -->
                                    </div>
                                </div>
                            </div>
                            <div class="appl-info-card">
                                <!-- <h3>Project Estimates Details</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedProjectCostUsd" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="ProjectCostInUSD" name="ProjectCostInUSD"
                                                    formControlName="ProjectCostInUSD" required>
                                            </div>
                                        </div>

                                        <!-- <span>${{projectDetails.ProjectCostInUSD ? projectDetails.ProjectCostInUSD :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedProjectCostRwf" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="ProjectCostInRwf" name="ProjectCostInRwf"
                                                    formControlName="ProjectCostInRwf" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.ProjectCostInRwf ? projectDetails.ProjectCostInRwf :
                                            '0'}} Rwf</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedMonthlyElectricityKwh" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="electricityConsumption"
                                                    name="electricityConsumption"
                                                    formControlName="electricityConsumption" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.electricityConsumption ?
                                            projectDetails.electricityConsumption : '0'}}</span> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card fx-2">
                                <h3>{{"otherApplication.developmentDetails" | translate }}</h3>
                                <div class="kbk-x kbk-wrap-4">
                                    <div class="form-out">
                                        <label>{{"otherApplication.plotSizeSquareMeters" | translate }}</label>

                                        <span>{{projectDetails.plotSize ? projectDetails.plotSize : '0'}} m²</span>
                                    </div>

                                    <div class="form-out">
                                        <label>{{"otherApplication.proposedNumberOfFloorsGPlus" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="numberOfFloor" name="numberOfFloor"
                                                    formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()"
                                                    required>
                                            </div>
                                        </div>
                                        <!-- <span>G+{{projectDetails.numberOfFloor ? projectDetails.numberOfFloor :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.numberOfParkingSpaces" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                                    formControlName="numberOfParkingSpace" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.numberOfParkingSpace ?
                                            projectDetails.numberOfParkingSpace : '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.priceOfDwellingUnit" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="priceOfDwellingUnitRwf"
                                                    name="priceOfDwellingUnitRwf"
                                                    formControlName="priceOfDwellingUnitRwf" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.priceOfDwellingUnitRwf ?
                                            projectDetails.priceOfDwellingUnitRwf : '0'}}</span> -->
                                    </div>


                                    <div class="form-out">
                                        <label>{{"otherApplication.builtUpArea" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="number" id="buildUpArea" name="buildUpArea"
                                                    formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                                    (keyup)="calculateNumberOfFlow()" noNegative required
                                                    [disabled]="userForm.value.buildTypeId?.length < 0 || userForm.value.buildTypeId?.length == 0"
                                                    required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.buildUpArea ? projectDetails.buildUpArea : '0'}} </span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.grossFloorArea" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="grossFloorArea" name="grossFloorArea"
                                                    formControlName="grossFloorArea" readonly required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.grossFloorArea ? projectDetails.grossFloorArea :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.percentageSpaceUse" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="percentageSpaceUse" name="percentageSpaceUse"
                                                    formControlName="percentageSpaceUse" readonly required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.buildUpArea ? projectDetails.buildUpArea : '0'}} </span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.numberOfDwellingUnits" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="numberOfDwellingUnits"
                                                    name="numberOfDwellingUnits" formControlName="numberOfDwellingUnits"
                                                    required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.buildUpArea ? projectDetails.buildUpArea : '0'}} </span> -->
                                    </div>


                                    <!-- <div class="form-out">
                                        <label>Estimate % of space</label>
                                        <span>60%</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.priceOfDwellingUnit" | translate }}</label>
                                        <span>$60</span>
                                    </div> -->
                                    <div class="form-out">
                                        <label>{{"otherApplication.descriptionOperations" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="DescriptionOfOperation"
                                                    name="DescriptionOfOperation"
                                                    formControlName="DescriptionOfOperation" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.buildUpArea ? projectDetails.buildUpArea : '0'}} </span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.capacityInfoPeopleSeats" | translate }}</label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="capacityInformation" name="capacityInformation"
                                                    formControlName="capacityInformation"
                                                    (keyup)="calculateNumberOfPeople()" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.capacityInformation ? projectDetails.capacityInformation
                                            : '0'}}</span> -->
                                    </div>
                                    <!-- <div class="form-out">
                                        <label>For Industrial projects</label>
                                        <span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut excepturi hic, eaque
                                            velit a quod adipisci accusamus exercitationem? Qui, libero.</span>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </section>
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"otherApplication.permitType" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select name="permitTypeId" id="permitTypeId" formControlName="permitTypeId"
                                        (change)="getPermitTypeObject($event)" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplication.buildingType" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled && !this.otherParams.permitTypeId"
                                        name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplication.category" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled || !this.otherParams.permitTypeId"
                                        name="categoryTypeId" id="categoryTypeId" formControlName="categoryTypeId"
                                        required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplication.upi" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="upi" name="upi" formControlName="upi" readonly required>
                                </div>
                            </div>
                        </div>
                        <!-- <h1>Test : {{doYouWantToAssignToEngineer}}</h1>
                        <h1>User : {{currentUser.data.user.userType.code}}</h1>
                        <h1>Is Rdb : {{formAccess.isRdbIAEchecked}}</h1>
                        <h1>Category: {{userForm.value.categoryCode}}</h1> -->
                        <!-- <div class="form-set">

                        </div> -->
                        <!-- This is the component that will differenciate additionally properties based on permit type selected -->
                        <app-other-application-permit-answer *ngIf="userForm.value.permitTypeId &&
                        isAccessQuestion" [inputData]="
                            {userForm: userForm.value, permitTypes: this.permitTypes,
                                otherApplicationValue: otherApplicationValue
                            }" (backToParent)="updateOtherInfo($event)"></app-other-application-permit-answer>






                        <div class="alert alert-danger" role="alert" *ngIf="!formAccess.isIAEverified &&
                    formAccess.isRdbIAEchecked
                    && (userForm.value.categoryCode === 'CAT5' ||
                    userForm.value.categoryCode === 'CAT4')">{{"otherApplication.dataNotFoundEIA" | translate }} </div>


                        <!-- <app-eia-form-checker *ngIf="
                (userForm.value.categoryCode === 'CAT5' ||
                userForm.value.categoryCode === 'CAT4') && this.projectDetails.upi"
                            [inputData]="{upi: this.projectDetails.upi}"
                            (backToParent)="comeAndAllowToAssign($event)"></app-eia-form-checker> -->
                        <app-eia-form-checker *ngIf="
                            (userForm.value.categoryCode === 'CAT5' ||
                            userForm.value.categoryCode === 'CAT4') && this.projectDetails.upi"
                            [inputData]="{upi: this.projectDetails.upi}"
                            (backToParent)="comeAndAllowToAssign($event)"></app-eia-form-checker>



                        <!-- <app-assign-to-engineer-form *ngIf="
                            doYouWantToAssignToEngineer ||
                            userForm.value.categoryCode === 'CAT3' ||
                                ((userForm.value.categoryCode === 'CAT4' ||
                                userForm.value.categoryCode === 'CAT5')
                                && formAccess.isRdbIAEchecked && currentUser.data.user.role.code !== 'ENG')
                                    " [inputData]="{
                                projectId: userForm.value.projectId,
                                allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form> -->


                        <h4 *ngIf="currentUser.data.user.role.code === 'APP' &&
                                (projectDetails?.projectStatus?.code === 'PAPRV'
                                    || projectDetails?.projectStatus?.code === 'PASGD'
                                )" style="color: red;">{{"otherApplication.projectAlreadyAssigned" | translate }}
                        </h4>


                        <app-assign-to-engineer-form *ngIf="
                    (
                        (doYouWantToAssignToEngineer ||
                        (userForm.value.categoryCode === 'CAT3' ||
                         userForm.value.categoryCode === 'CAT4' ||
                         userForm.value.categoryCode === 'CAT5')

                    )



                        &&  (currentUser.data.user.userType.code !== 'ENG')



                    )&& projectDetails?.projectStatus?.code !== 'PAPRV'
                    && projectDetails?.projectStatus?.code !== 'PASGD'
                            " [inputData]="{
                        projectId: userForm.value.projectId,
                        allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form>
                    </div>
                    <div class="step-panel_footer" *ngIf="
                (((userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2') &&
                (!formAccess.doYouWantToShareToEngineer))
                && (!this.pageForm.value.doYouWantToAssignToEngineer)) && (currentUser.data.user.role.code === 'APP' &&
                currentUser.data.user.userType.code !== 'ENG'
                )">
                        <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()">{{"otherApplication.cancel" | translate }}</button>
                        <button class="kbk-btn kbk-btn-main" type="submit" [disabled]="userForm.invalid">{{"otherApplication.next" | translate }} </button>
                    </div>

                    <!-- *ngIf="projectDetails.applyOtherApplication" -->

                    <div class="step-panel_footer" *ngIf="
                (userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2' ||
                userForm.value.categoryCode === 'CAT3'||
                userForm.value.categoryCode === 'CAT4' ||
                userForm.value.categoryCode === 'CAT5') &&  (currentUser.data.user.userType.code === 'ENG' || currentUser.data.user.userType.code === 'ARC' )">
                        <button (click)="cancel()">{{"otherApplication.prev" | translate }}</button>
                        <button type="submit" [disabled]="userForm.invalid">{{"otherApplication.next" | translate }}</button>
                    </div>
                </form>



                <div class="appl-info" *ngIf="!outputData.isLoading" style="display: none;">
                    <section>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card">
                                <!-- <h3>{{"otherApplication.projectDetails" | translate }}</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.projectBrief" | translate }}</label>
                                        <span class="form-out_txtarea">{{projectDetails.projectDescription}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedMonthlyWaterM3" | translate }}<span
                                                class="estrx">*</span></label>
                                        <!-- <span>{{projectDetails.waterConsumption ? projectDetails.waterConsumption :
                                            '0'}}</span> -->
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="waterConsumption" name="waterConsumption"
                                                    formControlName="waterConsumption" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.distanceToLandline" | translate }}<span
                                                class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="DistanceToTheNearestLandIn"
                                                    name="DistanceToTheNearestLandIn"
                                                    formControlName="DistanceToTheNearestLandIn" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.DistanceToTheNearestLandIn ?
                                            projectDetails.DistanceToTheNearestLandIn : '0'}}</span> -->
                                    </div>
                                </div>
                            </div>
                            <div class="appl-info-card">
                                <!-- <h3>Project Estimates Details</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedProjectCostUsd" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="ProjectCostInUSD" name="ProjectCostInUSD"
                                                    formControlName="ProjectCostInUSD" required>
                                            </div>
                                        </div>

                                        <!-- <span>${{projectDetails.ProjectCostInUSD ? projectDetails.ProjectCostInUSD :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedProjectCostRwf" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="ProjectCostInRwf" name="ProjectCostInRwf"
                                                    formControlName="ProjectCostInRwf" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.ProjectCostInRwf ? projectDetails.ProjectCostInRwf :
                                            '0'}} Rwf</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.estimatedMonthlyElectricityKwh" | translate }}<span
                                                class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="electricityConsumption"
                                                    name="electricityConsumption"
                                                    formControlName="electricityConsumption" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.electricityConsumption ?
                                            projectDetails.electricityConsumption : '0'}}</span> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card fx-2">
                                <h3>{{"otherApplication.developmentDetails" | translate }}</h3>
                                <div class="kbk-x kbk-wrap-4">
                                    <div class="form-out">
                                        <label>{{"otherApplication.plotSizeSquareMeters" | translate }}<span class="estrx">*</span></label>

                                        <span>{{projectDetails.plotSize ? projectDetails.plotSize : '0'}} m²</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.grossFloorArea" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="grossFloorArea" name="grossFloorArea"
                                                    formControlName="grossFloorArea" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.grossFloorArea ? projectDetails.grossFloorArea :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.proposedNumberOfFloorsGPlus" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="number" id="numberOfFloor" name="numberOfFloor"
                                                    formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()"
                                                    required>
                                            </div>
                                        </div>
                                        <!-- <span>G+{{projectDetails.numberOfFloor ? projectDetails.numberOfFloor :
                                            '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.numberOfParkingSpaces" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                                    formControlName="numberOfParkingSpace" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.numberOfParkingSpace ?
                                            projectDetails.numberOfParkingSpace : '0'}}</span> -->
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.priceOfDwellingUnit" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="priceOfDwellingUnitRwf"
                                                    name="priceOfDwellingUnitRwf"
                                                    formControlName="priceOfDwellingUnitRwf" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.priceOfDwellingUnitRwf ?
                                            projectDetails.priceOfDwellingUnitRwf : '0'}}</span> -->
                                    </div>

                                    <div class="form-out">

                                        <label>{{"otherApplication.grossFloorArea" | translate }}<span class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="grossFloorArea" name="grossFloorArea"
                                                    formControlName="grossFloorArea" required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.grossFloorArea ? projectDetails.grossFloorArea :
                                            '0'}}</span> -->
                                    </div>
                                    <!-- <div class="form-out">
                                        <label>Estimate % of space</label>
                                        <span>60%</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplication.priceOfDwellingUnit" | translate }}</label>
                                        <span>$60</span>
                                    </div> -->
                                    <div class="form-out">
                                        <label>{{"otherApplication.capacityInfoPeopleSeats" | translate }}<span
                                                class="estrx">*</span></label>
                                        <div class="form-input">
                                            <div>
                                                <input type="text" id="capacityInformation" name="capacityInformation"
                                                    formControlName="capacityInformation"
                                                    (keyup)="calculateNumberOfPeople()"required>
                                            </div>
                                        </div>
                                        <!-- <span>{{projectDetails.capacityInformation ? projectDetails.capacityInformation
                                            : '0'}}</span> -->
                                    </div>


                                    <!-- <div class="form-out">
                                        <label>For Industrial projects</label>
                                        <span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut excepturi hic, eaque
                                            velit a quod adipisci accusamus exercitationem? Qui, libero.</span>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                <!-- loading indicator -->
                <app-spinner *ngIf="outputData.isLoading" [inputData]="'otherApplication.loading' | translate"></app-spinner>
                <!-- loading indicator -->
            </div>
        </div>
    </div>
</div>
<ng-template #otherApplicationModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">{{"otherApplication.message" | translate }}</h2>
            <p> {{"otherApplication.applicationExists" | translate }} </p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="closeModal()"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="closeModal()"
                    id="deleteRecord-close">{{"otherApplication.chooseAnotherPermit" | translate }}</button>
                <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="yesContinue()">{{"otherApplication.continueCheckingDocs" | translate }}</button>
            </div>
        </div>
    </div>
</ng-template>
<app-other-application-document *ngIf="outputData.isDocumenting" [inputData]="outputData"
    (backToParent)="backAndCloseDocument()"></app-other-application-document>
