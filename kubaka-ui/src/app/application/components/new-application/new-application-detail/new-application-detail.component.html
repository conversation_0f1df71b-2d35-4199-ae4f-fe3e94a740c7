<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"newApplication.step3.step" | translate }} <span>3</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"newApplication.step3.projectDetails" | translate }}</h2>
                    <div class="form-incheckbox">
                        <label class="checkbox">
                            <input style="display: none;" type="checkbox" id="check" [checked]="isAssociated"
                                (click)="checkAssociated(associatedUpiContent)" data-bs-toggle="modal" type="button"
                                data-bs-target="#showModal" />
                            <span class="checkbox_box"></span>
                            <span class="checkbox_txt">{{"newApplication.step3.checkAddAssociatedUpi" | translate }}</span>
                        </label>
                    </div>
                </div>
                <!-- Simplified form access - removed restrictive user type check -->
                <form [formGroup]="pageForm" autocomplete="off" *ngIf="currentUser.data.user.userType.code === 'LO'">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToDoItYouself"
                                        (change)="changeForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"newApplication.step3.doItYourself" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToAssignToEngineer"
                                        (change)="changeAssignForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"newApplication.step3.assignToEngineerArchitect" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- Removed restrictive condition - form now always shows for update operations -->
                <form [formGroup]="userForm" autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"newApplication.step3.permitType" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="permitTypeId"
                                        id="permitTypeId" formControlName="permitTypeId"
                                        (change)="checkTemporalPermit()" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.plotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.combiningPlotSize">
                                <label>{{"newApplication.step3.combiningPlotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="combiningPlotSize" name="combiningPlotSize"
                                        formControlName="combiningPlotSize" readonly>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.buildingType" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>

                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.projectCategory" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <!--  -->
                                    <select [class.disabled-select]="isSelectDisabled" name="categoryTypeId"
                                        id="categoryTypeId" formControlName="categoryTypeId"
                                        [disabled]="isSelectDisabled" required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>

                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfFloorGPlus" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor" name="numberOfFloor" noNegative
                                        formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.builtUpAreaSqMeters" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <!--  -->
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                        (keyup)="calculateNumberOfFlow()" noNegative required
                                        [disabled]="userForm.value?.buildTypeId?.length < 0 || userForm.value?.buildTypeId?.length == 0">
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> {{"newApplication.step3.buildUpAreaRestriction" | translate }}. </div>
                                </div>
                            </div>

                            <div class="form-input">
                                <label>{{"newApplication.step3.grossFloorArea" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                        formControlName="grossFloorArea" noNegative readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfParkingSpaces" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedPriceDwellingRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="priceOfDwellingUnitRwf"
                                        name="priceOfDwellingUnitRwf" formControlName="priceOfDwellingUnitRwf" required
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.descriptionOperations" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.percentageSpaceUse" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfDwellingUnit" | translate }} <span class="estrx"></span></label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits" autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedMonthlyWaterM3" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedMonthlyElectricityWatts" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.distanceNearestLandLineFiberM" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn" formControlName="DistanceToTheNearestLandIn"
                                        noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedProjectCostUsd" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedProjectCostRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInRwf" name="ProjectCostInRwf"
                                        formControlName="ProjectCostInRwf" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.capacityInfoPeopleSeats" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation" noNegative
                                        (keyup)="calculateNumberOfPeople()" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.technologySurveyOptional" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId">
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permit-specific fields section -->
                    <div class="step-panel_body" *ngIf="userForm.value.permitTypeCode && userForm.value.permitTypeCode !== 'NCP'">
                        <div class="step-panel_header">
                            <h2>{{"otherApplicationPermitAnswer.otherInformation" | translate }}</h2>
                        </div>

                        <!-- RFWI specific fields -->
                        <div class="form-set" *ngIf="userForm.value.permitTypeCode === 'RFWI'">
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.doYouHaveTheOccupancy" | translate }}</label>
                                <div>
                                    <select name="doYouHaveTheOccupancy" id="doYouHaveTheOccupancy"
                                        formControlName="doYouHaveTheOccupancy">
                                        <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- OCP specific fields -->
                        <div *ngIf="userForm.value.permitTypeCode === 'OCP'">
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.firstAidBoxes" | translate }}</label>
                                    <div>
                                        <select name="isFastAidBox" id="isFastAidBox" formControlName="isFastAidBox">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.disabilityToilets" | translate }}</label>
                                    <div>
                                        <select name="disabilityToiletsFlipUpGrabBars" id="disabilityToiletsFlipUpGrabBars"
                                            formControlName="disabilityToiletsFlipUpGrabBars">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.paraLighteningSystem" | translate }}</label>
                                    <div>
                                        <select name="paraLighteningSystem" id="paraLighteningSystem" formControlName="paraLighteningSystem">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.equipmentCapacity" | translate }}</label>
                                    <div>
                                        <select name="equipmentCapacity" id="equipmentCapacity" formControlName="equipmentCapacity">
                                            <option *ngFor="let op of equipmentCapacities" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.constructionMethod" | translate }}</label>
                                    <div>
                                        <select name="constructionMethod" id="constructionMethod" formControlName="constructionMethod">
                                            <option *ngFor="let op of constructionMethods" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Disaster Prevention Section for OCP -->
                            <div class="step-panel_header">
                                <h2>{{"otherApplicationPermitAnswer.disasterPrevention" | translate }}</h2>
                            </div>
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.fireAlarmSystem" | translate }}</label>
                                    <div>
                                        <select name="fireAlarmSystemWithAnAlarmBellOnEach" id="fireAlarmSystemWithAnAlarmBellOnEach"
                                            formControlName="fireAlarmSystemWithAnAlarmBellOnEach">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.fireAlarmSystemWithAnAlarmBellOnEach == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoFireAlarmSystem" | translate }}</label>
                                <div>
                                    <textarea id="whyNotFireAlarmSystemWithAnAlarmBellOnEach"
                                        name="whyNotFireAlarmSystemWithAnAlarmBellOnEach"
                                        formControlName="whyNotFireAlarmSystemWithAnAlarmBellOnEach" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Fire Extinguishers -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.fireExtinguishers" | translate }}</label>
                                    <div>
                                        <select name="fireExtinguishersEvery50mOnEachFloor" id="fireExtinguishersEvery50mOnEachFloor"
                                            formControlName="fireExtinguishersEvery50mOnEachFloor">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.fireExtinguishersEvery50mOnEachFloor == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoFireExtinguishers" | translate }}</label>
                                <div>
                                    <textarea id="whyNotFireExtinguishersEvery50mOnEachFloor"
                                        name="whyNotFireExtinguishersEvery50mOnEachFloor"
                                        formControlName="whyNotFireExtinguishersEvery50mOnEachFloor" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Exit Signs -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.exitSigns" | translate }}</label>
                                    <div>
                                        <select name="functioningExitSignsOnEachFloor" id="functioningExitSignsOnEachFloor"
                                            formControlName="functioningExitSignsOnEachFloor">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.functioningExitSignsOnEachFloor == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoExitSigns" | translate }}</label>
                                <div>
                                    <textarea id="whyNotfunctioningExitSignsOnEachFloor" name="whyNotfunctioningExitSignsOnEachFloor"
                                        formControlName="whyNotfunctioningExitSignsOnEachFloor" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Emergency Exit -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.emergencyExit" | translate }}</label>
                                    <div>
                                        <select name="anEmergencyExitOnEachFloor" id="anEmergencyExitOnEachFloor"
                                            formControlName="anEmergencyExitOnEachFloor">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.anEmergencyExitOnEachFloor == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoEmergencyExit" | translate }}</label>
                                <div>
                                    <textarea id="whyNotanEmergencyExitOnEachFloor" name="whyNotanEmergencyExitOnEachFloor"
                                        formControlName="whyNotanEmergencyExitOnEachFloor" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Floor Plan -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.floorPlan" | translate }}</label>
                                    <div>
                                        <select name="floorPlanOnEachLevel" id="floorPlanOnEachLevel" formControlName="floorPlanOnEachLevel">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.floorPlanOnEachLevel == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoFloorPlan" | translate }}</label>
                                <div>
                                    <textarea id="whyNotfloorPlanOnEachLevel" name="whyNotfloorPlanOnEachLevel"
                                        formControlName="whyNotfloorPlanOnEachLevel" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Number Sign -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.numberSign" | translate }}</label>
                                    <div>
                                        <select name="numberSignOnEachFloor" id="numberSignOnEachFloor"
                                            formControlName="numberSignOnEachFloor">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.numberSignOnEachFloor == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoNumberSign" | translate }}</label>
                                <div>
                                    <textarea id="whyNotnumberSignOnEachFloor" name="whyNotnumberSignOnEachFloor"
                                        formControlName="whyNotnumberSignOnEachFloor" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Elevator Sign -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.elevatorSign" | translate }}</label>
                                    <div>
                                        <select name="signForbiddingTheUseOfElevatorsInCaseOfFire"
                                            id="signForbiddingTheUseOfElevatorsInCaseOfFire"
                                            formControlName="signForbiddingTheUseOfElevatorsInCaseOfFire">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.signForbiddingTheUseOfElevatorsInCaseOfFire == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoElevatorSign" | translate }}</label>
                                <div>
                                    <textarea id="whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire"
                                        name="whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire"
                                        formControlName="whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Helicopter Landing -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.helicopterLanding" | translate }}</label>
                                    <div>
                                        <select name="landingSpaceOnTopOfTheBuildingForHelicopters"
                                            id="landingSpaceOnTopOfTheBuildingForHelicopters"
                                            formControlName="landingSpaceOnTopOfTheBuildingForHelicopters">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.landingSpaceOnTopOfTheBuildingForHelicopters == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoHelicopterLanding" | translate }}</label>
                                <div>
                                    <textarea id="whyNotlandingSpaceOnTopOfTheBuildingForHelicopters"
                                        name="whyNotlandingSpaceOnTopOfTheBuildingForHelicopters"
                                        formControlName="whyNotlandingSpaceOnTopOfTheBuildingForHelicopters" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Terror Attacks Prevention Section for OCP -->
                            <div class="step-panel_header">
                                <h2>{{"otherApplicationPermitAnswer.terrorAttacks" | translate }}</h2>
                            </div>

                            <!-- CCTV Cameras -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.cctvCameras" | translate }}</label>
                                    <div>
                                        <select name="CCTVCameras" id="CCTVCameras" formControlName="CCTVCameras">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.CCTVCameras == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoCctvCameras" | translate }}</label>
                                <div>
                                    <textarea id="whyNotCCTVCameras" name="whyNotCCTVCameras" formControlName="whyNotCCTVCameras" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Metal Detector -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.metalDetector" | translate }}</label>
                                    <div>
                                        <select name="WalkThroughAndHeldMetalDetect" id="WalkThroughAndHeldMetalDetect"
                                            formControlName="WalkThroughAndHeldMetalDetect">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.WalkThroughAndHeldMetalDetect == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoMetalDetector" | translate }}</label>
                                <div>
                                    <textarea id="whyNotWalkThroughAndHeldMetalDetect" name="whyNotWalkThroughAndHeldMetalDetect"
                                        formControlName="whyNotWalkThroughAndHeldMetalDetect" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Under Search Mirror -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.underSearchMirror" | translate }}</label>
                                    <div>
                                        <select name="UnderSearchMirror" id="UnderSearchMirror" formControlName="UnderSearchMirror">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.UnderSearchMirror == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoUnderSearchMirror" | translate }}</label>
                                <div>
                                    <textarea id="whyNotUnderSearchMirror" name="whyNotUnderSearchMirror"
                                        formControlName="whyNotUnderSearchMirror" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Luggage Scanners -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.luggageScanners" | translate }}</label>
                                    <div>
                                        <select name="LuggageScanners" id="LuggageScanners" formControlName="LuggageScanners">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.LuggageScanners == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoLuggageScanners" | translate }}</label>
                                <div>
                                    <textarea id="whyNotLuggageScanners" name="whyNotLuggageScanners"
                                        formControlName="whyNotLuggageScanners" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Emergency Contact Plates -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.emergencyContactPlates" | translate }}</label>
                                    <div>
                                        <select name="PlatesIndicatingEmergencyResponseUnitsPhoneNumbers"
                                            id="PlatesIndicatingEmergencyResponseUnitsPhoneNumbers"
                                            formControlName="PlatesIndicatingEmergencyResponseUnitsPhoneNumbers">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.PlatesIndicatingEmergencyResponseUnitsPhoneNumbers == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoEmergencyContactPlates" | translate }}</label>
                                <div>
                                    <textarea id="whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers"
                                        name="whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers"
                                        formControlName="whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Evacuation Plan -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.evacuationPlan" | translate }}</label>
                                    <div>
                                        <select name="EmergencyEvacuationPlan" id="EmergencyEvacuationPlan"
                                            formControlName="EmergencyEvacuationPlan">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.EmergencyEvacuationPlan == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoEvacuationPlan" | translate }}</label>
                                <div>
                                    <textarea id="whyNotEmergencyEvacuationPlan" name="whyNotEmergencyEvacuationPlan"
                                        formControlName="whyNotEmergencyEvacuationPlan" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Security Manager Cameras -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.securityManagerCameras" | translate }}</label>
                                    <div>
                                        <select name="SecurityManagerAndStaffCameras" id="SecurityManagerAndStaffCameras"
                                            formControlName="SecurityManagerAndStaffCameras">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.SecurityManagerAndStaffCameras == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoSecurityManagerCameras" | translate }}</label>
                                <div>
                                    <textarea id="whyNotSecurityManagerAndStaffCameras" name="whyNotSecurityManagerAndStaffCameras"
                                        formControlName="whyNotSecurityManagerAndStaffCameras" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Internal Communication System -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.internalComms" | translate }}</label>
                                    <div>
                                        <select name="AnInternalCommunicationSystem" id="AnInternalCommunicationSystem"
                                            formControlName="AnInternalCommunicationSystem">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.AnInternalCommunicationSystem == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoInternalComms" | translate }}</label>
                                <div>
                                    <textarea id="whyNotAnInternalCommunicationSystem" name="whyNotAnInternalCommunicationSystem"
                                        formControlName="whyNotAnInternalCommunicationSystem" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Broadband Internet Services -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.broadband" | translate }}</label>
                                    <div>
                                        <select name="BroadBandInternetServices" id="BroadBandInternetServices"
                                            formControlName="BroadBandInternetServices">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.BroadBandInternetServices == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoBroadband" | translate }}</label>
                                <div>
                                    <textarea id="whyNotBroadBandInternetServices" name="whyNotBroadBandInternetServices"
                                        formControlName="whyNotBroadBandInternetServices" cols="30" rows="5"></textarea>
                                </div>
                            </div>

                            <!-- Access Cards -->
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.accessCards" | translate }}</label>
                                    <div>
                                        <select name="StaffAndVisitorAccessCards" id="StaffAndVisitorAccessCards"
                                            formControlName="StaffAndVisitorAccessCards">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.StaffAndVisitorAccessCards == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoAccessCards" | translate }}</label>
                                <div>
                                    <textarea id="whyNotStaffAndVisitorAccessCards" name="whyNotStaffAndVisitorAccessCards"
                                        formControlName="whyNotStaffAndVisitorAccessCards" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- PMP specific fields -->
                        <div *ngIf="userForm.value.permitTypeCode === 'PMP'">
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.fixedTelephone" | translate }}</label>
                                <div>
                                    <textarea id="applicationForFixedTelephoneLineConnection"
                                        name="applicationForFixedTelephoneLineConnection"
                                        formControlName="applicationForFixedTelephoneLineConnection" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="step-panel_header">
                                <h2>{{"otherApplicationPermitAnswer.facilitiesForPWD" | translate }}</h2>
                            </div>
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.facilitiesForDisabled" | translate }}</label>
                                    <div>
                                        <select name="areThereAnyFacilitiesForTheDisabledProvidedBuilding"
                                            id="areThereAnyFacilitiesForTheDisabledProvidedBuilding"
                                            formControlName="areThereAnyFacilitiesForTheDisabledProvidedBuilding">
                                            <option *ngFor="let op of yesOrNo" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.areThereAnyFacilitiesForTheDisabledProvidedBuilding == 'No'">
                                <label>{{"otherApplicationPermitAnswer.whyNoFacilitiesForDisabled" | translate }}</label>
                                <div>
                                    <textarea id="whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding"
                                        name="whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding"
                                        formControlName="whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- FINS specific fields -->
                        <div *ngIf="userForm.value.permitTypeCode === 'FINS'">
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.inspectionDate" | translate }}<span>*</span></label>
                                <div>
                                    <input type="date" id="dateForRequestedInspection" name="dateForRequestedInspection"
                                        formControlName="dateForRequestedInspection">
                                </div>
                            </div>
                            <div class="form-set">
                                <div class="form-input">
                                    <label>{{"otherApplicationPermitAnswer.userType" | translate }}</label>
                                    <div>
                                        <select name="userTypeId" id="userTypeId" formControlName="userTypeId">
                                            <option *ngFor="let op of accountTypes" [value]="op.id">{{op.name}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.licenseNumber" | translate }}<span>*</span></label>
                                <div>
                                    <input type="text" id="licenseNumber" name="licenseNumber" formControlName="licenseNumber">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.supervisingFirm" | translate }}<span> *</span></label>
                                <div>
                                    <input type="text" id="supervisingFirmSiteEngineer" name="supervisingFirmSiteEngineer"
                                        formControlName="supervisingFirmSiteEngineer" readonly>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.remarks" | translate }}</label>
                                <div>
                                    <textarea id="remarks" name="remarks" formControlName="remarks" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationPermitAnswer.stageOfConstruction" | translate }}</label>
                                <div>
                                    <textarea id="stageOfConstruction" name="stageOfConstruction"
                                        formControlName="stageOfConstruction" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Simplified footer - always visible for update operations -->
                    <div class="step-panel_footer">
                        <button type="button" (click)="onPrev()">{{"newApplication.step3.previous" | translate }}</button>
                        <button type="button" (click)="onNext()">{{"newApplication.step3.next" | translate }}</button>
                    </div>

                </form>

                <!-- Simplified engineer assignment - only show when explicitly requested -->
                <h4 *ngIf="currentUser.data.user.role.code === 'APP' &&
                    (projectDetails?.projectStatus?.code === 'PAPRV'
                        || projectDetails?.projectStatus?.code === 'PASGD'
                    )" style="color: red;">{{"newApplication.step3.cannotContinueAssigned" | translate }} </h4>

                <app-assign-to-engineer-form *ngIf="doYouWantToAssignToEngineer && currentUser.data.user.userType.code !== 'ENG'"
                    [inputData]="{
                        projectId: userForm.value.projectId,
                        allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form>
            </div>
        </div>
    </div>
</div>
<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"newApplication.step3.associatedUpi" | translate }}</h2>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
        (backToParent)="closeAssociatedPopup($event)"
        (backToParentAndSubmitCombining)="getNewCombiningAssociated($event)"></app-associated-upi-application>
    </div>
</ng-template>
