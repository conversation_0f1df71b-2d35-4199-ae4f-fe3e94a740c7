import { Component, TemplateRef, ViewChild } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import {
  NOTIFICATION_COLOR,
  UtilService,
} from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { CreateChecklistComponent } from '../create-checklist/create-checklist.component';

@Component({
  selector: 'app-application-review-detail',
  templateUrl: './application-review-detail.component.html',
  styleUrls: ['./application-review-detail.component.scss'],
})
export class ApplicationReviewDetailComponent {
  @ViewChild('verifyPopup') verifyPopup!: TemplateRef<any>;
  @ViewChild('viewDocumentContent') viewDocumentContent!: TemplateRef<any>;
  @ViewChild('confirmToSubmit') confirmToSubmit!: TemplateRef<any>;

  agencydata: any = { items: [] };
  verificationData: any = {};
  documents: any[] = [];
  approvalStatuses: any[] = [];
  loading: boolean = false;
  userForm!: UntypedFormGroup;
  checkForm!: UntypedFormGroup;
  outputData: any = {};
  applicationDetail: any = {};
  currentUser: any;
  submitted: boolean = false;
  formAccess: any = {};
  checkLists: any[] = [];
  invoiceItems: any = [];
  applicationStatuses: any[] = [];
  paramsId: any;
  checkListForm!: UntypedFormGroup;
  isApplicationDetailReady: boolean = false;
  outputCheckList: any = {};
  isChecked: boolean = false;
  isViewingCheckList: boolean = false;
  checkListsAnswers: any[] = [];
  stringListsComments: string = '';
  expiryDate!: string;
  isSelectDisabled: boolean = true;
  isAllowedToGenerateInvoice: boolean = false;
  checkingData: any = {};
  inspectionRight: any = {};
  // fileData: any = {};
  reviewers: any[] = [];
  isNotDirectorAndNotAllowwed: boolean = false;
  invoicePrices: any = [];
  invoicePriceId: any = {};
  additionalDocuments: any[] = [];
  permissions: any = {};
  associatedUpis: any[] = [];
  upiData: any = {};

  approvalComments: any = {};

  switch() {
    this.isChecked = !this.isChecked;
    if (this.isChecked) {
      this.getAmount();
    } else {
      this.userForm.controls['amount'].setValue(0);
    }
  }

  getAmount() {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'invoice/get-price-invoice?permitTypeId=' +
        this.applicationDetail.permitTypes?.id +
        '&rangeNumber=' +
        this.applicationDetail.buildUpArea
      )
      .subscribe(
        (data) => {
          this.userForm.controls['amount'].setValue(data.amount);
        },
        (error) => {
          this.userForm.controls['amount'].setValue(0);
        }
      );
  }

  switchReviews() {
    this.isViewingCheckList = !this.isViewingCheckList;
  }

  checkSQM(): any {
    if (
      this.applicationDetail?.buildUpArea > 0 &&
      this.applicationDetail?.buildUpArea < 100
    ) {
      return 20000;
    }
    if (
      this.applicationDetail?.buildUpArea > 100 &&
      this.applicationDetail?.buildUpArea < 200
    ) {
      return 100000;
    }
    if (
      this.applicationDetail?.buildUpArea > 200 &&
      this.applicationDetail?.buildUpArea < 500
    ) {
      return 150000;
    }
    if (this.applicationDetail?.buildUpArea > 500) {
      return 200000;
    } else {
      return 0;
    }
  }

  constructor(
    public applicationService: ApplicationService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private formBuilder: UntypedFormBuilder,
    private sessionService: SessionService,
    private utilService: UtilService,
    private router: Router,
    private appConfig: AppConfig
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (
      jwtDecode(this.currentUser.data.token.access_token) as any
    ).UserId;
    if (localStorage.getItem(this.appConfig.PERMI_ACCESS)) {
      this.permissions = JSON.parse(
        localStorage.getItem(this.appConfig.PERMI_ACCESS) as any
      );
    }

    this.applicationService
      .findAllWithPath(environment.applicationUrl + APIURLPATH.INVOICEPRICES)
      .subscribe((data) => {
        this.invoicePrices = data;
      });
    // let dataToSubmit = {
    //   "certificateNumber": 'RDB/EIA/6166/01/24'
    // }
    // // this.applicationService.saveAssetWithPathFormData(dataToSubmit, environment.eia + '?certificateNumber=' + 'RDB/EIA/6166/01/24')
    // this.applicationService.saveAssetWithPath(dataToSubmit, environment.integrationUrl + 'integration/eia?certificateNumber=' + 'RDB/EIA/6166/01/24')
    //   .subscribe(
    //     data => {
    //       // if (data.results && data.results.serial_number) {
    //       if (data.data && data.data.document_status === 'Valid') {
    //         let backData = {
    //           certNumber: this.userForm.value.certificateNumber,
    //           found: true
    //         }

    //         this.outputData.valid_until = data.data.valid_until;
    //         this.outputData.certificate_number = data.data.certificate_number;
    //         this.outputData.valid_from = data.data.valid_from;
    //         this.outputData.document_status = data.data.document_status;
    //         this.outputData.base64Data = data.data._pdf_string;
    //         this.modalService.open(this.viewDocumentContent, { size: 'ap-stas', centered: true });
    //       } else {
    //         this.utilService.showNotification(NOTIFICATION_COLOR.error, `Data not found.
    //       Please submit your request to RDB for an Environmental Impact Access certificate`, "bottom", "center");
    //       }
    //       this.submitted = false;
    //     }, error => {
    //       this.submitted = false;
    //     }
    //   )

    // this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.APPROVAL_STATUS)
    //   .subscribe(data => {

    //     this.approvalStatuses = data;

    //     // this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/approvalLevel/code/search?search=' + 'ADMSTR')
    //     // .subscribe(
    //     //   invoiceTypeData => {
    //     //     this.userForm.controls['approvalLevelId'].setValue(invoiceTypeData.items[0].id)

    //     //   }
    //     // )
    //   })

    this.loadApplicationInfo();

    this.applicationService
      .findAllWithPath(environment.applicationUrl + 'invoice/invoiceItem')
      .subscribe((data) => {
        this.invoiceItems = data;
        this.userForm.controls['invoiceItemId'].setValue(
          this.invoiceItems[0].id
        );
      });

    this.applicationService
      .findAllWithPath(
        environment.applicationUrl + 'application/applicationStatus'
      )
      .subscribe(
        (data) => {
          this.applicationStatuses = data;
        },
        (error) => { }
      );
  }

  loadApplicationInfo() {
    this.route.params.subscribe((params: any) => {
      this.paramsId = params.id;
      this.applicationService
        .findAllWithPath(
          environment.applicationUrl +
          'application/application/AllDetails/' +
          params.id
        )
        .subscribe((data) => {
          this.applicationDetail = data[0];

          this.reviewers = this.applicationDetail.reviewersOnApplications;
          if (
            this.currentUser.data.user.role.code !== 'DRCT' &&
            this.currentUser.data.user.role.code !== 'SINSP' &&
            this.applicationDetail.isLocked
          ) {
            this.router.navigate(['/account/dashboard']);
          }
          this.isApplicationDetailReady = true;
          this.getCheckListsByPermitTypes();
          this.loadAdditionalDocuments();
          this.loadAssociatedUpis();
          this.loadApprovalComments();
        });
      this.loadDocuments();
      this.loadCheckLists();
    });
  }

  loadApprovalComments() {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'approval/applicationApproval/applicationId/' +
        this.applicationDetail.id
      )
      .subscribe(
        (data) => {
          this.approvalComments = data;
        },
        (error) => { }
      );
  }

  loadAssociatedUpis() {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'application/associatedUPI/ByProject/' +
        this.applicationDetail.projects.id
      )
      .subscribe(
        (data) => {
          this.associatedUpis = data;
        },
        (error) => { }
      );
  }

  loadAdditionalDocuments() {
    this.applicationService
      .findAllWithPath(
        environment.documentUrl +
        'DocMgt/documents/uploadAdditional/' +
        this.paramsId
      )
      .subscribe(
        (data) => {
          this.additionalDocuments = data;
        },
        (error) => { }
      );
  }

  openPricePopup(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  getPrice() {
    this.userForm.controls['amount'].setValue(
      +this.invoicePrices.find(
        (invoice: any) => invoice.id === this.invoicePriceId
      ).amount
    );
    this.modalService.dismissAll();
  }

  loadDocuments() {
    this.applicationService
      .findAllWithPath(
        environment.documentUrl + 'DocMgt/documents/' + this.paramsId
      )
      .subscribe((data) => {
        this.documents = data;
      });
  }

  updateExpiryDate() {
    // if (+this.userForm.value.month >= 0 && +this.userForm.value.month <= 24) {
    if (+this.userForm.value.month >= 1 && +this.userForm.value.month <= 60) {
      const monthControl = this.userForm.get('month');
      const expiredDateControl = this.userForm.get('expiredDate');

      if (monthControl && expiredDateControl) {
        const month = monthControl.value;
        if (month) {
          const today = new Date();
          const expiry = new Date(
            today.setMonth(today.getMonth() + Number(month))
          );
          this.expiryDate = expiry.toISOString().split('T')[0];
          expiredDateControl.setValue(this.expiryDate);
        }
      }
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'You must ensure the duration is no more than 60 months and not less than 0 months.',
        'bottom',
        'center'
      );
      // this.userForm.controls['month'].setValue(0);
    }
  }

  ngOnInit(): void {
    this.userForm = this.formBuilder.group({
      id: [''],
      comment: ['', [Validators.required]],
      permitCheckListId: ['', Validators.required],
      // expiredDate: ['', Validators.required],
      expiredDate: [''],
      month: [null],
      approvalStatusId: [''],
      amount: ['', Validators.required],
      range: [0],
      invoiceItemId: ['', Validators.required],

      // Filled in constructor
      invoiceStatusId: [''],
      invoiceTypeId: [''],
      approvalLevelId: [''],
      applicationStatusId: ['', Validators.required],
      agencyId: [''],
    });

    this.checkForm = this.formBuilder.group({
      id: [''],
      comment: ['', [Validators.required]],
      permitCheckListId: [''],
      invoiceStatusId: [''],
      invoiceTypeId: [''],
      approvalLevelId: [''],
      applicationStatusId: ['', Validators.required],
    });

    // load approval status by user
    this.loadApprovalStatusByUserApprovalLevel();

    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'invoice/invoiceType/code/search?search=' +
        VALUE.INVOICE_TYPE_PERMIT_CODE
      )
      .subscribe((invoiceTypeData) => {
        if (invoiceTypeData && invoiceTypeData.items.length > 0) {
          this.userForm.controls['invoiceTypeId'].setValue(
            invoiceTypeData.items[0].id
          );
        } else {
          this.utilService.showNotification(
            NOTIFICATION_COLOR.error,
            'Please set invoice type',
            'bottom',
            'center'
          );
        }
      });

    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'invoice/invoiceStatus/code/search?search=' +
        VALUE.INVOICE_PENDING_STATUS_CODE
      )
      .subscribe((invoiceTypeData) => {
        this.userForm.controls['invoiceStatusId'].setValue(
          invoiceTypeData.items[0].id
        );
      });
  }

  loadApprovalStatusByUserApprovalLevel() {
    if (this.currentUser.data.user.approvalLevelId) {
      this.applicationService
        .findAllWithPath(
          environment.applicationUrl +
          'approval/approvalStatus/approvalLevel/' +
          this.currentUser.data.user.approvalLevelId
        )
        .subscribe((data) => {
          if (data.length > 0) {
            this.userForm.controls['approvalLevelId'].setValue(
              this.currentUser.data.user.approvalLevelId
            );
            if (
              this.currentUser.data.user?.agency?.code === 'RHA' &&
              this.currentUser.data.user?.userType?.code === 'STF'
            ) {
              this.approvalStatuses = data.filter(
                (x: any) =>
                  x.additionalCode === '4' ||
                  x.additionalCode === '5' ||
                  x.additionalCode === '6'
              );
            } else {
              this.approvalStatuses = data.filter(
                (x: any) =>
                  x.additionalCode !== '4' &&
                  x.additionalCode !== '5' &&
                  x.additionalCode !== '6'
              );
              if (this.currentUser.data?.user?.agency?.code !== 'RHA') {
                const code = 'RTNNO';
                const index = this.approvalStatuses.findIndex(
                  (valueData: any) => valueData.code === code
                );
                if (index !== -1) {
                  this.approvalStatuses.splice(index, 1);
                }
              }
            }
            // rty
          } else {
            this.utilService.showNotification(
              NOTIFICATION_COLOR.error,
              'No approval status found based on you approval level contact administrator to set approval status on your approval level',
              'bottom',
              'center'
            );
            this.close();
          }
        });
    } else {
      this.formAccess.isNotAllowedToSubmitFinalApproval = true;
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'No approval level provided',
        'bottom',
        'center'
      );
    }
  }

  viewDirectDocument(component: any, doc: any, sizeParams: any) {
    if (doc.fileBase64) {
      this.outputData = { base64Data: doc.fileBase64 };
      this.modalService.open(component, { size: sizeParams, centered: true });
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'No document provided',
        'bottom',
        'center'
      );
    }
  }

  viewDocument(component: any, doc: any, sizeParams: any) {
    this.loading = true;
    this.applicationService
      .findAllWithPath(
        environment.documentUrl + 'DocMgt/' + doc.fileName + '/base64'
      )
      .subscribe(
        (data) => {
          this.loading = false;
          if (this.utilService.isExcelFile(data.base64Data)) {
            this.utilService.downloadExcel(data.base64Data);
          } else {
            this.outputData = data;
            this.outputData.currentId = doc.id;
            if (data.base64Data) {
              this.modalService.open(component, {
                size: sizeParams,
                centered: true,
              });
            } else {
              this.utilService.showNotification(
                NOTIFICATION_COLOR.error,
                'No file found',
                'top',
                'right'
              );
            }
          }
        },
        (error) => {
          this.loading = false;
        }
      );
  }

  assignToUser(content: any, type: any) {
    this.outputData = {};
    this.outputData = this.applicationDetail;
    this.outputData.isAssigningData = true;
    this.openModal(content, type);
  }
  verifyEIACertificateNumber() {
    this.outputData.isEIACertificateNumberChecker = false;
    let dataToSubmit = {
      certificateNumber: this.applicationDetail.certificateNumberEIA,
    };
    this.checkingData.label = 'Checking EIA certificate';
    this.checkingData.isSearching = true;
    // this.applicationService.saveAssetWithPathFormData(dataToSubmit, environment.eia + '?certificateNumber=' + this.applicationDetail.certificateNumberEIA)

    if (this.applicationDetail.certificateNumberEIA) {
      this.applicationService
        .saveAssetWithPath(
          dataToSubmit,
          environment.integrationUrl +
          'integration/eia?certificateNumber=' +
          this.applicationDetail.certificateNumberEIA
        )
        .subscribe(
          (data) => {
            this.checkingData.label = '';
            this.checkingData.isSearching = false;
            if (data.data && data.data.document_status === 'Valid') {
              this.utilService.showNotification(
                NOTIFICATION_COLOR.success,
                `
              Certificate number found with status : ` +
                data.data.document_status,
                '',
                ''
              );
              this.outputData.valid_until = data.data.valid_until;
              this.outputData.certificate_number = data.data.certificate_number;
              this.outputData.valid_from = data.data.valid_from;
              this.outputData.document_status = data.data.document_status;
              this.outputData.base64Data = data.data._pdf_string;
              this.modalService.open(this.viewDocumentContent, {
                size: 'ap-stas',
                centered: true,
              });

              this.verifyRRA();
            } else {
              this.utilService.showNotification(
                NOTIFICATION_COLOR.error,
                `Data not found.
            Please submit your request to RDB for an Environmental Impact Access certificate`,
                'bottom',
                'center'
              );

              this.verificationData.title = 'EIA Certificate info';
              this.verificationData.message = `Data not found.
            Please submit your request to RDB for an Environmental Impact Access certificate`;
              this.modalService.open(this.verifyPopup, {
                size: 'md',
                centered: true,
              });
            }
            this.submitted = false;
          },
          (error) => {
            this.checkingData.label = '';
            this.checkingData.isSearching = false;
            this.submitted = false;
            this.verifyRRA();
          }
        );
    } else {
      this.outputData.isEIACertificateNumberChecker = true;
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        `
        EIA certicate number is not provided`,
        '',
        ''
      );
    }
  }

  continueWithoutCerticateEIAChecker() {
    this.verifyRRA();
  }

  confirmToSubmitMethod() {
    this.modalService.open(this.confirmToSubmit, {
      size: 'md',
      centered: true,
    });
  }

  submitAndVerifyEverything() {
    if (
      this.approvalStatuses.find(
        (x: any) => x.id === this.userForm.value.approvalStatusId
      ).code === 'CTFD'
    ) {
      if (this.applicationDetail.permitTypes?.code === 'NCP') {
        this.verifyEIAndRRABeforeApprove();
      } else if (this.userForm.value.expiredDate && this.userForm.value.month) {
        this.verifyEIAndRRABeforeApprove();
      } else {
        if (
          this.applicationDetail.permitTypeCode === 'FINS' ||
          this.applicationDetail.permitTypeCode === 'OCP'
        ) {
          this.verifyEIAndRRABeforeApprove();
        } else {
          this.utilService.showNotification(
            NOTIFICATION_COLOR.error,
            'Expiry date and month fields are required',
            'bottom',
            'center'
          );
        }
      }
    } else {
      this.approvalStatusApprovalSubmit();
    }
  }

  // submitAndVerifyEverything() {
  //   if (this.approvalStatuses.find((x: any) => x.id === this.userForm.value.approvalStatusId).code === "CTFD") {
  //     if (this.applicationDetail.permitTypes?.code === 'NCP') {
  //       this.verifyEIAndRRABeforeApprove();
  //     } else if (this.userForm.value.expiredDate && this.userForm.value.month &&
  //       this.applicationDetail.permitTypes?.code !== 'NCP') {
  //       this.verifyEIAndRRABeforeApprove();
  //     } else {
  //       this.utilService.showNotification(NOTIFICATION_COLOR.error, "Expiry date and month fields are required", "bottom", "center");
  //     }
  //   } else {
  //     this.approvalStatusApprovalSubmit();
  //   }
  // }

  verifyEIAndRRABeforeApprove() {
    if (
      this.applicationDetail.categoryTypes &&
      (this.applicationDetail.categoryTypes.code === 'CAT4' ||
        this.applicationDetail.categoryTypes.code === 'CAT5')
    ) {
      this.verifyEIACertificateNumber();
    } else {
      this.verifyRRA();
    }
  }

  approvalStatusApprovalSubmit() {
    let value = this.approvalStatuses.find(
      (x: any) => x.id == this.userForm.value.approvalStatusId
    );

    if((this.applicationDetail.applicationNumberForIremboHub) && ((value.code === 'CTFD') || (value.code === 'RFAC')||(value.code === 'REJT'))){
      this.approvalDataWithIrembo();
    }else{
      let subData = {
        comment: this.userForm.value.comment,
        approvalStatusId: this.userForm.value.approvalStatusId,
        applicationId: this.applicationDetail.id,
        applicantUserId: this.applicationDetail.userDetails.id,
        applicationStatusId: this.userForm.value.applicationStatusId,
        userId: this.currentUser.userId,
        approvalLevelId: this.userForm.value.approvalLevelId,
        agencyId: this.userForm.value.agencyId,
      };

      if (
        this.applicationDetail.permitTypeCode !== 'FINS' &&
        this.applicationDetail.permitTypeCode !== 'OCP' &&
        this.applicationDetail?.applicationStatus?.code === 'CTFD' &&
        this.userForm.value.amount !== 0 &&
        !this.userForm.value.expiredDate &&
        !this.userForm.value.month
      ) {
        this.utilService.showNotification(
          NOTIFICATION_COLOR.error,
          'Expiration date and month are required',
          'bottom',
          'center'
        );
      }

      else {
        this.submitted = true;
        this.applicationService
          .saveAssetWithPath(
            subData,
            environment.applicationUrl + 'approval/applicationApproval'
          )
          // this.applicationService.saveAssetWithPathFormData(formData, environment.applicationUrl + 'approval/applicationApproval')
          .subscribe(
            (data) => {
              if (this.isAllowedToGenerateInvoice) {
                this.generateInvoice();
              } else {
                this.submitted = false;
                this.modalService.dismissAll();
                this.utilService.showNotification(
                  NOTIFICATION_COLOR.success,
                  'Application status changed successfully',
                  'bottom',
                  'center'
                );
                this.router.navigate(['/account/all-applications/lists']);
              }
            },
            (error) => {
              this.submitted = false;
            }
          );
      }

    }


  }
  // approval data with irembo
  approvalDataWithIrembo() {
    this.applicationService
    .findAllWithPath(
      environment.authUrl +
      'user-management/agency/code/search?search=' +
      this.applicationDetail.agencyCode
    )
    .subscribe((data) => {
      if (data && data.items) {
        this.agencydata=data;
      }
    });
    let iremboApprovalData = {
      //infomation rerated to approval
      comment: this.userForm.value.comment,
      approvalStatusId: this.userForm.value.approvalStatusId,
      applicationId: this.applicationDetail.id,
      applicantUserId: this.applicationDetail.userDetails.id,
      applicationStatusId: this.userForm.value.applicationStatusId,
      userId: this.currentUser.userId,
      approvalLevelId: this.userForm.value.approvalLevelId,
      agencyId: this.userForm.value.agencyId,
      // information related to invoice
      agencyCode: this.applicationDetail.agencyCode,
      amount: this.userForm.value.amount + '',
      transactionNumber: 'string',
      invoiceTypeId: this.userForm.value.invoiceTypeId,
      invoiceItemId: this.userForm.value.invoiceItemId,
      invoiceStatusId: this.userForm.value.invoiceStatusId,
      paymentAccountIdentifier: this.agencydata.items[0]?.paymentAccountIdentifier,
      // information related to certificate
      permitTypeId: this.applicationDetail.permitTypes.id,
      title: 'string',
      lows: 'string',
      expiredDate: this.userForm.value.expiredDate,
      backgroundUrl: 'string',
      // applicationId: this.applicationDetail.id,
    };
    // save application approval with irembo

    this.submitted = true;
    this.applicationService
      .saveAssetWithPath(
        iremboApprovalData,
        environment.applicationUrl + 'approval/applicationApprovalWithIrembo'
      )
      // this.applicationService.saveAssetWithPathFormData(formData, environment.applicationUrl + 'approval/applicationApproval')
      .subscribe(
        (data) => {
            this.submitted = false;
            this.modalService.dismissAll();
            this.utilService.showNotification(
              NOTIFICATION_COLOR.success,
              'Application status changed successfully',
              'bottom',
              'center'
            );
            this.router.navigate(['/account/all-applications/lists']);

        },
        (error) => {
          this.submitted = false;
        }
      );
  }
  generateInvoice() {
    this.applicationService
      .findAllWithPath(
        environment.authUrl +
        'user-management/agency/code/search?search=' +
        this.applicationDetail.agencyCode
      )
      .subscribe((data) => {
        if (data && data.items) {
          this.userForm.controls['agencyId'].setValue(data.items[0].id);

          // invoice things

          let dataSubmit = {
            applicationId: this.applicationDetail.id,
            agencyCode: this.applicationDetail.agencyCode,
            amount: this.userForm.value.amount + '',
            applicantUserId: this.applicationDetail.userDetails.id,
            transactionNumber: 'string',
            userId: this.currentUser.userId,
            invoiceTypeId: this.userForm.value.invoiceTypeId,
            invoiceItemId: this.userForm.value.invoiceItemId,
            invoiceStatusId: this.userForm.value.invoiceStatusId,
            paymentAccountIdentifier: data.items[0]?.paymentAccountIdentifier,
          };

          if (
            this.userForm.value.invoiceItemId &&
            (this.userForm.value.amount || this.userForm.value.amount === 0)
          ) {
            this.applicationService
              .saveAssetWithPath(
                dataSubmit,
                environment.applicationUrl + 'invoice/invoice'
              )
              .subscribe(
                (data) => {
                  // this.router.navigate(['/account/all-application/lists']);
                  if (
                    this.applicationDetail?.applicationStatus?.code !== 'CTFD'
                  ) {
                    this.generateCertificate();
                  }
                },
                (error) => {
                  this.submitted = false;
                }
              );
          } else {
            this.submitted = false;
            this.utilService.showNotification(
              NOTIFICATION_COLOR.error,
              'Choose invoice item and fill amount to proceed',
              'bottom',
              'center'
            );
          }

          // invoice things
        }
      });
  }

  regenerateInvoice() {
    this.applicationService
      .findAllWithPath(
        environment.authUrl +
        'user-management/agency/code/search?search=' +
        this.applicationDetail.agencyCode
      )
      .subscribe((data) => {
        if (data && data.items) {
          this.userForm.controls['agencyId'].setValue(data.items[0].id);

          // invoice things

          let dataSubmit = {
            applicationId: this.applicationDetail.id,
            agencyCode: this.applicationDetail.agencyCode,
            amount: this.userForm.value.amount + '',
            applicantUserId: this.applicationDetail.userDetails.id,
            transactionNumber: 'string',
            userId: this.currentUser.userId,
            invoiceTypeId: this.userForm.value.invoiceTypeId,
            invoiceItemId: this.userForm.value.invoiceItemId,
            invoiceStatusId: this.userForm.value.invoiceStatusId,
            paymentAccountIdentifier: data.items[0]?.paymentAccountIdentifier,
          };

          if (
            this.userForm.value.invoiceItemId &&
            (this.userForm.value.amount || this.userForm.value.amount === 0)
          ) {
            this.loading = true;
            this.submitted = true;

            this.applicationService
              .saveAssetWithPath(
                dataSubmit,
                environment.applicationUrl + 'invoice/invoice'
              )
              .subscribe(
                (data) => {
                  this.submitted = false;
                  this.loading = false;

                  this.utilService.showNotification(
                    NOTIFICATION_COLOR.success,
                    data.message,
                    'bottom',
                    'center'
                  );
                },
                (error) => {
                  this.submitted = false;
                  this.utilService.showNotification(
                    NOTIFICATION_COLOR.error,
                    'Error occur',
                    'bottom',
                    'center'
                  );
                }
              );
          } else {
            this.submitted = false;
            this.utilService.showNotification(
              NOTIFICATION_COLOR.error,
              'Choose invoice item and fill amount to proceed',
              'bottom',
              'center'
            );
          }

          // invoice things
        }
      });
  }

  generateCertificate() {
    let dataSubmit = {
      permitTypeId: this.applicationDetail.permitTypes.id,
      agencyCode: this.applicationDetail.agencyCode,
      title: 'string',
      lows: 'string',
      applicantUserId: this.applicationDetail.userDetails.id,
      userId: this.currentUser.userId,
      expiredDate: this.userForm.value.expiredDate,
      backgroundUrl: 'string',
      applicationId: this.applicationDetail.id,
    };
    this.applicationService
      .saveAssetWithPath(
        dataSubmit,
        environment.applicationUrl + 'certificate/certificate'
      )
      .subscribe(
        (data) => {
          this.submitted = false;
          this.modalService.dismissAll();
          this.utilService.showNotification(
            NOTIFICATION_COLOR.success,
            'Application approved successfully',
            'bottom',
            'center'
          );
          this.router.navigate(['/account/all-applications/lists']);
        },
        (error) => {
          this.submitted = false;
        }
      );
  }

  continue() {
    this.verifyRRA();
  }
  cancelDocumentView() {
    this.modalService.dismissAll();
  }

  inspectionForm(event: any) {
    this.inspectionRight.applicationDetail = this.applicationDetail;
    if (event === '1') {
      this.inspectionRight.isRandomInspection = true;
      this.inspectionRight.isFoundationInspection = false;
      this.inspectionRight.isOccupancyInspection = false;

      this.inspectionRight.name = 'Inspection';
    }
    if (event === '2') {
      this.inspectionRight.isFoundationInspection = true;
      this.inspectionRight.isOccupancyInspection = false;
      this.inspectionRight.isRandomInspection = false;
      this.inspectionRight.name = 'Foundation Inspection';
    }
    if (event === '3') {
      this.inspectionRight.isOccupancyInspection = true;
      this.inspectionRight.isFoundationInspection = false;
      this.inspectionRight.isRandomInspection = false;
      this.inspectionRight.name = 'Occupancy Inspection';
    }
  }

  verifyRRA() {
    this.submitted = true;
    this.checkingData.isSearching = true;
    this.checkingData.label = 'Checking UPI in RRA';
    this.applicationService
      .findAllWithPath(
        environment.integrationUrl +
        'integration/rra?upi=' +
        this.applicationDetail.projects.upi
      )
      .subscribe(
        (data) => {
          this.checkingData.label = '';
          this.checkingData.isSearching = false;
          if (
            data &&
            data.ResponseObject &&
            data.ResponseObject.StatusCode === 1
          ) {
            this.utilService.showNotification(
              NOTIFICATION_COLOR.success,
              'This plot has no unpaid taxes',
              'bottom',
              'center'
            );
            this.approvalStatusApprovalSubmit();
          } else {
            this.submitted = false;
            this.utilService.showNotification(
              NOTIFICATION_COLOR.error,
              'This plot has some unpaid taxes',
              'bottom',
              'center'
            );
            this.verificationData.title = 'RRA info';
            this.verificationData.message = `This plot has some unpaid taxes`;
            this.modalService.open(this.verifyPopup, {
              size: 'md',
              centered: true,
            });
          }
        },
        (error) => {
          this.checkingData.label = '';
          this.checkingData.isSearching = false;
          this.submitted = false;
          this.approvalStatusApprovalSubmit();
        }
      );
  }

  cancelCheckList() {
    this.modalService.dismissAll();
    this.loadCheckLists();
  }

  dismissCheckListModal() {
    this.modalService.dismissAll();
    this.loadCheckLists();
  }

  clearChecklistCache() {
    if (this.applicationDetail?.id) {
      CreateChecklistComponent.clearChecklistCache(this.applicationDetail.id);
    }
  }

  hasCachedChecklistData(): boolean {
    if (!this.applicationDetail?.id) {
      return false;
    }

    try {
      const cacheKey = 'checklist_cache_' + this.applicationDetail.id;
      const cachedData = localStorage.getItem(cacheKey);

      if (!cachedData) {
        return false;
      }

      const parsedData = JSON.parse(cachedData);
      const isExpired = Date.now() - parsedData.timestamp > (7 * 24 * 60 * 60 * 1000);

      if (isExpired) {
        this.clearChecklistCache();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking cached checklist data:', error);
      return false;
    }
  }

  loadCheckLists() {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'approval/applicationApprovalCheckList/all/' +
        this.paramsId
      )
      .subscribe((data) => {
        if (
          this.currentUser.data.user.role.code === 'DRCT' ||
          this.currentUser.data.user.role.code === 'TMLD' ||
          this.currentUser.data.user.role.code === 'OFCMNG' ||
          this.currentUser.data.user.role.code === 'ADM'
        ) {
          this.checkListsAnswers = data;
        } else if (this.currentUser.data.user.role.code === 'STF' &&
          this.currentUser.data.user.userType.code === 'STF'
        ) {
          this.checkListsAnswers = data;
        } else {
          this.checkListsAnswers = data.filter(
            (item: any) => item.userId === this.currentUser.userId
          );
        }
      });
  }

  saveCheckList(event: any) {
    let dataTosubmit = {};
    this.applicationService
      .saveAssetWithPath(dataTosubmit, environment.applicationUrl + '')
      .subscribe((data) => { });
  }

  approveDocument(viewDocument: any, event: any) {
    this.outputData = event;
    this.outputData.applicationId = this.applicationDetail.id;
    this.outputData.isDocumentApproval = true;
    this.modalService.open(viewDocument, { size: 'md', centered: true });
  }

  closePopup(event: any) {
    this.outputData.isAssigningData = false;
    this.loadDocuments();
    this.modalService.dismissAll();

    this.loadApplicationInfo();
  }

  getCheckListsByPermitTypes() {
    this.applicationService
      .findAllWithPath(environment.applicationUrl + 'approval/permitCheckList')
      // this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/permitCheckList' + this.applicationDetail.permitTypes.id)
      .subscribe((data) => {
        this.checkLists = data;
      });
  }

  close() {
    this.router.navigate(['/account/all-applications/lists']);
  }

  verify(content: any, event: any, type: any) {
    if (event === 'LIS') {
      this.upiData = this.applicationDetail.projects.upi;
      this.openModal(content, type);
    } else {
      this.outputData = {};
      this.outputData = this.applicationDetail;
      this.outputData.verifyType = event;
      this.outputData.isVerifying = true;
      this.openModal(content, type);
    }
  }

  viewUpiDetails(content: any, event: any, type: any) {
    this.upiData = event.projects?.upi;
    this.openModal(content, type);
  }

  openModal(content: any, sizeParams: any) {
    this.outputCheckList = {};
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  // getFileToSave(event: any) {
  //   this.fileData = event;
  // }

  viewCheckList(content: any, data: any, sizeParams: any) {
    this.outputCheckList = data;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  validateApproval() {
    let aprovalStatus = this.approvalStatuses.find(
      (x: any) => x.id === this.userForm.value.approvalStatusId
    );

    this.userForm.controls['applicationStatusId'].setValue(
      this.checkApprovalStatus()
    );
    let value = this.approvalStatuses.find(
      (x: any) => x.id == this.userForm.value.approvalStatusId
    );
    if (value.code == 'NORHA') {
      this.applicationService
        .findAllWithPath(
          environment.authUrl + 'user-management/agency/code/search?search=RHA'
        )
        .subscribe((data) => {
          if (data && data.items) {
            this.userForm.controls['agencyId'].setValue(data.items[0].id);
          } else {
            this.utilService.showNotification(
              NOTIFICATION_COLOR.error,
              'No agency of RHA found',
              'top',
              'right'
            );
          }
        });
    } else {
      this.userForm.controls['agencyId'].setValue(
        this.currentUser.data.user.agency.id
      );
    }

    if (value.code === 'CTFD') {
      this.isAllowedToGenerateInvoice = true;
    } else {
      this.isAllowedToGenerateInvoice = false;
    }
  }
  // validateApproval() {
  //   let aprovalStatus = this.approvalStatuses.find((x: any) => x.id === this.userForm.value.approvalStatusId);
  //   if (
  //     (this.applicationDetail.categoryTypes.code === 'CAT4' || this.applicationDetail.categoryTypes.code === 'CAT5')
  //      &&
  //     (this.applicationDetail.isNonObjection === "0" && aprovalStatus.code === 'CTFD')
  //     &&
  //     (this.applicationDetail.permitTypes.code != "FINS" && this.applicationDetail.permitTypes.code != "OCP")
  //   ) {
  //     this.utilService.showNotification(NOTIFICATION_COLOR.error, "This application must first receive Non-Objection approval from RHA before it can be certified.", "top", "right");
  //     this.userForm.controls['approvalStatusId'].setValue('');
  //   } else {
  //     this.userForm.controls['applicationStatusId'].setValue(this.checkApprovalStatus());
  //     let value = this.approvalStatuses.find((x: any) => x.id == this.userForm.value.approvalStatusId);
  //     if (value.code == "NORHA") {
  //       this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=RHA')
  //         .subscribe(
  //           data => {
  //             if (data && data.items) {
  //               this.userForm.controls['agencyId'].setValue(data.items[0].id);
  //             } else {
  //               this.utilService.showNotification(NOTIFICATION_COLOR.error, "No agency of RHA found", "top", "right")
  //             }
  //           }
  //         )
  //     } else {
  //       this.userForm.controls['agencyId'].setValue(this.currentUser.data.user.agency.id);
  //     }

  //     if (value.code === 'CTFD') {
  //       this.isAllowedToGenerateInvoice = true;
  //     } else {
  //       this.isAllowedToGenerateInvoice = false;
  //     }
  //   }

  // }

  checkApprovalStatus() {
    let aprovalStatus = this.approvalStatuses.find(
      (x: any) => x.id === this.userForm.value.approvalStatusId
    );
    if (aprovalStatus.code === 'CTFD') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'CTFD'
      );
      return applicationStatus.id;
    } else if (aprovalStatus.code === 'REJT') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'CXL'
      );
      return applicationStatus.id;
    } else if (aprovalStatus.code === 'RFAC') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'UNCRN'
      );
      return applicationStatus.id;
    } else if (aprovalStatus.code === 'NORHA') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'NORHA'
      );
      return applicationStatus.id;
    } else if (aprovalStatus.code === 'PAPRV') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'PAPRV'
      );
      return applicationStatus.id;
    } else if (aprovalStatus.code === 'RTNNO') {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'RTNNO'
      );
      return applicationStatus.id;
    } else if (
      aprovalStatus.code === 'RHAAPRVD' ||
      aprovalStatus.code === 'RHARJCT' ||
      aprovalStatus.code === 'RHABFCR'
    ) {
      let applicationStatus = this.applicationStatuses.find(
        (x: any) => x.code === 'RTNNO'
      );
      return applicationStatus.id;
    } else {
      // return 'Test';
    }
  }

  closeInspection() {
    this.inspectionRight.isOccupancyInspection = false;
    this.inspectionRight.isFoundationInspection = false;
    this.inspectionRight.isRandomInspection = false;
  }

  viewReviewers(component: any, sizeParams: any) {
    this.outputData.reviewers = this.reviewers;
    this.outputData.applicationId = this.applicationDetail.id;
    this.outputData.isReassignReviewer = true;
    this.modalService.open(component, { size: sizeParams, centered: true });
  }

  viewApplicationProcessMethod(component: any, sizeParams: any) {
    this.outputData.applicationId = this.applicationDetail.id;
    this.outputData.isApplicationProcess = true;
    this.outputData.isBackNotAllowed = true;
    this.modalService.open(component, { size: sizeParams, centered: true });
  }

  viewProcessHistoryComponent(component: any, sizeParams: any) {
    this.outputData.applicationId = this.applicationDetail.id;
    this.outputData.isApplicationProcess = true;
    this.outputData.isBackNotAllowed = true;
    this.modalService.open(component, { size: sizeParams, centered: true });
  }

  closeReAssignPopup(event: any) {
    this.outputData.isReassignReviewer = false;
    this.modalService.dismissAll();
    this.loadApplicationInfo();
  }

  cancel() {
    this.modalService.dismissAll();
  }


  getShortComment(comment: string, limit: number = 100): string {
    return comment?.length > limit ? comment.substring(0, limit) + '...' : comment;
  }
}
