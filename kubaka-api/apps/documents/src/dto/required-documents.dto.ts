import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateRequiredDocumentDto {
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field permitType id is required' })
  @IsOptional()
  permitTypeId?: string;

  @ApiProperty({ description: 'The field documentType id is required' })
  @IsOptional()
  documentTypeId?: string;

  @ApiProperty({ description: 'The field category id is required' })
  @IsOptional()
  categoryId?: string;
}
