<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-sysheader">
                <span class="hder" aria-label="header tittle">System Administrator</span>
                <ul class="kbk-tabs">
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/setting/user']">User</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./role']">Role</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./build-type']">Build
                            Type</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./permit-type']">Permit
                            Type</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['./required-document']">Required Document</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./approval/level']">Approval
                            Level</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./approval/status']">Approval
                            Status</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./permit-checklist']">Permit
                            Question</a>
                    </li>
                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./agency']">Agency</a>
                    </li>

                    <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./invoice-prices']">Invoice Price</a>
                    </li>
                    
                    <!-- <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active" [routerLink]="['./question-category']">Question Category</a>
                    </li> -->
                </ul>
            </div>
            <div class="app-cont">
                <!-- <div class="app-cont_title">

                </div> -->
                <div class="app-cont_list" style="padding: 0; border: none;">
                    <!-- body page -->
                    <router-outlet></router-outlet>
                    <!-- body page -->
                </div>
            </div>
        </div>
    </div>
</div>