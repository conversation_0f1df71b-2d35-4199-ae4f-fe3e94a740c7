import { AbstractEntity } from '@app/common';
import { Column, Entity, OneToMany } from 'typeorm';
import { User } from './user.entity';

@Entity()
export class UserType extends AbstractEntity<UserType> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column({ type: 'varchar' })
  name: string;

  @Column()
  code: string;

  @OneToMany(() => User, (user) => user.userType)
  users: User[];
}
