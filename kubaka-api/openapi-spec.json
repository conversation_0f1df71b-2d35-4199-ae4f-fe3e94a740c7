{"openapi": "3.0.0", "paths": {"/application/project": {"post": {"operationId": "ApplicationController_CreateProject", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllProjects", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/upi/search": {"get": {"operationId": "ApplicationController_searchProjectsByUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/all-application/upi/search": {"get": {"operationId": "ApplicationController_searchProjectsAllApplicationByUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/application/upi/search": {"get": {"operationId": "ApplicationController_searchProjectsAndApplicationsByUPI", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/agency/search": {"get": {"operationId": "ApplicationController_searchProjectsByAgency", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/many/search": {"get": {"operationId": "ApplicationController_searchProjects", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "projectName", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "created_at", "required": true, "in": "query", "schema": {"format": "date-time", "type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/{id}": {"get": {"operationId": "ApplicationController_findOneProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeProject", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/user/{userId}": {"get": {"operationId": "ApplicationController_findProjectByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/getProjectByUserIdIrembo": {"get": {"operationId": "ApplicationController_getProjectByUserIdIrembo", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/and/application/user/{userId}": {"get": {"operationId": "ApplicationController_findProjectAndApplicationByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/upi/search": {"get": {"operationId": "ApplicationController_searchApplicationsByUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/associated/upi/search": {"get": {"operationId": "ApplicationController_searchApplicationsByUPIAssociated", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/and/application/engORarch/{userId}": {"get": {"operationId": "ApplicationController_findProjectAndApplicationBySubmittedUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/project/landOwners/{agencyId}": {"get": {"operationId": "ApplicationController_findAllUsersByAgency", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application": {"post": {"operationId": "ApplicationController_CreateApplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllApplications", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "statusId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/submissionLog": {"get": {"operationId": "ApplicationController_findAllSubmissionLogs", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/submissionLog/{applicationId}": {"get": {"operationId": "ApplicationController_findOneSubmissionLogByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/applicationBydateRange": {"get": {"operationId": "ApplicationController_searchApplicationsByDateRange", "parameters": [{"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/applicationBydateRangeMonitor": {"get": {"operationId": "ApplicationController_searchApplicationsByDateRangeFormonioting", "parameters": [{"name": "startDate", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application": {"get": {"operationId": "ApplicationController_findApplications", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/user/{userId}": {"get": {"operationId": "ApplicationController_findAllApplicationByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/landOwner/{userId}": {"get": {"operationId": "ApplicationController_findAllApplicationByLandOwnerUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyId", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/applicationStatus/{applicationStatusId}": {"get": {"operationId": "ApplicationController_findAllApplicationByApplicationStatusId", "parameters": [{"name": "applicationStatusId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/{applicationStatusId}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByApplicationStatusIdAndAgencyId", "parameters": [{"name": "applicationStatusId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspectionApplication/{applicationStatusId}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId", "parameters": [{"name": "applicationStatusId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspectionApplication/status/{applicationStatusCode}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationWithInspectionByApplicationStatusCodeAndAgencyId", "parameters": [{"name": "applicationStatusCode", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/withouInspectionApplication/{applicationStatusId}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId", "parameters": [{"name": "applicationStatusId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/withouInspectionApplication/status/{applicationStatusCode}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId2", "parameters": [{"name": "applicationStatusCode", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/director/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationForDirectorAndAgencyId", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/applications/report": {"post": {"operationId": "ApplicationController_findApplicationsByParamsInReport", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/application/generalSearch": {"post": {"operationId": "ApplicationController_searchApplications", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"agencyId": {"type": "string", "nullable": true}, "applicationName": {"type": "string", "nullable": true}, "UPI": {"type": "string", "nullable": true}, "applicationStatusId": {"type": "string", "nullable": true}, "permitTypeId": {"type": "string", "nullable": true}, "categoryTypeId": {"type": "string", "nullable": true}, "buildTypeId": {"type": "string", "nullable": true}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/application/nonObjections": {"get": {"operationId": "ApplicationController_findAllApplicationsWithNonObjections", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/nonObjections/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationsWithNonObjectionInAgency", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/categoryType/{categoryTypeId}": {"get": {"operationId": "ApplicationController_findProjectAndApplicationByCategoryTypeId", "parameters": [{"name": "categoryTypeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/user/{userId}": {"get": {"operationId": "ApplicationController_findAllApplicationByUserIdOnDashboard", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/submittedUser/{submittedUserId}": {"get": {"operationId": "ApplicationController_findAllApplicationBySubmittedUserOnDashboard", "parameters": [{"name": "submittedUserId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/dashboardInspectionAll/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyIdOnDashboardInspection", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboardAgency/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyIdOnDashboard", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/inspection/foundationAll/{agencyId}": {"get": {"operationId": "ApplicationController_DashboardForFoundationNotice", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/inspection/occupancyAll/{agencyId}": {"get": {"operationId": "ApplicationController_DashboardForOccupancyPermit", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/allData/inspection/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyIdForInspection", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/allData/inspection/reviewer/{userId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyIdForInspectionReviewer", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/foundation/inspection/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationsByAgencyIdForInspectionFoundation", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/occupancy/inspection/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationsByAgencyIdForInspectionOccupancy", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/Agency/{agencyId}/role/{roleCode}": {"get": {"operationId": "ApplicationController_countApplicationsByStatusByAgencyAndRole", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "roleCode", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/dashboard/all": {"get": {"operationId": "ApplicationController_findAllApplicationOnDashboard", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/{id}": {"get": {"operationId": "ApplicationController_findOneApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/submit/{id}": {"post": {"operationId": "ApplicationController_updateApplicationSubmitController", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationDtoUpdate"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/resubmit/{id}": {"put": {"operationId": "ApplicationController_updateApplicationResubmitController", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationDtoUpdate"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/data/{id}": {"patch": {"operationId": "ApplicationController_updateApplicationToResubmitOnlyData", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/resubmitIrembo": {"patch": {"operationId": "ApplicationController_updateApplicationResubmitIrembo", "parameters": [{"name": "id", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationResubmitIremboDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/{projectId}/permit-type/{permitTypeId}": {"get": {"operationId": "ApplicationController_getApplicationsByProjectAndPermitType", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "permitTypeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/AllDetails/{id}": {"get": {"operationId": "ApplicationController_findOneApplicationAllDetails", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/applicationName/search": {"get": {"operationId": "ApplicationController_findOneApplicationByApplicationId", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/search/{applicationName}": {"get": {"operationId": "ApplicationController_searchApplicationByApplicationName", "parameters": [{"name": "applicationName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/{applicationName}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_searchApplicationByApplicationNameInAgency", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "applicationName", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/draft/{id}": {"delete": {"operationId": "ApplicationController_deleteDraftApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/application/lockApplication/{applicationId}": {"put": {"operationId": "ApplicationController_lockApplication", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LockApplicationDtoUpdate"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/associatedUPI": {"post": {"operationId": "ApplicationController_CreateAssociatedUPI", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssociatedUPIDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllAssociatedUPIs", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/synchronization": {"post": {"operationId": "ApplicationController_synchronization", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/calculatecategory": {"post": {"operationId": "ApplicationController_calculatecategory", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateCategoryDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/checkassociatedUPI": {"get": {"operationId": "ApplicationController_associatedUPIByUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/associatedUPI/{id}": {"get": {"operationId": "ApplicationController_findOneAssociatedUPI", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateAssociatedUPI", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssociatedUPIDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeAssociatedUPI", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/associatedUPI/ByProject/{projectId}": {"get": {"operationId": "ApplicationController_findOneAssociatedUPIByProjectId", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/associatedUPI/upi/search": {"get": {"operationId": "ApplicationController_checkUPIInAssociatedUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/associatedUPI/InListIsUPIExist": {"get": {"operationId": "ApplicationController_checkIfUPIExistInAssociatedUPI", "parameters": [{"name": "upi", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/permitType": {"post": {"operationId": "ApplicationController_CreatePermitType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllPermitTypes", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/permitType/{id}": {"get": {"operationId": "ApplicationController_findOnePermitType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updatePermitType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removePermitType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/permitType/{name}": {"get": {"operationId": "ApplicationController_findOnePermitTypeByName", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/permitType/code/search": {"get": {"operationId": "ApplicationController_searchPermitTypeByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/categoryRule": {"post": {"operationId": "ApplicationController_CreateCategoryRule", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRuleDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllCategoryRules", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/categoryRule/{id}": {"get": {"operationId": "ApplicationController_findOneCategoryRule", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateCategoryRule", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRuleDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeCategoryRule", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/categoryType": {"post": {"operationId": "ApplicationController_CreateCategoryType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllCategoryTypes", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/categoryType/{id}": {"get": {"operationId": "ApplicationController_findOneCategoryType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateCategoryType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeCategoryType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/categoryType/code/search": {"get": {"operationId": "ApplicationController_searchCategoryTypeByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/buildType": {"post": {"operationId": "ApplicationController_CreateBuildType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuildTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllBuildTypes", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/buildType/{id}": {"get": {"operationId": "ApplicationController_findOneBuildType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateBuildType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuildTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeBuildType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/buildType/code/search": {"get": {"operationId": "ApplicationController_searchBuildTypeByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/documentType": {"post": {"operationId": "ApplicationController_CreateDocumentType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllDocumentTypes", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/documentType/{id}": {"get": {"operationId": "ApplicationController_findOneDocumentType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateDocumentType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeDocumentType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/documentType/code/search": {"get": {"operationId": "ApplicationController_searchDocumentTypeByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/requiredDocument": {"post": {"operationId": "ApplicationController_CreateRequiredDocument", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequiredDocumentDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllRequiredDocuments", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/requiredDocument/{id}": {"get": {"operationId": "ApplicationController_findOneRequiredDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateRequiredDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequiredDocumentDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeRequiredDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/requiredDocument/permitType/{permitTypeId}": {"get": {"operationId": "ApplicationController_findAllRequiredDocumentByPermitTypeId", "parameters": [{"name": "permitTypeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/requiredDocument/permitType/{permitTypeId}/category/{categoryId}": {"get": {"operationId": "ApplicationController_findAllRequiredDocumentByPermitTypeAndCategory", "parameters": [{"name": "permitTypeId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "categoryId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/technologySurvey": {"post": {"operationId": "ApplicationController_CreateTechnologySurvey", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnologySurveyDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllTechnologySurveys", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/technologySurvey/{id}": {"get": {"operationId": "ApplicationController_findOneTechnologySurvey", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateTechnologySurvey", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TechnologySurveyDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeTechnologySurvey", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/applicationStatus": {"post": {"operationId": "ApplicationController_CreateApplicationStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllApplicationStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/applicationStatus/{id}": {"get": {"operationId": "ApplicationController_findOneApplicationStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateApplicationStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeApplicationStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/applicationStatus/code/search": {"get": {"operationId": "ApplicationController_searchApplicationStatusByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/projectStatus": {"post": {"operationId": "ApplicationController_CreateProjectStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllProjectStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/projectStatus/{id}": {"get": {"operationId": "ApplicationController_findOneProjectStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateProjectStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeProjectStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/projectStatus/code/search": {"get": {"operationId": "ApplicationController_searchProjectStatusByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/assignee": {"post": {"operationId": "ApplicationController_CreateAssignee", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssigneeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllAssignee", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/{id}": {"get": {"operationId": "ApplicationController_findOneAssignee", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateAssignee", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAssigneeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeAssignee", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/byProject/{projectId}": {"get": {"operationId": "ApplicationController_findAssigneeOfTheProject", "parameters": [{"name": "projectId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/deleteEngineerOnProject": {"post": {"operationId": "ApplicationController_deleteEngineerOnProject", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEngineerOnProjectDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/rejectProjectAsEngOrArch": {"post": {"operationId": "ApplicationController_rejectProjectAssigned", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectProjectDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/{id}/status": {"put": {"operationId": "ApplicationController_updateAssignStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/reassign/{id}": {"put": {"operationId": "ApplicationController_reassignEngineer", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/assignee/licenseNumber/search": {"get": {"operationId": "ApplicationController_searchAssigneeByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application", "application"]}}, "/application/assignUsersFoReview/{applicationId}/assign-users": {"post": {"operationId": "ApplicationController_assignUsersFoReview", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/getReviewers/{applicationId}/review-user-ids": {"get": {"operationId": "ApplicationController_getReviewUserIdsByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/removeReviewer/{applicationId}/remove-review-user/{userId}": {"delete": {"operationId": "ApplicationController_removeUserFromReview", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reassignReviewer/{applicationId}/reassign-review-user/{userId}/doneBy/{doneByUserId}": {"delete": {"operationId": "ApplicationController_reassignUserFromReviewConsider", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "doneByUserId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reassignReviewer/{applicationId}/reassign-review-user/{userId}": {"delete": {"operationId": "ApplicationController_reassignUserFromReview", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/addReviewer/{applicationId}/add-review-user/{userId}": {"post": {"operationId": "ApplicationController_addUserToReview", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/allApplicationsForReview": {"get": {"operationId": "ApplicationController_findAllWithAssignedUsers", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/by-assign-user-for-review/{userId}": {"get": {"operationId": "ApplicationController_findByAssignedUserForReview", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/questionCategory": {"post": {"operationId": "ApplicationController_CreateQuestionCategory", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionCategoryDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["settings"]}, "get": {"operationId": "ApplicationController_findAllQuestionCategory", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/questionCategory/{id}": {"get": {"operationId": "ApplicationController_findOneQuestionCategory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "patch": {"operationId": "ApplicationController_updateQuestionCategory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QuestionCategoryDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["settings"]}, "delete": {"operationId": "ApplicationController_removeQuestionCategory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/equipmentCapacity/code/search": {"get": {"operationId": "ApplicationController_searchQuestionCategoryByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["settings"]}}, "/application/otherInfoApplication": {"post": {"operationId": "ApplicationController_CreateOtherInfoApplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtherInfoApplicationDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllOtherInfoApplication", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/otherInfoApplication/{id}": {"get": {"operationId": "ApplicationController_findOneOtherInfoApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateOtherInfoApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtherInfoApplicationDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeOtherInfoApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/permitQuestion": {"post": {"operationId": "ApplicationController_CreatePermitQuestion", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitQuestionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllPermitQuestion", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/permitQuestion/{id}": {"get": {"operationId": "ApplicationController_findOnePermitQuestion", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updatePermitQuestion", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitQuestionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removePermitQuestion", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/permitQuestion/permitType/{permitTypeId}": {"get": {"operationId": "ApplicationController_findPermitQuestionByPermitType", "parameters": [{"name": "permitTypeId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/answer": {"post": {"operationId": "ApplicationController_CreateAnswer", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnswerDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllAnswer", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/answer/{id}": {"get": {"operationId": "ApplicationController_findOneAnswer", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateAnswer", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnswerDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeAnswer", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication": {"post": {"operationId": "ApplicationController_CreateReviewersOnApplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewersOnApplicationDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllReviewersOnApplication", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/{id}": {"get": {"operationId": "ApplicationController_findOneReviewersOnApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateReviewersOnApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewersOnApplicationDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeReviewersOnApplication", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewerOnApplication/{applicationId}": {"get": {"operationId": "ApplicationController_findReviewerOnApplicationByApplication", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/findReviewersOnApplication/{applicationId}": {"get": {"operationId": "ApplicationController_findOneReviewersOnApplicationByApplication", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/findReviewersOnApplication/{userId}": {"get": {"operationId": "ApplicationController_findOneReviewersOnApplicationByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/application/{applicationId}/reviewer/{userId}": {"get": {"operationId": "ApplicationController_findTheReviewersOnSpecificApplication", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/myBox/{userId}": {"get": {"operationId": "ApplicationController_countApplicationOfReviewer", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/myBox/allApplications/{userId}": {"get": {"operationId": "ApplicationController_getApplicationsOfReviewer", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/myBox/allApplications/all/{userId}": {"get": {"operationId": "ApplicationController_getAllApplicationsOfReviewer", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/occupancy": {"post": {"operationId": "ApplicationController_CreateOccupancyInspection", "parameters": [], "requestBody": {"required": true, "description": "Create occupancy Inspection  with file upload", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/OccupancyInspectionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}, "get": {"operationId": "ApplicationController_findAllOccupancyInspection", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/applicationChartDashboard": {"post": {"operationId": "ApplicationController_generateChart", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataForChartDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/occupancy/{id}": {"get": {"operationId": "ApplicationController_findOneOccupancyInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateOccupancyInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OccupancyInspectionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeOccupancyInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/occupancy/ByApplication/{applicationId}": {"get": {"operationId": "ApplicationController_findOneOccupancyInspectionByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/foundationNotice": {"post": {"operationId": "ApplicationController_CreateFoundationInspectionWithAFile", "parameters": [], "requestBody": {"required": true, "description": "Create Foundation Notice Inspection  with file upload", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/FoundationInspectionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/foundation": {"get": {"operationId": "ApplicationController_findAllFoundationInspection", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/foundation/{id}": {"get": {"operationId": "ApplicationController_findOneFoundationInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}, "patch": {"operationId": "ApplicationController_updateFoundationInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FoundationInspectionDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["application"]}, "delete": {"operationId": "ApplicationController_removeFoundationInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/inspection/foundation/ByApplication/{applicationId}": {"get": {"operationId": "ApplicationController_findOneFoundationInspectionByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/dashboardWithoutInspection/{agencyId}": {"get": {"operationId": "ApplicationController_findAllApplicationByAgencyWithoutInspection2", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/allApplicationWithoutInspection/{agencyId}": {"get": {"operationId": "ApplicationController_allApplicationByAgencyWithoutInspection2", "parameters": [{"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["application"]}}, "/application/reviewersOnApplication/staffReport": {"post": {"operationId": "ApplicationController_findApprovalsByUserIdInDateRange", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewerReportDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["application"]}}, "/application/applicationStatus/{applicationStatusCode}/agency/{agencyId}": {"get": {"operationId": "ApplicationController_findApplicationsByApplicationCodeAndAgencyId", "parameters": [{"name": "applicationStatusCode", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "agencyId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "page", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "limit", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}}}, "/approval/approvalLevel": {"post": {"operationId": "ApprovalController_CreateApprovalLevel", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalLevelDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllApprovalLevels", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalLevel/{id}": {"get": {"operationId": "ApprovalController_findOneApprovalLevel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updateApprovalLevel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalLevelDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removeApprovalLevel", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalLevel/code/search": {"get": {"operationId": "ApprovalController_searchApprovalLevelByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalStatus": {"post": {"operationId": "ApprovalController_CreateApprovalStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllApprovalStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalStatus/{id}": {"get": {"operationId": "ApprovalController_findOneApprovalStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updateApprovalStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removeApprovalStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalStatus/code/search": {"get": {"operationId": "ApprovalController_searchApprovalStatusByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalStatus/approvalLevel/{approvalLevelId}": {"get": {"operationId": "ApprovalController_findApprovalStatusByApprovalLevelId", "parameters": [{"name": "approvalLevelId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval": {"post": {"operationId": "ApprovalController_createApplicationApproval", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationApprovalDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllApplicationApprovals", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalWithIrembo": {"post": {"operationId": "ApprovalController_createApplicationApprovalWithIrembo", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationApprovalWithIremboDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval/{id}": {"get": {"operationId": "ApprovalController_findOneApplicationApproval", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updateApplicationApproval", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationApprovalDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removeApplicationApproval", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval/applicationId/{applicationId}": {"get": {"operationId": "ApprovalController_findOneApplicationApprovalByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval/{applicationId}/status/{applicationStatusId}": {"get": {"operationId": "ApprovalController_findOneApplicationApprovalByApplicationIdAndApplicationStatus", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "applicationStatusId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval/reviewer/{userId}": {"get": {"operationId": "ApprovalController_findApplicationsApprovalByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApproval/staffReport": {"post": {"operationId": "ApprovalController_findApprovalsByUserIdInDateRange", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReviewerReportDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}}, "/approval/permitCheckList": {"post": {"operationId": "ApprovalController_CreatePermitCheckList", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitCheckListDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllPermitCheckLists", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/permitCheckList/{id}": {"get": {"operationId": "ApprovalController_findOnePermitCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updatePermitCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermitCheckListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removePermitCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/permitCheckList/code/search": {"get": {"operationId": "ApprovalController_searchPermitCheckListByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalCheckList": {"post": {"operationId": "ApprovalController_CreateApplicationApprovalCheckList", "parameters": [], "requestBody": {"required": true, "description": "Create Application checklist with file upload", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/ApplicationApprovalCheckListDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllApplicationApprovalCheckLists", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalCheckList/{id}": {"get": {"operationId": "ApprovalController_findOneApplicationApprovalCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updateApplicationApprovalCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationApprovalCheckListDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removeApplicationApprovalCheckList", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalCheckList/allDetails/{id}": {"get": {"operationId": "ApprovalController_findOneApplicationApprovalCheckListAllDetails", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalCheckList/user/{userId}": {"get": {"operationId": "ApprovalController_findAllApplicationApprovalCheckListByUserId", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/applicationApprovalCheckList/all/{applicationId}": {"get": {"operationId": "ApprovalController_findAllApplicationApprovalCheckListByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalDocument": {"post": {"operationId": "ApprovalController_CreateApprovalDocument", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalDocumentDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["approval"]}, "get": {"operationId": "ApprovalController_findAllApprovalDocuments", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/approvalDocument/{id}": {"get": {"operationId": "ApprovalController_findOneApprovalDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "patch": {"operationId": "ApprovalController_updateApprovalDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovalDocumentDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["approval"]}, "delete": {"operationId": "ApprovalController_removeApprovalDocument", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/approval/application/dashboardAgency/RHA": {"get": {"operationId": "ApprovalController_findAllApplicationByAgencyIdOnDashboardRHA", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["approval", "application"]}}, "/approval/application/approvalStatus/{approvalStatusId}": {"get": {"operationId": "ApprovalController_findAllApplicationByApprovalStatusId", "parameters": [{"name": "approvalStatusId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["approval"]}}, "/invoice/invoiceStatus": {"post": {"operationId": "InvoiceController_CreateInvoiceStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllInvoiceStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceStatus/{id}": {"get": {"operationId": "InvoiceController_findOneInvoiceStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updateInvoiceStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removeInvoiceStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceStatus/code/search": {"get": {"operationId": "InvoiceController_searchInvoiceStatusByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/price": {"post": {"operationId": "InvoiceController_CreatePrice", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllPrice", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/price/{id}": {"get": {"operationId": "InvoiceController_findOnePrice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updatePrice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PriceDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removePrice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceType": {"post": {"operationId": "InvoiceController_CreateInvoiceType", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceTypeDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllInvoiceType", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceType/{id}": {"get": {"operationId": "InvoiceController_findOneInvoiceType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updateInvoiceType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceTypeDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removeInvoiceType", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceType/code/search": {"get": {"operationId": "InvoiceController_searchInvoiceByCode", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceItem": {"post": {"operationId": "InvoiceController_CreateInvoiceItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllInvoiceItem", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoiceItem/{id}": {"get": {"operationId": "InvoiceController_findOneInvoiceItem", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updateInvoiceItem", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceItemDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removeInvoiceItem", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice": {"post": {"operationId": "InvoiceController_CreateInvoice", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllInvoices", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/{id}": {"get": {"operationId": "InvoiceController_findOneInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updateInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removeInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/allData/{id}/{applicationId}": {"get": {"operationId": "InvoiceController_findOneInvoiceAllData", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/application/{applicationId}": {"get": {"operationId": "InvoiceController_findAllInvoiceOfApplication", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/zeroAmount/application/{applicationId}": {"get": {"operationId": "InvoiceController_findAllInvoiceWithZeroAmount", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/withZeroAmount/application/{applicationId}": {"get": {"operationId": "InvoiceController_findInvoiceWithZeroAmount", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/applicant/{applicantUserId}/invoiceStatus/{invoiceStatusId}": {"get": {"operationId": "InvoiceController_findAllInvoiceWithInvoiceStatusOfAnUser", "parameters": [{"name": "applicantUserId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "invoiceStatusId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/agency/{agencyCode}": {"get": {"operationId": "InvoiceController_findAllInvoiceByAgency", "parameters": [{"name": "agencyCode", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/cancel/{id}": {"patch": {"operationId": "InvoiceController_cancelInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/refunded/{id}": {"patch": {"operationId": "InvoiceController_refundedInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/invoiceStatus/{id}": {"patch": {"operationId": "InvoiceController_updateInvoiceStatusOnInvoice", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInvoiceDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/invoiceNumber/search": {"get": {"operationId": "InvoiceController_findInvoiceByInvoiceNumber", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/invoice/applicantUserId/search": {"get": {"operationId": "InvoiceController_getInvoicesByUserId", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/receipt": {"post": {"operationId": "InvoiceController_CreateReceipt", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice"]}, "get": {"operationId": "InvoiceController_findAllReceipt", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/receipt/{id}": {"get": {"operationId": "InvoiceController_findOneReceipt", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "patch": {"operationId": "InvoiceController_updateReceipt", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReceiptDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["invoice"]}, "delete": {"operationId": "InvoiceController_removeReceipt", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/invoice/updateToIrembo": {"post": {"operationId": "InvoiceController_updateInvoiceToIrembo", "parameters": [], "requestBody": {"required": true, "description": "Invoice update payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateInvoiceDto"}, "examples": {"example": {"value": {"invoiceId": "12345", "amount": 10000, "expirationTime": "2024-11-28T16:04:30.281Z"}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["invoice", "Invoices"]}}, "/invoice/get-price-invoice": {"get": {"operationId": "InvoiceController_getPrice", "parameters": [{"name": "permitTypeId", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "rangeNumber", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["invoice"]}}, "/certificate/certificate": {"post": {"operationId": "CertificateController_CreateCertificate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificateDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllCertificates", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/{id}": {"get": {"operationId": "CertificateController_findOneCertificate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "patch": {"operationId": "CertificateController_updateCertificate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificateDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "delete": {"operationId": "CertificateController_removeCertificate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/allData/{id}": {"get": {"operationId": "CertificateController_findOneCertificateAllData", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/user/{userId}": {"get": {"operationId": "CertificateController_findAllCertificateAllData", "parameters": [{"name": "userId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/agency/{agencyCode}": {"get": {"operationId": "CertificateController_findAllCertificateByAgency", "parameters": [{"name": "agencyCode", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/cancel/{id}": {"patch": {"operationId": "CertificateController_cancelCertificate", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/updateStatus/{invoiceNumber}": {"patch": {"operationId": "CertificateController_updateStatusTo1OnCertificate", "parameters": [{"name": "invoiceNumber", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/certificateNumber/search": {"get": {"operationId": "CertificateController_findCertificateByCertificateNumber", "parameters": [{"name": "search", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/applicantUserId/search": {"get": {"operationId": "CertificateController_searchCertificatesByUserId", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/cert/{applicantUserId}": {"get": {"operationId": "CertificateController_getCertificatesByUserId", "parameters": [{"name": "applicantUserId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/signatory": {"post": {"operationId": "CertificateController_CreateSignatory", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignatoryDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllSignatorys", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/signatory/{id}": {"get": {"operationId": "CertificateController_findOneSignatory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "patch": {"operationId": "CertificateController_updateSignatory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignatoryDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "delete": {"operationId": "CertificateController_removeSignatory", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/transfer": {"post": {"operationId": "CertificateController_TransferCertificate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferCertificateDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllTransfers", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/transfer/{certificateNumber}": {"get": {"operationId": "CertificateController_getTransferCertificateByCertificateNumber", "parameters": [{"name": "certificateNumber", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/developmentStatus": {"post": {"operationId": "CertificateController_DevelopmentStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DevelopmentStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllDevelopmentStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/developmentStatus/{id}": {"get": {"operationId": "CertificateController_findOneDevelopmentStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/siteStatus": {"post": {"operationId": "CertificateController_CreateSiteStatus", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SiteStatusDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllSiteStatus", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/siteStatus/{id}": {"get": {"operationId": "CertificateController_findOneSiteStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "patch": {"operationId": "CertificateController_updateSiteStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SiteStatusDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["certificate"]}, "delete": {"operationId": "CertificateController_removeSiteStatus", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection": {"post": {"operationId": "CertificateController_CreateRandomInspection", "parameters": [], "requestBody": {"required": true, "description": "Create random inspection with file upload", "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/RandomInspectionDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}, "get": {"operationId": "CertificateController_findAllRandomInspection", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/expiring": {"get": {"operationId": "CertificateController_getExpiringCertificates", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/expiringSendSMS": {"get": {"operationId": "CertificateController_getExpiringCertificatesAndSendSMS", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/expiringSendNotifications": {"get": {"operationId": "CertificateController_getExpiringCertificatesAndSendNotifications", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection/{id}": {"get": {"operationId": "CertificateController_findOneRandomInspection", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection/allData/{id}": {"get": {"operationId": "CertificateController_findOneRandomInspectionAllData", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection/upi/{UPI}": {"get": {"operationId": "CertificateController_findRandomInspectionAllDataByUPI", "parameters": [{"name": "UPI", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection/siteStatus/{siteStatusId}": {"get": {"operationId": "CertificateController_findRandomInspectionAllDataByStatus", "parameters": [{"name": "siteStatusId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/randomInspection/certificate/{certificateId}": {"get": {"operationId": "CertificateController_findRandomInspectionByCertificateId", "parameters": [{"name": "certificateId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/check-NCPCertificateValidity": {"post": {"operationId": "CertificateController_isNCPCertificateValid", "parameters": [], "requestBody": {"required": true, "description": "UPI for check the certificate validity", "content": {"application/json": {"schema": {"type": "object", "properties": {"upi": {"type": "string", "format": "string", "example": "5/06/11/04/642", "description": "UPI for check the certificate validity"}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/check-IfRenewalOfNCPCertificateValidity": {"post": {"operationId": "CertificateController_isRenewalForNCPCertificateValid", "parameters": [], "requestBody": {"required": true, "description": "UPI for check the certificate validity", "content": {"application/json": {"schema": {"type": "object", "properties": {"upi": {"type": "string", "format": "string", "example": "5/06/11/04/642", "description": "UPI for check the certificate validity"}}}}}}, "responses": {"201": {"description": ""}}, "tags": ["certificate"]}}, "/certificate/certificate/application/{applicationId}": {"get": {"operationId": "CertificateController_getCertificatesByApplicationId", "parameters": [{"name": "applicationId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["certificate"]}}}, "info": {"title": "KUBAKA BPMIS", "description": "KUBAKA is a national platform that allows citizens and investors to apply for a construction permit in accordance with the land use and urbanization master plan. This system is intended to be used by: \n\n- **Rwandan citizen** that has a national ID and a plot in Rwanda\n- **Investor** with a valid passport and a plot in Rwanda \n- **Engineer**: Any engineer who is recognized by the association of engineers in Rwanda.\n- **Architect**: Any Architect who is recognized by the association of architects in Rwanda.\n\n**Application process to receive a construction permit**\n\n- **Create a project:** A project and its details will be created based on your UPI and what is allowed to be built on your plot under Rwandan law.\n- **Submit your application:** Your application needs to be submitted, and you need to make sure you don not have any unpaid taxes or EIA certificates.\n- **Application under Review:** Upon receiving your application, a member of the RHA team will review it, and the engineer will make sure everything is in compliance with the law.\n- **Invoice to be paid:** Invoices will be generated after the application is approved, and you must pay to receive the certificate.\n- **Certificate generated :** Your certificate will be available for download after payment has been made.\n\n**How to integrate with KUBAKA APIs**\n\nIntegration with the Kubaka API requires you to follow the steps below:\n1. The public and secret keys for the test environment would be shared with you during signup.\n2. Setup: The requestor must have a Kubaka API key that must be sent in header of any request that goes to the server.\n3. VPN chanel: They must be a VPN chanel between two parties.\n### Resource\n - **Test:** http://testing.kubaka.gov.rw/docs\n - **Production:** http://kubaka.gov.rw/docs\n- **Headers:** kubaka-secretKey: secreKey \n", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"ProjectDto": {"type": "object", "properties": {"isAssociatedUpi": {"type": "boolean"}, "upi": {"type": "string", "description": "The field upi is required"}, "ownerFullName": {"type": "string", "description": "The field owner<PERSON>ullName is required"}, "ownerIdNo": {"type": "string", "description": "The field ownerIdNo is required"}, "isFromOldSystem": {"type": "boolean"}, "isFromOldSystemDevelopersName": {"type": "string"}, "isFromOldSystemPermitNumber": {"type": "string"}, "isFromOldSystemInvoiceNumber": {"type": "string"}, "isRRAVerified": {"type": "boolean"}, "isUnderMortgage": {"type": "boolean"}, "isUnderRestriction": {"type": "boolean"}, "centralCoordinateX": {"type": "string"}, "centralCoordinateY": {"type": "string"}, "villageCode": {"type": "string"}, "villageName": {"type": "string"}, "cellCode": {"type": "string"}, "cellName": {"type": "string"}, "sectorCode": {"type": "string"}, "sectorName": {"type": "string"}, "districtCode": {"type": "string"}, "districtName": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceName": {"type": "string"}, "selectedUse": {"type": "string", "description": "The field selectedUse id is required"}, "selectedCategoryUse": {"type": "string", "description": "The field selectedCategoryUse id is required"}, "projectName": {"type": "string", "description": "The field projectName is required"}, "projectDescription": {"type": "string", "description": "The field projectDescription is required"}, "plotSize": {"type": "number", "description": "The field plotSize is required"}, "originalPlotSize": {"type": "number", "description": "The field originalPlotSize is required"}, "userId": {"type": "string", "description": "The field user id is required"}, "agencyId": {"type": "string", "description": "The field agency id is required"}, "projectStatusId": {"type": "string", "description": "The field technologySurvey id is required"}}, "required": ["isAssociatedUpi", "upi", "ownerFullName", "ownerIdNo", "isFromOldSystem", "isFromOldSystemDevelopersName", "isFromOldSystemPermitNumber", "isFromOldSystemInvoiceNumber", "isRRAVerified", "isUnderMortgage", "isUnderRestriction", "centralCoordinateX", "centralCoordinateY", "villageCode", "villageName", "cellCode", "cellName", "sectorCode", "sectorName", "districtCode", "districtName", "provinceCode", "provinceName", "selectedUse", "selectedCategoryUse", "projectName", "projectDescription", "plotSize", "originalPlotSize", "userId", "agencyId", "projectStatusId"]}, "OtherInfoApplicationDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field userId is required"}, "licenseNumber": {"type": "string", "description": "The field licenseNumber is required"}, "permitTypeId": {"type": "string", "description": "The field permitType id is required"}, "applicationId": {"type": "string"}, "doYouHaveTheOccupancy": {"type": "string"}, "isFastAidBox": {"type": "string"}, "disabilityToiletsFlipUpGrabBars": {"type": "string"}, "paraLighteningSystem": {"type": "string"}, "equipmentCapacity": {"type": "string"}, "constructionMethod": {"type": "string"}, "fireAlarmSystemWithAnAlarmBellOnEach": {"type": "string"}, "whyNotFireAlarmSystemWithAnAlarmBellOnEach": {"type": "string"}, "fireExtinguishersEvery50mOnEachFloor": {"type": "string"}, "whyNotFireExtinguishersEvery50mOnEachFloor": {"type": "string"}, "functioningExitSignsOnEachFloor": {"type": "string"}, "whyNotfunctioningExitSignsOnEachFloor": {"type": "string"}, "anEmergencyExitOnEachFloor": {"type": "string"}, "whyNotanEmergencyExitOnEachFloor": {"type": "string"}, "floorPlanOnEachLevel": {"type": "string"}, "whyNotfloorPlanOnEachLevel": {"type": "string"}, "numberSignOnEachFloor": {"type": "string"}, "whyNotnumberSignOnEachFloor": {"type": "string"}, "signForbiddingTheUseOfElevatorsInCaseOfFire": {"type": "string"}, "whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire": {"type": "string"}, "landingSpaceOnTopOfTheBuildingForHelicopters": {"type": "string"}, "whyNotlandingSpaceOnTopOfTheBuildingForHelicopters": {"type": "string"}, "CCTVCameras": {"type": "string"}, "whyNotCCTVCameras": {"type": "string"}, "WalkThroughAndHeldMetalDetect": {"type": "string"}, "whyNotWalkThroughAndHeldMetalDetect": {"type": "string"}, "UnderSearchMirror": {"type": "string"}, "whyNotUnderSearchMirror": {"type": "string"}, "LuggageScanners": {"type": "string"}, "whyNotLuggageScanners": {"type": "string"}, "PlatesIndicatingEmergencyResponseUnitsPhoneNumbers": {"type": "string"}, "whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers": {"type": "string"}, "EmergencyEvacuationPlan": {"type": "string"}, "whyNotEmergencyEvacuationPlan": {"type": "string"}, "SecurityManagerAndStaffCameras": {"type": "string"}, "whyNotSecurityManagerAndStaffCameras": {"type": "string"}, "AnInternalCommunicationSystem": {"type": "string"}, "whyNotAnInternalCommunicationSystem": {"type": "string"}, "BroadBandInternetServices": {"type": "string"}, "whyNotBroadBandInternetServices": {"type": "string"}, "StaffAndVisitorAccessCards": {"type": "string"}, "whyNotStaffAndVisitorAccessCards": {"type": "string"}, "applicationForFixedTelephoneLineConnection": {"type": "string"}, "areThereAnyFacilitiesForTheDisabledProvidedBuilding": {"type": "string"}, "whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding": {"type": "string"}, "stageOfConstruction": {"type": "string"}, "supervisingFirmSiteEngineer": {"type": "string"}, "remarks": {"type": "string"}, "dateForRequestedInspection": {"type": "string"}}, "required": ["userId", "licenseNumber", "permitTypeId", "applicationId", "doYouHaveTheOccupancy", "isFastAidBox", "disabilityToiletsFlipUpGrabBars", "paraLighteningSystem", "equipmentCapacity", "constructionMethod", "fireAlarmSystemWithAnAlarmBellOnEach", "whyNotFireAlarmSystemWithAnAlarmBellOnEach", "fireExtinguishersEvery50mOnEachFloor", "whyNotFireExtinguishersEvery50mOnEachFloor", "functioningExitSignsOnEachFloor", "whyNotfunctioningExitSignsOnEachFloor", "anEmergencyExitOnEachFloor", "whyNotanEmergencyExitOnEachFloor", "floorPlanOnEachLevel", "whyNotfloorPlanOnEachLevel", "numberSignOnEachFloor", "whyNotnumberSignOnEachFloor", "signForbiddingTheUseOfElevatorsInCaseOfFire", "whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire", "landingSpaceOnTopOfTheBuildingForHelicopters", "whyNotlandingSpaceOnTopOfTheBuildingForHelicopters", "CCTVCameras", "whyNotCCTVCameras", "WalkThroughAndHeldMetalDetect", "whyNotWalkThroughAndHeldMetalDetect", "UnderSearchMirror", "whyNotUnderSearchMirror", "LuggageScanners", "whyNotLuggageScanners", "PlatesIndicatingEmergencyResponseUnitsPhoneNumbers", "whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers", "EmergencyEvacuationPlan", "whyNotEmergencyEvacuationPlan", "SecurityManagerAndStaffCameras", "whyNotSecurityManagerAndStaffCameras", "AnInternalCommunicationSystem", "whyNotAnInternalCommunicationSystem", "BroadBandInternetServices", "whyNotBroadBandInternetServices", "StaffAndVisitorAccessCards", "whyNotStaffAndVisitorAccessCards", "applicationForFixedTelephoneLineConnection", "areThereAnyFacilitiesForTheDisabledProvidedBuilding", "whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding", "stageOfConstruction", "supervisingFirmSiteEngineer", "remarks", "dateForRequestedInspection"]}, "ApplicationDto": {"type": "object", "properties": {"upi": {"type": "string"}, "applicationNumberForIremboHub": {"type": "string"}, "isAssociatedUpi": {"type": "boolean"}, "waterConsumption": {"type": "number"}, "electricityConsumption": {"type": "number"}, "DistanceToTheNearestLandIn": {"type": "number"}, "ProjectCostInUSD": {"type": "number"}, "ProjectCostInRwf": {"type": "number"}, "buildUpArea": {"type": "number"}, "numberOfFloor": {"type": "number"}, "grossFloorArea": {"type": "number"}, "numberOfParkingSpace": {"type": "number"}, "priceOfDwellingUnitRwf": {"type": "number"}, "capacityInformation": {"type": "number"}, "numberOfDwellingUnits": {"type": "string"}, "DescriptionOfOperation": {"type": "string"}, "projectName": {"type": "string"}, "projectDescription": {"type": "string"}, "percentageSpaceUse": {"type": "string"}, "userId": {"type": "string", "description": "The field user id is required"}, "submittedByUserId": {"type": "string", "description": "The field submittedByUserId id is required"}, "certificateNumberEIA": {"type": "string"}, "combiningPlotSize": {"type": "number"}, "isEIAVerified": {"type": "boolean"}, "projectId": {"type": "string", "description": "The field project id is required"}, "permitTypeId": {"type": "string", "description": "The field permitType id is required"}, "categoryTypeId": {"type": "string", "description": "The field categoryTypeId id is required"}, "buildTypeId": {"type": "string", "description": "The field buildType id is required"}, "technologySurveyId": {"type": "string", "description": "The field technologySurvey id is required"}, "agencyId": {"type": "string", "description": "The field agency id is required"}, "applicationStatusId": {"type": "string", "description": "The field applicationStatus id is required"}, "permitTypeCode": {"type": "string", "description": "The field categoryType id is required"}, "agencyCode": {"type": "string", "description": "The field agencyCode id is required"}, "other": {"description": "Other related details", "allOf": [{"$ref": "#/components/schemas/OtherInfoApplicationDto"}]}}, "required": ["upi", "applicationNumberForIremboHub", "isAssociatedUpi", "waterConsumption", "electricityConsumption", "DistanceToTheNearestLandIn", "ProjectCostInUSD", "ProjectCostInRwf", "buildUpArea", "numberOfFloor", "grossFloorArea", "numberOfParkingSpace", "priceOfDwellingUnitRwf", "capacityInformation", "numberOfDwellingUnits", "DescriptionOfOperation", "projectName", "projectDescription", "percentageSpaceUse", "userId", "submittedByUserId", "certificateNumberEIA", "combiningPlotSize", "isEIAVerified", "projectId", "permitTypeId", "categoryTypeId", "buildTypeId", "technologySurveyId", "agencyId", "applicationStatusId", "permitTypeCode", "agencyCode", "other"]}, "ApplicationDtoUpdate": {"type": "object", "properties": {"projectId": {"type": "string", "description": "The field project id is required"}, "userId": {"type": "string", "description": "The field user id is required"}, "permitTypeId": {"type": "string", "description": "The field permitType id is required"}, "categoryTypeId": {"type": "string", "description": "The field categoryTypeId id is required"}, "buildTypeId": {"type": "string", "description": "The field buildType id is required"}, "agencyId": {"type": "string", "description": "The field agency id is required"}, "applicationStatusId": {"type": "string", "description": "The field applicationStatus id is required"}, "permitTypeCode": {"type": "string", "description": "The field categoryType id is required"}, "agencyCode": {"type": "string", "description": "The field agencyCode id is required"}}, "required": ["projectId", "userId", "permitTypeId", "categoryTypeId", "buildTypeId", "agencyId", "applicationStatusId", "permitTypeCode", "agencyCode"]}, "ApplicationResubmitIremboDto": {"type": "object", "properties": {"upi": {"type": "string"}, "applicationNumberForIremboHub": {"type": "string"}, "isAssociatedUpi": {"type": "boolean"}, "waterConsumption": {"type": "number"}, "electricityConsumption": {"type": "number"}, "DistanceToTheNearestLandIn": {"type": "number"}, "ProjectCostInUSD": {"type": "number"}, "ProjectCostInRwf": {"type": "number"}, "buildUpArea": {"type": "number"}, "numberOfFloor": {"type": "number"}, "grossFloorArea": {"type": "number"}, "numberOfParkingSpace": {"type": "number"}, "priceOfDwellingUnitRwf": {"type": "number"}, "capacityInformation": {"type": "number"}, "numberOfDwellingUnits": {"type": "string"}, "DescriptionOfOperation": {"type": "string"}, "projectName": {"type": "string"}, "projectDescription": {"type": "string"}, "percentageSpaceUse": {"type": "string"}, "userId": {"type": "string", "description": "The field user id is required"}, "submittedByUserId": {"type": "string", "description": "The field submittedByUserId id is required"}, "certificateNumberEIA": {"type": "string"}, "combiningPlotSize": {"type": "number"}, "isEIAVerified": {"type": "boolean"}, "projectId": {"type": "string", "description": "The field project id is required"}, "permitTypeId": {"type": "string", "description": "The field permitType id is required"}, "categoryTypeId": {"type": "string", "description": "The field categoryTypeId id is required"}, "buildTypeId": {"type": "string", "description": "The field buildType id is required"}, "technologySurveyId": {"type": "string", "description": "The field technologySurvey id is required"}, "agencyId": {"type": "string", "description": "The field agency id is required"}, "applicationStatusId": {"type": "string", "description": "The field applicationStatus id is required"}, "permitTypeCode": {"type": "string", "description": "The field categoryType id is required"}, "agencyCode": {"type": "string", "description": "The field agencyCode id is required"}, "other": {"description": "Other related details", "allOf": [{"$ref": "#/components/schemas/OtherInfoApplicationDto"}]}}, "required": ["upi", "applicationNumberForIremboHub", "isAssociatedUpi", "waterConsumption", "electricityConsumption", "DistanceToTheNearestLandIn", "ProjectCostInUSD", "ProjectCostInRwf", "buildUpArea", "numberOfFloor", "grossFloorArea", "numberOfParkingSpace", "priceOfDwellingUnitRwf", "capacityInformation", "numberOfDwellingUnits", "DescriptionOfOperation", "projectName", "projectDescription", "percentageSpaceUse", "userId", "submittedByUserId", "certificateNumberEIA", "combiningPlotSize", "isEIAVerified", "projectId", "permitTypeId", "categoryTypeId", "buildTypeId", "technologySurveyId", "agencyId", "applicationStatusId", "permitTypeCode", "agencyCode", "other"]}, "LockApplicationDtoUpdate": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field user id is required"}, "applicationStatusId": {"type": "string", "description": "The field applicationStatus id is required"}}, "required": ["userId", "applicationStatusId"]}, "AssociatedUPIDto": {"type": "object", "properties": {"upi": {"type": "string", "description": "The field upi is required"}, "plotSize": {"type": "string", "description": "The field plotSize is required"}, "ownerFullName": {"type": "string", "description": "The field owner<PERSON>ullName is required"}, "ownerIdNo": {"type": "string", "description": "The field ownerIdNo is required"}, "isRRAVerified": {"type": "boolean", "description": "The field isRRAVerified is required"}, "userId": {"type": "string", "description": "The field userId is required"}, "projectId": {"type": "string", "description": "The field projectId is required"}}, "required": ["upi", "plotSize", "ownerFullName", "ownerIdNo", "isRRAVerified", "userId", "projectId"]}, "CalculateCategoryDto": {"type": "object", "properties": {"permitTypeId": {"type": "string", "description": "The field permitTypeId is  mandatory"}, "buildTypeId": {"type": "string", "description": "The field buildTypeId is  mandatory"}, "buildUpArea": {"type": "number", "description": "The field buildUpArea is mandatory"}, "numberOfFloors": {"type": "number", "description": "The field numberOfFloors is mandatory"}, "capacityInformation": {"type": "number", "description": "The field capacityInformation is  mandatory"}, "plotSize": {"type": "number", "description": "The field plotSize is  mandatory"}}, "required": ["permitTypeId", "buildTypeId", "buildUpArea", "numberOfFloors", "capacityInformation", "plotSize"]}, "PermitTypeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}, "issuedDays": {"type": "number", "description": "The field issuedDays is required"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "code", "issuedDays", "created<PERSON>y", "updatedBy"]}, "CategoryRuleDto": {"type": "object", "properties": {"GrossFloorArea": {"type": "number", "description": "The field GrossFloorArea is required"}, "GPlus": {"type": "number", "description": "The field code is required"}, "NumberOfPeople": {"type": "number", "description": "The field NumberOfPeople is required"}}, "required": ["GrossFloorArea", "GPlus", "NumberOfPeople"]}, "CategoryTypeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}, "categoryRuleId": {"type": "string"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "code", "categoryRuleId", "created<PERSON>y", "updatedBy"]}, "BuildTypeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}, "description": {"type": "string", "description": "The field description is required"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "code", "description", "created<PERSON>y", "updatedBy"]}, "DocumentTypeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}}, "required": ["name", "code"]}, "RequiredDocumentDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "documentTypeId": {"type": "string"}, "permitTypeId": {"type": "string"}, "categoryTypeId": {"type": "string"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "documentTypeId", "permitTypeId", "categoryTypeId", "created<PERSON>y", "updatedBy"]}, "TechnologySurveyDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "created<PERSON>y", "updatedBy"]}, "ApplicationStatusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "code", "created<PERSON>y", "updatedBy"]}, "ProjectStatusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}, "createdBy": {"type": "string", "description": "The field createdBy"}, "updatedBy": {"type": "string", "description": "The field updatedBy"}}, "required": ["name", "code", "created<PERSON>y", "updatedBy"]}, "AssigneeDto": {"type": "object", "properties": {"timeLineDays": {"type": "string", "description": "The field timeLineDays is required"}, "userIdForAssignee": {"type": "string", "description": "The field Assignee is required"}, "userTypeId": {"type": "string", "description": "The field userType is required"}, "licenseNumber": {"type": "string", "description": "The field licenseNumber is required"}, "projectId": {"type": "string", "description": "The field projectId is required"}, "projectStatusId": {"type": "string", "description": "The field projectStatusId is required"}}, "required": ["timeLineDays", "userIdForAssignee", "userTypeId", "licenseNumber", "projectId", "projectStatusId"]}, "UpdateAssigneeDto": {"type": "object", "properties": {"userIdForAssignee": {"type": "string"}, "userTypeId": {"type": "string"}, "licenseNumber": {"type": "string"}, "projectId": {"type": "string", "description": "The field projectId is required"}, "projectStatusId": {"type": "string", "description": "The field projectStatusId is required"}}, "required": ["userIdForAssignee", "userTypeId", "licenseNumber", "projectId", "projectStatusId"]}, "DeleteEngineerOnProjectDto": {"type": "object", "properties": {"projectId": {"type": "string", "description": "The field projectId is required"}, "licenseNumber": {"type": "string", "description": "The field licenseNumber is required"}, "userId": {"type": "string", "description": "The field userId is required"}}, "required": ["projectId", "licenseNumber", "userId"]}, "RejectProjectDto": {"type": "object", "properties": {"projectId": {"type": "string", "description": "The field projectId is required"}, "licenseNumber": {"type": "string", "description": "The field licenseNumber is required"}, "projectUserId": {"type": "string", "description": "The field projectUserId is required"}}, "required": ["projectId", "licenseNumber", "projectUserId"]}, "QuestionCategoryDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name is required"}, "code": {"type": "string", "description": "The field code is required"}}, "required": ["name", "code"]}, "PermitQuestionDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field user is required"}, "description": {"type": "string", "description": "The field description is required"}, "code": {"type": "string", "description": "The field code is required"}, "comment": {"type": "string"}, "permitTypeId": {"type": "string", "description": "The field permitTypeId is required"}, "questionCategoryId": {"type": "string"}}, "required": ["userId", "description", "code", "comment", "permitTypeId", "questionCategoryId"]}, "AnswerDto": {"type": "object", "properties": {"permitQuestionId": {"type": "string", "description": "The field permitQuestion is required"}, "userId": {"type": "string", "description": "The field user is required"}, "comment": {"type": "string"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}}, "required": ["permitQuestionId", "userId", "comment", "applicationId"]}, "ReviewersOnApplicationDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field user is required"}, "status": {"type": "string"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "ipAddress": {"type": "string"}, "browser": {"type": "string"}, "operatingSystem": {"type": "string"}}, "required": ["userId", "status", "applicationId", "ip<PERSON><PERSON><PERSON>", "browser", "operatingSystem"]}, "OccupancyInspectionDto": {"type": "object", "properties": {"fireExtinguishersInResidence": {"type": "string", "description": "The field fireExtinguishersInResidence"}, "fireBlanketInKitchen": {"type": "string", "description": "The field fireBlanketInKitchen"}, "twoExitsPerResidence": {"type": "string", "description": "The field twoExitsPerResidence"}, "emergencyExit": {"type": "string", "description": "The field emergencyExit"}, "fireAlarmOnEachFloor": {"type": "string", "description": "The field fireAlarmOnEachFloor"}, "smokeDetectorsSprinklers": {"type": "string", "description": "The field smokeDetectorsSprinklers"}, "hoseReelsEachFloor": {"type": "string", "description": "The field hoseReelsEachFloor"}, "lightningArrestor": {"type": "string", "description": "The field lightningArrestor"}, "fireExtinguishersEvery30m": {"type": "string", "description": "The field fireExtinguishersEvery30m"}, "exitSignsEachFloor": {"type": "string", "description": "The field exitSignsEachFloor"}, "emergencyExitEachFloor": {"type": "string", "description": "The field emergencyExitEachFloor"}, "floorPlanEachLevel": {"type": "string", "description": "The field floorPlanEachLevel"}, "signForElevators": {"type": "string", "description": "The field signForElevators"}, "landingSpaceHelicopter": {"type": "string", "description": "The field landingSpaceHelicopter"}, "evacuationPlan": {"type": "string", "description": "The field evacuationPlan"}, "cctvCameras": {"type": "string", "description": "The field cctvCameras"}, "metalDetectors": {"type": "string", "description": "The field metalDetectors"}, "luggageScanners": {"type": "string", "description": "The field luggageScanners"}, "emergencyPhoneNumbers": {"type": "string", "description": "The field emergencyPhoneNumbers"}, "fireHydrantNearby": {"type": "string", "description": "The field fireHydrantNearby"}, "evacuationPlanSecurity": {"type": "string", "description": "The field evacuationPlanSecurity"}, "securityManagerStaff": {"type": "string", "description": "The field securityManagerStaff"}, "restrictedAccess": {"type": "string", "description": "The field restrictedAccess"}, "insuranceBuilding": {"type": "string", "description": "The field insuranceBuilding"}, "comments": {"type": "string", "description": "The field comments"}, "userId": {"type": "string", "description": "The field userId"}, "applicationId": {"type": "string", "description": "The field applicationId"}, "file": {"type": "string", "format": "binary", "description": "The uploaded file"}}, "required": ["fireExtinguishersInResidence", "fireBlanketInKitchen", "twoExitsPerResidence", "emergencyExit", "fireAlarmOnEachFloor", "smokeDetectorsSprinklers", "hoseReelsEachFloor", "lightningArrestor", "fireExtinguishersEvery30m", "exitSignsEachFloor", "emergencyExitEachFloor", "floorPlanEachLevel", "signForElevators", "landingSpaceHelicopter", "evacuationPlan", "cctvCameras", "metalDetectors", "luggageScanners", "emergencyPhoneNumbers", "fireHydrant<PERSON><PERSON><PERSON>", "evacuationPlanSecurity", "securityManagerStaff", "restrictedAccess", "insuranceBuilding", "comments", "userId", "applicationId"]}, "DataForChartDto": {"type": "object", "properties": {"agencyId": {"type": "string", "description": "The field agency"}, "year": {"type": "number", "description": "The field year"}}, "required": ["agencyId", "year"]}, "FoundationInspectionDto": {"type": "object", "properties": {"geotechnicalReportRespected": {"type": "string", "description": "The field geotechnicalReportRespected"}, "foundationType": {"type": "string", "description": "The field foundationType"}, "proceduresComply": {"type": "string", "description": "The field proceduresComply"}, "soilTreatmentComply": {"type": "string", "description": "The field soilTreatmentComply"}, "erosionControlMeasures": {"type": "string", "description": "The field erosionControlMeasures"}, "footingSizeComply": {"type": "string", "description": "The field footingSizeComply"}, "locationComply": {"type": "string", "description": "The field locationComply"}, "reinforcementSizeComply": {"type": "string", "description": "The field reinforcementSizeComply"}, "spacingReinforcementsComply": {"type": "string", "description": "The field spacingReinforcementsComply"}, "concretePlacementComply": {"type": "string", "description": "The field concretePlacementComply"}, "plumbingComply": {"type": "string", "description": "The field plumbingComply"}, "comments": {"type": "string", "description": "The field comments"}, "userId": {"type": "string", "description": "The field userId"}, "applicationId": {"type": "string", "description": "The field applicationId"}, "file": {"type": "string", "format": "binary", "description": "The uploaded file"}}, "required": ["geotechnicalReportRespected", "foundationType", "proceduresComply", "soilTreatmentComply", "erosionControlMeasures", "footingSizeComply", "locationComply", "reinforcementSizeComply", "spacingReinforcementsComply", "concretePlacementComply", "plumbingComply", "comments", "userId", "applicationId"]}, "ReviewerReportDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field userId required"}, "startDate": {"type": "string", "description": "The field startDate is required"}, "endDate": {"type": "string", "description": "The field endDate is required"}}, "required": ["userId", "startDate", "endDate"]}, "ApprovalLevelDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}, "agencyId": {"type": "string", "description": "The field agencyId required"}}, "required": ["name", "code", "agencyId"]}, "ApprovalStatusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}, "additionalCode": {"type": "string", "description": "The field additionalCode is required"}, "approvalLevelId": {"type": "string", "description": "The field approvalLevelId required"}}, "required": ["name", "code", "additionalCode", "approvalLevelId"]}, "ApplicationApprovalDto": {"type": "object", "properties": {"comment": {"type": "string", "description": "The field comment required"}, "applicationStatusId": {"type": "string"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "applicantUserId": {"type": "string", "description": "The field applicantUserId is required"}, "approvalStatusId": {"type": "string", "description": "The field approvalStatusId is required"}, "approvalLevelId": {"type": "string", "description": "The field approvalLevelId is required"}, "userId": {"type": "string", "description": "The field userId is required"}, "agencyId": {"type": "string", "description": "The field agencyId is required"}}, "required": ["comment", "applicationStatusId", "applicationId", "applicantUserId", "approvalStatusId", "approvalLevelId", "userId", "agencyId"]}, "ApplicationApprovalWithIremboDto": {"type": "object", "properties": {"comment": {"type": "string", "description": "The field comment required"}, "approvalStatusId": {"type": "string", "description": "The field approvalStatusId is required"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "applicantUserId": {"type": "string", "description": "The field applicantUserId is required"}, "applicationStatusId": {"type": "string"}, "userId": {"type": "string", "description": "The field userId is required"}, "approvalLevelId": {"type": "string", "description": "The field approvalLevelId is required"}, "agencyId": {"type": "string", "description": "The field agencyId is required"}, "agencyCode": {"type": "string", "description": "The field agencyCode is required"}, "amount": {"type": "string", "description": "The field agencyCode is required"}, "transactionNumber": {"type": "string", "description": "The field transactionNumber is required"}, "invoiceTypeId": {"type": "string", "description": "The field invoiceTypeId is required"}, "invoiceItemId": {"type": "string", "description": "The field invoiceTypeId is required"}, "invoiceStatusId": {"type": "string", "description": "The field invoiceStatusId is required"}, "permitTypeId": {"type": "string", "description": "The field permitTypeId is required"}, "title": {"type": "string", "description": "The field title is required"}, "lows": {"type": "string", "description": "The field lows is required"}, "expiredDate": {"type": "string", "description": "The field expiredDate is required"}, "backgroundUrl": {"type": "string", "description": "The backgroundUrl lows is required"}}, "required": ["comment", "approvalStatusId", "applicationId", "applicantUserId", "applicationStatusId", "userId", "approvalLevelId", "agencyId", "agencyCode", "amount", "transactionNumber", "invoiceTypeId", "invoiceItemId", "invoiceStatusId", "permitTypeId", "title", "lows", "expiredDate", "backgroundUrl"]}, "PermitCheckListDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}, "permitTypeId": {"type": "string", "description": "The field permitTypeId required"}}, "required": ["name", "code", "permitTypeId"]}, "ApplicationApprovalCheckListDto": {"type": "object", "properties": {"decision": {"type": "string"}, "conditionsOfApproval": {"type": "string"}, "structuralComment": {"type": "string"}, "civilEngineeringComment": {"type": "string"}, "architecturalComment": {"type": "string"}, "urbanPlanningComment": {"type": "string"}, "siteAnalysisComment": {"type": "string"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "approvalStatusId": {"type": "string", "description": "The field approvalStatusId is required"}, "approvalLevelId": {"type": "string", "description": "The field approvalLevelId is required"}, "userId": {"type": "string", "description": "The field userId is required"}, "file": {"type": "string", "format": "binary", "description": "The uploaded file"}}, "required": ["decision", "conditionsOfApproval", "structuralComment", "civilEngineeringComment", "architecturalComment", "urbanPlanningComment", "siteAnalysisComment", "applicationId", "approvalStatusId", "approvalLevelId", "userId"]}, "ApprovalDocumentDto": {"type": "object", "properties": {"documentId": {"type": "string", "description": "The field documentId required"}, "userId": {"type": "string", "description": "The field user id is required"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "requiredDocumentId": {"type": "string", "description": "The field document id is required"}, "approvalStatusId": {"type": "string", "description": "The field approvalStatusId is required"}}, "required": ["documentId", "userId", "applicationId", "requiredDocumentId", "approvalStatusId"]}, "InvoiceStatusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}}, "required": ["name", "code"]}, "PriceDto": {"type": "object", "properties": {"amount": {"type": "number", "description": "The field amount required"}, "rangeInSqmMin": {"type": "string", "description": "The field rangeInSqmMin required"}, "rangeInSqmMax": {"type": "string", "description": "The field rangeInSqmMax required"}, "permitTypeId": {"type": "string", "description": "The field permitTypeId required"}, "userId": {"type": "string", "description": "The field userId required"}}, "required": ["amount", "rangeInSqmMin", "rangeInSqmMax", "permitTypeId", "userId"]}, "InvoiceTypeDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}}, "required": ["name", "code"]}, "InvoiceItemDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "priceId": {"type": "string", "description": "The field name required"}}, "required": ["name", "priceId"]}, "InvoiceDto": {"type": "object", "properties": {"applicationId": {"type": "string", "description": "The field applicationId is required"}, "applicantUserId": {"type": "string", "description": "The field applicantUserId is required"}, "userId": {"type": "string", "description": "The field userId is required"}, "agencyCode": {"type": "string", "description": "The field agencyCode is required"}, "paymentAccountIdentifier": {"type": "string", "description": "The field paymentAccountIdentifier is required"}, "amount": {"type": "string", "description": "The field amount is required"}, "transactionNumber": {"type": "string", "description": "The field transactionNumber is required"}, "invoiceTypeId": {"type": "string", "description": "The field invoiceTypeId is required"}, "invoiceItemId": {"type": "string", "description": "The field invoiceItemId is required"}, "invoiceStatusId": {"type": "string", "description": "The field invoiceStatusId is required"}}, "required": ["applicationId", "applicantUserId", "userId", "agencyCode", "paymentAccountIdentifier", "amount", "transactionNumber", "invoiceTypeId", "invoiceItemId", "invoiceStatusId"]}, "UpdateInvoiceDto": {"type": "object", "properties": {"applicationId": {"type": "string", "description": "The field applicationId is required"}, "invoiceStatusId": {"type": "string", "description": "The field invoiceStatusId is required"}}, "required": ["applicationId", "invoiceStatusId"]}, "ReceiptDto": {"type": "object", "properties": {"invoiceNumber": {"type": "string", "description": "The field invoiceNumber is required"}, "transactionId": {"type": "string", "description": "The field transactionId is required"}, "paymentStatus": {"type": "string", "description": "The field paymentStatus is required"}, "currency": {"type": "string", "description": "The field currency is required"}, "paidAt": {"type": "string", "description": "The field paidAt is required"}, "customer": {"type": "string", "description": "The field customer is required"}, "amount": {"type": "string", "description": "The field amount is required"}, "paymentMethod": {"type": "string", "description": "The field paymentMethod is required"}}, "required": ["invoiceNumber", "transactionId", "paymentStatus", "currency", "paidAt", "customer", "amount", "paymentMethod"]}, "CertificateDto": {"type": "object", "properties": {"permitTypeId": {"type": "string", "description": "The field permitType Id required"}, "agencyCode": {"type": "string", "description": "The field agencyCode required"}, "applicantUserId": {"type": "string", "description": "The field applicantUserId is required"}, "userId": {"type": "string", "description": "The field userId is required"}, "title": {"type": "string", "description": "The field title is required"}, "lows": {"type": "string", "description": "The field lows is required"}, "backgroundUrl": {"type": "string", "description": "The field backgroundUrl is required"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}}, "required": ["permitTypeId", "agencyCode", "applicantUserId", "userId", "title", "lows", "backgroundUrl", "applicationId"]}, "SignatoryDto": {"type": "object", "properties": {"names": {"type": "string", "description": "The field names required"}, "signatureUrl": {"type": "string", "description": "The field signatureUrl is required"}, "title": {"type": "string", "description": "The field title is required"}, "orientation": {"type": "string", "description": "The field orientation is required"}, "activityTpe": {"type": "string", "description": "The field orientation is required"}, "isActive": {"type": "boolean", "description": "The field isActive is required"}, "certificateId": {"type": "string", "description": "The field certificateId is required"}}, "required": ["names", "signatureUrl", "title", "orientation", "activityTpe", "isActive", "certificateId"]}, "TransferCertificateDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field userId"}, "firstName": {"type": "string", "description": "The field firstName is required"}, "lastName": {"type": "string", "description": "The field lastName is required"}, "documentNumber": {"type": "string", "description": "The field documentNumber is required"}, "certificateNumber": {"type": "string", "description": "The field certificateNumber is required"}}, "required": ["userId", "firstName", "lastName", "documentNumber", "certificateNumber"]}, "DevelopmentStatusDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field userId"}, "UPI": {"type": "string", "description": "The field UPI is required"}, "applicationId": {"type": "string", "description": "The field applicationId is required"}, "certificateId": {"type": "string", "description": "The field certificateId is required"}, "startingDate": {"type": "string", "description": "The field startingDate is required"}, "siteStatusId": {"type": "string", "description": "The field siteStatusId is required"}}, "required": ["userId", "UPI", "applicationId", "certificateId", "startingDate", "siteStatusId"]}, "SiteStatusDto": {"type": "object", "properties": {"name": {"type": "string", "description": "The field name required"}, "code": {"type": "string", "description": "The field code is required"}}, "required": ["name", "code"]}, "RandomInspectionDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "The field userId"}, "UPI": {"type": "string", "description": "The field UPI is required"}, "reason": {"type": "string", "description": "The field reason is required"}, "certificateId": {"type": "string", "description": "The field certificateId is required"}, "siteStatusId": {"type": "string", "description": "The field siteStatusId is required"}, "file": {"type": "string", "format": "binary", "description": "The uploaded file"}}, "required": ["userId", "UPI", "reason", "certificateId", "siteStatusId"]}}}}