import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormGroupDirective, NgForm, Validators } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { Router } from '@angular/router';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { GENDERS, NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    return !!(control && control.valid && (control.dirty || control.touched) && form?.hasError('notMatched'));
  }
}
@Component({
  selector: 'app-register-info-detail',
  templateUrl: './register-info-detail.component.html',
  styleUrls: ['./register-info-detail.component.scss']
})
export class RegisterInfoDetailComponent {
  @Input() inputData: any = {};
  @Output() backToParent = new EventEmitter();
  accountDetailsForm!: FormGroup;
  submitted: any = false;
  genders = GENDERS;
  date!: { year: number; month: number };
  isReadOnly: boolean = true;

  documentTypes = [
    { id: '1', name: 'National ID' },
    { id: '2', name: 'Passport' },

  ];

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    public utilService: UtilService,
    private router: Router
  ) {
    // 16 year @2025
    this.utilService.maxDate = { year: 2009, month: 12, day: 31 };
    // 105 year @2025
    this.utilService.minDate = { year: 1920, month: 1, day: 1 };
  }

  getThirdName(name: any): string {
    if (name) {
      return name;
    } else return ''
  }

  getTypeOfUserData() {
    if (this.inputData.accountTypeId === '1') {
      let data: any = {};
      data = this.inputData.nidData;
      if (this.inputData.nidData) {
        data.foreName = this.inputData.nidData ? data?.foreName : '';
        data.surnames = this.inputData.nidData ? data?.surnames : '';
        data.documentNumber = this.inputData.nidData ? data?.documentNumber : '';
        data.sex = this.inputData.nidData?.sex === 'F' ? 'Female' : 'Male';
      }
      return data;
    }
    if (this.inputData.accountTypeId === '2') {
      let data: any = {};
      let names = this.inputData && this.inputData.engineerData && this.inputData.engineerData.name ? this.inputData.engineerData.name.split(' ') : '';

      data.foreName = names[0];
      data.surnames = names[1] + ' ' + (names[2] ? names[2] : '');
      data.licenceIdEng = this.inputData.engineerData.registrationNumber;
      return data;
    }
    if (this.inputData.accountTypeId === '3') {
      let data: any = {};
      data.licenceIdArch = this.inputData.archictData && this.inputData.archictData.registrationindex ? this.inputData.archictData.registrationindex : "";
      data.foreName = this.inputData.archictData && this.inputData.archictData.name ? this.inputData.archictData.name.replace(/[.\s]/g, "") : "";
      data.surnames = this.inputData.archictData && this.inputData.archictData.last_name ? this.inputData.archictData.last_name : "";
      data.nationalId = this.inputData.archictData && this.inputData.archictData.identificationNumber ? this.inputData.archictData.identificationNumber : "";
      return data;
    }
    // else {
    //   return {
    //     foreName: "",
    //     surnames: ""
    // licenceIdArch licenceIdEng
    //   };
    // }
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    return `${year}-${month}-${day}`;
  }


  setDateOfBirth(dateString: string) {
    const [month, day, year] = dateString.split('/').map(Number);

    // Create NgbDateStruct object
    const dateStruct: NgbDateStruct = { year, month, day };
    return dateStruct;
    // // Patch the form control value
    // this.accountDetailsForm.patchValue({
    //   dateOfBirthForm: dateStruct
    // });
  }


  checkNumberOfArchEng() {
    if (this.inputData.accountTypeId === "2" && this.inputData.engineerData &&
      !this.inputData.engineerData.identificationNumber
    ) {

    }
    if ((this.accountDetailsForm.value.phoneNumber.length === 10
      || this.accountDetailsForm.value.phoneNumber.length > 10)
      && this.inputData && this.inputData.engineerData &&
      (this.inputData.accountTypeId === "2" ||
        this.inputData.accountTypeId === "3"
      )
    ) {

    }
  }


  getPhoneNumber() {
    if ((this.inputData.phoneNumber)) {
      return this.inputData.phoneNumber;
    } else { return "" }
  }

  ngOnInit(): void {

    const currentDate = new Date();
    this.accountDetailsForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]],
      password: ['', [Validators.required, Validators.pattern(this.utilService.passwordPattern)]],
      confirmPassword: ['', [Validators.required, Validators.pattern(this.utilService.passwordPattern)]],
      isConsent: [false],
      firstName: [
        // (this.inputData.archictData && this.inputData.archictData.name) ||
        //   (this.inputData && this.inputData.engineerData && this.inputData.engineerData.name) ||
        //   (this.inputData.nidData) ?
        this.utilService.removeSpaceInString(
          this.getTypeOfUserData()?.foreName),
        [Validators.required,
        Validators.pattern("^[A-Za-zÀ-ÿ. ']+( [A-Za-zÀ-ÿ. ']+)?$")
          // Validators.pattern("^[A-Za-z. ']+( [A-Za-z. ']+)?$")
        ]

      ],
      // firstName: [data ? data.foreName : '', [Validators.required, Validators.pattern("^[A-Za-z]+( [A-Za-z]+)?$")]],
      lastName: [
        // (this.inputData.archictData && this.inputData.archictData.name) ||
        //   (this.inputData && this.inputData.engineerData && this.inputData.engineerData.name) ||
        //   (this.inputData.nidData) ?
        this.utilService.removeSpaceInString(
          this.getTypeOfUserData()?.surnames),
        [Validators.required,
        // Validators.pattern("^[A-Za-z.]+( [A-Za-z.]+)?$")
        Validators.pattern("^[A-Za-zÀ-ÿ. ']+( [A-Za-zÀ-ÿ. ']+)?$")
        ]

      ],
      dateOfBirth: [''],
      // dateOfBirthForm: [data ? this.utilService.convertSlashDateToDateFormat(data.dateOfBirth) : '', [Validators.required]],
      // dateOfBirthForm: ['', [Validators.required]],
      dateOfBirthForm: [this.inputData.nidData && this.inputData.nidData.dateOfBirth ? this.setDateOfBirth(this.inputData.nidData.dateOfBirth) : ""],
      phoneNumber: [this.getPhoneNumber(),
      [Validators.required,


        // Validators.pattern("^(078|079|073|072)\\d{7}$")
        // Validators.pattern(/^\+[1-9]\d{1,14}$/) 



      ]],
      gender: [this.inputData.nidData ? this.getTypeOfUserData()?.sex : ""],
      // nationalId: [this.inputData.nidData ? this.getTypeOfUserData()?.documentNumber : ""],
      nationalId: [this.getNationalId()],


      verified: [this.getTypeOfUserData() ? true : false],
      verifiedAt: [this.getTypeOfUserData() ? this.formatDate(currentDate) : '1900-01-01'],
      isEmailValid: [false],

      passport: [this.inputData.passport],
      licenceIdArch: [this.inputData.archictData ? this.getTypeOfUserData()?.licenceIdArch : ""],
      licenceIdEng: [this.inputData.engineerData ? this.getTypeOfUserData()?.licenceIdEng : ""],
      roleId: [""],
      userTypeId: [""],
    })


    this.authService.findAllWithPath(environment.authUrl + 'user-management/userType/code/search?search=' + this.utilService.whichTypeOfUser(this.inputData))
      .subscribe(
        data => {
          this.accountDetailsForm.controls['userTypeId'].setValue(data.items[0].id);
        }
      )

    this.authService.findAllWithPath(environment.authUrl + 'user-management/role/code/search?search=' + this.utilService.whichTypeOfRole(this.inputData))
      .subscribe(
        data => {
          this.accountDetailsForm.controls['roleId'].setValue(data.items[0].id);
        }
      )

  }



  getNationalId() {
    if (this.inputData.accountTypeId === '3') {
      this.getTypeOfUserData();
    } else {
      return this.inputData.nidData ? this.getTypeOfUserData()?.documentNumber : "";
    }
  }






  get f() { return this.accountDetailsForm.controls; }


  verifyPhoneAndNationalId(retreiviedData: any) {
    this.authService.findAllWithPath(environment.integrationUrl + 'integration/nida/' + this.accountDetailsForm.value.phoneNumber)
      .subscribe(data => {
        if (this.inputData.accountTypeId === "3" && this.inputData.archictData.number) {
          if (data.result.replace(/\s+/g, '') === this.inputData.archictData.number.replace(/\s+/g, '')) {
            this.submitted = false;
          }
        } else {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, "Phone number " + retreiviedData.phoneNumber + " is not registered to your national Id " + retreiviedData.nationalId, "top", "right");
        }

      })
  }

  onSubmit() {
    if (this.accountDetailsForm.invalid) {
    } else {

      // verify phone number if is assigned to the national id provided
      // if (this.inputData.accountTypeId === '1') {
      //   this.authService.findAllWithPath(environment.integrationUrl + 'integration/' + this.accountDetailsForm.value.phoneNumber)
      //     .subscribe(
      //       data => {
      //         if (this.inputData.nid === data.result) {
      //           this.confirmToSubmit();
      //         } else {
      //           this.utilService.showNotification(NOTIFICATION_COLOR.error, "National ID is not belong the phone number provided", "bottom", "center");
      //         }
      //       }
      //     )
      // } else {
      // this.confirmToSubmit();
      // }


      if (this.inputData.accountTypeId === "1" && this.inputData.documentTypeId === '1') {
        // this.authService.findAllWithPath(environment.integrationUrl + 'integration/nida/' + this.accountDetailsForm.value.phoneNumber)
        //   .subscribe(
        //     data => {

        //       if (data.result.replace(/\s+/g, '') === this.accountDetailsForm.value.nationalId.replace(/\s+/g, '')) {
        this.confirmToSubmit();
        //       }
        //       else {
        //         this.utilService.showNotification(NOTIFICATION_COLOR.error, "Phone number " + this.accountDetailsForm.value.phoneNumber + " is not registered to your national Id " + this.accountDetailsForm.value.nationalId, "top", "right");
        //       }
        //     },
        //     error => { }
        //   )
      } else
        if (this.inputData.accountTypeId === "2" && this.inputData.engineerData.identificationNumber && this.inputData.engineerData.identificationNumber.replace(/\s+/g, '').length === 16) {
          //   this.authService.findAllWithPath(environment.integrationUrl + 'integration/nida/' + this.accountDetailsForm.value.phoneNumber)
          //     .subscribe(
          //       data => {
          //         if (data.result.replace(/\s+/g, '') === this.inputData.engineerData.identificationNumber.replace(/\s+/g, '')) {
          this.confirmToSubmit();
          //         }
          //         else {
          //           this.utilService.showNotification(NOTIFICATION_COLOR.error, "Phone number " + this.accountDetailsForm.value.phoneNumber + " is not registered to your national Id " + this.accountDetailsForm.value.nationalId, "top", "right");
          //         }
          //       },
          //       error => { }
          //     )
        }
        else
          if (this.inputData.accountTypeId === "3" && this.inputData.archictData.number.replace(/\s+/g, '') && this.inputData.archictData?.number.replace(/\s+/g, '').length === 16) {
            // this.authService.findAllWithPath(environment.integrationUrl + 'integration/nida/' + this.accountDetailsForm.value.phoneNumber)
            //   .subscribe(
            //     data => {

            //       if (data.result.replace(/\s+/g, '') === this.inputData.archictData.number.replace(/\s+/g, '')) {
            this.confirmToSubmit();
            //         // 
            //       }
            //       else {
            //         this.utilService.showNotification(NOTIFICATION_COLOR.error, "Phone number " + this.accountDetailsForm.value.phoneNumber + " is not registered to your national Id " + this.accountDetailsForm.value.nationalId, "top", "right");
            //       }
            //     },
            //     error => { }
            //   )
          } else {
            this.confirmToSubmit();
          }
    }
  }


  confirmToSubmit() {
    this.accountDetailsForm.controls['dateOfBirth'].setValue(this.utilService.convertToDateFormat(this.accountDetailsForm.value.dateOfBirthForm));
    this.accountDetailsForm.controls['licenceIdArch'].setValue(this.accountDetailsForm.value.licenceIdArch ? this.accountDetailsForm.value.licenceIdArch : '')
    this.accountDetailsForm.controls['licenceIdEng'].setValue(this.accountDetailsForm.value.licenceIdEng ? this.accountDetailsForm.value.licenceIdEng : '')

    if (this.inputData.accountTypeId === '1' && this.inputData.documentTypeId === '1') {
      // this.accountDetailsForm.controls['nationalId'].setValue(this.fillNationalIdIfUserIsArchtectureOrEngineer());
      this.accountDetailsForm.controls['nationalId'].setValue(this.accountDetailsForm.value.nationalId ? this.accountDetailsForm.value.nationalId : '');
    } else {
      this.accountDetailsForm.controls['nationalId'].setValue(this.fillNationalIdIfUserIsArchtectureOrEngineer());
    }
    this.submitted = true;
    this.authService.saveAssetWithPath(this.accountDetailsForm.value, environment.authUrl + 'auth/register').
      subscribe(
        data => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "bottom", "center");
          // this.router.navigate(['/login']);
          this.router.navigate(['/login-message']);
        }, error => { this.submitted = false; }
      )
  }






  checkUserType() {
    if (this.inputData.accountTypeId === "3" && this.inputData.archictData.number) {
      return this.inputData.archictData.number;
    }
  }



  fillNationalIdIfUserIsArchtectureOrEngineer() {
    if (this.inputData.accountTypeId === '2') {
      this.accountDetailsForm.value.licenceIdEng;
    }
    if (this.inputData.accountTypeId === '3') {
      this.accountDetailsForm.value.licenceIdArch
    }
  }



  backToRegister() {
    if (!this.accountDetailsForm.value.licenceIdEng) {
      this.inputData.licenceIdEng = this.inputData.licenceNumber ? this.inputData.licenceNumber : ""
        ;
    }
    if (!this.accountDetailsForm.value.licenceIdArch) {
      this.inputData.licenceIdArch = this.inputData.licenceNumber ? this.inputData.licenceNumber : ""
        ;
    }
    this.backToParent.emit(this.inputData);
  }

  checkValidations(control: any, type: any) {
    switch (type) {
      case 'special-character':
        return /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(control.value);;
      case 'number':
        return /\d/.test(control.value);
      case 'lowercase':
        return /[a-z]/.test(control.value);
      case 'uppercase':
        return /[A-Z]/.test(control.value);
      case 'length':
        return control.value.length >= 8 && control.value.length <= 16;
      default:
        return false
    }
  }


  convertToLowercase() {
    const emailControl = this.accountDetailsForm.get('email');
    if (emailControl) {
      const value = emailControl.value;
      emailControl.setValue(value.toLowerCase(), { emitEvent: false });
    }
  }
}
