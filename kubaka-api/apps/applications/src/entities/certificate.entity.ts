import { AbstractEntity } from '@app/common';
import {
  Entity,
  Column,
  OneToMany,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Application } from './application.entity';
import { PermitType } from './settings.entity';

@Entity()
export class Certificate extends AbstractEntity<Certificate> {
  // @Column({ type: 'varchar' })
  // permitTypeId: string;

  @Column({ default: '0' })
  statusId: string;

  @Column({ nullable: true })
  upi: string;

  @Column({ nullable: true })
  invoiceNumber: string;

  @Column({ nullable: true })
  applicantUserId: string;

  @Column({ nullable: true })
  userId: string;

  @ManyToOne(() => PermitType, (permitType) => permitType.certificates)
  permitTypes: PermitType;

  @ManyToOne(() => Application, (application) => application.certificates)
  applications: Application;

  @Column({ type: 'varchar' })
  title: string;

  @Column()
  lows: string;

  @Column({ type: 'varchar' })
  certificateNumber: string;

  @Column()
  backgroundUrl: string;

  @Column()
  expiredDate: Date;

  @Column({ type: 'varchar' })
  agencyCode: string;

  @OneToMany(() => Signatory, (signatory) => signatory.certificates)
  signatories: Signatory[];

  @OneToMany(
    () => DevelopmentStatus,
    (developmentStatus) => developmentStatus.certificate,
  )
  developmentStatus: DevelopmentStatus[];

  @OneToMany(
    () => RandomInspection,
    (randomInspection) => randomInspection.certificate,
  )
  randomInspections: RandomInspection[];

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}

@Entity()
export class Signatory extends AbstractEntity<Signatory> {
  @Column({ type: 'varchar' })
  names: string;

  @Column()
  signatureUrl: string;

  @Column({ type: 'varchar' })
  title: string;

  @Column()
  orientation: string;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => Certificate, (certificate) => certificate.signatories)
  certificates: Certificate[];

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}

@Entity()
export class TransferCertificate extends AbstractEntity<TransferCertificate> {
  @Column({ nullable: true })
  userId: string;

  @Column({ type: 'varchar' })
  firstName: string;

  @Column({ type: 'varchar' })
  lastName: string;

  @Column({ type: 'varchar' })
  documentNumber: string;

  @Column({ type: 'varchar' })
  certificateNumber: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}

@Entity()
export class SiteStatus extends AbstractEntity<SiteStatus> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(
    () => DevelopmentStatus,
    (developmentStatus) => developmentStatus.siteStatus,
  )
  developmentStatus: DevelopmentStatus[];

  @OneToMany(
    () => RandomInspection,
    (randomInspection) => randomInspection.siteStatus,
  )
  randomInspections: RandomInspection[];
}

@Entity()
export class RandomInspection extends AbstractEntity<RandomInspection> {
  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar' })
  UPI: string;

  @Column({ type: 'varchar' })
  reason: string;

  @ManyToOne(() => Certificate, (certificate) => certificate.randomInspections)
  certificate: Certificate;

  @ManyToOne(() => SiteStatus, (siteStatus) => siteStatus.randomInspections)
  siteStatus: SiteStatus;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @Column({ type: 'text', nullable: true })
  fileBase64: string;
}

@Entity()
export class DevelopmentStatus extends AbstractEntity<DevelopmentStatus> {
  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar' })
  UPI: string;

  @Column({ type: 'varchar' })
  startingDate: string;

  @ManyToOne(() => Certificate, (certificate) => certificate.developmentStatus)
  certificate: Certificate;

  @ManyToOne(() => Application, (application) => application.developmentStatus)
  applications: Application;

  @ManyToOne(() => SiteStatus, (siteStatus) => siteStatus.developmentStatus)
  siteStatus: SiteStatus;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}

export { Application } from './application.entity';
