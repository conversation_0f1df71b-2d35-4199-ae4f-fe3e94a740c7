import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Entity_Type } from '../../entities/documents.entity';

export class DocumentsDto {
  @ApiProperty({ description: 'The URL of the uploaded documents' })
  @IsNotEmpty({ message: 'fileUrl is required' })
  @IsString()
  fileUrl: string;

  @ApiProperty({ description: 'The original file name' })
  @IsNotEmpty({ message: 'fileName is required' })
  @IsString()
  fileName: string;

  @ApiPropertyOptional({ description: 'Optional description of the file' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Status ID of the document' })
  @IsOptional()
  @IsString()
  documentStatusId?: string;

  @ApiPropertyOptional({
    description: 'UUID of the user who uploaded the file',
  })
  @IsOptional()
  @IsString()
  uploadedBy?: string;

  @ApiProperty({
    enum: Entity_Type,
    description: 'The entity type this document is attached to',
    example: Entity_Type.CHAT,
  })
  @IsEnum(Entity_Type, { message: 'entityType must be a valid enum value' })
  entityType: Entity_Type;
}
