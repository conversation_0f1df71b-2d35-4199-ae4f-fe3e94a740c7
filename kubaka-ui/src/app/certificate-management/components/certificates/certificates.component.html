<!-- Applicant Permits -->
<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <div class="app-welnote">
        <div class="app-welnote_dtails">
          <span class="prim-nt">{{"applicantPermits.welcomeMsg" | translate }}!</span>
          <h3>
            <span> {{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
          </h3>
        </div>
      </div>
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle"> {{"applicantPermits.permits" | translate }} </span>

              <div class="tbleFilter">
                <div class="form-input_search">
                  <input type="text" name="searchTerm" [(ngModel)]="applicationService.searchTerm"
                    (ngModelChange)="onSearchTermChange($event)" [placeholder]="'applicantInvoices.search' | translate" />
                  <button type="button" class="btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <g data-name="Layer 2">
                        <g data-name="search">
                          <rect width="24" height="24" opacity="0" />
                          <path
                            d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                        </g>
                      </g>
                    </svg>
                  </button>
                </div>
                <div class="form-input clear-m w-aut">
                  <label><Strong>{{"applicantPermits.fromDate" | translate }} </Strong> </label>
                  <div>
                    <input class="w-aut" type="date" name="dateFrom" [(ngModel)]="searchData.dateFrom"
                      (ngModelChange)="onSearchByDateChange($event)" required />
                  </div>
                </div>
                <div class="form-input clear-m w-aut">
                  <label><Strong>{{"applicantPermits.toDate" | translate }} </Strong> </label>
                  <div>
                    <input class="w-aut" type="date" name="dateTo" [(ngModel)]="searchData.dateTo"
                      (ngModelChange)="onSearchByDateChange($event)" required />
                  </div>
                </div>
                <div class="form-input clear-m w-aut">
                  <label><Strong>{{"applicantPermits.pageSize" | translate }} </Strong> </label>
                  <div>
                    <select name="pageSize" id="pageSize" [(ngModel)]="pageSize" (change)="changePageSize()">
                      <!-- <option disabled value="">All</option> -->
                      <option *ngFor="let r of utilService.tableArraySelector" [value]="r.id"> {{ r.id }} </option>
                    </select>
                  </div>
                </div>
              </div>

          </div>
          <ul class="tblist">
            <li class="tblist-item" *ngFor="let li of lists">
              <div class="tblist-item_icon bg-l-o">
                <img src="assets/ikons/colored/ikon-conspaper.svg" alt="" />
              </div>


              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">UPI</span> {{ li.upi }} </span>
                <span>
                  <span class="ttl">{{"applicantPermits.invoiceNo" | translate }} </span> {{ li.invoiceNumber }} </span>
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">{{"applicantPermits.permitNo" | translate }} </span> {{ li.certificateNumber }} </span>
                <span>
                  <span class="ttl">{{"applicantPermits.applicationNo" | translate }} </span> {{ li.applications.applicationName }} </span>
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">{{"applicantPermits.permitType" | translate }} </span> {{ li.permitTypes.name }} </span>
                <span>
                  <span class="ttl">{{"applicantPermits.agencyCode" | translate }} </span> {{ li.applications.agencyCode }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"applicantPermits.issuedOn" | translate }} </span> {{ li.created_at | date }} </span>
                <span>
                  <span class="ttl">{{"applicantPermits.expiryDate" | translate }} </span> {{ li.expiredDate | date }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"applicantPermits.status" | translate }} </span> {{ li.statusId == '0' ? ('applicantPermits.waitingForPayment' | translate): ('applicantPermits.generated' | translate) }} </span>
              </div>
              <div class="tblist-item_dt" *ngIf="li.statusId === '2'">
                <span>
                  <span class="ttl">{{"applicantPermits.permitStatus" | translate }} </span>
                  <span class="bdg bdg-complete">{{"applicantPermits.transfer" | translate }} </span>
                </span>
              </div>
              <div class="tblist-item_xcn">
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Pay"> Pay </a>
                                <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Delete">
                                    <img src="assets/ikons/colored/ikon-trash.svg" alt="">
                                </a> -->
                <a *ngIf="li.statusId === '1'" class="kbk-link-btn hs-tp" data-kbk-tooltip="View Certificate"
                  [routerLink]="[
                    '/account/certificate/certificate-detail',
                    li.id
                  ]">
                  <img src="assets/ikons/colored/ikon-eye.svg" alt="" />
                </a>
                <!-- *ngIf="li.statusId === '2'" -->
                <!-- <a *ngIf="li.statusId === '1'" class="kbk-link-btn hs-tp" data-kbk-tooltip="{{"applicantPermits.transfer" | translate }}  Ownership" data-bs-toggle="modal"
                  data-bs-target="#showModal" (click)="openModal(isTransferOwnerShip, 'lg', li)"> {{"applicantPermits.transfer" | translate }}  Ownership
                </a> -->
                <a *ngIf="currentUser.data.user.role.code === 'DRCT' && li.statusId !== '3'" class="kbk-link-btn hs-tp"
                  data-kbk-tooltip="Cancel Certificate" data-bs-toggle="modal" data-bs-target="#showModal"
                  (click)="openModal(deleteModel, 'lg', li)"> {{"applicantPermits.cancel" | translate }}  </a>
              </div>
            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} - {{endIndex}}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="getPremiumData();">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div>
    <div class="app-dash">
        <div class="container">
            <div class="app-main">
                <div class="app-cont">
                    <div class="app-cont_title">
                        <span class="hder" aria-label="header tittle">Tittle</span>
                        <div class="btns">
                            <button type="button" class="kbk-btn kbk-btn-main">Button</button>
                        </div>
                    </div>
                    <div class="app-cont_list">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="app-dash">
        <div class="container">
            <div class="app-main">
                <div class="app-lists">
                    <div class="app-tblist">
                        <div class="app-tblist_title">
                            <span class="hder" aria-label="header tittle">Tittle</span>
                        </div>
                        <div class="btns">
                            <button type="button" class="kbk-btn kbk-btn-main">Button</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->
<ng-template #isTransferOwnerShip role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"applicantPermits.transferOwnership" | translate }}</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="step-panel_body">
    <form [formGroup]="saveForm" (ngSubmit)="onSubmit()">
      <div class="form-set">
        <div class="form-input">
          <label>{{"applicantPermits.documentType" | translate }} </label>
          <div>
            <select name="documentTypeId" id="documentTypeId" formControlName="documentTypeId">
              <option [value]="">{{"applicantPermits.selectDocumentType" | translate }}</option>
              <option *ngFor="let op of utilService.documentTypes" [value]="op.id">{{op.name}}</option>
            </select>
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantPermits.documentNumber" | translate }} </label>
          <div>
            <input type="text" id="documentNumber" name="documentNumber" formControlName="documentNumber"
              (keyup)="changeAndGetNamesByNationalId()" required>
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantPermits.firstName" | translate }} </label>
          <div>
            <input type="text" id="firstName" name="firstName" formControlName="firstName" required>
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantPermits.lastName" | translate }} </label>
          <div>
            <input type="text" id="lastName" name="lastName" formControlName="lastName" required>
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantPermits.phoneNumber" | translate }} </label>
          <div>
            <input type="text" id="phoneNumber" name="phoneNumber" formControlName="phoneNumber" required>
          </div>
        </div>
      </div>
      <div class="modol-content">
        <div class="kbk-x-c sp-sm mt-md">
          <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> {{"applicantPermits.cancel" | translate }}  </button>
          <button class="kbk-btn kbk-btn-main" type="submit"> {{"applicantPermits.submit" | translate }} </button>
        </div>
      </div>
    </form>
  </div>
</ng-template>
<ng-template #deleteModel let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel">{{"applicantPermits.confirmCancelPermit" | translate }} {{cancelData.certificateNumber}} ?</h2>
      <!-- <p>Deleting your {{cancelData.name}} will remove all of your information from our database.</p> -->
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-md">
        <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
      </div>
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close">{{"applicantPermits.close" | translate }}</button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="confirmCancel()">{{"applicantPermits.yesDelete" | translate }}</button>
      </div>
    </div>
  </div>
</ng-template>
