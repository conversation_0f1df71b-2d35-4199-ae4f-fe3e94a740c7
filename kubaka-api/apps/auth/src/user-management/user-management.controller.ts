import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpStatus,
  HttpCode,
  Query,
  UseInterceptors,
  ClassSerializerInterceptor,
  BadRequestException,
  UploadedFile,
  UseGuards,
  // Put,
} from '@nestjs/common';
import { UserManagementService } from './user-management.service';
import {
  CivilStatusDto,
  // PermissionDto,
  RoleDto,
  UpdateCivilStatusDto,
  // UpdatePermissionDto,
  UpdateRoleDto,
  // UpdateUserActivityDto,
  UpdateUserTypeDto,
  UserActivityDto,
  UserTypeDto,
} from '../dto/user-management.dto';
import {
  CellDto,
  CountryDto,
  DistrictDto,
  ProvinceDto,
  SectorDto,
  UpdatedDistrictDto,
  VillageDto,
} from '../dto/delimitation.dto';
import {
  AgencyDto,
  CreateAgencyDistrictDto,
  UpdateAgencyDto,
} from '../dto/agency.dto';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ResponseError, ResponseSuccess } from '@app/common/dto/response.dto';
// import { IResponse } from '@app/common/interfaces';
import {
  CreateUserDto,
  UpdateAnyUserDto,
  UpdateUserProfileDto,
  UserProfileDto,
} from '../dto/user.dto';
import { AuthGuard } from '@nestjs/passport';
import { ApiPaginatedResponse } from '@app/common/decorators';
import { PageOptionsDto } from '@app/common/dto/page-obptions.dto';
import { PageDto } from '@app/common/dto/page.dto';
import { User } from '../entities/user-management/user.entity';
import { AgencyDistrict } from '../entities/agency/agencyDistrict.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GenericSearch, GenericSearch2 } from '../generic-search.service';
import { District } from '../entities/delimitation/district.entity';
import { Agency } from '../entities/agency/agency.entity';
import { UserType } from '../entities/user-management/userType.entity';
import { Role } from '../entities/user-management/role.entity';
import { CivilStatus } from '../entities/user-management/civilStatus.entity';
import { FileInterceptor } from '@nestjs/platform-express';
import * as multer from 'multer';
import { JwtAuthGuard } from '../jwt.auth.guard';

@Controller('user-management')
export class UserManagementController {
  constructor(
    private readonly userManagementService: UserManagementService,

    @InjectRepository(AgencyDistrict)
    protected readonly agencyDistrictRepository: Repository<AgencyDistrict>,
    protected readonly genericSearch: GenericSearch<AgencyDistrict>,

    @InjectRepository(District)
    protected readonly districtRepository: Repository<District>,
    protected readonly genericSearch2: GenericSearch2<District>,

    @InjectRepository(Agency)
    protected readonly agencyRepository: Repository<Agency>,
    protected readonly genericSearch3: GenericSearch<Agency>,

    @InjectRepository(Role)
    protected readonly roleRepository: Repository<Role>,
    protected readonly genericSearch4: GenericSearch<Role>,

    @InjectRepository(UserType)
    protected readonly userTypeRepository: Repository<UserType>,
    protected readonly genericSearch5: GenericSearch<UserType>,

    @InjectRepository(CivilStatus)
    protected readonly civilStatusRepository: Repository<CivilStatus>,
    protected readonly genericSearch6: GenericSearch<CivilStatus>,

    @InjectRepository(User)
    protected readonly userRepository: Repository<User>,
    protected readonly genericSearch7: GenericSearch<User>,
  ) {}

  @ApiTags('user-management')
  @Get('users')
  // @UseGuards(JwtAuthGuard)
  // @UseGuards(AuthGuard('api-key'))
  async findAllUsers() {
    return this.userManagementService.findAllUsers();
  }

  // @ApiTags('user-management')
  // @Get('users')
  // async findAllUsers() {
  //   return this.userManagementService.findAllUsers();
  // }

  @ApiTags('user-management')
  @Get('users/paginated')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiPaginatedResponse(CreateUserDto)
  async getUsersPaginated(
    @Query() pageOptionsDto: PageOptionsDto,
  ): Promise<PageDto<User>> {
    return this.userManagementService.findAllUsersPaginated(pageOptionsDto);
  }

  @ApiTags('user-management')
  @Get('user/:email')
  async findUserByEmail(@Param('email') email: string) {
    // async findUserByEmail(@Param('email') email: string): Promise<IResponse> {
    try {
      const user = await this.userManagementService.findUserByEmail(email);
      return new ResponseSuccess('COMMON.SUCCESS', user);
    } catch (error) {
      return new ResponseError('COMMON.ERROR.GENERIC_ERROR', error);
    }
  }

  @ApiTags('user-management')
  @Get('user/:id')
  async findOneUser(@Param('id') id: string) {
    return this.userManagementService.findUserById(id);
  }

  // @ApiTags('user-management')
  // @Get('user/:agency')
  // async findUserByAgency(@Param('agencyId') agencyId: string) {
  //   console.log(agencyId);
  //   return await this.userManagementService.findUserByAgency(agencyId);
  // }

  // @ApiTags('user-management')
  // @Get('user/:agencyId')
  // async findUserByAgency(@Param('agencyId') agencyId: string) {
  //   return this.userManagementService.findUserByAgency(agencyId);
  // }

  @ApiTags('user-management')
  @Get('user/agency/:agencyId')
  async findUserByAgency(@Param('agencyId') agencyId: string) {
    return this.userManagementService.getAllUserByAgencyId(agencyId);
  }

  // @ApiTags('user-management')
  // @Get('user/:userType')
  // async findUserByUserType(@Param('userTypeId') userTypeId: string) {
  //   return await this.userManagementService.findUsersByUserType(userTypeId);
  // }

  // @ApiTags('user-management')
  // @Get('user/:userTypeId')
  // async findByAssignedUserForReview(
  //   @Param('userTypeId') userTypeId: string,
  // ): Promise<User[]> {
  //   return this.userManagementService.getUsersByUserTypeId(userTypeId);
  // }

  @ApiTags('user-management')
  @Get('user/:userTypeId')
  async findByAssignedUserForReview(@Param('userTypeId') userTypeId: string) {
    console.log(userTypeId);
    return this.userManagementService.findUsersByUserType(userTypeId);
  }

  // @ApiTags('user-management')
  // @Get('user/userType/search')
  // async searchUserByUserType(@Query('search') search: string) {
  //   const searchFields: (keyof User)[] = ['userType'];
  //   return this.genericSearch7.search(
  //     this.userRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  @ApiTags('user-management')
  @Post('profile/user/update')
  async updateProfile(@Body() profileDto: UserProfileDto) {
    // async updateProfile(@Body() profileDto: UserProfileDto): Promise<IResponse> {
    try {
      const user = await this.userManagementService.updateProfile(profileDto);
      return new ResponseSuccess('PROFILE.UPDATE_SUCCESS', user);
    } catch (error) {
      return new ResponseError('PROFILE.UPDATE_ERROR', error);
    }
  }

  // @ApiTags('user-management')
  // @Patch('user/updateAnyUser')
  // async updateUser(@Body() updateAnyUserDto: UpdateAnyUserDto) {
  //   return await this.userManagementService.updateAnyUser(
  //     updateAnyUserDto.id,
  //     updateAnyUserDto,
  //   );
  // }

  @ApiTags('user-management')
  @Patch('user/updateAnyUser/:id')
  async updateUser(
    @Param('id') id: string,
    @Body() updateAnyUserDto: UpdateAnyUserDto,
  ) {
    return this.userManagementService.updateAnyUser(id, updateAnyUserDto);
  }

  @ApiTags('user-management')
  @Patch('user/updateProfile')
  async updateUserProfile(@Body() updateUserProfileDto: UpdateUserProfileDto) {
    return await this.userManagementService.updateUserProfile(
      updateUserProfileDto,
    );
  }

  // @ApiTags('user-management')
  // @Get('user/Engineer/search')
  // async searchEngineerByLicense(@Query('search') search: string) {
  //   const searchFields: (keyof User)[] = ['licenceIdEng'];
  //   return this.genericSearch7.search(
  //     this.userRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  @ApiTags('user-management')
  @Get('user/Engineer/search/:licenceIdEng')
  // async findUserByEngineerNumber(@Param('licenceIdEng') licenceIdEng: string) {
  //   return this.userManagementService.findUserByEngineerNumber(licenceIdEng);
  // }
  async findUserByEngineerNumber(@Query('licenceIdEng') licenceIdEng: string) {
    return this.userManagementService.findUserByEngineerNumber(licenceIdEng);
  }

  // @ApiTags('user-management')
  // @Get('user/Architect/search')
  // async searchArchitectByLicense(@Query('search') search: string) {
  //   const searchFields: (keyof User)[] = ['licenceIdArch'];
  //   return this.genericSearch7.search(
  //     this.userRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  @ApiTags('user-management')
  @Get('user/Architect/search/:licenceIdArch')
  async findUserByArchitectNumber(
    @Param('licenceIdArch') licenceIdArch: string,
  ) {
    return this.userManagementService.findUserByArchitectNumber(licenceIdArch);
  }

  // Users at administrator
  // @Post('user')
  // async createUser(@Body() createUserDto: CreateUserDto) {
  //   return this.userManagementService.createUserByAdmin(createUserDto);
  // }

  // @ApiTags('user-management')
  // @Post('user')
  // async createUserByAdmin(@Body() createUserDto: CreateUserDto) {
  //   return this.userManagementService.createUserByAdmin(createUserDto);
  // }

  // @ApiTags('user-management')
  // @Get('user/details')
  // @UseGuards(JwtAuthGuard)
  // async getUser(@CurrentUser() user: User) {
  //   return user;
  // }

  // @ApiTags('user-management')
  // @Get('users')
  // async findAllUsers() {
  //   return this.userManagementService.getAllUsers();
  // }

  @ApiTags('user-management')
  @Post('role')
  // @UseGuards(JwtAuthGuard)
  // @UseGuards(AuthGuard('api-key'))
  async createRole(@Body() roleDto: RoleDto) {
    return this.userManagementService.createRole(roleDto);
  }

  @ApiTags('user-management')
  @Get('roles')
  // @UseGuards(JwtAuthGuard)
  // @UseGuards(AuthGuard('api-key'))
  async findAll() {
    return this.userManagementService.findAllRole();
  }

  @ApiTags('user-management')
  @UseGuards(JwtAuthGuard)
  @UseGuards(AuthGuard('api-key'))
  @Get('role/:id')
  async findOne(@Param('id') id: string) {
    return this.userManagementService.findOneRole(id);
  }

  @ApiTags('user-management')
  @UseGuards(JwtAuthGuard)
  @UseGuards(AuthGuard('api-key'))
  @Patch('role/:id')
  async update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto) {
    return this.userManagementService.updateRole(id, updateRoleDto);
  }

  @ApiTags('user-management')
  @UseGuards(JwtAuthGuard)
  @UseGuards(AuthGuard('api-key'))
  @Delete('role/:id')
  async remove(@Param('id') id: string) {
    return this.userManagementService.removeRole(id);
  }

  @ApiTags('user-management')
  @Get('role/code/search')
  async searchRoleByCode(@Query('search') search: string) {
    const searchFields: (keyof Role)[] = ['code'];
    return this.genericSearch4.search(
      this.roleRepository,
      searchFields,
      search,
    );
  }

  @ApiTags('user-management')
  // User Activities
  @Post('userActivity')
  async CreateUserActivity(@Body() userActivityDto: UserActivityDto) {
    return this.userManagementService.createUserActivity(userActivityDto);
  }

  @ApiTags('user-management')
  @Get('userActivities')
  async findAllActivities() {
    return this.userManagementService.findAllUserActivities();
  }

  // @Get('userActivity/:id')
  // async findOneUserActivity(@Param('id') id: string) {
  //   return this.userManagementService.findOneUserActivity(id);
  // }

  // @Patch('userActivity/:id')
  // async updateUserActivity(
  //   @Param('id') id: string,
  //   @Body() updateUserActivityDto: UpdateUserActivityDto,
  // ) {
  //   return this.userManagementService.updateUserActivity(
  //     id,
  //     updateUserActivityDto,
  //   );
  // }

  // @Delete('userActivity/:id')
  // async removeUserActivity(@Param('id') id: string) {
  //   return this.userManagementService.removeUserActivity(id);
  // }

  // User Type
  @ApiTags('user-management')
  @Post('userType')
  async CreateUserType(@Body() userTypeDto: UserTypeDto) {
    return this.userManagementService.createUserType(userTypeDto);
  }

  @ApiTags('user-management')
  @Get('userType')
  async findAllUserTypes() {
    return this.userManagementService.findAllUserTypes();
  }

  @ApiTags('user-management')
  @Get('userType/:id')
  async findOneUserType(@Param('id') id: string) {
    return this.userManagementService.findOneUserType(id);
  }

  @ApiTags('user-management')
  @Patch('userType/:id')
  async updateUserType(
    @Param('id') id: string,
    @Body() updateUserTypeDto: UpdateUserTypeDto,
  ) {
    return this.userManagementService.updateUserType(id, updateUserTypeDto);
  }

  @ApiTags('user-management')
  @Delete('userType/:id')
  async removeUserType(@Param('id') id: string) {
    return this.userManagementService.removeUserType(id);
  }

  @ApiTags('user-management')
  @Get('userType/code/search')
  async searchUserTypeByCode(@Query('search') search: string) {
    const searchFields: (keyof UserType)[] = ['code'];
    return this.genericSearch5.search(
      this.userTypeRepository,
      searchFields,
      search,
    );
  }

  // Civil Status
  @ApiTags('user-management')
  @Post('civilStatus')
  async CreateCivilStatus(@Body() civilStatusDto: CivilStatusDto) {
    return this.userManagementService.createCivilStatus(civilStatusDto);
  }

  @ApiTags('user-management')
  @Get('civilStatus')
  async findAllCivilStatus() {
    return this.userManagementService.findAllCivilStatus();
  }

  @ApiTags('user-management')
  @Get('civilStatus/:id')
  async findOneCivilStatus(@Param('id') id: string) {
    return this.userManagementService.findOneCivilStatus(id);
  }

  @ApiTags('user-management')
  @Patch('civilStatus/:id')
  async updateCivilStatus(
    @Param('id') id: string,
    @Body() updateCivilStatusDto: UpdateCivilStatusDto,
  ) {
    return this.userManagementService.updateCivilStatus(
      id,
      updateCivilStatusDto,
    );
  }

  @ApiTags('user-management')
  @Delete('civilStatus/:id')
  async removeCivilStatus(@Param('id') id: string) {
    return this.userManagementService.removeCivilStatus(id);
  }

  @ApiTags('user-management')
  @Get('civilStatus/code/search')
  async searchCivilStatusByCode(@Query('search') search: string) {
    const searchFields: (keyof CivilStatus)[] = ['code'];
    return this.genericSearch6.search(
      this.civilStatusRepository,
      searchFields,
      search,
    );
  }

  // // Permissions
  @ApiTags('user-management')
  @Post('createPermission')
  async createUserActivityRole(
    @Body() createUserActivityRole: { userActivityId: string; roleId: string },
  ) {
    await this.userManagementService.createUserActivityRole(
      createUserActivityRole,
    );
  }

  // @Post('permission')
  // async CreatePermission(@Body() permissionDto: PermissionDto) {
  //   return this.userManagementService.createPermission(permissionDto);
  // }

  @ApiTags('user-management')
  @UseInterceptors(ClassSerializerInterceptor)
  @Get('permissions')
  async findAllPermissions() {
    return this.userManagementService.findAllPermissions();
  }

  @ApiTags('user-management')
  @Get('permission/:id')
  async findOnePermission(@Param('id') id: string) {
    return this.userManagementService.findPermissionByRoleId(id);
  }
  // @Get('permission/:id')
  // async findOnePermission(@Param('id') id: string) {
  //   return this.userManagementService.findOnePermission(id);
  // }

  @ApiTags('user-management')
  @Get('permission/role/:id')
  async findPermissionByRole(@Param('id') id: string) {
    return this.userManagementService.findPermissionByRoleId(id);
  }

  // @Patch('permission/:id')
  // async updatePermission(
  //   @Param('id') id: string,
  //   @Body() updatePermissionDto: UpdatePermissionDto,
  // ) {
  //   return this.userManagementService.updatePermission(id, updatePermissionDto);
  // }

  @ApiTags('user-management')
  @Patch('permission/deleteAction')
  async updatePermission(
    @Body()
    updateUserActivityRole: {
      userActivityId: string;
      roleId: string;
      isDeleted: boolean;
    },
  ) {
    await this.userManagementService.createUserActivityRole(
      updateUserActivityRole,
    );
  }

  @ApiTags('user-management')
  @Delete('permission/:id')
  async removePermission(@Param('id') id: string) {
    return this.userManagementService.removePermission(id);
  }

  @ApiTags('user-management')
  @Delete('permission/delete/:role/:userActivity')
  async removePermissionCorrect(
    @Param('role') role: string,
    @Param('userActivity') userActivity: string,
  ) {
    return this.userManagementService.removePermissionByRoleAndUserActivity(
      role,
      userActivity,
    );
  }

  @ApiTags('delimitation')
  // Delimitation
  // Country
  @Post('country')
  async CreateCountry(@Body() countryDto: CountryDto) {
    return this.userManagementService.createCountry(countryDto);
  }

  @ApiTags('delimitation')
  @Get('countries')
  async findAllCountries() {
    return this.userManagementService.findAllCountries();
  }

  @ApiTags('delimitation')
  // Province
  @Post('province')
  async CreateProvince(@Body() provinceDto: ProvinceDto) {
    return this.userManagementService.createProvince(provinceDto);
  }

  @ApiTags('delimitation')
  @Get('provinces')
  async findAllProvinces() {
    return this.userManagementService.findAllProvinces();
  }

  @ApiTags('delimitation')
  @Delete('province/:id')
  async removeProvince(@Param('id') id: string) {
    return this.userManagementService.removeProvince(id);
  }

  @ApiTags('delimitation')
  // District
  @Post('district')
  async CreateDistrict(@Body() districtDto: DistrictDto) {
    return this.userManagementService.createDistrict(districtDto);
  }

  @ApiTags('delimitation')
  @Get('districts')
  async findAllDistricts() {
    return this.userManagementService.findAllDistricts();
  }
  @ApiTags('delimitation')
  @Patch('district/:id')
  async updatePermitType(
    @Param('id') id: string,
    @Body() districtDto: UpdatedDistrictDto,
  ) {
    return this.userManagementService.updateDistrict(id, districtDto);
  }

  @ApiTags('delimitation')
  // Sector
  @Post('sector')
  async CreateSector(@Body() sectorDto: SectorDto) {
    return this.userManagementService.createSector(sectorDto);
  }

  //upload sector
  // @Post('upload-sectors')
  // @UseInterceptors(FileInterceptor('file'))
  // @ApiConsumes('multipart/form-data')
  // async uploadSectors(@UploadedFile() file: Express.Multer.File) {
  //   if (!file) {
  //     throw new BadRequestException('File is required');
  //   }

  //   return this.userManagementService.uploadSectorsFromExcel(file.buffer);
  // }
  @Post('upload-sectors')
  @UseInterceptors(FileInterceptor('file', { storage: multer.memoryStorage() })) // Ensures buffer storage
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload sectors via Excel file' })
  @ApiBody({
    description:
      'Excel file containing sectors data (District code, sector code, sector name)',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadSectors(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // // Check if the file buffer exists and has content
    // if (!file.buffer || file.buffer.length === 0) {
    //   throw new BadRequestException('Uploaded file is empty or not readable');
    // }

    // Pass the buffer to the service
    return this.userManagementService.uploadSectorsFromExcel(file.buffer);
  }

  @ApiTags('delimitation')
  @Get('sectors')
  async findAllSectors() {
    return this.userManagementService.findAllSectors();
  }

  @ApiTags('delimitation')
  @Get('sectors/ByDistrictCode/:code')
  async findSectorByDistrictCode(@Param('code') code: string) {
    return this.userManagementService.findSectorByDistrictCode(code);
  }

  @ApiTags('delimitation')
  // Cell
  @Post('cell')
  async CreateCell(@Body() cellDto: CellDto) {
    return this.userManagementService.createCell(cellDto);
  }

  @ApiTags('delimitation')
  @Get('cells')
  async findAllCells() {
    return this.userManagementService.findAllCells();
  }

  @ApiTags('delimitation')
  // Villages
  @Post('village')
  async CreateVillage(@Body() villageDto: VillageDto) {
    return this.userManagementService.createVillage(villageDto);
  }

  @ApiTags('delimitation')
  @Get('villages')
  async findAllVillages() {
    return this.userManagementService.findAllVillages();
  }

  // Agency
  @ApiTags('agency')
  @Post('agency')
  async CreateAgency(@Body() agencyDto: AgencyDto) {
    return this.userManagementService.createAgency(agencyDto);
  }

  @ApiTags('agency')
  @Get('agencies')
  async findAllAgencies() {
    return this.userManagementService.findAllAgencies();
  }
  @ApiTags('agency')
  @Get('agency/:id')
  async findOneAgency(@Param('id') id: string) {
    return this.userManagementService.findOneAgency(id);
  }

  @ApiTags('agency')
  @Patch('agency/:id')
  async updateAgency(
    @Param('id') id: string,
    @Body() updateAgencyDto: UpdateAgencyDto,
  ) {
    return this.userManagementService.updateAgency(id, updateAgencyDto);
  }

  @ApiTags('agency')
  @Delete('agency/:id')
  async removeAgency(@Param('id') id: string) {
    return this.userManagementService.removeProvince(id);
  }

  @ApiTags('agency')
  @Get('agency/code/search')
  async searchAgencyByCode(@Query('search') search: string) {
    const searchFields: (keyof Agency)[] = ['code'];
    return this.genericSearch3.search(
      this.agencyRepository,
      searchFields,
      search,
    );
  }

  // Agency District
  @ApiTags('agency')
  @Post('agencyDistrict')
  async createAgencyDistrict(
    @Body() createAgencyDistrictDto: CreateAgencyDistrictDto,
  ): Promise<AgencyDistrict> {
    return this.userManagementService.createAgencyDistrict(
      createAgencyDistrictDto,
    );
  }

  @ApiTags('agency')
  @Get('agencyDistrict')
  async getAllAgencyDistricts(): Promise<AgencyDistrict[]> {
    return this.userManagementService.getAllAgencyDistricts();
  }

  // @ApiTags('agency')
  // @Get('agencyDistrict/byAgencyId/')
  // async getAgencyDistrictsByAgencyId(@Param('agencyId') agencyId: string) {
  //   return this.userManagementService.getAgencyDistrictsByAgencyId(agencyId);
  // }
  @ApiTags('agency')
  @Get('agencyDistrict/byAgencyId/:agencyId')
  async findProjectByUserId(@Param('agencyId') agencyId: string) {
    return this.userManagementService.getAgencyDistrictsByAgencyId(agencyId);
  }

  // @ApiTags('agency')
  // @Get('agencyDistrict/districtCode/search')
  // async searchPermitTypeByCode(@Query('search') search: string) {
  //   const searchFields: (keyof AgencyDistrict)[] = ['districts'];
  //   return this.genericSearch.search(
  //     this.agencyDistrictRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  @ApiTags('agency')
  @Get('agencyDistricts/ByDistrictCode/:code')
  async findOnePermitType(@Param('code') code: string) {
    return this.userManagementService.findAgencyByDistrictCode(code);
  }

  @ApiTags('agency')
  @Delete('agencyDistrict/:id')
  async removeAgencyDistrict(@Param('id') id: string) {
    return this.userManagementService.removeAgency(id);
  }
}
