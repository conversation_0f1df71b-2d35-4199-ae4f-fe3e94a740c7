import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CountryDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class ProvinceDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field country id is required' })
  @IsNotEmpty({ message: 'The field country activity cannot be empty' })
  @Expose()
  countryId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class DistrictDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field province id is required' })
  @IsNotEmpty({ message: 'The field province activity cannot be empty' })
  @Expose()
  provinceId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class UpdatedDistrictDto {
  @ApiProperty({ description: 'The field province id is required' })
  provinceId: string;

  @ApiProperty({ description: 'The field name is required' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  code: string;
}

export class SectorDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field district id is required' })
  @IsNotEmpty({ message: 'The field districtId cannot be empty' })
  @Expose()
  districtId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class UploadSectorDto {
  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  code: string;

  @IsNotEmpty()
  districtCode: string;
}

export class CellDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field sector id is required' })
  @IsNotEmpty({ message: 'The field sectorId cannot be empty' })
  @Expose()
  sectorId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class VillageDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field cell id is required' })
  @IsNotEmpty({ message: 'The field cellId cannot be empty' })
  @Expose()
  cellId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}
