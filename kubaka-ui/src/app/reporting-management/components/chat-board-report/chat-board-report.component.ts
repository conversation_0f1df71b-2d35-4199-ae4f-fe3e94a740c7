import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CellValueChangedEvent,
  ColDef,
  GridApi,
  GridReadyEvent,
  SelectionChangedEvent,
  ValueFormatterParams,
} from 'ag-grid-community';
import { jwtDecode } from 'jwt-decode';
import { SessionService } from 'src/app/authentication-services/session.service';
import {
  NOTIFICATION_COLOR,
  UtilService,
} from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import { interval, Subscription, takeWhile } from 'rxjs';

@Component({
  selector: 'app-chat-board-report',
  templateUrl: './chat-board-report.component.html',
  styleUrls: ['./chat-board-report.component.scss'],
})
export class ChatBoardReportComponent {
  @ViewChild('chatContainer') chatContainer!: ElementRef;

  lists: any[] = [];
  page = 1;
  pageSize = 100;
  filterValue = '';
  startIndex = 0;
  endIndex = 9;
  messageForm!: FormGroup;
  totalRecords = 0;
  attachementsServerUrl = environment?.uploadedDocumentUrl || 'https://api-gatway.kubaka.gov.rw/attachment/path/';
  // Variables to store the grid API and column API
  private gridApi!: GridApi;

  rowData: any[] = [];
  colDefs: ColDef[] = [
    { field: 'senderNames', width: 200, checkboxSelection: true },
    { field: 'content', width: 600 },
    { field: 'issueCategory.name', width: 300 },
    { field: 'agencyName', width: 300 },
  ];

  defaultColDef: ColDef = {
    filter: true,
    editable: true,
  };
  currentUser: any = {};
  fullList: any[] = [];
  content: any[] = [];
  filteredList: any[] = [];
  saveForm!: UntypedFormGroup;
  messages: any[] = [];
  messageWithAttachments: any;
  private subscription!: Subscription;
  searchData: any = {};
  selectedFiles: File[] = [];

  constructor(
    private http: HttpClient,
    public userService: UserMgtService,
    private sessionService: SessionService,
    private modalService: NgbModal,
    private utilService: UtilService,
    private fb: FormBuilder
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (
      jwtDecode(this.currentUser.data.token.access_token) as any
    ).UserId;
    this.loadList();
  }

  ngAfterViewChecked(): void {
    this.scrollToBottom();
  }

  // scrollToBottom(): void {
  //   const container = this.chatContainer.nativeElement;
  //   container.scrollTop = container.scrollHeight;
  // }
  scrollToBottom(): void {
    // Ensure `chatContainer` is defined
    if (this.chatContainer) {
      const container = this.chatContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

  onImageLoad() {
    this.scrollToBottom();
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  // startCheckingBoolean(booleanData: any) {
  startCheckingBoolean() {
    this.subscription = interval(20000) // emits every 10 second
      .pipe(
        takeWhile(() => this.messageForm.value.phoneNumber) // continue checking while myBoolean is false
      )
      .subscribe(() => {
        this.getMessage(this.messageForm.value.phoneNumber);
      });
  }

  ngOnInit() {
    this.messageForm = this.fb.group({
      // senderId: [this.currentUser.userId, Validators.required],
      // receiverId: ['', Validators.required],
      // content: ['', Validators.required],

      phoneNumber: ['', Validators.required],
      agencyId: ['', Validators.required],
      content: ['', Validators.required],
      issueCategoryId: ['', Validators.required],
      isReply: ['1', Validators.required],
      documentIds: [[]]
    });
  }

  loadList() {
    // if (this.currentUser.data.user.userType.code !== "STF") {
    //   this.userService
    //     .findAllWithPath(environment.chatUrl + 'chatBoardDistinct')
    //     .subscribe(
    //       (data) => {
    //         this.content = data;
    //         this.fullList = data.reverse();
    //         this.totalRecords = this.fullList.length;
    //         this.filterAndPaginate();
    //       });
    // } else
    if (this.currentUser.data.user.role.code === 'DRCT') {
      this.userService
        .findAllWithPath(
          environment.chatUrl +
            'chatBoardDistinct/agency/' +
            this.currentUser.data.user.agency.id
        )
        .subscribe((data) => {
          this.content = data;
          this.fullList = data.reverse();
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();
        });
    }

    // if (this.currentUser.data.user.userType.code !== "STF") {
    //   this.userService
    //     .findAllWithPath(environment.chatUrl + 'chatBoardDistinct')
    //     .subscribe(
    //       (data) => {
    //         this.content = data;
    //         this.fullList = data.reverse();
    //         this.totalRecords = this.fullList.length;
    //         this.filterAndPaginate();
    //       });
    // } else
    else {
      this.userService
        .findAllWithPath(environment.chatUrl + 'chatBoardDistinct')
        .subscribe((data) => {
          this.content = data;
          this.fullList = data.reverse();
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();
        });
    }
  }
  
  getPremiumData() {
    this.filterAndPaginate();
  }

  openModal(content: any, sizeParams: any, event: any) {
    this.messageForm.controls['phoneNumber'].setValue(event.phoneNumber);
    this.messageForm.controls['agencyId'].setValue(event.agencyId);
    this.messageForm.controls['issueCategoryId'].setValue(
      event.issueCategory.id
    );

    this.getMessage(this.messageForm.value.phoneNumber);
    this.startCheckingBoolean();
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  getMessage(event: any) {
    // this.messages = [];
    if (event) {
      this.userService
        .findAllWithPath(environment.chatUrl + 'chatBoard/phone/' + event)
        .subscribe((data) => {
          this.messages = data;
          this.scrollToBottom();
          if (data.length > 0) {
            this.getChatAttachments().subscribe((attachments: any) => {
              this.messages = attachments;
              this.scrollToBottom();
            });
          }
        });
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'No phone number provided',
        'bottom',
        'center'
      );
    }
  }

  sortChatBoardsByCreatedAt(data: any[]): any[] {
  return data.sort((a, b) => {
    const dateA = new Date(a.createdAt).getTime();
    const dateB = new Date(b.createdAt).getTime();
    return dateA - dateB;
  });
}

  verifyUser() {
    // this.modalService.open(content, { size: "lg", centered: true });
  }

  filterAndPaginate() {
    const filtered = this.fullList.filter((item) => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }

    this.lists = this.sortChatBoardsByCreatedAt(filtered).slice(this.startIndex - 1, this.endIndex);
    this.lists.forEach((element) => {
      if (element.statusId === '1') {
        element.statusName = 'Valid';
      }
      if (element.statusId === '2') {
        element.statusName = 'Transfered';
      }
      if (element.statusId === '3') {
        element.statusName = 'Cancelled';
      }
    });
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some((val) =>
      String(val).toLowerCase().includes(term)
    );
  }

  onGridReady(params: GridReadyEvent) {
    // Save the grid API reference
    this.gridApi = params.api;

    this.http
      .get<any[]>(environment.chatUrl + 'chatBoard')
      .subscribe((data) => (this.rowData = data));
  }

  onSelectionChanged(event: SelectionChangedEvent) {}

  onCellValueChanged(event: CellValueChangedEvent) {
    console.log(`New Cell Value: ${event.value}`);
  }

  dateFormatter(params: ValueFormatterParams) {
    return new Date(params.value).toLocaleDateString('en-us', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Export to Excel using the grid API
  onBtnExport() {
    const params = {
      fileName: 'user-report.xlsx',
      onlySelected: true, // Export only selected rows
      columnKeys: ['firstName', 'lastName', 'email'], // Export specific columns
    };
    // Use the grid API to export data as Excel
    this.gridApi.exportDataAsExcel(params);
  }

  onSubmit() {
    this.saveMessage();
  }

  cancel() {}

  filterByRead() {
    this.fullList = this.content.filter(
      (x: any) => x.isRead + '' === this.filterValue
    );
    this.filterAndPaginate();
  }

  filterByIsReply() {
    this.fullList = this.content.filter(
      (x: any) => x.isReply + '' === this.searchData.reply
    );
    this.filterAndPaginate();
  }

  onSearchByDateChange(date: string): void {
    // this.searchData.dateFrom = date;

    if (this.searchData.dateFrom && this.searchData.dateTo) {
      const from = this.searchData.dateFrom
        ? new Date(this.searchData.dateFrom)
        : null;
      const to = this.searchData.dateTo
        ? new Date(this.searchData.dateTo)
        : null;

      if (to) {
        // Add one day to the 'to' date to include the entire day
        to.setDate(to.getDate() + 1);
      }

      const filtered = this.fullList.filter((item) => {
        const createdAt = new Date(item.createdAt);
        return (!from || createdAt >= from) && (!to || createdAt < to);
      });

      // const from = this.searchData.dateFrom ? new Date(this.searchData.dateFrom) : null;
      // const to = this.searchData.dateTo ? new Date(this.searchData.dateTo) : null;

      // const filtered = this.fullList.filter((item: any) => {
      //   const createdAt = new Date(item.createdAt);
      //   return (!from || createdAt >= from) && (!to || createdAt <= to);
      // });

      this.filteredList = filtered;
      this.totalRecords = filtered.length;
      this.startIndex = (this.page - 1) * this.pageSize + 1;
      this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
      if (this.endIndex > this.totalRecords) {
        this.endIndex = this.totalRecords;
      }
      this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    }

    // this.filterAndPaginate();
  }

  uploadChatAttachment(): Promise<any> {
    return new Promise((resolve, reject) => {
      const formData = new FormData();

      if (this.selectedFiles && this.selectedFiles.length > 0) {
        for (const file of this.selectedFiles) {
          formData.append('files', file, file.name);
        }
      }

      this.userService
        .saveAssetWithPathFormData(
          formData,
          environment.documentUrl + 'DocMgt/chatBoard/uploadAttachment'
        )
        .subscribe(
          (data) => {
            this.messageForm.controls['documentIds'].setValue(data);
            resolve(data);
          },
          (err) => {
            reject(err);
          }
        );
    });
  }


  getChatAttachments() {
    return this.userService.saveAssetWithPath(
      this.messages,
      environment.documentUrl + 'DocMgt/chatBoard/getAttachments'
    );
  }

  downloadDocument(doc: { fileUrl: string; fileName?: string }) {
    const link = document.createElement('a');
    link.href = this.attachementsServerUrl + doc.fileName || doc.fileUrl;
    link.download = doc.fileName || 'download';
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  previewDocument(doc: { fileUrl: string; fileName: string }) {
    const safeUrl = doc.fileName
      ? this.attachementsServerUrl + doc.fileName
      : doc.fileUrl;
    const isPreviewable =
      /\.(pdf|jpg|jpeg|png|gif|webp|bmp|svg|mp4|webm)$/i.test(safeUrl);
    // preview with the right format
    if (isPreviewable) {
      window.open(safeUrl, '_blank', 'noopener');
    } else {
      // download it
      this.downloadDocument(doc);
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      const selected = Array.from(input.files);
      this.selectedFiles = selected;
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }


  getFileIcon(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf':
        return 'assets/ikons/SVG/ikon-pdf-file.svg';
      case 'xls':
      case 'xlsx':
        return 'assets/ikons/SVG/ikon-xlsx-file.svg';
      case 'ppt':
      case 'pptx':
        return 'assets/ikons/SVG/ikon-pptx-file.svg';
      case 'zip':
      case 'rar':
        return 'assets/ikons/SVG/ikon-zip-file.svg';
      default:
        return 'assets/ikons/SVG/ikon-filec.svg';
    }
  }

  isImageFile(fileName: string): boolean {
    const ext = fileName?.toLowerCase().split('.').pop();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(
      ext || ''
    );
  }

  async saveMessage() {
    if (this.selectedFiles && this.selectedFiles.length > 0) {
      try {
        await this.uploadChatAttachment();
      } catch (error) {
        this.utilService.showNotification(
          NOTIFICATION_COLOR.error,
          'Error uploading attachments',
          'bottom',
          'center'
        );
        return;
      }
    }

    if (this.messageForm.value.content) {
      this.userService
        .saveAssetWithPath(
          this.messageForm.value,
          environment.chatUrl + 'chatBoard/reply'
        )
        .subscribe((data) => {
          this.selectedFiles = [];
          this.messageForm.controls['content'].setValue('');
          this.getMessage(this.messageForm.value.phoneNumber);
        });
    }
  }
}
