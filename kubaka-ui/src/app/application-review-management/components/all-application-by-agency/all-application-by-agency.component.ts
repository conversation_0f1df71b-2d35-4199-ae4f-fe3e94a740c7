import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../../application/services/application.service';

@Component({
  selector: 'app-all-application-by-agency',
  templateUrl: './all-application-by-agency.component.html',
  styleUrls: ['./all-application-by-agency.component.scss']
})
export class AllApplicationByAgencyComponent {
  @ViewChild("yourSelfContent") modalContent!: TemplateRef<any>;
  @ViewChild("isNewPermit") modalNewPermitContent!: TemplateRef<any>
  currentUser: any = {};
  applicationDetail: any = {};
  lists: any[] = [];
  outputData: any = {};
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];
  paramsId: any;

  searchData: any = {};

  permissions: any = {};
  applicationStatuses: any[] = [];
  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    public userService: UserMgtService,
    private applicationService: ApplicationService,
    private router: Router,
    private route: ActivatedRoute,
    public utilService: UtilService,
    private appConfig: AppConfig
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;



    if (localStorage.getItem(this.appConfig.PERMI_ACCESS)) {
      this.permissions = JSON.parse(localStorage.getItem(this.appConfig.PERMI_ACCESS) as any);
    }
    route.params.subscribe((params: any) => {
      this.paramsId = params.status;
      this.loadList(params);
    })




    this.applicationService.findAllWithPath(environment.applicationUrl +
      'application/applicationStatus')
      .subscribe(
        (response: any) => {
          this.applicationStatuses = response;
        }
      )


  }


  changeByApplicationStatus() {

    if (this.applicationDetail.filterByApplicationStatus !== 'all') {
      // this.fullList = this.content.filter((x: any) => x.applicationStatus.id === this.applicationDetail.filterByApplicationStatus);
      this.fullList = this.paramsId ?
        this.calculateAndAddDaysColumn(
          this.content.filter((x: any) => x.applicationStatus.id === this.applicationDetail.filterByApplicationStatus &&
            x.applicationStatus.code === this.paramsId)) :
        this.calculateAndAddDaysColumn(this.content.filter((x: any) => x.applicationStatus.id === this.applicationDetail.filterByApplicationStatus));
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    } else {
      this.paginateLists();
    }

  }

  paginateLists() {
    if (this.applicationDetail.filterByApplicationStatus === 'all') {
      this.fullList = this.content;
      this.fullList.forEach((element: any) => {
        element.count = element.assignUsersFoReview?.length;
        element.categoryTypeName = element.categoryTypes.name;
        element.buildTypeName = element.buildTypes.name;
        element.permitTypeName = element.permitTypes.name;
        element.upi = element.projects.upi;
      });
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    } else {
      this.fullList = this.content;
      this.changeByApplicationStatus();
    }

  }


  loadList(params: any) {



    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + params.status)
      .subscribe(
        statusData => {
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + statusData.items[0]?.id + '/agency/' + params.agency)
            .subscribe(
              data => {
                this.content = data;
                if (this.paramsId) {
                  this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId));
                } else {
                  this.fullList = this.calculateAndAddDaysColumn(data);
                }
                this.fullList.forEach((element: any) => {
                  element.count = element.assignUsersFoReview?.length;
                  element.categoryTypeName = element.categoryTypes.name;
                  element.buildTypeName = element.buildTypes.name;
                  element.permitTypeName = element.permitTypes.name;
                  element.upi = element.projects.upi;
                });
                this.totalRecords = this.fullList.length;
                this.filterAndPaginate();
              },
            )

        })





  }

  // loadList() {
  //   if (this.currentUser.data.user.role.code === "ADM") {
  //     this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application')
  //       .subscribe(
  //         data => {
  //           this.content = data;
  //           if (this.paramsId) {
  //             this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId));
  //           } else {
  //             this.fullList = this.calculateAndAddDaysColumn(data);
  //           }
  //           this.fullList.forEach((element: any) => {
  //             element.count = element.assignUsersFoReview?.length;
  //             element.categoryTypeName = element.categoryTypes.name;
  //             element.buildTypeName = element.buildTypes.name;
  //             element.permitTypeName = element.permitTypes.name;
  //             element.upi = element.projects.upi;
  //           });
  //           this.totalRecords = this.fullList.length;
  //           this.filterAndPaginate();
  //         },
  //       )
  //   } else if (this.currentUser.data.user?.agency?.code === 'RHA' && this.currentUser.data.user.userType.code === 'STF') {
  //     if (this.paramsId) {
  //       if (this.paramsId === 'MYTAKS') {
  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/by-assign-user-for-review/' + this.currentUser.userId)
  //           .subscribe(
  //             data => {
  //               this.content = data;
  //               this.fullList = data;
  //               this.fullList.forEach((element: any) => {
  //                 element.count = element.assignUsersFoReview?.length;
  //                 element.categoryTypeName = element.categoryTypes.name;
  //                 element.buildTypeName = element.buildTypes.name;
  //                 element.permitTypeName = element.permitTypes.name;
  //                 element.upi = element.projects.upi;
  //               });
  //               this.totalRecords = this.fullList.length;
  //               this.filterAndPaginate();
  //             }
  //           )
  //       } if (this.paramsId === 'RHAAPRVD' || this.paramsId === 'RHARJCT' || this.paramsId === 'RHABFCR') {
  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/approvalStatus/code/search?search=' + this.paramsId)
  //           .subscribe(
  //             statusData => {
  //               this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/application/approvalStatus/' + statusData.items[0]?.id)
  //                 .subscribe(
  //                   data => {
  //                     this.content = data;
  //                     this.fullList = data;
  //                     this.fullList.forEach((element: any) => {
  //                       element.count = element.assignUsersFoReview?.length;
  //                       element.categoryTypeName = element.categoryTypes.name;
  //                       element.buildTypeName = element.buildTypes.name;
  //                       element.permitTypeName = element.permitTypes.name;
  //                       element.upi = element.projects.upi;
  //                     });
  //                     this.totalRecords = this.fullList.length;
  //                     this.filterAndPaginate();
  //                   })
  //             }
  //           )
  //       } else {
  //         // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=NORHA')
  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + this.paramsId)
  //           .subscribe(
  //             statusData => {
  //               this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/applicationStatus/' + statusData.items[0]?.id)
  //                 .subscribe(
  //                   data => {
  //                     this.content = data;
  //                     if (this.paramsId) {
  //                       this.fullList = this.calculateAndAddDaysColumn(data.filter(
  //                         (item: any) => item.applicationStatus.code === this.paramsId))
  //                     } else {
  //                       this.fullList = data;
  //                       this.fullList.forEach((element: any) => {
  //                         element.count = element.assignUsersFoReview?.length;
  //                         element.categoryTypeName = element.categoryTypes.name;
  //                         element.buildTypeName = element.buildTypes.name;
  //                         element.permitTypeName = element.permitTypes.name;
  //                         element.upi = element.projects.upi;
  //                       });
  //                       // this.totalRecords = this.fullList.length;
  //                       // this.filterAndPaginate();
  //                     }

  //                     this.totalRecords = this.fullList.length;
  //                     this.filterAndPaginate();
  //                   })
  //             }
  //           )
  //       }

  //     } else {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/NonObjections')
  //         .subscribe(
  //           data => {
  //             this.content = data;
  //             if (this.paramsId) {
  //               this.fullList = this.calculateAndAddDaysColumn(data.filter(
  //                 (item: any) => item.applicationStatus.code === this.paramsId))
  //             } else {
  //               this.fullList = data;
  //               // }
  //               this.fullList.forEach((element: any) => {
  //                 element.count = element.assignUsersFoReview?.length;
  //                 element.categoryTypeName = element.categoryTypes.name;
  //                 element.buildTypeName = element.buildTypes.name;
  //                 element.permitTypeName = element.permitTypes.name;
  //                 element.upi = element.projects.upi;
  //               });
  //             }
  //             this.totalRecords = this.fullList.length;
  //             this.filterAndPaginate();
  //           }
  //         )
  //     }

  //   }
  //   else if (this.currentUser.data.user.role.code === 'TMLD') {

  //     if (this.paramsId) {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + this.paramsId)
  //         .subscribe(
  //           statusData => {
  //             if (this.currentUser.data.user.agency.code === 'COK') {
  //               this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + statusData?.items[0]?.id + '/agency/' + this.currentUser.data.user.agency.id)
  //                 .subscribe(
  //                   data => {
  //                     this.content = data;
  //                     this.fullList = data;
  //                     this.fullList.forEach((element: any) => {
  //                       element.count = element.assignUsersFoReview?.length;
  //                       element.categoryTypeName = element.categoryTypes.name;
  //                       element.buildTypeName = element.buildTypes.name;
  //                       element.permitTypeName = element.permitTypes.name;
  //                       element.upi = element.projects.upi;
  //                     });
  //                     this.totalRecords = this.fullList.length;
  //                     this.filterAndPaginate();
  //                   })
  //             } else {
  //               this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + statusData?.items[0]?.id + '/agency/' + this.currentUser.data.user.agency.id)
  //                 .subscribe(
  //                   data => {
  //                     this.content = data;
  //                     this.fullList = data;
  //                     this.fullList.forEach((element: any) => {
  //                       element.count = element.assignUsersFoReview?.length;
  //                       element.categoryTypeName = element.categoryTypes.name;
  //                       element.buildTypeName = element.buildTypes.name;
  //                       element.permitTypeName = element.permitTypes.name;
  //                       element.upi = element.projects.upi;
  //                     });
  //                     this.totalRecords = this.fullList.length;
  //                     this.filterAndPaginate();
  //                   })
  //             }

  //           })
  //     } else {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application')
  //         .subscribe(
  //           data => {
  //             this.content = data;
  //             if (this.paramsId) {
  //               this.fullList =
  //                 this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId))
  //             } else {
  //               this.fullList = data;
  //             }
  //             this.fullList.forEach((element: any) => {
  //               element.count = element.assignUsersFoReview?.length;
  //               element.categoryTypeName = element.categoryTypes.name;
  //               element.buildTypeName = element.buildTypes.name;
  //               element.permitTypeName = element.permitTypes.name;
  //               element.upi = element.projects.upi;
  //             });
  //             this.totalRecords = this.fullList.length;
  //             this.filterAndPaginate();
  //           },
  //         )
  //     }
  //   } else if (this.currentUser.data.user.role.code === 'STF' && this.currentUser.data.user.userType.code === 'STF') {
  //     if (this.paramsId) {
  //       // olivier
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + this.paramsId)
  //         .subscribe(
  //           statusData => {
  //             this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + statusData?.items[0]?.id + '/agency/' + this.currentUser.data.user.agency.id)
  //               .subscribe(
  //                 data => {
  //                   this.content = data;
  //                   this.fullList = data;
  //                   this.fullList.forEach((element: any) => {
  //                     element.count = element.assignUsersFoReview?.length;
  //                     element.categoryTypeName = element.categoryTypes.name;
  //                     element.buildTypeName = element.buildTypes.name;
  //                     element.permitTypeName = element.permitTypes.name;
  //                     element.upi = element.projects.upi;
  //                   });
  //                   this.totalRecords = this.fullList.length;
  //                   this.filterAndPaginate();
  //                 })
  //           }
  //         )
  //     } else {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/agency/' + this.currentUser.data?.user?.agency?.id)
  //         .subscribe(
  //           data => {
  //             this.content = data;
  //             let array = data.filter(
  //               (item: any) => item.applicationStatus.code === 'UNRV')
  //             if (this.paramsId) {
  //               this.fullList =
  //                 this.calculateAndAddDaysColumn(array.filter((item: any) => item.applicationStatus.code === this.paramsId))
  //             } else {
  //               this.fullList = this.calculateAndAddDaysColumn(data);
  //             }
  //             this.fullList.forEach((element: any) => {
  //               element.count = element.assignUsersFoReview?.length;
  //               element.categoryTypeName = element.categoryTypes.name;
  //               element.buildTypeName = element.buildTypes.name;
  //               element.permitTypeName = element.permitTypes.name;
  //               element.upi = element.projects.upi;
  //             });
  //             this.totalRecords = this.fullList.length;
  //             this.filterAndPaginate();
  //           })
  //     }


  //   } else if (this.currentUser.data.user.role.code === 'INSP' || this.currentUser.data.user.role.code === 'DINSP' || this.currentUser.data.user.role.code === 'SINSP') {



  //     if (this.paramsId) {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + this.paramsId)
  //         .subscribe(
  //           statusData => {
  //             this.applicationService.findAllWithPath(environment.applicationUrl + 'application/inspectionApplication/' + statusData.items[0]?.id + '/agency/' + this.currentUser.data.user.agency.id)
  //               .subscribe(
  //                 data => {
  //                   this.content = data;
  //                   if (this.paramsId) {
  //                     this.fullList = this.calculateAndAddDaysColumn(data.filter(
  //                       (item: any) => item.applicationStatus.code === this.paramsId))
  //                   } else {
  //                     this.fullList = data;
  //                     this.fullList.forEach((element: any) => {
  //                       element.count = element.assignUsersFoReview?.length;
  //                       element.categoryTypeName = element.categoryTypes.name;
  //                       element.buildTypeName = element.buildTypes.name;
  //                       element.permitTypeName = element.permitTypes.name;
  //                       element.upi = element.projects.upi;
  //                     });
  //                   }
  //                   this.totalRecords = this.fullList.length;
  //                   this.filterAndPaginate();
  //                 })
  //           }
  //         )
  //     } else {
  //       this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=CTFD')
  //         .subscribe(
  //           statusData => {
  //             this.applicationService.findAllWithPath(environment.applicationUrl + 'application/inspectionApplication/' + statusData.items[0]?.id + '/agency/' + this.currentUser.data.user.agency.id)
  //               .subscribe(
  //                 data => {
  //                   this.content = data;
  //                   this.fullList = data;
  //                   this.fullList.forEach((element: any) => {
  //                     // KJHFKJA
  //                     element.count = element.assignUsersFoReview?.length;
  //                     element.categoryTypeName = element.categoryTypes.name;
  //                     element.buildTypeName = element.buildTypes.name;
  //                     element.permitTypeName = element.permitTypes.name;
  //                     element.upi = element.projects.upi;
  //                   });
  //                   this.totalRecords = this.fullList.length;
  //                   this.filterAndPaginate();
  //                 })
  //           }
  //         )
  //     }





  //   } else {

  //     if (this.paramsId) {
  //       // olivier
  //       if (this.currentUser.data.user.role.code === 'DRCT' && this.currentUser.data.user.userType.code === 'STF') {
  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + this.paramsId)
  //           .subscribe(
  //             statusData => {
  //               // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/applicationStatus/' + statusData.items[0]?.id)
  //               this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' +
  //                 statusData.items[0]?.id + '/agency/' + this.currentUser?.data?.user?.agency?.id)
  //                 .subscribe(
  //                   data => {
  //                     this.content = data;
  //                     if (this.paramsId) {
  //                       this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId));
  //                     } else {
  //                       this.fullList = this.calculateAndAddDaysColumn(data);
  //                     }
  //                     this.fullList.forEach((element: any) => {
  //                       element.count = element?.assignUsersFoReview?.length;
  //                       element.categoryTypeName = element?.categoryTypes?.name;
  //                       element.buildTypeName = element?.buildTypes?.name;
  //                       element.permitTypeName = element?.permitTypes?.name;
  //                       element.upi = element?.projects?.upi;
  //                     });
  //                     this.totalRecords = this?.fullList?.length;
  //                     this.filterAndPaginate();
  //                   })
  //             }
  //           )
  //       }

  //     }

  //     else {


  //       if (this.currentUser.data.user.agency.code === 'COK') {

  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/allApplicationWithoutInspection/' + this.currentUser.data?.user?.agency?.id)
  //           .subscribe(
  //             data => {
  //               this.content = data;
  //               if (this.paramsId) {
  //                 this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId));
  //               } else {
  //                 this.fullList = this.calculateAndAddDaysColumn(data);
  //               }
  //               this.fullList.forEach((element: any) => {
  //                 element.count = element.assignUsersFoReview?.length;
  //                 element.categoryTypeName = element.categoryTypes.name;
  //                 element.buildTypeName = element.buildTypes.name;
  //                 element.permitTypeName = element.permitTypes.name;
  //                 element.upi = element.projects.upi;
  //               });
  //               this.totalRecords = this.fullList.length;
  //               this.filterAndPaginate();
  //             },
  //           )
  //       } else {

  //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/agency/' + this.currentUser.data?.user?.agency?.id)
  //           .subscribe(
  //             data => {
  //               this.content = data;
  //               if (this.paramsId) {
  //                 this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId));
  //               } else {
  //                 this.fullList = this.calculateAndAddDaysColumn(data);
  //               }
  //               this.fullList.forEach((element: any) => {
  //                 element.count = element.assignUsersFoReview?.length;
  //                 element.categoryTypeName = element.categoryTypes.name;
  //                 element.buildTypeName = element.buildTypes.name;
  //                 element.permitTypeName = element.permitTypes.name;
  //                 element.upi = element.projects.upi;
  //               });
  //               this.totalRecords = this.fullList.length;
  //               this.filterAndPaginate();
  //             },
  //           )
  //       }

  //     }

  //   }
  // }



  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    // if (!this.userService.searchTerm) return true;
    // const term = this.userService.searchTerm.toLowerCase();
    // return Object.values(item).some(val =>
    //   String(val).toLowerCase().includes(term)
    // );
    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';

    const matchesTerm = term ? Object.values(item).some(val => String(val).toLowerCase().includes(term)) : true;
    const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

    return matchesTerm && matchesDate;
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {

    if (this.searchData.dateFrom && this.searchData.dateTo) {
      this.onSearchByDateChange('');
    } else if (this.userService.searchTerm) {
      this.onSearchTermChange(this.userService.searchTerm);
    } else {
      this.filterAndPaginate();
    }
  }



  onSearchByDateChange(date: string): void {
    // this.userService.filterByDate = date;
    // this.filterAndPaginate();
    if (this.searchData.dateFrom && this.searchData.dateTo) {
      const from = this.searchData.dateFrom ? new Date(this.searchData.dateFrom) : null;
      const to = this.searchData.dateTo ? new Date(this.searchData.dateTo) : null;

      if (from) {
        // Convert 'from' to UTC start of the day
        from.setHours(0, 0, 0, 0);
      }

      if (to) {
        // Convert 'to' to UTC end of the day
        to.setHours(23, 59, 59, 999);
      }
      if (to) {
        // Add one day to the 'to' date to include the entire day
        to.setDate(to.getDate() + 1);
      }

      const filtered = this.fullList.filter(item => {
        const createdAt = new Date(item.submittedDate);
        return (!from || createdAt >= from) && (!to || createdAt < to);
      });




      this.filteredList = filtered;
      this.totalRecords = filtered.length;
      this.startIndex = (this.page - 1) * this.pageSize + 1;
      this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
      if (this.endIndex > this.totalRecords) {
        this.endIndex = this.totalRecords;
      }
      this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    }
  }


  changePageSize() {
    this.pageSize = (+this.pageSize);
    this.filterAndPaginate();
  }



  // calculateAndAddDaysColumn(dates: any[]): any[] {
  //   const currentDate = new Date();
  //   return dates.map(dateObj => {
  //     if (dateObj.isCountingActive) {
  //       if (dateObj.applicationStatus.code === 'RSMB') {
  //         const createdAt = new Date(dateObj.submittedDate);
  //         const differenceInTime = currentDate.getTime() - createdAt.getTime();
  //         const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
  //         return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
  //       } else {
  //         const createdAt = new Date(dateObj.submittedDate);
  //         const differenceInTime = currentDate.getTime() - createdAt.getTime();
  //         const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
  //         return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
  //       }
  //     } else {
  //       return { ...dateObj, daysBetween: null };
  //     }

  //   });
  // }


  calculateAndAddDaysColumn(dates: any[]): any[] {
    const currentDate = new Date();
    return dates.map(dateObj => {
      if (dateObj.isCountingActive) {
        if (
          // dateObj.applicationStatus.code === 'RSMB'


          // &&

          ((new Date(dateObj.reSubmittedDate).getTime())
            > (new Date(dateObj.submittedDate).getTime()))


        ) {
          const createdAt = new Date(dateObj.reSubmittedDate);
          const differenceInTime = currentDate.getTime() - createdAt.getTime();
          const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
          return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
        }



        else {
          const createdAt = new Date(dateObj.submittedDate);
          const differenceInTime = currentDate.getTime() - createdAt.getTime();
          const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
          return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
        }




      } else {
        return { ...dateObj, daysBetween: dateObj.numberOfDayItTakes };
        // return dateObj.numberOfDayItTakes
      }
    });
  }















  getChoosenSelectedUse(event: any) {
    let existingUpiInApplication = this.lists.find((l: any) => l.projects.upi === event.upi);
    this.applicationDetail = event;
    if (existingUpiInApplication) {
      this.applicationDetail.isUpiExists = true;
    } else {
      this.applicationDetail.isUpiExists = false;
      this.goToApplication();
    }
    // if (event === 'yes') {
    //   // integration with RRA
    //   this.rraIntegration();
    //   // integration with RRA
    //   this.modalService.dismissAll();
    //   this.cancel();
    //   // this.modalService.open(this.modalNewPermitContent, { size: 'md', centered: true });
    // } else {
    //   this.applicationDetail.hasEngineerToFollow = true;
    //   this.checkIsNewPermit();
    // }
  }


  isNewPermitM(event: any) {
    if (event === 'yes') {
      this.applicationDetail.isNewPermit = false;
      this.cancel();
      this.goToApplication();
    } else {
      this.applicationDetail.isNewPermit = true;
    }
  }

  goToApplication() {
    localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(this.applicationDetail));
    this.router.navigate(['/account/application/new-application/0/0']);
  }





  rraIntegration() {

  }




  searchEngineerByLicenseNumber() {
    // search existing certificate number through this.applicationDetail.certificateNumber properties
    // then
    this.checkIsNewPermit();


    // this.cancel();
    // this.router.navigate(['/account/application/new-application/1/0']);
  }


  checkIsNewPermit() {
    this.cancel();
    this.modalService.open(this.modalNewPermitContent, { size: 'md', centered: true });
  }




  cancel() {
    this.modalService.dismissAll();
  }


  ngOnDestroy() {
    this.userService.searchTerm = '';
  }


  submitCertificateNumber() {
    this.goToApplication();
    // applicationDetail.isNewPermit
    // applicationDetail.certificateNumber
  }

  assignToUser(content: any, event: any, type: any) {
    this.outputData = {};
    this.outputData = event;
    this.outputData.isAssigningData = true;
    this.openModal(content, type);
  }

  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }



  closePopup(event: any) {
    if (event) {
      this.outputData = {};
      this.modalService.dismissAll();
      // this.loadList();
    } else {
      this.modalService.dismissAll()
    }

  }


  viewCertificate(event: any) {
    if (event.statusId === '3') {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, `The certificate has been cancelled`, "top", "right");
    } else {
      this.router.navigate(['/account/certificate/certificate-detail/', event?.id]);
    }
  }





}
