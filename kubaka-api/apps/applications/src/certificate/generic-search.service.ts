import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';

@Injectable()
export class GenericSearch<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.andWhere(whereSearch);
    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

@Injectable()
export class GenericSearchWithRelations<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
    relations?: string[],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.where(whereSearch);

    // Include relations if specified
    if (relations && relations.length > 0) {
      for (const relationName of relations) {
        queryBuilder.leftJoinAndSelect(`alias.${relationName}`, relationName);
      }
    }

    const [items, totalCount] = await queryBuilder.getManyAndCount();
    console.log(items);
    return { items, totalCount };
  }
}

// @Injectable()
// export class GenericSearchWithRelations2<T> {
//   async search(
//     repository: Repository<T>,
//     searchFields: (keyof T & string)[],
//     search: string,
//     relations?: string[],
//   ) {
//     const queryBuilder = repository.createQueryBuilder('alias');
//     const whereSearch: { [key: string]: any } = {};

//     // Constructing the search criteria
//     searchFields.forEach((field) => {
//       if (typeof field === 'string') {
//         if (field.toLowerCase().includes('id')) {
//           whereSearch[field] = search; // Exact match for UUIDs
//         } else {
//           whereSearch[field] = `${search}`; // ILike for non-UUID fields
//           console.log(search);
//         }
//       }
//     });
//     queryBuilder.where(whereSearch);

//     // Include relations if specified
//     if (relations && relations.length > 0) {
//       relations.forEach((relationName) => {
//         queryBuilder.leftJoinAndSelect(`alias.${relationName}`, relationName);
//       });
//     }

//     // Filter based on userId
//     const userIdField = 'userId'; // Assuming 'userId' is a property of the Application entity
//     if (relations && relations.includes('applications')) {
//       queryBuilder.innerJoin('alias.applications', 'application');
//       // Use equality for UUID comparison
//       queryBuilder.andWhere(`application.${userIdField} = :userId`, {
//         userId: search,
//       });
//       console.log(relations);
//     }

//     // Fetching items and total count
//     const items = await queryBuilder.getMany();
//     const totalCount = items.length;
//     console.log(items);
//     return { items, totalCount };
//   }
// }
@Injectable()
export class GenericSearchWithRelations2<T> {
  async search(
    repository: Repository<T>,
    searchFields: (keyof T & string)[],
    search: string,
    relations?: string[],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: { [key: string]: any } = {};

    // Constructing the search criteria
    searchFields.forEach((field) => {
      if (typeof field === 'string') {
        if (field.toLowerCase().includes('id')) {
          whereSearch[field] = search; // Exact match for UUIDs
        } else {
          whereSearch[field] = `${search}`; // ILike for non-UUID fields
        }
      }
    });
    queryBuilder.where(whereSearch);

    // Include relations if specified
    if (relations && relations.length > 0) {
      // const relationName = relations[0]; // Assuming only one relation for simplicity

      queryBuilder.andWhere(`application.userId = :userId`, {
        userId: search,
      });
      console.log(queryBuilder.innerJoin('alias.applications', 'application'));
    }

    // Fetching items and total count
    const items = await queryBuilder.getMany();
    const totalCount = items.length;
    console.log(items);
    return { items, totalCount };
  }
}
