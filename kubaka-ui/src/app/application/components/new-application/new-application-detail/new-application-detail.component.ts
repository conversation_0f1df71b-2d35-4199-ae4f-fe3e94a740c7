import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-new-application-detail',
  templateUrl: './new-application-detail.component.html',
  styleUrls: ['./new-application-detail.component.scss']
})
export class NewApplicationDetailComponent {
  @ViewChild('mySelect') mySelect!: ElementRef<HTMLSelectElement>;
  isSelectDisabled = true;
  @Input() inputData: any = {};
  userForm!: UntypedFormGroup;
  pageForm!: UntypedFormGroup;
  categoryTypes: any[] = [];
  buildingTypes: any[] = [];
  permitTypes: any[] = [];
  loading: boolean = false;
  currentUser: any;
  paramsId: any = {};
  upiInfo: any = {};
  applicationDetails: any = {};
  formAccess: any = {};
  submitted: boolean = false;
  technologySurveys: any[] = [];
  agencies: any[] = [];
  doYouWantToDoItYouself: boolean = true;
  doYouWantToAssignToEngineer: boolean = false;
  isAssociated: boolean = false;
  outputData: any = {};



  projectDetails: any = {}


  checkValue(event: any) {
    if (event.target.value < 0) {
      event.target.value = 0;
    }
  }

  constructor(
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private appConfig: AppConfig,
    private route: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
    private sessionService: SessionService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;

    // this.currentUser.data.user.userType.code = 'APP'
    this.loadLookups();
    this.route.params.subscribe((params: any) => {
      this.paramsId = params.id;
      if (this.paramsId === '0') { }
    })
  }




  checkAssociated(event: any) {

    if (this.paramsId !== '0') {
      this.outputData.applicationId = this.paramsId;
      this.outputData.combiningPlotSize = this.applicationDetails.combiningPlotSize;
      this.outputData.plotSize = this.applicationDetails.plotSize;
      this.outputData.applicationStatusCode = this.userForm.value.applicationStatusCode;
      this.isAssociated = !this.isAssociated;
      this.openModal(event, 'lg');
    } else {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Project identifier is not yet generated", "top", "right");
    }
  }


  closeAssociatedPopup(event: any) {
    this.modalService.dismissAll();
    this.isAssociated = !this.isAssociated;
    // update value of percentage
    this.checkIfUPIhasAlreadyProject();
  }

  getNewCombiningAssociated(event: any) {
    if (event.isCombining) {
      this.userForm.controls['combiningPlotSize'].setValue(event.combining + this.userForm.value.plotSize);
    }
  }
  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  ngOnInit(): void {
    this.refresh();
    this.formAccessData();
    if (this.paramsId === '0') {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'Invalid operation. This component is for updating existing applications only.',
        "top",
        "center"
      );
      this.router.navigate(['/account/dashboard']);
      return;
    }

    // Load existing application data for update
    this.checkIfUPIhasAlreadyProject();
  }

  /**
   * Validate form for update operation
   */
  private validateForUpdate(): boolean {
    console.log('validateForUpdate() called');
    console.log('Form invalid:', this.userForm.invalid);
    console.log('Application ID:', this.userForm.value.applicationId);
    console.log('Params ID:', this.paramsId);

    if (this.userForm.invalid) {
      console.log('Form validation failed - form is invalid');
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Fill all required fields', "bottom", "center");
      return false;
    }

    // Ensure we have an application ID for updates
    if (!this.userForm.value.applicationId) {
      console.log('Form validation failed - no application ID');
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'No application found to update. This component is for updating existing applications only.', "bottom", "center");
      return false;
    }

    // Additional validation to ensure we're in update mode
    if (this.paramsId === '0') {
      console.log('Form validation failed - invalid params ID');
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Invalid operation. This component cannot create new applications.', "bottom", "center");
      return false;
    }

    console.log('Form validation passed');
    return true;
  }

  checkIfUPIhasAlreadyProject() {
    // Call project by upi
    if (this.paramsId === '0') {
      this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_NEW_INFO) as any);
      // this.getProjectByUPI(this.upiInfo.upi);
    } else {
      this.loading = true;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/' + this.paramsId)
        .subscribe(
          data => {
            this.applicationDetails = data;
            if (this.applicationDetails) {
              this.userForm.controls['projectId'].setValue(this.applicationDetails?.id);
              this.userForm.controls['agencyId'].setValue(this.applicationDetails?.agencyId);
              this.userForm.controls['plotSize'].setValue(this.applicationDetails?.plotSize);
              this.userForm.controls['upi'].setValue(this.applicationDetails?.upi)
              if (this.applicationDetails?.districtCode) {
                this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + this.applicationDetails.districtCode)
                  .subscribe(
                    agencyData => {
                      this.userForm.controls['agencyCode'].setValue(agencyData.code);
                      this.loading = false;
                    },
                    error => { this.loading = false; }
                  )
              } else {
                this.utilService.showNotification(NOTIFICATION_COLOR.error, "District code is missing", "top", "right");
              }

              this.checkIfProjectUPIHasAssociatedUpi(this.applicationDetails);
              // Call exiting application by project id and permit type
              this.getExistingApplicationAndPermitType();
              // Call exiting application by project id and permit type
            } else {
              this.utilService.showNotification(NOTIFICATION_COLOR.error, "Project you are looking for does not found", "top", "right");
            }
          },
          error => { this.loading = false; }
        )
    }
  }

  getExistingApplicationAndPermitType() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe({
        next: (permitTypeDatadata) => {
          if (permitTypeDatadata?.items?.length > 0) {
            this.userForm.controls['usedPermitType'].setValue(permitTypeDatadata.items[0]?.id);

            // Check if application already exists for this project and permit type
            this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.paramsId + '/permit-type/' + this.userForm.value.usedPermitType)
              .subscribe({
                next: (data) => {
                  if (data.length > 0) {
                    this.projectDetails = data[0].projects;

                    if (this.projectDetails.applicationStatus?.code !== 'CXL') {
                      this.formAccess.ApplicationAlreadyExists = true;
                      this.fillTheDataTotheForm(data[0]);
                    }
                  } else {
                    this.userForm.controls['plotSize'].setValue(this.applicationDetails.plotSize);
                    this.formAccess.ApplicationAlreadyExists = false;
                  }
                },
                error: (error) => {
                  console.error('Error fetching existing application:', error);
                  this.formAccess.ApplicationAlreadyExists = false;
                }
              });
          }
        },
        error: (error) => {
          console.error('Error fetching permit type:', error);
        }
      });
  }


  checkIfProjectUPIHasAssociatedUpi(projectDetails: any) {
    if (projectDetails.isAssociatedUpi && this.paramsId !== '0') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/associatedUPI/ByProject/${projectDetails.id}`)
        .subscribe(dataItem => {
          // Initialize combiningPlotSize to zero
          let totalPlotSize = 0;

          // Accumulate plot sizes
          dataItem.forEach((element: any) => {
            totalPlotSize += +element.plotSize;
          });
          // lkjfla
          // Update the combiningPlotSize and percentageSpaceUse once
          this.userForm.controls['combiningPlotSize'].setValue(totalPlotSize + this.userForm.value.plotSize);

          const buildUpArea = this.userForm.value.buildUpArea || 1; // Avoid division by zero
          const percentageSpaceUse = ((buildUpArea / (totalPlotSize + this.userForm.value.plotSize)) * 100).toFixed(2);
          this.userForm.controls['percentageSpaceUse'].setValue(percentageSpaceUse);
        });
    }
  }


  buildUpAreaValidator: ValidatorFn = (control: AbstractControl): { [key: string]: any } | null => {
    const plotSize = control.get('plotSize');
    const combiningPlotSize = control.get('combiningPlotSize');

    // const plotSize = this.upiInfo.size;
    const buildUpArea = control.get('buildUpArea');
    const percentageSpaceUse: any = control.get('percentageSpaceUse');
    if (combiningPlotSize && combiningPlotSize?.value && buildUpArea && combiningPlotSize?.value > plotSize?.value) {
      if (buildUpArea.value > combiningPlotSize.value) {
        return { 'greaterThanPlotSize': true };
      }
    } else
      if (plotSize && buildUpArea && plotSize.value && buildUpArea.value) {
        if (buildUpArea.value > plotSize.value) {
          return { 'greaterThanPlotSize': true };
        }
      }
    return null;
  };




  changeForm() {
    this.doYouWantToAssignToEngineer = false;
    this.pageForm.controls['doYouWantToAssignToEngineer'].setValue(false);
    this.doYouWantToDoItYouself = true;
    this.pageForm.controls['doYouWantToDoItYouself'].setValue(true);

  }

  changeAssignForm() {
    this.doYouWantToAssignToEngineer = true;
    this.pageForm.controls['doYouWantToAssignToEngineer'].setValue(true);
    this.doYouWantToDoItYouself = false;
    this.pageForm.controls['doYouWantToDoItYouself'].setValue(false);

  }





  fillTheDataTotheForm(data: any) {

    this.userForm.controls['applicationId'].setValue(data?.id);
    this.userForm.controls['permitTypeId'].setValue(data?.permitTypes?.id);
    this.userForm.controls['categoryTypeId'].setValue(data?.categoryTypes?.id);
    this.userForm.controls['buildTypeId'].setValue(data?.buildTypes?.id);
    this.userForm.controls['permitTypeCode'].setValue(data?.permitTypes?.code);
    this.userForm.controls['applicationStatusId'].setValue(data?.applicationStatus?.id);
    this.userForm.controls['applicationStatusCode'].setValue(data?.applicationStatus?.code);
    // this.userForm.controls['userId'].setValue(data?.userDetails?.id);
    // this.userForm.controls['agencyId'].setValue(data?.agency?.id);
    this.userForm.controls['agencyId'].setValue(data?.agencyId);
    this.userForm.controls['projectId'].setValue(data?.projects?.id);
    // this.userForm.controls['agencyCode'].setValue(this.agencies.length > 0 ? this.agencies.find((x: any) => x.id === data?.agencyId).code : data?.agencyId);
    this.getAgencyCode();
    this.userForm.controls['categoryCode'].setValue(data?.categoryTypes?.code);
    // this.userForm.controls['usedPermitType'].setValue(data.permitType.id);
    this.userForm.controls['certificateNumberEIA'].setValue(data?.certificateNumberEIA);
    this.userForm.controls['waterConsumption'].setValue(data?.waterConsumption);
    this.userForm.controls['electricityConsumption'].setValue(data?.electricityConsumption);
    this.userForm.controls['DistanceToTheNearestLandIn'].setValue(data?.DistanceToTheNearestLandIn);
    this.userForm.controls['ProjectCostInUSD'].setValue(data?.ProjectCostInUSD);
    this.userForm.controls['ProjectCostInRwf'].setValue(data?.ProjectCostInRwf);
    this.userForm.controls['buildUpArea'].setValue(data?.buildUpArea);
    this.userForm.controls['numberOfFloor'].setValue(data?.numberOfFloor);
    this.userForm.controls['grossFloorArea'].setValue(data?.grossFloorArea);
    this.userForm.controls['numberOfParkingSpace'].setValue(data?.numberOfParkingSpace);
    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(data?.priceOfDwellingUnitRwf);
    this.userForm.controls['capacityInformation'].setValue(data?.capacityInformation);
    this.userForm.controls['numberOfDwellingUnits'].setValue(data?.numberOfDwellingUnits);
    this.userForm.controls['DescriptionOfOperation'].setValue(data?.DescriptionOfOperation);
    // optional
    this.userForm.controls['plotSize'].setValue(data?.projects?.plotSize);
    // optional
    this.userForm.controls['percentageSpaceUse'].setValue(data?.percentageSpaceUse);

    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(data?.priceOfDwellingUnitRwf);


    this.userForm.controls['technologySurveyId'].setValue(data?.technologySurveys?.id);
    // this.userForm.controls['upi'].setValue(data?.upi);

    if (this.userForm.value.buildTypeId) {
      this.getBuildTypeObject('');
      this.calculateNumberOfPeople();
    }

  }

  getAgencyCode() {

    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/' + this.userForm.value.agencyId)
      .subscribe(
        agencyData => {
          this.userForm.controls['agencyCode'].setValue(agencyData.code);
        },
        error => { }
      )

  }

  // Improved function to handle permit type changes without page navigation for updates
  checkTemporalPermit() {
    let permitType = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId);

    if (!permitType) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Invalid permit type selected', "top", "center");
      return;
    }

    // Update permit type code
    this.userForm.controls['permitTypeCode'].setValue(permitType.code);

    // Update category based on current form values
    const categoryCode = this.utilService.getCategory(this.userForm.value);
    this.userForm.controls['categoryCode'].setValue(categoryCode);
    this.findCategoryByCode(categoryCode);

    // For this update component, we don't navigate away - just show a warning if permit type changed
    if (this.paramsId !== '0' && permitType.code !== 'NCP') {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.warning,
        'Permit type changed to ' + permitType.name + '. Please ensure this is correct before saving.',
        "top",
        "center"
      );
    }

    // Log the change for debugging
    console.log('Permit type changed to:', permitType.code, permitType.name);
  }



  // 1
  getBuildTypeObject(event: any) {
    let buildType: any = this.buildingTypes.find((x: any) => x.id === this.userForm.value.buildTypeId);
    // getting permit type code

    let permitTypecode = this.userForm.value.permitTypeCode;
    this.userForm.controls['buildTypeCode'].setValue(buildType.code);

    if (buildType.code === 'FAI' ||
      buildType.code === VALUE.EDUCATION_BUILDING_TYPE ||
      buildType.code === 'ASMB'
      || buildType.code === 'INSTNAL'
      || buildType.code === 'MEM'
      || buildType.code === 'STRG'
      || buildType.code === 'MISLNS'
      || buildType.code === 'INST'
    ) {
      this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
      this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));

    } else {
      this.calculateToGetCategory()
    }

  }

  // 2
  calculateToGetCategory() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }
  // 3
  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / (this.paramsId === '0' ? this.upiInfo.size : this.userForm.value.plotSize)) * 100).toFixed(2));
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  calculateNumberOfPeople() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  calculateNumberOfFlow() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    if (this.userForm.value.buildUpArea && this.userForm.value.numberOfFloor) {
      this.userForm.controls['grossFloorArea'].setValue((this.userForm.value.buildUpArea) * ((+this.userForm.value.numberOfFloor) + 1));
    }
  }


  findCategoryByCode(code: any) {
    if (code) {
      this.userForm.controls['categoryTypeId'].setValue(this.categoryTypes.find((x: any) => x.code === code)?.id);
    }


  }



  updateApplication() {
    console.log('updateApplication() called');
    this.submitted = true;
    const dataToSubmit = this.prepareUpdateData();

    const updateEndpoint = environment.applicationUrl + 'application/application/' + this.userForm.value.applicationId;
    console.log('Update endpoint:', updateEndpoint);
    console.log('Data to submit:', dataToSubmit);

    // Single API call to update the application using PATCH
    this.applicationService.patchAssetWithoutParams(dataToSubmit, updateEndpoint)
      .subscribe({
        next: (response: any) => {
          console.log('Update successful, response:', response);
          this.submitted = false;

          // Handle the response which now includes attachment clearing info
          if (response?.message) {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, response.message, "bottom", "center");
          } else {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "Application updated successfully", "bottom", "center");
          }

          // Store the form data in sessionStorage for use in the attachment page
          const formData = this.prepareUpdateData();
          sessionStorage.setItem('applicationFormData', JSON.stringify(formData));

          // Navigate to the attachment page
          const navigationUrl = '/account/application/resume-application-attachment/NCP/' + this.getUrlParamater();
          console.log('Navigating to:', navigationUrl);
          this.router.navigate(['/account/application/resume-application-attachment/NCP/', this.getUrlParamater()]);
        },
        error: (error) => {
          console.error('Update application error:', error);
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, "Failed to update application", "bottom", "center");
        }
      });
  }

  deleteApplicationAttachments() {

    const deleteEndpoint = environment.documentUrl + 'DocMgt/documents/' + this.userForm.value.applicationId;
    this.applicationService.deleteWithPathNoId(deleteEndpoint)
      .subscribe({
        next: (response: any) => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Application attachments deleted successfully", "bottom", "center");
        },
        error: (error) => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, "Failed to delete application attachments", "bottom", "center");
          console.error('Delete application attachments error:', error);
        }
      });
  }


  /**
   * Prepare data for update submission
   */
  private prepareUpdateData() {
    return {
      "waterConsumption": +this.userForm.value.waterConsumption,
      "electricityConsumption": +this.userForm.value.electricityConsumption,
      "DistanceToTheNearestLandIn": +this.userForm.value.DistanceToTheNearestLandIn,
      "ProjectCostInUSD": +this.userForm.value.ProjectCostInUSD,
      "ProjectCostInRwf": +this.userForm.value.ProjectCostInRwf,
      "buildUpArea": +this.userForm.value.buildUpArea,
      "numberOfFloor": +this.userForm.value.numberOfFloor,
      "grossFloorArea": +this.userForm.value.grossFloorArea,
      "numberOfParkingSpace": +this.userForm.value.numberOfParkingSpace,
      "priceOfDwellingUnitRwf": +this.userForm.value.priceOfDwellingUnitRwf,
      "capacityInformation": +this.userForm.value.capacityInformation,
      "numberOfDwellingUnits": this.userForm.value.numberOfDwellingUnits ? this.userForm.value.numberOfDwellingUnits : 0,
      "DescriptionOfOperation": this.userForm.value.DescriptionOfOperation,
      "percentageSpaceUse": this.userForm.value.percentageSpaceUse,
      "userId": this.currentUser.userId,
      "certificateNumberEIA": this.userForm.value.certificateNumberEIA,
      "projectId": this.userForm.value.projectId,
      "permitTypeId": this.userForm.value.permitTypeId,
      "categoryTypeId": this.userForm.value.categoryTypeId,
      "buildTypeId": this.userForm.value.buildTypeId,
      "technologySurveyId": this.userForm.value.technologySurveyId,
      "agencyId": this.userForm.value.agencyId,
      "applicationStatusId": this.userForm.value.applicationStatusId,
      "permitTypeCode": this.userForm.value.permitTypeCode,
      "agencyCode": this.userForm.value.agencyCode,
      "upi": this.userForm.value.upi
    };
  }

  onSubmit() {
    // Validate form first
    if (!this.validateForUpdate()) {
      this.submitted = false;
      return;
    }

    // Prevent multiple submissions
    if (this.submitted) {
      return;
    }

    // This component is only for updating existing applications
    if (this.userForm.value.applicationId) {
      this.updateApplication();
      this.deleteApplicationAttachments();
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'No application found to update. Please ensure you have a valid application.',
        "bottom",
        "center"
      );
    }
  }



  comeAndAllowToAssign(event: any) {
    if (event.found) {
      this.formAccess.isIAEverified = true;
      this.formAccess.isEIAVerified = event.isEIAVerified;
      this.formAccess.isRdbIAEchecked = true;
      this.formAccess.isRetreived = 1;
      this.userForm.controls['certificateNumberEIA'].setValue(event?.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event?.isEIAVerified);

    } else {
      this.formAccess.isIAEverified = false;
      this.formAccess.isEIAVerified = event.isEIAVerified;
      this.formAccess.isRdbIAEchecked = true;
      // this.formAccess.isRetreived = 0;
      this.formAccess.isRetreived = 1;
      this.userForm.controls['certificateNumberEIA'].setValue(event?.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event?.isEIAVerified);

    }
  }


  allowToAssign() {
    this.formAccess.doYouWantToShareToEngineer = !this.formAccess.doYouWantToShareToEngineer;
  }

  onNext() {
    console.log('onNext() called');
    console.log('Form valid:', this.userForm.valid);
    console.log('Submitted flag:', this.submitted);
    console.log('Application ID:', this.userForm.value.applicationId);

    // Validate form first
    if (!this.validateForUpdate()) {
      console.log('Validation failed');
      return;
    }

    // Prevent multiple submissions
    if (this.submitted) {
      console.log('Already submitted, preventing duplicate submission');
      return;
    }

    console.log('Calling updateApplication()');
    // Update the application data before navigating to attachment page
    this.updateApplication();
  }


  getUrlParamater() {
    if (!this.formAccess.ApplicationAlreadyExists) {
      return this.userForm.value.applicationId;
    } else {
      return this.userForm.value.applicationId;
      // return this.paramsId
    }
  }

  onPrev() {
    this.router.navigate(['/account/application/new-application-project/', this.paramsId]);
  }



  formAccessData() {
    this.pageForm = this.formBuilder.group({
      doYouWantToDoItYouself: [false],
      doYouWantToAssignToEngineer: [false],
    })
  }


  refresh() {
    this.userForm = this.formBuilder.group({
      applicationId: [""],
      permitTypeId: ["", [Validators.required]],
      categoryTypeId: ["", [Validators.required]],
      buildTypeId: ["", [Validators.required]],
      buildTypeCode: [""],
      permitTypeCode: [""],
      applicationStatusId: [""], // hard coded
      userId: [this.currentUser.userId],
      agencyId: [""],
      projectId: ["", [Validators.required]],
      agencyCode: ["", [Validators.required]],
      categoryCode: [""],
      usedPermitType: [""],
      certificateNumberEIA: [""],
      applicationStatusCode: [''],
      waterConsumption: ["", [Validators.required]],
      electricityConsumption: ["", [Validators.required]],
      DistanceToTheNearestLandIn: ["", [Validators.required]],
      ProjectCostInUSD: ["", [Validators.required]],
      ProjectCostInRwf: ["", [Validators.required]],
      buildUpArea: ["", [Validators.required]],
      numberOfFloor: ["", [Validators.required]],  // help to calculate amount
      grossFloorArea: ["", [Validators.required]],
      numberOfParkingSpace: [""],
      priceOfDwellingUnitRwf: ["", [Validators.required]],
      capacityInformation: ["", [Validators.required]],
      numberOfDwellingUnits: [""],
      DescriptionOfOperation: [""],
      plotSize: [0],
      percentageSpaceUse: ["", [Validators.required]],
      technologySurveyId: [""],
      upi: [""],
      combiningPlotSize: [""],
      isAssociated: [""],
    }, { validator: this.buildUpAreaValidator })
  }


  loadLookups() {
    this.applicationService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => {
          this.agencies = data;
        }
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.TECHNOLOGYSURVEY)
      .subscribe(
        data => {
          this.technologySurveys = data;
          this.userForm.controls['technologySurveyId'].setValue(this.technologySurveys[0].id);
        }
      )
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.BUILDING_TYPE)
      .subscribe(
        data => { this.buildingTypes = data; }
      )
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => { this.categoryTypes = data; }
      )


    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=NCP')
    //   .subscribe(
    //     data => { this.permitTypes = data.items }
    //   )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => { this.permitTypes = data; }
      )

  }




  returnOtherInfo() {
    return {
      "userId": "",
      "licenseNumber": "",
      "permitTypeId": "",
      "applicationId": "",
      "doYouHaveTheOccupancy": "",
      "isFastAidBox": "",
      "disabilityToiletsFlipUpGrabBars": "",
      "paraLighteningSystem": "",
      "equipmentCapacity": "",
      "constructionMethod": "",
      "fireAlarmSystemWithAnAlarmBellOnEach": "",
      "whyNotFireAlarmSystemWithAnAlarmBellOnEach": "",
      "fireExtinguishersEvery50mOnEachFloor": "",
      "whyNotFireExtinguishersEvery50mOnEachFloor": "",
      "functioningExitSignsOnEachFloor": "",
      "whyNotfunctioningExitSignsOnEachFloor": "",
      "anEmergencyExitOnEachFloor": "",
      "whyNotanEmergencyExitOnEachFloor": "",
      "floorPlanOnEachLevel": "",
      "whyNotfloorPlanOnEachLevel": "",
      "numberSignOnEachFloor": "",
      "whyNotnumberSignOnEachFloor": "",
      "signForbiddingTheUseOfElevatorsInCaseOfFire": "",
      "whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire": "",
      "landingSpaceOnTopOfTheBuildingForHelicopters": "",
      "whyNotlandingSpaceOnTopOfTheBuildingForHelicopters": "",
      "CCTVCameras": "",
      "whyNotCCTVCameras": "",
      "WalkThroughAndHeldMetalDetect": "",
      "whyNotWalkThroughAndHeldMetalDetect": "",
      "UnderSearchMirror": "",
      "whyNotUnderSearchMirror": "",
      "LuggageScanners": "",
      "whyNotLuggageScanners": "",
      "PlatesIndicatingEmergencyResponseUnitsPhoneNumbers": "",
      "whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers": "",
      "EmergencyEvacuationPlan": "",
      "whyNotEmergencyEvacuationPlan": "",
      "SecurityManagerAndStaffCameras": "",
      "whyNotSecurityManagerAndStaffCameras": "",
      "AnInternalCommunicationSystem": "",
      "whyNotAnInternalCommunicationSystem": "",
      "BroadBandInternetServices": "",
      "whyNotBroadBandInternetServices": "",
      "StaffAndVisitorAccessCards": "",
      "whyNotStaffAndVisitorAccessCards": "",
      "applicationForFixedTelephoneLineConnection": "",
      "areThereAnyFacilitiesForTheDisabledProvidedBuilding": "",
      "whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding": "",
      "stageOfConstruction": "",
      "supervisingFirmSiteEngineer": "",
      "remarks": "",
      "dateForRequestedInspection": ""
    }
  }

}
