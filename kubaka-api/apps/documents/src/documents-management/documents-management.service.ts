import { Injectable } from '@nestjs/common';
import { ApplicationDocumentRepository } from './documents-management.repository';
import { MinioClientService } from '../minio-client/minio-client.service';
import { BufferedFile } from '../minio-client/file.model';
import { CreateApplicationDocumentDto } from '../dto/application-documents.dto';
import { ApplicationDocument } from 'apps/documents/entities/application-documents.entity';

@Injectable()
export class DocumentsManagementService {
  constructor(
    private minioClientService: MinioClientService,
    private readonly applicationDocumentRepository: ApplicationDocumentRepository,
  ) {}

  // application document logic
  async createApplicationDocument(
    createApplicationDocumentDto: CreateApplicationDocumentDto,
  ) {
    try {
      const applicationDocument = new ApplicationDocument({
        ...createApplicationDocumentDto,
      });
      return this.applicationDocumentRepository.create(applicationDocument);
    } catch (error) {
      console.error('Error create application service failed:', error);
      // Handle the error appropriately, possibly by returning an error response.
      throw new Error('File meta-data creation failed');
    }
  }

  // file upload handling logic

  async uploadFile(file: BufferedFile) {
    console.log('Received file:', file);

    try {
      const uploaded_file = await this.minioClientService.upload(file);
      console.log('File uploaded:', uploaded_file);

      const fileName = uploaded_file.fileName;
      // let fileUrl = await this.minioClientService.getFileUrl(filename); // getting file Url from get file method
      const fileUrl = uploaded_file.url; // let get file url  directry
      console.log('File URL:', fileUrl);
      console.log(fileName);

      return {
        fileUrl,
        fileName,
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      // Handle the error appropriately, possibly by returning an error response.
      throw new Error('File upload failed');
    }
  }

  async getFileUrl(fileName: string) {
    const fileUrl = await this.minioClientService.getFileUrl(fileName);
    const filename = fileName;
    return {
      message: 'File url retuned successfully!',
      data: {
        url: fileUrl,
        filename: filename,
      },
    };
  }
  catch(error) {
    console.error('Error file file not find:', error);
    // Handle the error appropriately, possibly by returning an error response.
    throw new Error('no file with file name provided in repo');
  }

  async deleteFile(fileName: string) {
    await this.minioClientService.deleteFile(fileName);
    return { message: 'File deleted successfully' };
  }
}
