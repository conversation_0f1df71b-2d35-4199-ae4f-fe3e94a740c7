import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import {
  ChatBoard,
  IssueCategory,
  Message,
} from './entities/notification.entity';
import { AbstractRepository } from '@app/common';

// application
@Injectable()
export class MessagesRepository extends AbstractRepository<Message> {
  constructor(
    @InjectRepository(Message)
    messageRepository: Repository<Message>,
    entityManager: EntityManager,
  ) {
    super(entityManager, messageRepository);
  }
}
// chat board
@Injectable()
export class ChatBoardRepository extends AbstractRepository<ChatBoard> {
  constructor(
    @InjectRepository(ChatBoard)
    chatBoardRepository: Repository<ChatBoard>,
    entityManager: EntityManager,
  ) {
    super(entityManager, chatBoardRepository);
  }
}

// issue category
@Injectable()
export class IssueCategoryRepository extends AbstractRepository<IssueCategory> {
  constructor(
    @InjectRepository(IssueCategory)
    issueCategoryRepository: Repository<IssueCategory>,
    entityManager: EntityManager,
  ) {
    super(entityManager, issueCategoryRepository);
  }
}
