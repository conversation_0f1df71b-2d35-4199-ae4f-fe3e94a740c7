<div class="step-panel"
*ngIf="this.currentUser.data.user.userType.code !== 'ENG' || this.currentUser.data.user.userType.code !== 'ARC'"

>
    <form [formGroup]="userForm" (ngSubmit)="submit()">
        <div class="step-panel_header">
            <h2>{{"newApplication.assignToEngineer.header" | translate }}
            </h2>
        </div>

        <div class="step-panel_body">
            <div class="kbk-x">
                <div class="form-set">
                    <div class="form-input">
                        <label>{{"newApplication.assignToEngineer.userType" | translate }}</label>
                        <div>
                            <select name="userCategoryId" id="userCategoryId" formControlName="userCategoryId"
                                (change)="changeDetectionValue()" required>
                                <option *ngFor="let op of userTypes" [value]="op.id">{{op.name}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-input">
                        <label>{{"newApplication.assignToEngineer.engineerArchitectLicenseNumber" | translate }}</label>
                        <div>
                            <input type="text" id="licenseNumber" name="licenseNumber" formControlName="licenseNumber"
                                (focusout)="checkEngineer()" required>
                        </div>
                    </div>
                    <div class="form-input">
                        <label>{{"newApplication.assignToEngineer.names" | translate }}</label>
                        <div>
                            <input type="text" id="names" name="names" formControlName="names" readonly required>
                        </div>
                    </div>
                    <div class="form-input">
                        <label>{{"newApplication.assignToEngineer.daysNeededForSubmission" | translate }}</label>
                        <div>
                            <input type="text" id="timeLineDays" name="timeLineDays" formControlName="timeLineDays" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="alert alert-danger" role="alert" *ngIf="userForm.value.message"> {{userForm.value.message}} </div>
        <div class="step-panel_footer">
            <!-- <button (click)="onPrev()">Previous</button> -->
            <button type="button" *ngIf="submitted"> {{"newApplication.assignToEngineer.wait" | translate }}... </button>
            <button type="submit" *ngIf="!submitted"> {{"newApplication.assignToEngineer.assign" | translate }} </button>
        </div>
    </form>
</div>
