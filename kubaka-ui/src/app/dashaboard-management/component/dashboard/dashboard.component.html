<!-- Applicant dashboard -->
<div class="app-dash">
  <div class="container">
    <div class="dash-flex">
      <div class="app-l">
        <div class="progress-cards">
          <span class="progress-title">{{"guideLineHeader" | translate }}</span>
          <div class="progr-bar">
            <div class="progr-bar_item" role="progressbar"></div>
          </div>
        </div>
        <div class="progress-steps">
          <ul class="step-list">
            <li class="step-list_item">
              <div class="step-list_titl">
                <h4>{{"guideLineTitle_1" | translate }}</h4>
              </div>
              <div class="step-list_dscb">
                <p>{{"guideLineContent_1" | translate }} </p>
              </div>
            </li>
            <li class="step-list_item">
              <div class="step-list_titl">
                <h4>{{"guideLineTitle_2" | translate }}</h4>
              </div>
              <div class="step-list_dscb">
                <p>{{"guideLineContent_2" | translate }}</p>
              </div>
            </li>
            <li class="step-list_item">
              <div class="step-list_titl">
                <h4>{{"guideLineTitle_3" | translate }}</h4>
              </div>
              <div class="step-list_dscb">
                <p>{{"guideLineContent_3" | translate }}</p>
              </div>
            </li>
            <!-- <li class="step-list_item">
                            <div class="step-list_titl">
                                <h4>Approve or Request for Action</h4>
                            </div>
                            <div class="step-list_dscb">
                                <p>Your application can be approved or you may be request to send more details and to change things according to the requirements</p>
                                <button type="button" class="kbk-btn kbk-btn-sm kbk-btn-sec">Read More</button>
                            </div>
                        </li> -->
            <li class="step-list_item">
              <div class="step-list_titl">
                <h4>{{"guideLineTitle_4" | translate }}</h4>
              </div>
              <div class="step-list_dscb">
                <p>{{"guideLineContent_4" | translate }} </p>
              </div>
            </li>
            <li class="step-list_item">
              <div class="step-list_titl">
                <h4>{{"guideLineTitle_5" | translate }}</h4>
              </div>
              <div class="step-list_dscb">
                <p>{{"guideLineContent_5" | translate }} </p>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <!-- land owner -->
      <div class="app-main" *ngIf="
          currentUser.data.user.userType.code === 'LO' &&
          currentUser.data.user?.agency?.code != 'RHA'
        ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}! </span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                  currentUser.data.user.role.code !== 'APP' &&
                  currentUser.data.user.role.code !== 'ADM'
                ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !==
                "ADM" ? currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
        currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>
        </div>
        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }} </span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('PND')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.pending }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
        <div class="app-cont">
          <div class="app-cont_title">
            <span class="hder" aria-label="header tittle" *ngIf="currentUser.data.user.role.code === 'APP'">{{"dashboardComponent.plotsTitle" | translate }}</span>
            <span class="hder" aria-label="header tittle" *ngIf="currentUser.data.user.role.code === 'STFRVWR'">{{"dashboardComponent.tasksTitle" | translate }}</span>
            <div class="btns">
              <!-- <button type="button" class="kbk-btn kbk-btn-main">Add New Plot</button> -->
            </div>
          </div>
          <div class="app-cont_list" *ngIf="!outputData.isMap">
            <div class="kbk-wrap-2">
              <div class="plot-card" *ngFor="let li of lists">
                <div class="plot-card_hder">
                  <h5>{{ li.districtName }}</h5>
                  <div class="kbk-x-e">
                    <button class="kbk-btn kbk-btn-sm kbk-btn-sec" (click)="viewItOnMap(li)"> {{"dashboardComponent.viewMap" | translate }} </button>
                  </div>
                </div>
                <div class="plot-card_body">
                  <img class="hld" src="assets/ikons/SVG/ikon-map.svg" alt="" />
                </div>
                <div class="plot-card_footer">
                  <!-- <p> <label>Representer:</label> Muvugizi Peter</p> -->
                  <p>
                    <label>{{"dashboardComponent.plotLocation" | translate }}:</label> {{ li.districtName }}, {{ li.projects?.sectorName }}
                  </p>
                  <!-- <p> <label>Plot Size(SQM):</label> {{li.plotSize}} SQM</p> -->
                  <p><label>UPI:</label> {{ li.upi }}</p>
                  <p><label>{{"dashboardComponent.plotUse" | translate }}:</label> {{ li.selectedUse }}</p>
                </div>
              </div>
            </div>
          </div>
          <app-view-plot-on-map *ngIf="outputData.isMap" [inputData]="outputData"
            (backToParent)="closeMap($event)"></app-view-plot-on-map>
          <!-- Required Documents list     -->
        </div>
      </div>
      <!-- land owner -->
      <!-- Director inspector -->
      <div class="app-main" style="display: none;" *ngIf="
      currentUser.data.user.userType.code === 'STF' &&
      currentUser.data.user?.agency?.code != 'RHA' &&
      currentUser.data?.user?.role?.code === 'DINSP' &&
      currentUser.data.user.userType.code !== 'LO'
    ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
              currentUser.data.user.role.code !== 'APP' &&
              currentUser.data.user.role.code !== 'ADM'
            ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !== "ADM" ?
                currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&

    currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>


        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>
        </div>

        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- Director inspector -->
      <!-- Senior inspector -->
      <div class="app-main" *ngIf="
      currentUser.data.user.userType.code === 'STF' &&
      currentUser.data.user?.agency?.code != 'RHA' &&
      (currentUser.data?.user?.role?.code === 'SINSP' ||
        currentUser.data?.user?.role?.code === 'DINSP'
      ) &&
      currentUser.data.user.userType.code !== 'LO'">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
              currentUser.data.user.role.code !== 'APP' &&
              currentUser.data.user.role.code !== 'ADM'
            ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !== "ADM" ?
                currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
    currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>


        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>
        </div>
        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
        <div class="icon">
          <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
        </div>
        <div class="dtail">
          <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
          <span class="dtail-num">{{ countingBoard.pending }} </span>
        </div>
      </div> -->
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }} </span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }} </span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- Senior inspector -->
      <!-- Team leader -->
      <div class="app-main" *ngIf="
      currentUser.data.user.userType.code === 'STF' &&
      currentUser.data.user?.agency?.code != 'RHA' &&
      currentUser.data?.user?.role?.code === 'TMLD' &&
      currentUser.data.user.userType.code !== 'LO'
    ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
              currentUser.data.user.role.code !== 'APP' &&
              currentUser.data.user.role.code !== 'ADM'
            ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !== "ADM" ?
                currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
    currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>

        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>

        </div>
           <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>
        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
        <div class="icon">
          <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
        </div>
        <div class="dtail">
          <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
          <span class="dtail-num">{{ countingBoard.pending }} </span>
        </div>
      </div> -->
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- Team leader -->
      <!-- Office Manager -->
      <div class="app-main" *ngIf="
      currentUser.data.user.userType.code === 'STF' &&
      currentUser.data.user?.agency?.code != 'RHA' &&
      currentUser.data?.user?.role?.code === 'OFCMNG' &&
      currentUser.data.user.userType.code !== 'LO'
    ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
              currentUser.data.user.role.code !== 'APP' &&
              currentUser.data.user.role.code !== 'ADM'
            ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !== "ADM" ?
                currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
    currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>

        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>
        </div>
            <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>

        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
        <div class="icon">
          <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
        </div>
        <div class="dtail">
          <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
          <span class="dtail-num">{{ countingBoard.pending }} </span>
        </div>
      </div> -->
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- Office Manager -->
      <!-- One Stop Center Manager -->
      <div class="app-main" *ngIf="
      currentUser.data.user.userType.code === 'STF' &&
      currentUser.data.user?.agency?.code != 'RHA' &&
      currentUser.data?.user?.role?.code === 'OSCM' &&
      currentUser.data.user.userType.code !== 'LO'
    ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
              currentUser.data.user.role.code !== 'APP' &&
              currentUser.data.user.role.code !== 'ADM'
            ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !== "ADM" ?
                currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
    currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>


        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>
        </div>
           <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>
        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
        <div class="icon">
          <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
        </div>
        <div class="dtail">
          <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
          <span class="dtail-num">{{ countingBoard.pending }} </span>
        </div>
      </div> -->
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- One Stop Center Manager -->
      <!-- Director -->
      <div class="app-main" *ngIf="
          currentUser.data.user.userType.code === 'STF' &&
          currentUser.data.user?.agency?.code != 'RHA' &&
          currentUser.data?.user?.role?.code === 'DRCT' &&
          currentUser.data.user.userType.code !== 'LO'
        ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                  currentUser.data.user.role.code !== 'APP' &&
                  currentUser.data.user.role.code !== 'ADM'
                ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !==
                "ADM" ? currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data.user.role.code !== 'ENG' &&
        currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          &nbsp;&nbsp;
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
                placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>
        </div>

        <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>

        <div class="app-lists">
          <div class="app-tblist">
            <div class="app-tblist_title">
              <span class="hder" aria-label="header tittle"></span>
              <div class="btns" *ngIf="currentUser.data.user.userType.code === 'LO'">
                <a class="kbk-btn kbk-btn-main" [routerLink]="['/account/application/applications']" id="create-btn">{{"dashboardComponent.applyPermitInstruction" | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.pending }} </span>
            </div>
          </div> -->
          <div class="app-stats_card" (click)="goToApplication('PAPRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.preApproval }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.review }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.submittedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
            <!-- <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span>
            </div> -->
          </div>
        </div>
        <!-- *ngIf="currentUser.permissions.isAllowToSeeMyPlotDashboard" -->
      </div>
      <!-- Director -->
      <!-- Engineer dashboard -->
      <div class="app-main" *ngIf="
          (currentUser?.data?.user?.userType?.code === 'ENG' ||
          currentUser?.data?.user?.userType?.code === 'ARC') &&
          currentUser.data.user?.agency?.code !== 'RHA'
        ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser?.data?.user?.lastName }}</span> {{ currentUser?.data?.user?.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                  currentUser.data.user.role.code !== 'APP' &&
                  currentUser.data.user.role.code !== 'ENG'
                ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !==
                "ADM" ? currentUser.data.user?.agency?.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="(currentUser?.data?.user?.userType?.code !== 'ARC' &&
         currentUser.data.user.role.code !== 'ENG') &&
        currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('PND')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.pending }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('SUB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">Submitted</span>
              <span class="dtail-num">{{ countingBoard.submitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RSMB')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reSubmitted }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('UNCRN')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.underCorrection }} </span>
            </div>
          </div>

          <div class="app-stats_card" (click)="goToApplication('CXL')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rejected" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.certified" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.certified }} </span>
            </div>
          </div>
        </div>
      </div>
      <!-- Engineer dashboard -->
      <!-- director from rha -->
      <div class="app-main" *ngIf="
          currentUser.data.user.role.code === 'DRCT' &&
          currentUser.data.user?.agency?.code === 'RHA'
        ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                  currentUser?.data?.user?.role?.code !== 'APP' &&
                  currentUser?.data?.user?.role?.code !== 'ADM'
                ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser?.data?.user?.role.code !== "APP" && currentUser?.data?.user?.role?.code
                !== "ADM" ? currentUser?.data?.user?.agency?.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="(currentUser.data.user.role.code !== 'ENG' &&
          currentUser?.data?.user?.userType?.code !== 'ARC'
        ) &&
        currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <!-- RHA director dashboard -->

          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>

          &nbsp;&nbsp;
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
                placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> Loading... </button>
            </div>
          </div>



          <!--  End of RHA director dashboard -->
        </div>
                <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
              placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <!-- <div class="app-stats_card" (click)="goToApplication('PND')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.draftApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.pending }} </span>
            </div>
          </div> -->
          <div class="app-stats_card" (click)="goToApplication('NORHA')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.rhaApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rha }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.returnedApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>

          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span>
            </div>
          </div> -->
          <div class="app-stats_card" (click)="goToApplication('NORVW')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReviewed" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.nonObjectionReviewed }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NOUNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionUnderReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.nonObjectionUnderReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RHAAPRVD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionApproved" | translate }} </span>
              <span class="dtail-num">{{ countingBoard.nonObjectionApproved }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RHARJCT')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionRejected" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.nonObjectionRejected }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RHABFCR')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionBackForCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.nonObjectionBackForCorrection }} </span>
            </div>
          </div>

        </div>
      </div>
      <!-- director from rha -->
      <!-- other staff from RHA -->
      <div class="app-main" *ngIf="
          currentUser.data.user.role.code === 'STF' &&
          currentUser.data.user?.agency?.code === 'RHA' &&
          currentUser.data?.user?.role?.code !== 'TMLD' &&
          currentUser.data?.user?.role?.code !== 'OFCMNG'
        ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                  currentUser.data.user.role.code !== 'APP' &&
                  currentUser.data.user.role.code !== 'ADM'
                ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser.data.user.role.code !== "APP" && currentUser.data.user.role.code !==
                "ADM" ? currentUser.data.user.agency.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="(currentUser.data.user.role.code !== 'ENG'
          && currentUser?.data?.user?.userType?.code !== 'ARC'
        ) &&
        currentUser.data.user.userType.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number" (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
          currentUser.data.user.role.code === 'ADM' ||
          currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>

          &nbsp;&nbsp;
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
                placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> Loading... </button>
            </div>
          </div>
        </div>
                  <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>
        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.returnedApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('RTNNO')">
          <!-- <div class="app-stats_card" (click)="goToApplication('NORVW')"> -->
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionReturned" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.rhaUnderReview }} </span> -->
              <span class="dtail-num">{{ countingBoard.rhaReturned }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('NOUNRV')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.nonObjectionUnderReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.nonObjectionUnderReview }} </span>
            </div>
          </div>
          <div class="app-stats_card" (click)="goToApplication('MYTAKS')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.taskTitle" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.reviewMyTask }} </span>
            </div>
          </div>
        </div>
      </div>
      <!-- other staff from RHA -->
      <!-- reviewer and staff not even team leader -->
      <div class="app-main" *ngIf="
         currentUser.data.user.userType.code !== 'ENG' &&
         currentUser?.data?.user?.userType?.code !== 'ARC' &&
         currentUser.data?.user?.agency?.code !== 'RHA' &&
         currentUser.data.user.userType.code !== 'LO' &&
         currentUser.data?.user?.role?.code !== 'DRCT' &&
         currentUser.data?.user?.role?.code !== 'DINSP' &&
         currentUser.data?.user?.role?.code !== 'SINSP' &&
         currentUser.data?.user?.role?.code !== 'TMLD' &&
         currentUser.data?.user?.role?.code !== 'OFCMNG'
       ">
        <div class="app-welnote">
          <div class="app-welnote_dtails">
            <span class="prim-nt">{{"dashboardComponent.welcomeMsg" | translate }}!</span>
            <h3>
              <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
            </h3>
            <h4>
              <span *ngIf="
                 currentUser.data?.user?.role?.code !== 'APP' &&
                 currentUser.data?.user.role?.code !== 'ADM'
               ">{{"dashboardComponent.agencyTxt" | translate }}{{ currentUser?.data?.user?.role?.code !== "APP" && currentUser?.data?.user?.role?.code
                !== "ADM" ? currentUser.data?.user?.agency?.name : "" }} </span>
            </h4>
          </div>
        </div>
        <div *ngIf="currentUser.data?.user?.role?.code !== 'ENG' &&
       currentUser.data?.user?.userType?.code !== 'LO'" class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
          <div class="form-input clear-m w-aut">
            <div class="form-input_search fsm" style="border-radius: 0.5rem;">
              <input class="flg" type="text" name="searchApplicationId" [(ngModel)]="searchData.searchApplicationId"
                placeholder="Search by application number " (keydown.enter)="searchTermEvent()" />
              <button type="button" class="btn" *ngIf="!loading" (click)="searchTermEvent()">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <g data-name="Layer 2">
                    <g data-name="search">
                      <rect width="24" height="24" opacity="0" />
                      <path
                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                    </g>
                  </g>
                </svg>
              </button>
              <button *ngIf="loading"> {{"dashboardComponent.loadingTxt" | translate }}... </button>
            </div>
          </div>
          <div class="btns" style="min-height: fit-content;" *ngIf="
         currentUser.data.user.role.code === 'ADM' ||
         currentUser.data.user?.agency?.code == 'RHA'">
            <a class="kbk-btn kbk-btn-main" id="create-btn" (click)="openGeneralSearchComponent(openToSearch, 'lg')">
              {{"dashboardComponent.findApplication" | translate }}</a>
          </div>


        &nbsp;&nbsp;
        <div class="form-input clear-m w-aut">
          <div class="form-input_search fsm" style="border-radius: 0.5rem;">
            <input class="flg" type="text" name="searchUpi" [(ngModel)]="searchData.searchUpi"
              placeholder="Search by upi" (keydown.enter)="searchUpiEvent(openSearchedUpi, 'lg')" />
            <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiEvent(openSearchedUpi, 'lg')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <g data-name="Layer 2">
                  <g data-name="search">
                    <rect width="24" height="24" opacity="0" />
                    <path
                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                  </g>
                </g>
              </svg>
            </button>
            <button *ngIf="loading"> Loading... </button>
          </div>
        </div>

        </div>


        <div class="app-cont spc-cmb"
          style="min-height: fit-content; padding: 1.5rem;">
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchAssociatedUpi" [(ngModel)]="searchData.searchAssociatedUpi"
                  placeholder="Search by associated upi" (keydown.enter)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInAssociatedEvent(openSearchedAssociatedUpi, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
              <div class="form-input clear-m w-aut">
              <div class="form-input_search fsm" style="border-radius: 0.5rem;">
                <input class="flg" type="text" name="searchUpiInLIS" [(ngModel)]="searchData.searchUpiInLIS"
                  placeholder="Search upi in LIS" (keydown.enter)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')" />
                <button type="button" class="btn" *ngIf="!loading" (click)="searchUpiInLISEvent(openSearchedUpiInLIS, 'lg')">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
                <button *ngIf="loading"> Loading... </button>
              </div>
              </div>
          </div>



        <div class="app-stats">
          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allApplication" | translate }}</span>
              <span class="dtail-num">{{ countingBoard.all }} </span>
            </div>
          </div>


          <div class="app-stats_card" (click)="goToApplication('CTFD')">
            <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="dtail">
              <span class="dtail-title">{{"dashboardComponent.allPermitted" | translate }}</span>
              <!-- <span class="dtail-num">{{ countingBoard.certified }} </span> -->
            </div>
          </div>
        </div>
      </div>
      <!-- reviewer and staff not even team leader -->




    </div>

    <app-country-level-dashboard *ngIf="currentUser?.data?.user?.agency?.code === 'RHA'"></app-country-level-dashboard>
  </div>
</div>

<ng-template #openToSearch role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">General Search</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="outputData.isGeneralSearch">
    <app-general-search (backToParent)="closeAndGeneralSearch($event)"></app-general-search>
  </div>
</ng-template>


<ng-template #openSearchedUpi role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"dashboardComponent.search.choose" | translate }}</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="searchData.isGeneralSearchUpi">
    <app-searched-upi  *ngIf="searchData.isGeneralSearchUpi" [inputData]="searchData.listOfUpi"></app-searched-upi>
  </div>
</ng-template>

<ng-template #openSearchedAssociatedUpi role="document" let-modal>
  <div class="modol-header">
    <!-- <h2 id="exampleModalLabel">Which one do you want to choose</h2> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="searchData.isGeneralSearchUpiAssociated">
    <app-search-associated-upi  *ngIf="searchData.isGeneralSearchUpiAssociated" [inputData]="searchData.listOfAssociatedUpi"></app-search-associated-upi>
  </div>
</ng-template>

<ng-template #openSearchedUpiInLIS role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">LIS Details</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="searchData.isGeneralSearchUpiInLIS">
    <app-upi-info  [info]="'2'" [upiData]="searchData.searchUpiInLIS"></app-upi-info>
  </div>
</ng-template>


