import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AgencyDistrictComponent } from 'src/app/system-setting/agency-district/agency-district.component';
import { AgencyComponent } from 'src/app/system-setting/agency/agency.component';
import { BuildingTypeComponent } from 'src/app/system-setting/building-type/building-type.component';
import { DocumentRequiredComponent } from 'src/app/system-setting/document-required/document-required.component';
import { PermitChecklistComponent } from 'src/app/system-setting/permit-checklist/permit-checklist.component';
import { PermitTypeComponent } from 'src/app/system-setting/permit-type/permit-type.component';
import { RoleComponent } from '../role/role.component';
import { SystemAdminLayoutComponent } from '../system-admin-layout/system-admin-layout.component';
import { UsersComponent } from '../users/users.component';
import { QuestionCatogoriesComponent } from 'src/app/system-setting/question-catogories/question-catogories.component';
import { PriceComponent } from 'src/app/system-setting/price/price.component';
import { ActivityComponent } from '../activity/activity.component';
import { PermissionComponent } from '../permission/permission.component';



const routes: Routes = [
    {
        path: "", component: SystemAdminLayoutComponent,
        children: [
            { path: 'user', component: UsersComponent },
            { path: 'role', component: RoleComponent },
            { path: 'build-type', component: BuildingTypeComponent },
            { path: 'required-document', component: DocumentRequiredComponent },
            { path: 'permit-type', component: PermitTypeComponent },
            { path: 'permit-checklist', component: PermitChecklistComponent },
            // { path: 'permit-questions', component: ApplicationPermitQuestionComponent},
            { path: 'question-category', component: QuestionCatogoriesComponent},
            { path: 'agency', component: AgencyComponent },
            { path: 'invoice-prices', component: PriceComponent },
            { path: 'agency-district/:id', component: AgencyDistrictComponent },
            { path: 'activity', component: ActivityComponent },
            { path: 'permission', component: PermissionComponent },
            
            { path: 'approval', loadChildren: () => import('../../approval-management/modules/approval.module').then(m => m.ApprovalModule) }
        ]
    }

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class UserMgtRoutingModule { }
