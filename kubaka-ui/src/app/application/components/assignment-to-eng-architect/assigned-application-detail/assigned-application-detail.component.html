<div class="modol-content">
    <div class="kbk-userprofile">
        <div class="kbk-userprofile-avtar">
            <img src="assets/imgs/profile1.svg" alt="profile picture" />
        </div>
        <div class="kbk-userprofile-info">
            <span class="names">{{ currentUser.data.user.firstName }} {{ currentUser.data.user.lastName }}</span>
            <span class="title">{{ currentUser.data.user.role.name }}</span>
        </div>
    </div>
    <div class="kbk-x sp-2">
        <div class="req-card">
            <h3>{{"assignApplicationDetail.parcelDetails" | translate }}</h3>
            <div class="form-out">
                <label>{{"assignApplicationDetail.plotNumberUPI" | translate }}</label>
                <span>{{inputData.projects.upi}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.projectName" | translate }}</label>
                <span>{{inputData?.projects?.projectName}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.plannedFor" | translate }}</label>
                <span>{{inputData?.projects.selectedUse}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.location" | translate }}</label>
                <div class="kbk-x sp-sm">
                    <span>{{inputData?.projects?.provinceName}}</span>
                    <span>{{inputData?.projects?.districtName}}</span>
                    <span>{{inputData?.projects?.sectorName}}</span>
                    <span>{{inputData?.projects?.cellName}}</span>
                    <span>{{inputData?.projects?.villageName}}</span>
                </div>
            </div>
        </div>
        <div class="req-card">
            <h3>{{"assignApplicationDetail.applicationDetails" | translate }}</h3>
            <div class="form-out">
                <label>{{"assignApplicationDetail.requestFrom" | translate }}</label>
                <span>{{inputData?.details?.firstName}} {{inputData?.details?.lastName}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.plotSize" | translate }}</label>
                <span>{{inputData?.projects?.plotSize}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.buildUpArea" | translate }}</label>
                <span>{{inputData?.projects?.buildUpArea}}</span>
            </div>
            <div class="form-out">
                <label>{{"assignApplicationDetail.applicationDate" | translate }}</label>
                <span>{{inputData?.created_at | date}}</span>
            </div>
        </div>
    </div>
    <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-error" (click)="submit('reject')"> {{"assignApplicationDetail.rejectRequest" | translate }} </button>
        <button type="button" class="kbk-btn kbk-btn-success" (click)="submit('approve')"> Accept Request </button>
    </div>
</div>