import { ApiTags } from '@nestjs/swagger';
import { IntegrationsService } from './integrations.service';
import {
  // Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  // Headers,
} from '@nestjs/common';

@Controller('integration')
export class IntegrationsController {
  constructor(private readonly integrationsService: IntegrationsService) {}

  // Old BPMIS integration
  @ApiTags('OldBPMIS')
  // @Get('old-bpmis/:upi')
  // searchInOldBPMIS(@Param('upi') upi: string) {
  //   return this.integrationsService.searchInOldBPMIS(upi);
  // }
  @Get('old-bpmis')
  searchInOldBPMIS(@Query('upi') upi: string) {
    return this.integrationsService.searchInOldBPMIS(upi);
  }

  // Land / LIS integration
  @ApiTags('LAND')
  @Get('land/:upi')
  searchUPIOnLIS(@Param('upi') upi: string) {
    return this.integrationsService.searchUPIOnLIS(upi);
  }

  // Regulations / integration
  @ApiTags('LAND')
  @Get('zone/:zone')
  searchZone(@Param('zone') zone: string) {
    return this.integrationsService.searchUPIOnLIS(zone);
  }

  // Arch / integration
  @ApiTags('architecture')
  @Get('engineer')
  getAllArch() {
    return this.integrationsService.getAllArch();
  }

  @ApiTags('architecture')
  @Post('engineer/search')
  searchArchitectByRegIndex(
    @Query('registrationindex') registrationindex: string,
  ) {
    return this.integrationsService.searchArchitectByRegIndex(
      registrationindex,
    );
  }

  // Engineer / integration with condition on NIDA
  @ApiTags('architecture')
  @Post('architect/search')
  searchArchitectByRegIndexWithCondition(
    @Query('registrationindex') registrationindex: string,
  ) {
    return this.integrationsService.searchArchitectByRegIndexWithCondition(
      registrationindex,
    );
  }

  // Engineer / LIS integration
  // @ApiTags('engineer')
  // @Get('engineer/:registrationNumber')
  // searchEngineer(@Param('registrationNumber') registrationNumber: string) {
  //   return this.integrationsService.searchEngineer(registrationNumber);
  // }
  @ApiTags('engineer')
  @Get('engineer/:registrationNumber')
  searchEngineer(@Query('registrationNumber') registrationNumber: string) {
    return this.integrationsService.searchEngineer(registrationNumber);
  }

  @ApiTags('engineer')
  @Get('engineerIrembo/')
  engineerIrembo(@Query('registrationNumber') registrationNumber: string) {
    return this.integrationsService.searchEngineer(registrationNumber);
  }

  @ApiTags('engineer')
  @Get('engineer/conditions/:registrationNumber')
  searchEngineerWithCondition(
    @Query('registrationNumber') registrationNumber: string,
  ) {
    return this.integrationsService.searchEngineerWithConditions(
      registrationNumber,
    );
  }

  @ApiTags('NIDA')
  @Post('nida/')
  async checkCiInfo(@Query('documentNumber') documentNumber: string) {
    try {
      const response =
        await this.integrationsService.getNIDAInfo(documentNumber);
      return response;
    } catch (error) {
      throw new Error('Failed to check citizen information.');
    }
  }

  @ApiTags('RRA')
  @Get('rra/')
  async checkRRAInfo(@Query('upi') upi: string) {
    try {
      const response = await this.integrationsService.getRRAInfo(upi);
      return response;
    } catch (error) {
      throw new Error('Failed to check taxes information.');
    }
  }
  @ApiTags('EIA')
  @Post('eia/')
  async checkEIAInfo(@Query('upi') upi: string) {
    try {
      const response = await this.integrationsService.searchEIAInfo(upi);
      return response;
    } catch (error) {
      throw new Error('Failed to check RDB information.');
    }
  }

  // Phone & NID / LIS integration
  @ApiTags('nida/phoneNumber')
  @Get('nida/:phoneNumber')
  checkPhoneNumberWithNIDA(@Param('phoneNumber') phoneNumber: string) {
    return this.integrationsService.searchPhone(phoneNumber);
  }
}
