import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-new-application',
  templateUrl: './new-application.component.html',
  styleUrls: ['./new-application.component.scss']
})
export class NewApplicationComponent {
  submitted!: boolean;
  projectDetails: any = {};
  @Output() backToParent = new EventEmitter();
  userForm!: UntypedFormGroup;
  outputData: any;

  currentUser: any = {};
  upiInfo: any = {};
  paramsId: any = {}



  constructor(

    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private sessionService: SessionService,
    private utilService: UtilService,
    private route: ActivatedRoute,
    private cdr: ChangeDetectorRef,
    private appConfig: AppConfig) {

    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.refresh();
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_NEW_INFO) as any);


    this.route.params.subscribe((params: any) => {
      this.paramsId = params.id;
    })

  }


  ngOnInit(): void {
    if (this.paramsId === "0" && this.upiInfo) {
      this.initialize(this.upiInfo);
      // Calling agency by district code from what you have selected to apply
      this.getAgencyByDistrictCode(this.upiInfo.parcelLocation.district.districtCode, '0');
      // this.initializeDataFromLandIntergration();
    } else {
      this.checkIfProjectExits(this.paramsId);
    }
  }

  getAgencyByDistrictCode(districtCode: any, type: any) {
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + districtCode)
      .subscribe(
        data => {

          this.userForm.controls['agencyId'].setValue(data.id);
          if (type === '1') {
            this.confirmToSave();
          }
        },
        error => { }
      )
  }


  checkIfProjectExits(paramsId: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/' + paramsId)
      .subscribe(
        data => {
          this.projectDetails = data;
          this.initialize(this.projectDetails);
        }
      )

  }







  cancel() {
    this.router.navigate(['/account/application/applications'])
  }


  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / this.upiInfo.size) * 100).toFixed(2));
  }



  getProjectStatusId(event: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus/code/search?search=' + event)
      .subscribe(
        data => {
          this.userForm.controls['projectStatusId'].setValue(data.items[0].id);
        }
      )
  }


  initialize(event: any) {

    if (this.paramsId === "0") {
      if(this.upiInfo.representative){
        this.userForm = this.formBuilder.group({
        // id: [""],
        upi: [this.upiInfo.upi],
        // ownerFullName: [this.upiInfo.owners[0].fullName],
        // ownerIdNo: [this.upiInfo.owners[0].idNo],


        ownerFullName: [this.upiInfo.representative.surname + '  ' + this.upiInfo.representative.foreNames],
        ownerIdNo: [this.upiInfo.representative.idNo],
        isRRAVerified: [false],
        // isRRAVerified: [this.upiInfo.isRRAVerified],
        isUnderMortgage: [this.upiInfo.isUnderMortgage],
        isUnderRestriction: [this.upiInfo.isUnderRestriction],
        centralCoordinateX: [this.upiInfo.centralCoordinate.x ? this.upiInfo.centralCoordinate.x : "none"],
        centralCoordinateY: [this.upiInfo.centralCoordinate.y ? this.upiInfo.centralCoordinate.y : "none"],
        villageName: [this.upiInfo.parcelLocation.village.villageName],
        cellCode: [this.upiInfo.parcelLocation.cell.cellCode],
        sectorCode: [this.upiInfo.parcelLocation.sector.sectorCode],
        districtCode: [this.upiInfo.parcelLocation.district.districtCode],
        provinceCode: [this.upiInfo.parcelLocation.province.provinceCode],
        cellName: [this.upiInfo.parcelLocation.cell.cellName],
        sectorName: [this.upiInfo.parcelLocation.sector.sectorName],
        districtName: [this.upiInfo.parcelLocation.district.districtName],
        provinceName: [this.upiInfo.parcelLocation.province.provinceName],
        selectedUse: [this.upiInfo.applicationName],
        selectedCategoryUse: [this.upiInfo.seletedCategoryUse],
        projectName: ["", [Validators.required]],
        projectDescription: ["", [Validators.required]],
        plotSize: [this.upiInfo.size, [Validators.required]],
        originalPlotSize: [this.upiInfo.size],
        userId: [this.currentUser.userId],

        agencyId: [""],
        projectStatusId: [""],
        isFromOldSystem: [false],
      });

      }
      else {
        let ownersarraylength = this.upiInfo.owners.length;
        let FullName = '';
        let IdNo = '';
        for (let i = 0; i < ownersarraylength; i++) {
          if(this.upiInfo.owners[i].idNo){
            FullName = this.upiInfo.owners[i].fullName;
            IdNo = this.upiInfo.owners[i].idNo;
            break;
          }

        }

        this.userForm = this.formBuilder.group({
        // id: [""],
        upi: [this.upiInfo.upi],
        ownerFullName: [FullName],
        ownerIdNo: [IdNo],


        // ownerFullName: [this.upiInfo.representative.surname + '  ' + this.upiInfo.representative.foreNames],
        // ownerIdNo: [this.upiInfo.representative.idNo],
        isRRAVerified: [false],
        // isRRAVerified: [this.upiInfo.isRRAVerified],
        isUnderMortgage: [this.upiInfo.isUnderMortgage],
        isUnderRestriction: [this.upiInfo.isUnderRestriction],
        centralCoordinateX: [this.upiInfo.centralCoordinate.x ? this.upiInfo.centralCoordinate.x : "none"],
        centralCoordinateY: [this.upiInfo.centralCoordinate.y ? this.upiInfo.centralCoordinate.y : "none"],
        villageName: [this.upiInfo.parcelLocation.village.villageName],
        cellCode: [this.upiInfo.parcelLocation.cell.cellCode],
        sectorCode: [this.upiInfo.parcelLocation.sector.sectorCode],
        districtCode: [this.upiInfo.parcelLocation.district.districtCode],
        provinceCode: [this.upiInfo.parcelLocation.province.provinceCode],
        cellName: [this.upiInfo.parcelLocation.cell.cellName],
        sectorName: [this.upiInfo.parcelLocation.sector.sectorName],
        districtName: [this.upiInfo.parcelLocation.district.districtName],
        provinceName: [this.upiInfo.parcelLocation.province.provinceName],
        selectedUse: [this.upiInfo.applicationName],
        selectedCategoryUse: [this.upiInfo.seletedCategoryUse],
        projectName: ["", [Validators.required]],
        projectDescription: ["", [Validators.required]],
        plotSize: [this.upiInfo.size, [Validators.required]],
        originalPlotSize: [this.upiInfo.size],
        userId: [this.currentUser.userId],

        agencyId: [""],
        projectStatusId: [""],
        isFromOldSystem: [false],
      });

      }

      this.getProjectStatusId('PCRTD');
    } else {
      this.userForm = this.formBuilder.group({
        id: [this.projectDetails.id],
        upi: [this.projectDetails.upi],
        ownerFullName: [this.projectDetails.ownerFullName],
        ownerIdNo: [this.projectDetails.ownerIdNo],
        isRRAVerified: [false],
        // isRRAVerified: [this.projectDetails.isRRAVerified],
        isUnderMortgage: [this.projectDetails.isUnderMortgage],
        isUnderRestriction: [this.projectDetails.isUnderRestriction],
        centralCoordinateX: [this.projectDetails.centralCoordinateX],
        centralCoordinateY: [this.projectDetails.centralCoordinateY],
        villageName: [""],
        cellCode: [this.projectDetails.cellCode],
        sectorCode: [this.projectDetails.sectorCode],
        // districtCode: [this.upiInfo?.parcelLocation?.district?.districtCode],

        districtCode: [this.projectDetails.districtCode],
        provinceCode: [this.projectDetails.provinceCode],
        cellName: [this.projectDetails.cellName],
        sectorName: [this.projectDetails.sectorName],
        // districtName: [this.projectDetails.districtName],
        districtName: [this.upiInfo?.parcelLocation?.district?.districtName],
        provinceName: [this.projectDetails.provinceName],
        selectedUse: [this.projectDetails.selectedUse],
        selectedCategoryUse: [this.projectDetails.selectedCategoryUse],
        projectName: [this.projectDetails.projectName, [Validators.required]],
        projectDescription: [this.projectDetails.projectDescription, [Validators.required]],
        plotSize: [this.projectDetails.plotSize, [Validators.required]],
        userId: [this.currentUser.userId],
        agencyId: [this.projectDetails.agencyId],
        originalPlotSize: [this.projectDetails.originalPlotSize],
        projectStatusId: [""],
        isFromOldSystem: [this.projectDetails.isFromOldSystem]
      }
      );
    }
  }


  refresh() {
    this.userForm = this.formBuilder.group({
      // id: [""],
      upi: [""],
      ownerFullName: [""],
      ownerIdNo: [""],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      centralCoordinateX: [""],
      centralCoordinateY: [""],
      villageName: [""],
      cellCode: [""],
      sectorCode: [""],
      districtCode: [""],
      provinceCode: [""],
      cellName: [""],
      sectorName: [""],
      districtName: [""],
      provinceName: [""],
      selectedUse: [""],
      selectedCategoryUse: [""],
      projectName: ["", [Validators.required]],
      projectDescription: ["", [Validators.required]],
      plotSize: ["", [Validators.required]],
      userId: [this.currentUser.userId],
      agencyId: [""],
      projectStatusId: [""],
      isFromOldSystem: [false],
      originalPlotSize: ['']
    })
  }



  next() {
    if (this.projectDetails.id) {
      this.router.navigate(['/account/application/new-application-development-detail', this.projectDetails.id]);
    } else {
      this.router.navigate(['/account/application/new-application-development-detail', 0]);

    }
  }

  // getAgencyByCode(agencyCode: any) {
  //   this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=' + agencyCode)
  //     .subscribe(
  //       data => {
  //         this.userForm.controls['agencyId'].setValue(data.items[0].id);
  //       },
  //       error => { }
  //     )
  // }













  // getProjectStatus() {
  //   this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus/code/search?search=' + VALUE.PROJECT_STATUS_CREATED_CODE)
  //     .subscribe(
  //       data => { this.userForm.controls['projectStatusId'].setValue(data.items[0].id); },
  //       error => { }
  //     )
  // }


  onSubmit() {
    if (this.userForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill all required fields', "bottom", "center")
      return;
    } else {
      this.userForm.controls['originalPlotSize'].setValue(+this.userForm.value.plotSize);
      this.userForm.controls['isFromOldSystem'].setValue(this.upiInfo?.isFromOldSystem);
      this.getAgencyByDistrictCode(this.userForm.value.districtCode, '1');
    }
  }


  confirmToSave() {
    this.submitted = true;

    if (this.paramsId !== '0') {

      this.applicationService.patchAssetWithPath(this.userForm.value.id, this.userForm.value, environment.applicationUrl + 'application/project')
        .subscribe(
          data => {
            this.router.navigate(['/account/application/new-application-development-detail/', this.paramsId]);
            this.submitted = false;

          })
    } else {
      this.applicationService.saveWithPath(this.userForm.value, environment.applicationUrl + 'application/project')
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your project saved successfully", "bottom", "center");
            this.router.navigate(['/account/application/new-application-development-detail/', data.id]);
            this.submitted = false;
          }, error => {
            this.submitted = false;
            if (error?.error === 'UPI already exists') {
              this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.userForm.value.upi)
                .subscribe(
                  data => {
                    this.router.navigate(['/account/application/new-application-development-detail/', data.items[0].id]);
                  }
                )
            }
          }
        )
    }

  }



  onPrev() {
    // this.router.navigate(['/account/application'])
    this.router.navigate(['/account/application/new-application/', this.paramsId]);
  }


}
