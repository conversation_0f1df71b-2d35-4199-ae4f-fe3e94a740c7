header {
  background: var(--kbk-tu-surface-mian);
  position: relative;
  //   height: 70vh;
  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: url("./../../../../assets/ikons/logo/bg.svg") no-repeat;
    background-size: 50%;
    background-position: right center;
    z-index: 0;
    mix-blend-mode: multiply;
    opacity: 0.25;
    pointer-events: none;
  }

  .gen-main,
  .gen-hero {
    position: relative;
    z-index: 1;
  }
  .lang-links {
    padding: 0.5rem 0;
    border-bottom: 3px solid #ffcb2d4a;
    a {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 1.75rem;
      height: 1.375rem;
      overflow: hidden;
      border-radius: 0.375rem;
      cursor: pointer;
      margin-inline: 0.125rem;
      border: 2px solid transparent;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      &.active {
        border-color: #0099e8;
      }
    }
  }
  .prim-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .logo {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 10rem;
      height: 3rem;
      aspect-ratio: 1;
      img {
        width: auto;
        height: 2rem;
        object-fit: contain;
      }
    }
    .main-nav {
      width: fit-content;
      a {
        color: #b6e5ff;
      }
      &-list {
        display: flex;
        justify-content: center;
        align-items: center;
        .kbk-btn-close{
          display: none;
        }
      }
    }
    .mob-menu {
      display: none;
    }
    .kbk-btn-outline {
      background-color: transparent;
      border-color: #2cbcff;
      color: #b6e5ff;
      &:hover {
        background-color: #0099e8;
        border-color: #0099e8;
        color: #b6e5ff;
      }
    }
    .kbk-btn-main {
      background-color: #def1ff;
      border-color: #def1ff;
      color: #0099e8;
      &:hover {
        background-color: #eff9ff;
        border-color: #eff9ff;
        color: #0099e8;
      }
    }
  }
  .gen-hero {
    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 4rem 0 12rem;
      color: #fff;
      h1 {
        // font-size: 1.875rem;
        font-size: clamp(1.75rem, 100vw, 3rem);
        margin-bottom: 0.25rem;
      }
      p {
        font-size: 1.125rem;
        margin-bottom: 2rem;
        opacity: 0.5;
      }
      input {
        width: 25rem;
      }
      form {
        margin-bottom: 2rem;
      }
      a {
        color: inherit;
        text-decoration: underline;
        opacity: 0.75;
        &:hover {
          // color: #0099e8;
          opacity: 1;
        }
      }
    }
  }
}
.section-serv {
  margin-top: -7rem;
  margin-bottom: 4rem;
  .gen-serve {
    // position: absolute;
    // width: 100%;
    // top: calc(100% - 5rem);
    // left: 50%;
    // transform: translateX(-50%);
    z-index: 3;
    .page-lead {
      //   background: var(--kbk-tu-surface-mian);
      background: #def1ff;
      border-radius: 24px;
      overflow: hidden;
      position: relative;
      .content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 3rem 4rem;
        // background: hsla(216, 100%, 74%, 0.4);
        h1 {
          font-size: 1.875rem;
          margin-bottom: 0.125rem;
        }
        p {
          font-size: 1.125rem;
          margin-bottom: 2rem;
          opacity: 0.5;
        }
      }
    }
  }
}
.section-faq {
  background-color: #f8fafc;
  .content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 3rem 4rem;
    // background: hsla(216, 100%, 74%, 0.4);
    .content-body {
      width: 100%;
    }
    h1 {
      font-size: 1.875rem;
      margin-bottom: 0.125rem;
    }
    p {
      font-size: 1.125rem;
      margin-bottom: 2rem;
      opacity: 0.5;
    }
  }
}
// .lng-nav {
//   height: 2rem;

//   .flex {
//     justify-content: flex-end;
//   }
//   a {
//     display: block;
//     width: 1rem;
//     height: 1rem;
//     overflow: hidden;
//     border: 2px solid #fff;
//     img {
//       width: 100%;
//       height: 100%;
//       object-fit: contain;
//     }
//   }
// }

.wig-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  .wid-item {
    flex: 1 0 12rem;
    width: fit-content;
    .wid-card {
      padding: 1.25rem 2rem;
      display: flex;
      gap: 1rem;
      background-color: #fff;
      border-radius: 0.5rem;
      border: 1px solid transparent;
      .wid-icon {
        width: 4rem;
        height: 4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .wid-dtails {
        span {
          display: inline-block;
          color: #000;
          font-size: 0.875rem;
          line-height: 1.2;
          font-weight: 500;
        }
      }
      &:hover {
        border: 1px solid #75d2ff;
        box-shadow: 0 0 0.5em 0.25em rgba(0, 37, 99, 0.025);
      }
    }
  }
}

footer {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  .footer-link {
    font-size: 0.875rem;
    color: #94a3b8;
    // &:hover{
    //   color: #0099e8;
    // }
  }
  .footer-link2 {
    font-size: 0.575rem;
    color: #dfe6f0;

  }
}

body {
  --colo: #f7893b;
  --colo: #48ae64;
  --colo: #f35e61;
}

@media only screen and (max-device-width: 512px) {
  header {
    .gen-main {
      z-index: 2;
    }
    .prim-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .logo {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 10rem;
        height: 3rem;
        aspect-ratio: 1;
        img {
          width: auto;
          height: 2rem;
          object-fit: contain;
        }
      }
      .main-nav {
        display: none;
        z-index: 2040;
        box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.2);
        &-list{
          transition: transform 300ms ease-in-out ;
        }
      }
      .mob-menu {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: transparent;
        border: 1px solid #2cbcff;
        border-radius: 0.25rem;
        width: 2.125rem;
        height: 2.125rem;
        &:hover {
          background-color: #0099e8;
          border-color: #0099e8;
          color: #b6e5ff;
        }
      }
      .kbk-btn-outline {
        background-color: transparent;
        border-color: #2cbcff;
        color: #b6e5ff;
        &:hover {
          background-color: #0099e8;
          border-color: #0099e8;
          color: #b6e5ff;
        }
      }
      .kbk-btn-main {
        background-color: #def1ff;
        border-color: #def1ff;
        color: #0099e8;
        &:hover {
          background-color: #eff9ff;
          border-color: #eff9ff;
          color: #0099e8;
        }
      }
      &:has(input[type="checkbox"]:checked) {
        .main-nav {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
          position: fixed;
          top: 0px;
          right: 0;
          width: 100vw;
          height: 100vh;
          background-color: rgba(0, 37, 99, 0.5);
          &-list {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;
            gap: 2rem;
            background: var(--kbk-tu-surface-mian);
            padding: 1rem;
            width: 240px;
            position: relative;
            top: 0px;
            left: calc(100% - 120px);
            transform: translateX(-50%);
            height: 100vh;
            padding-top: 4rem;
            .kbk-btn-close {
              position: absolute;
              top: 1.25rem;
              right: 1.25rem;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: transparent;
              border: 1px solid #2cbcff;
              border-radius: 0.25rem;
              width: 2.125rem;
              height: 2.125rem;
            }
          }
          .kbk-x {
            flex-direction: column;
          }
          .kbk-x-e {
            flex-direction: column;
            align-items: flex-start;
            padding-left: 1rem;
            button{
              min-width: 8rem;
              max-width: fit-content;
            }
          }
        }
      }
    }
    .gen-hero {
      .content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem 0 8rem;
        color: #fff;
        h1 {
          font-size: 1.875rem;
          margin-bottom: 0.25rem;
        }
        p {
          width: 75%;
        }
        input {
          width: 20rem;
        }
        form {
          margin-bottom: 2rem;
          .kbk-x-c {
            flex-direction: column;
          }
        }
      }
    }
  }
  .section-serv {
    margin-top: -6rem;
    margin-bottom: 3rem;
    .gen-serve {
      z-index: 3;
      .page-lead {
        .content {
          padding: 2rem 2.5rem;
          align-items: flex-start;
          h1 {
            font-size: 1.75rem;
            margin-bottom: 0.125rem;
            text-align: left;
          }
          p {
            font-size: 1rem;
            margin-bottom: 2rem;
            opacity: 0.5;
          }
        }
      }
    }
  }
  .section-faq {
    background-color: #f8fafc;
    .content {
      padding: 2rem 2rem;
      .content-body {
        width: 100%;
      }
      h1 {
        font-size: 1.75rem;
        margin-bottom: 0.125rem;
      }
      p {
        font-size: 1rem;
        margin-bottom: 2rem;
        opacity: 0.5;
      }
    }
  }
  .wig-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    .wid-item {
      flex: 1 0 8rem;
      width: fit-content;
      .wid-card {
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        gap: 0.375rem;
      }
      &.fitWidth{
        flex: 1 0 auto;
      }
    }

  }
}
@media only screen and (max-device-width: 768px) {
}
@media only screen and (max-device-width: 1024px) {
}
@media only screen and (max-device-width: 1200px) {
}
