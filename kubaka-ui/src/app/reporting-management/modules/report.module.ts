import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { ApplicationChartsComponent } from '../components/application-charts/application-charts.component';
import { ApplicationReportComponent } from '../components/application-report/application-report.component';
import { GridChartsSampleComponent } from '../components/grid-charts-sample/grid-charts-sample.component';
import { ReportLayoutComponent } from '../components/report-layout/report-layout.component';
import { UserReportComponent } from '../components/user-report/user-report.component';
import { AllDataByReviewerReportComponent } from '../components/all-data-by-reviewer-report/all-data-by-reviewer-report.component';
import { ReviewerWeeklyReportComponent } from '../components/reviewer-weekly-report/reviewer-weekly-report.component';
import { ChatBoardReportComponent } from '../components/chat-board-report/chat-board-report.component';
import { NonObjectionReportComponent } from '../components/non-objection-report/non-objection-report.component';
import { ApplicationApprovalReportComponent } from '../components/application-approval-report/application-approval-report.component';


export const reportRoutes: Routes = [
  {
    path: '', component: ReportLayoutComponent,
    children: [
      { path: 'user-report', component: UserReportComponent },
      { path: 'application-report', component: ApplicationReportComponent },
      { path: 'application-charts', component: ApplicationChartsComponent },
      { path: 'grid-chart-sample', component: GridChartsSampleComponent },



      { path: 'all-data-by-reviewer-report', component: AllDataByReviewerReportComponent },
      { path: 'reviewer-weekly-report', component: ReviewerWeeklyReportComponent },
      { path: 'chat-board-report', component: ChatBoardReportComponent },
      { path: 'non-objection-report', component: NonObjectionReportComponent },

      { path: 'application-approval-report', component: ApplicationApprovalReportComponent },

      { path: 'dashboard', loadChildren: () => import('../../dashaboard-management/module/dasbaord.module').then(m => m.DasbaordModule) },

    ]
  }
]


@NgModule({
  declarations: [
    ReportLayoutComponent,
    AllDataByReviewerReportComponent,
    ReviewerWeeklyReportComponent,
    ChatBoardReportComponent,
    NonObjectionReportComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(reportRoutes),
    SharedModule
  ]
})
export class ReportModule { }
