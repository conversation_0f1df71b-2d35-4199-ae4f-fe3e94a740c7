import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { ApplicationDocument } from '../../entities/application-documents.entity';

@Injectable()
export class ApplicationDocumentRepository extends AbstractRepository<ApplicationDocument> {
  constructor(
    @InjectRepository(ApplicationDocument)
    applicationDocumentManagementRepository: Repository<ApplicationDocument>,
    entityManager: EntityManager,
  ) {
    super(entityManager, applicationDocumentManagementRepository);
  }
}
