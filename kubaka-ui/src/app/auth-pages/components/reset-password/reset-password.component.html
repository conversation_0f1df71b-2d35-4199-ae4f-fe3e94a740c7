<div class="login-container">
  <div class="login-content">
    <div class="illust">
      <img class="logo" [routerLink]="['/']" src="assets/ikons/logo/NavLogo.svg" alt="">
      <img class="ill" src="assets/imgs/SVG/illustration.svg" alt="">
    </div>
    <div class="dtials">
      <form class="form" [formGroup]="saveForm" (ngSubmit)="onSubmit()">
        <div class="form-header">
          <h2 class="display-md">Reset password</h2>
        </div>
        <div class="form-body">
          <div class="form-input">
            <label>Email</label>
            <div>
              <input class="flg" type="email" id="email" name="email" formControlName="email" required />
            </div>
          </div>
          <div class="form-input">
            <label>Token</label>
            <div>
              <input class="flg" type="text" id="newPasswordToken" name="newPasswordToken"
                formControlName="newPasswordToken" required />
            </div>
          </div>
          <!-- <div class="form-input">
            <label>Current password</label>
            <div>
              <input class="flg" type="currentPassword" id="currentPassword" name="currentPassword" formControlName="currentPassword"
                placeholder="**********" required />
            </div>
          </div> -->
          <div class="form-input">
            <label>New password</label>
            <div>
              <input class="flg" type="password" id="password" name="password" formControlName="password"
                placeholder="**********" required />
              <div *ngIf="
              saveForm.get('password')?.invalid &&
              saveForm.get('password')?.touched
            ">
                <small *ngIf="saveForm.get('password')?.hasError('required')" class="text-danger">Password is
                  required</small>
                <small *ngIf="saveForm.get('password')?.hasError('minlength')" class="text-danger">Password must be at
                  least 8 characters</small>
                <small *ngIf="saveForm.get('password')?.hasError('pattern')" class="text-danger"> Password must contain
                  at least one lowercase letter, one uppercase letter, and one digit </small>
              </div>
              <div *ngIf="saveForm.controls['password'].hasError('pattern')">
                <span class="text-danger">Password Must contain below conditions.</span>
                <ul>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'uppercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'uppercase'
                  )
                }"> Min 1 Uppercase Letter. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'lowercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'lowercase'
                  )
                }"> Min 1 Lowercase Letter. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'special-character'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'special-character'
                  )
                }"> Min 1 Special Character. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'number'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'number'
                  )
                }"> Min 1 Number. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'length'
                  )
                }"> Min 8 Characters. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['password'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['password'],
                    'length'
                  )
                }"> Max 16 Characters. </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="form-input">
            <label>Confirm Password</label>
            <div>
              <input class="flg" type="password" name="confirmPassword" formControlName="confirmPassword"
                placeholder="**********" required />
              <div *ngIf="
              saveForm.get('confirmPassword')?.invalid &&
              saveForm.get('confirmPassword')?.touched
            ">
                <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('required')
              " class="text-danger">Password is required</small>
                <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('minlength')
              " class="text-danger">Password must be at least 8 characters</small>
                <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('pattern')
              " class="text-danger"> Password must contain at least one lowercase letter, one uppercase letter, and one
                  digit </small>
              </div>
              <div *ngIf="
              saveForm.controls['confirmPassword'].hasError('pattern')
            ">
                <span class="text-danger">Password Must contain below conditions.</span>
                <ul>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'uppercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'uppercase'
                  )
                }"> Min 1 Uppercase Letter. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'lowercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'lowercase'
                  )
                }"> Min 1 Lowercase Letter. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'special-character'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'special-character'
                  )
                }"> Min 1 Special Character. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'number'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'number'
                  )
                }"> Min 1 Number. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  )
                }"> Min 8 Characters. </li>
                  <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  )
                }"> Max 16 Characters. </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-1" *ngIf="saveForm.hasError('notMatched')">
          <mat-error *ngIf="saveForm.hasError('notMatched')">
            <span class="danger">Passwords are not matched.</span>
          </mat-error>
        </div>
        <div class="form-footer">
          <button class="kbk-btn kbk-btn-lg kbk-btn-main" [disabled]="saveForm.invalid" type="submit">Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>