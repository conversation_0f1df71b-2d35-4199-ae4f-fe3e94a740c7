import { Component, <PERSON>ement<PERSON><PERSON>, Query<PERSON>ist, ViewChildren } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AppConfig } from 'src/app/app.config';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { TokenService } from '../../services/token.service';

@Component({
  selector: 'app-factor-login',
  templateUrl: './factor-login.component.html',
  styleUrls: ['./factor-login.component.scss']
})
export class FactorLoginComponent {

  // pinDigits: string[] = ['', '', '', '', '', ''];
  // email: any;
  // constructor(
  //   private router: Router,
  //   private authService: AuthService,
  //   private tokenService: TokenService,
  //   private utilService: UtilService,
  //   private appConfig: AppConfig,
  //   private route: ActivatedRoute
  // ) {
  //   this.route.queryParams.subscribe((params: any) => {
  //     this.utilService.decryptString(params['email'], '123456')
  //       .then((encryptedString) => {
  //         this.email = encryptedString
  //       });
  //   })
  // }

  // onInput(event: Event, index: number) {
  //   const input = event.target as HTMLInputElement;
  //   // Ensure only one character is entered
  //   if (input.value.length > 1) {
  //     input.value = input.value[0]; // Trim to the first character
  //   }

  //   this.pinDigits[index] = input.value; // Update the current value in the pinDigits array

  //   // Move to the next input field if this is not the last input
  //   if (input.value && index < this.pinDigits.length - 1) {
  //     const nextInput = document.querySelector(`#input${index + 1}`) as HTMLInputElement;
  //     nextInput?.focus();
  //   }

  //   // Auto-submit when all fields are complete
  //   if (this.isComplete()) {
  //     this.submitAction();
  //   }
  // }


  // isComplete(): boolean {
  //   return this.pinDigits.every((digit) => digit !== '');
  // }

  // submitted: any;
  // submitAction() {
  //   if (this.isComplete()) {
  //     // Perform your submit logic here
  //     this.authService.saveAssetWithPath({
  //       "email": this.email,
  //       "otp": this.pinDigits.join('')
  //     }, environment.authUrl + 'auth/verify-otp')
  //       .subscribe(data => {
  //         this.submitted = false;
  //         this.tokenService.setToken(data);
  //         this.router.navigate(['/account/dashboard']);
  //       }, (error) => {
  //         this.submitted = false;
  //         this.utilService.showNotification(NOTIFICATION_COLOR.error, error, "bottom", "center");
  //       });
  //   }
  // }

  // cancel() {
  //   this.pinDigits = ['', '', '', '', '', ''];
  // }


  pinDigits: string[] = ['', '', '', '', '', '']; // Store the OTP digits
  email: any;
  submitted: boolean = false;

  @ViewChildren('pinInput') pinInputs!: QueryList<ElementRef>; // Get all input fields

  constructor(
    private router: Router,
    private authService: AuthService,
    private tokenService: TokenService,
    private utilService: UtilService,
    private appConfig: AppConfig,
    private route: ActivatedRoute
  ) {
    this.route.queryParams.subscribe((params: any) => {
      this.utilService.decryptString(params['email'], '123456').then((decryptedEmail) => {
        this.email = decryptedEmail;
      });
    });
  }

  ngAfterViewInit() {
    // Attach paste event listener to all inputs
    this.pinInputs.forEach((inputEl, index) => {
      inputEl.nativeElement.addEventListener('paste', (event: ClipboardEvent) => this.onPaste(event, index));
    });
  }

  // Handle typing input
  onInput(event: Event, index: number) {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, ''); // Remove non-numeric characters

    if (value.length > 1) {
      this.pasteOTP(value); // If user pastes multiple digits
      return;
    }

    this.pinDigits[index] = value; // Store the single-digit value

    // Move focus to the next input field if value is entered
    if (value && index < this.pinDigits.length - 1) {
      this.pinInputs.toArray()[index + 1].nativeElement.focus();
    }

    // Auto-submit if all fields are filled
    if (this.isComplete()) {
      this.submitAction();
    }
  }

  // Handle backspace navigation
  onKeyDown(event: KeyboardEvent, index: number) {
    if (event.key === 'Backspace') {
      if (!this.pinDigits[index] && index > 0) {
        // Move to the previous input field if empty
        this.pinInputs.toArray()[index - 1].nativeElement.focus();
      }
    }
  }

  // Handle paste event
  onPaste(event: ClipboardEvent, index: number) {
    event.preventDefault(); // Prevent default paste behavior

    const clipboardData = event.clipboardData?.getData('text') || ''; // Get pasted text
    const digits = clipboardData.replace(/\D/g, '').slice(0, 6).split(''); // Extract first 6 digits

    if (digits.length > 0) {
      this.pasteOTP(digits.join(''));
    }
  }

  // Fill OTP fields when pasting
  pasteOTP(pastedValue: string) {
    let digits = pastedValue.slice(0, 6).split(''); // Get first 6 characters

    for (let i = 0; i < digits.length; i++) {
      this.pinDigits[i] = digits[i];
    }

    // Update input fields with pasted values
    this.pinInputs.forEach((input, i) => {
      input.nativeElement.value = this.pinDigits[i] || '';
    });

    // Move focus to the last field if all fields are filled
    if (this.isComplete()) {
      this.submitAction();
    }
  }

  // Check if all fields are filled
  isComplete(): boolean {
    return this.pinDigits.every((digit) => digit !== '');
  }

  // Submit the OTP
  // submitAction() {
  //   if (this.isComplete()) {
  //     this.submitted = true;
  //     this.authService
  //       .saveAssetWithPath(
  //         {
  //           email: this.email,
  //           otp: this.pinDigits.join(''),
  //         },
  //         environment.authUrl + 'auth/verify-otp'
  //       )
  //       .subscribe(
  //         (data) => {
  //           this.submitted = false;
  //           this.tokenService.setToken(data);
  //           this.router.navigate(['/account/dashboard']);
  //         },
  //         (error) => {
  //           this.submitted = false;
  //           console.error('Error verifying OTP:', error);
  //         }
  //       );
  //   }
  // }
  // submitted: any;
  submitAction() {
    if (this.isComplete()) {
      // Perform your submit logic here
      this.authService.saveAssetWithPath({
        "email": this.email,
        "otp": this.pinDigits.join('')
      }, environment.authUrl + 'auth/verify-otp')
        .subscribe(data => {
          this.submitted = false;
          this.tokenService.setToken(data);
          this.router.navigate(['/account/dashboard']);
        }, (error) => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, error, "bottom", "center");
        });
    }
  }

  // Reset all input fields
  cancel() {
    this.pinDigits.fill('');
    this.pinInputs.forEach((input) => (input.nativeElement.value = '')); // Clear input fields
    this.pinInputs.first.nativeElement.focus(); // Focus on first input field
  }

  confirm() {
    this.submitAction();
  }

}
