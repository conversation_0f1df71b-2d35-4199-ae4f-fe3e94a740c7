<!-- <div class="content">
  <h2 style="text-align: center"></h2>
  <p style="text-align: center">
    Track the status of your permit application here
  </p>
  application status content here....
</div> -->
<div class="track-container">
  <div class="trackcard-header">
    <div class="">
      <div class="track">
        <div class="kbk-x">
          <div class="track-info">
            <div class="track-profile">
              <img src="../../../../assets/imgs/profile1.svg" alt="" />
            </div>
            <div class="track-dtail">
              <!-- <span class="track-user">{{inputData.userDetails.firstName}} {{inputData.userDetails.lastName}}</span> -->
              <span class="track-usersub">District: {{inputData.projects.districtName}}</span>
            </div>
          </div>
        </div>
        <hr>
        <div class="kbk-x kbk-wrap-2">
          <div class="track-info">
            <div class="track-icon track-g">
              <img src="assets/ikons/colored/ikon-file.svg" alt="" />
            </div>
            <div class="track-dtail">
              <span class="track-subtitle">{{ "zoningCheckDetails.permit" | translate }}</span>
              <span class="track-title">{{inputData.permitTypes.name}}</span>
            </div>
          </div>
          <div class="track-info">
            <div class="track-icon track-b">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
            </div>
            <div class="track-dtail">
              <span class="track-subtitle">{{ "zoningCheckDetails.permitNumber" | translate }}</span>
              <span class="track-title">{{inputData.applicationName}}</span>
            </div>
          </div>
          <div class="track-info">
            <div class="track-icon track-o">
              <img src="assets/ikons/colored/ikon-calendar.svg" alt="" />
            </div>
            <div class="track-dtail">
              <span class="track-subtitle">{{ "zoningCheckDetails.date" | translate }}</span>
              <span class="track-title">{{inputData.created_at | date}}</span>
            </div>
          </div>
          <div class="track-info">
            <div class="track-icon track-p">
              <img src="assets/ikons/colored/ikon-pin.svg" alt="" />
            </div>
            <div class="track-dtail">
              <span class="track-subtitle">{{ "zoningCheckDetails.location" | translate }}</span>
              <span class="track-title">{{inputData.projects.districtName}}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="track-grf">

      </div> -->
    </div>
    <!-- <div class="trackrepo">
      <p> Ubusabe bwawe bwaremewe ku itariki <strong>Oct 19, 2023</strong>. </p>
      <p> I byangombwa byawe bikaba bifite nimero <strong>COK-FOUN-AAB827</strong>. </p>
      <p> Injira muri konti yawe urabusangamo <a class="kbk-link">KANDA HANO WINJIRE</a>. </p>
    </div> -->
  </div>
  <hr>
  <div class="trackcard-body">
    <h3>{{ "zoningCheckDetails.stageTitle" | translate }}</h3>
    <div class="trackcard-body-content kbk-x">
      <div class="trackcard-body-stgz">
        <ul class="stg-list">

          <li class="stg-item" [ngClass]="{'stg-active': inputData.applicationStatus.code === 'PND',
          'stg-pending' : inputData.applicationStatus.code !== 'PND' }">
            <div class="stg-crd">
              <span class="stg-crd-num"> {{ "zoningCheckDetails.stage0" | translate }} </span>
              <span class="stg-crd-title">{{ "zoningCheckDetails.stage0Title" | translate }}</span>
              <span class="stg-crd-info"> {{ "zoningCheckDetails.stage0Description" | translate }} </span>
            </div>
          </li>
          <li class="stg-item "
          [ngClass]="{'stg-active': inputData.applicationStatus.code === 'SUB'
          || inputData.applicationStatus.code === 'RSMB'
          ,
          'stg-pending' : inputData.applicationStatus.code !== 'SUB'
          || inputData.applicationStatus.code !== 'RSMB'
           }">
            <div class="stg-crd">
              <span class="stg-crd-num"> {{ "zoningCheckDetails.stage1" | translate }} </span>
              <span class="stg-crd-title">{{ "zoningCheckDetails.stage1Title" | translate }}</span>
              <span class="stg-crd-info"> {{ "zoningCheckDetails.stage1Description" | translate }} </span>
            </div>
          </li>
          <li class="stg-item " [ngClass]="{'stg-active': inputData.applicationStatus.code === 'UNCRN',

          'stg-pending' : inputData.applicationStatus.code !== 'UNCRN' }">
            <div class="stg-crd">
              <span class="stg-crd-num"> {{ "zoningCheckDetails.stage2" | translate }}</span>
              <span class="stg-crd-title">{{ "zoningCheckDetails.stage2Title" | translate }}</span>
              <span class="stg-crd-info"> {{ "zoningCheckDetails.stage2Description" | translate }} </span>
            </div>
          </li>
          <li class="stg-item " [ngClass]="{'stg-active': inputData.applicationStatus.code === 'RVW'
          || inputData.applicationStatus.code === 'UNRV'
          || inputData.applicationStatus.code === 'RTNNO'
          || inputData.applicationStatus.code === 'NORVW'
          || inputData.applicationStatus.code === 'NOUNRV'
          || inputData.applicationStatus.code === 'PAPRV'
          || inputData.applicationStatus.code === 'NORHA',

          'stg-pending' : inputData.applicationStatus.code !== 'RVW'
          || inputData.applicationStatus.code !== 'UNRV'
          || inputData.applicationStatus.code !== 'RTNNO'
          || inputData.applicationStatus.code !== 'NORVW'
          || inputData.applicationStatus.code !== 'NOUNRV'
          || inputData.applicationStatus.code !== 'PAPRV'
          || inputData.applicationStatus.code !== 'NORHA',
           }">
            <div class="stg-crd">
              <span class="stg-crd-num"> {{ "zoningCheckDetails.stage3" | translate }}</span>
              <span class="stg-crd-title">{{ "zoningCheckDetails.stage3Title" | translate }}</span>
              <span class="stg-crd-info"> {{ "zoningCheckDetails.stage3Description" | translate }} </span>
            </div>
          </li>

          <li class="stg-item " [ngClass]="{'stg-active': inputData.applicationStatus.code === 'CTFD'
                    || inputData.applicationStatus.code === 'CXL'
          ,
          'stg-pending' : inputData.applicationStatus.code !== 'CTFD'
                              || inputData.applicationStatus.code !== 'CXL',


                              'stg-done': inputData.applicationStatus.code === 'CTFD'
                    || inputData.applicationStatus.code === 'CXL'
 }">
            <div class="stg-crd">
              <span class="stg-crd-num"> {{ "zoningCheckDetails.stage4" | translate }} </span>
              <span class="stg-crd-title"> {{ "zoningCheckDetails.stage4Title" | translate }} </span>
              <span class="stg-crd-info"> {{ "zoningCheckDetails.stage4Description" | translate }} </span>
            </div>
          </li>

          <!-- <li class="stg-item" [ngClass]="{'stg-active': inputData.applicationStatus.code === 'PND',
          'stg-pending' : inputData.applicationStatus.code !== 'PND' }">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Gatanu</span>
              <span class="stg-crd-title">Kwishyura</span>
              <span class="stg-crd-info">Aha ubutumwa bugufi buba bwamaze koherezwa kuri telephone ya enjeniyeri. Ubwo
                butumwa bwereka uko uri bwishyure. Numara kwisyura icyangombwa cyawe urahita ucyibona muri
                sisitemu.</span>
            </div>
          </li>
          <li class="stg-item" [ngClass]="{'stg-active': inputData.applicationStatus.code === 'PND',
          'stg-pending' : inputData.applicationStatus.code !== 'PND' }">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Gatandatu</span>
              <span class="stg-crd-title">Icyangombwa cyabonetse</span>
              <span class="stg-crd-info">Hano icyangombwa cyawe kiba cyabonetse ushobora kujya muri sisitemu
                ukagifata.</span>
            </div>
          </li> -->
        </ul>
        <!-- <ul class="stg-list">
          <li class="stg-item stg-pending">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cyambere</span>
              <span class="stg-crd-title">Gutanga ubusabe</span>
              <span class="stg-crd-info">Iki ni ikiciro cyambere igihe uba umaze kohereza ubusabe.</span>
            </div>
          </li>
          <li class="stg-item stg-active">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Kabiri</span>
              <span class="stg-crd-title">Igenzura</span>
              <span class="stg-crd-info">Hano, ubusabe bwawe buba buri kugenzurwa n'abakozi bashinzwe gutanga
                ibyangombwa.</span>
            </div>
          </li>
          <li class="stg-item stg-pending">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Gatatu</span>
              <span class="stg-crd-title">Byasubiye kuri enjeniyeri</span>
              <span class="stg-crd-info">Aha haba hari ikibazo cyagaragaye kw'idosiye yawe, none enjeniyeri akaba asabwa
                gukosora ibibazo birimo ubundi akongera akohereza ubusabe.</span>
            </div>
          </li>
          <li class="stg-item">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Kane</span>
              <span class="stg-crd-title">Kwishyura</span>
              <span class="stg-crd-info">Aha ubutumwa bugufi buba bwamaze koherezwa kuri telephone ya enjeniyeri. Ubwo
                butumwa bwereka uko uri bwishyure. Numara kwisyura icyangombwa cyawe urahita ucyibona muri
                sisitemu.</span>
            </div>
          </li>
          <li class="stg-item">
            <div class="stg-crd">
              <span class="stg-crd-num">Icyiciro cya Gatanu</span>
              <span class="stg-crd-title">Icyangombwa cyabonetse</span>
              <span class="stg-crd-info">Hano icyangombwa cyawe kiba cyabonetse ushobora kujya muri sisitemu
                ukagifata.</span>
            </div>
          </li>
        </ul> -->
      </div>
    </div>
  </div>
</div>
