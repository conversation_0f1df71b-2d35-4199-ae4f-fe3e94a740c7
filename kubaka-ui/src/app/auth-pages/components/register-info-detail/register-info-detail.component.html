<form class="form" [formGroup]="accountDetailsForm" (ngSubmit)="onSubmit()">
  <div class="form-header">
    <h2 class="display-md">{{"registerPage.title" | translate }}</h2>
  </div>
  <div class="form-set">
    <div class="form-short">
      <div class="form-input">
        <label>{{"registerPage.firstNameLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="text" class="flg" id="firstName" name="firstName" placeholder="" formControlName="firstName"
           [class.is-invalid]="
              accountDetailsForm.get('firstName')?.invalid &&
              accountDetailsForm.get('firstName')?.touched
          " required />
        </div>
        <!-- <div>
          <input type="text" class="flg" id="firstName" name="firstName" placeholder="" formControlName="firstName"
          [readonly]="(this.inputData.accountTypeId === '1' &&
          inputData.documentTypeId === '1') || this.inputData.verified" [class.is-invalid]="
              accountDetailsForm.get('firstName')?.invalid &&
              accountDetailsForm.get('firstName')?.touched
            " required />
        </div> -->
      </div>
      <div class="form-input">
        <label>{{"registerPage.lastNameLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="text" class="flg" id="lastName" name="lastName" placeholder="" formControlName="lastName"
           [class.is-invalid]="
              accountDetailsForm.get('lastName')?.invalid &&
              accountDetailsForm.get('lastName')?.touched
            " required />
        </div>
        <!-- <div>
          <input type="text" class="flg" id="lastName" name="lastName" placeholder="" formControlName="lastName"
          [readonly]="(this.inputData.accountTypeId === '1' &&
          inputData.documentTypeId === '1') || this.inputData.verified" [class.is-invalid]="
              accountDetailsForm.get('lastName')?.invalid &&
              accountDetailsForm.get('lastName')?.touched
            " required />
        </div> -->
      </div>
    </div>
    <div class="form-short">
      <div class="form-input">
        <label>{{"registerPage.emailLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="text" class="flg" id="email" name="email" [placeholder]="'registerPage.emailPlaceholder' | translate"
          (input)="convertToLowercase()"
            appNoSpaces formControlName="email" [class.is-invalid]="
              accountDetailsForm.get('email')?.invalid &&
              accountDetailsForm.get('email')?.touched
            " required />
          <div *ngIf="
              (accountDetailsForm.get('email')?.invalid &&
                accountDetailsForm.get('email')?.touched) ||
              accountDetailsForm.get('email')?.dirty
            ">
            <small *ngIf="accountDetailsForm.get('email')?.hasError('required')" class="text-danger">Email is
              required</small>
            <small *ngIf="accountDetailsForm.get('email')?.hasError('pattern')" class="text-danger">Please provide a
              valid email address</small>
          </div>
        </div>
      </div>
      <div class="form-input">
        <label>{{"registerPage.phoneLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="text" name="phoneNumber" formControlName="phoneNumber" class="flg" appNoSpaces
            placeholder="Phone Number" (keyup)="checkNumberOfArchEng()" required />
          <div class="wedu-warntip"
            *ngIf="accountDetailsForm.get('phoneNumber')?.invalid && accountDetailsForm.get('phoneNumber')?.touched">
            <small *ngIf="accountDetailsForm.get('phoneNumber')?.hasError('required')" class="text-danger">Phone number
              is required</small>
            <!-- <small *ngIf="accountDetailsForm.get('phoneNumber')?.hasError('pattern')" class="text-danger"> Please
              provide a valid phone number starting with 078, 079, 073, or 072 </small> -->
          </div>
        </div>
      </div>
    </div>
    <div class="form-short">
      <div class="form-input">
        <label>{{"registerPage.dobLabel" | translate }}<span class="estrx"> *</span></label>
        <div class="input-group">
          <input class="flg form-control" placeholder="yyyy-mm-dd" name="dateOfBirthForm"
            formControlName="dateOfBirthForm" ngbDatepicker #d="ngbDatepicker" [maxDate]="utilService.maxDate"
            [minDate]="utilService.minDate" />
          <button class="kbk-btn kbk-btn-lg kbk-btn-main" (click)="d.toggle()" type="button">
            <img src="assets/ikons/SVG/ikon-calendar-w.svg" alt="">
          </button>
        </div>
      </div>
      <div class="form-input">
        <label>{{"registerPage.genderLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <select class="flg" name="gender" formControlName="gender">
            <option value="">{{"registerPage.genderOptions.placeholder" | translate }}</option>
            <option *ngFor="let gn of genders" [value]="gn.id">{{gn.name}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="form-short">
      <div class="form-input">
        <label>{{"registerPage.passwordLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="password" class="flg" id="password" placeholder="" name="password" formControlName="password"
            appNoSpaces />
          <div *ngIf="
              accountDetailsForm.get('password')?.invalid &&
              accountDetailsForm.get('password')?.touched
            ">
            <small *ngIf="accountDetailsForm.get('password')?.hasError('required')" class="text-danger">Password is
              required</small>
            <small *ngIf="accountDetailsForm.get('password')?.hasError('minlength')" class="text-danger">Password must
              be at least 8 characters</small>
            <small *ngIf="accountDetailsForm.get('password')?.hasError('pattern')" class="text-danger"> Password must
              contain at least one lowercase letter, one uppercase letter, and one digit </small>
          </div>
          <div *ngIf="accountDetailsForm.controls['password'].hasError('pattern')">
            <span class="text-danger">Password Must contain below conditions.</span>
            <ul>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'uppercase'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'uppercase'
                  )
                }"> Min 1 Uppercase Letter. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'lowercase'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'lowercase'
                  )
                }"> Min 1 Lowercase Letter. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'special-character'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'special-character'
                  )
                }"> Min 1 Special Character. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'number'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'number'
                  )
                }"> Min 1 Number. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'length'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'length'
                  )
                }"> Min 8 Characters. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['password'],
                    'length'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['password'],
                    'length'
                  )
                }"> Max 16 Characters. </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="form-input">
        <label>{{"registerPage.confirmPasswordLabel" | translate }}<span class="estrx"> *</span></label>
        <div>
          <input type="password" class="flg" id="confirmPassword" name="confirmPassword"
            formControlName="confirmPassword" appNoSpaces required />
          <div *ngIf="
              accountDetailsForm.get('confirmPassword')?.invalid &&
              accountDetailsForm.get('confirmPassword')?.touched
            ">
            <small *ngIf="
                accountDetailsForm.get('confirmPassword')?.hasError('required')
              " class="text-danger">Password is required</small>
            <small *ngIf="
                accountDetailsForm.get('confirmPassword')?.hasError('minlength')
              " class="text-danger">Password must be at least 8 characters</small>
            <small *ngIf="
                accountDetailsForm.get('confirmPassword')?.hasError('pattern')
              " class="text-danger">Password must contain at least one lowercase letter, one uppercase letter, and one
              digit </small>
          </div>
          <div *ngIf="
              accountDetailsForm.controls['confirmPassword'].hasError('pattern')
            ">
            <span class="text-danger">Password Must contain below conditions.</span>
            <ul>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'uppercase'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'uppercase'
                  )
                }"> Min 1 Uppercase Letter. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'lowercase'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'lowercase'
                  )
                }"> Min 1 Lowercase Letter. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'special-character'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'special-character'
                  )
                }"> Min 1 Special Character. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'number'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'number'
                  )
                }"> Min 1 Number. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'length'
                  )
                }"> Min 8 Characters. </li>
              <li [ngClass]="{
                  'text-success': checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !checkValidations(
                    accountDetailsForm.controls['confirmPassword'],
                    'length'
                  )
                }"> Max 16 Characters. </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-1" *ngIf="accountDetailsForm.hasError('notMatched')">
    <mat-error *ngIf="accountDetailsForm.hasError('notMatched')">
      <span class="danger">Passwords are not matched.</span>
    </mat-error>
  </div>
  <div class="form-footer">
    <div class="form-incheckbox">
      <label class="checkbox">
        <input type="checkbox" id="check" name="isConsent" formControlName="isConsent" />
        <span class="checkbox_box"></span>
        <span class="checkbox_txt">{{ "registerPage.consent" | translate }}</span>
      </label>
    </div>
    <button class="kbk-btn kbk-btn-lg kbk-btn-main"
      [disabled]="accountDetailsForm.invalid || !accountDetailsForm.value.isConsent" *ngIf="!submitted" type="submit">
      {{ "registerPage.createAccountButton" | translate }} </button>
    <button class="kbk-btn kbk-btn-lg kbk-btn-main" *ngIf="submitted" type="button"> Wait... </button>
    <p class="mb-0"> {{ "registerPage.haveAccount" | translate }} <a routerLink="/login" class="kbk-link"> {{ "registerPage.loginHere" | translate }} </a> {{ "registerPage.orCancel" | translate }} <a
        (click)="backToRegister()" class="kbk-link"> {{ "registerPage.backTxt" | translate }} </a></p>
  </div>
</form>
