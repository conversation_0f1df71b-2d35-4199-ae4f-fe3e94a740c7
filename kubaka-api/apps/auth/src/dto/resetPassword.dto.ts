import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class ResetPasswordDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty({ message: 'The field email cannot be empty' })
  email: string;

  @ApiProperty({ description: 'The field newPassword is required' })
  @IsNotEmpty({ message: 'The field newPassword cannot be empty' })
  newPassword: string;

  // @ApiProperty({ description: 'The field currentPassword is required' })
  // @IsNotEmpty({ message: 'The field currentPassword cannot be empty' })
  // currentPassword: string;

  @ApiProperty({ description: 'The field newPasswordToken is required' })
  @IsNotEmpty({ message: 'The field newPasswordToken cannot be empty' })
  newPasswordToken: string;
}

export class SetPasswordDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field newPassword is required' })
  @IsNotEmpty({ message: 'The field newPassword cannot be empty' })
  newPassword: string;

  @ApiProperty({ description: 'The field newPasswordToken is required' })
  @IsNotEmpty({ message: 'The field newPasswordToken cannot be empty' })
  newPasswordToken: string;
}

export class ChangePasswordDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty({ message: 'The field email cannot be empty' })
  email: string;

  @ApiProperty({ description: 'The field newPassword is required' })
  @IsNotEmpty({ message: 'The field newPassword cannot be empty' })
  newPassword: string;

  @ApiProperty({ description: 'The field currentPassword is required' })
  @IsNotEmpty({ message: 'The field currentPassword cannot be empty' })
  currentPassword: string;
}
