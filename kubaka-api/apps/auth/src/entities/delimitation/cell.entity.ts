import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { Sector } from './sector.entity';
import { AbstractEntity } from '@app/common';
import { Village } from './village.entity';

@Entity()
export class Cell extends AbstractEntity<Cell> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @Column()
  name: string;

  @Column()
  code: string;

  @ManyToOne(() => Sector, (sector) => sector.cells)
  sector: Sector;

  @OneToMany(() => Village, (village) => village.cell)
  villages: Village[];
}
