<!-- Applicant Invoices -->
<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <div class="app-welnote">
        <div class="app-welnote_dtails">
          <span class="prim-nt">{{"applicantInvoices.welcomeMsg" | translate }}!</span>
          <h3>
            <span> {{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
          </h3>
        </div>
      </div>
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle">{{"applicantInvoices.myInvoices" | translate }}</span>
            <div class="tbleFilter">
              <div class="form-input_search">
                <input type="text" name="searchTerm" 
                [(ngModel)]="applicationService.searchTerm"
                  (ngModelChange)="onSearchTermChange($event)" 
                  [placeholder]="'applicantInvoices.search' | translate" />
                <button type="button" class="btn">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>{{"applicantInvoices.fromDate" | translate }}</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateFrom" [(ngModel)]="searchData.dateFrom"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>{{"applicantInvoices.toDate" | translate }}</Strong> </label>
                <div>
                  <input class="w-aut" type="date" name="dateTo" [(ngModel)]="searchData.dateTo"
                    (ngModelChange)="onSearchByDateChange($event)" required />
                </div>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>{{"applicantInvoices.pageSize" | translate }}</Strong> </label>
                <div>
                  <select name="pageSize" id="pageSize" [(ngModel)]="pageSize" (change)="changePageSize()">
                    <!-- <option disabled value="">All</option> -->
                    <option *ngFor="let r of utilService.tableArraySelector" [value]="r.id"> {{ r.id }} </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <ul class="tblist">
            <li class="tblist-item" *ngFor="let li of lists">
              <div class="tblist-item_icon bg-l-o">
                <img src="assets/ikons/colored/ikon-invo.svg" alt="" />
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">{{"applicantInvoices.invoiceItem" | translate }}</span> {{ li.invoiceItems?.name }} </span>
                <span>
                  <span class="ttl">{{"applicantInvoices.invoiceNo" | translate }}</span> {{ li.invoiceNumber }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"applicantInvoices.amount" | translate }}</span>Rwf {{ li.amount }} </span>
                <span>
                  <span class="ttl">{{"applicantInvoices.transactionNumber" | translate }}</span> {{ li.externalReferenceNumber }} </span>
              </div>
              <div class="tblist-item_dt">
                <span>
                  <span class="ttl">{{"applicantInvoices.createdOn" | translate }}</span> {{ li.created_at | date }} </span>
                <span>
                  <span class="ttl">{{"applicantInvoices.dueDate" | translate }}</span> {{ li.dueDate | date }} </span>
              </div>
              <div class="tblist-item_dt txt-l">
                <span>
                  <span class="ttl">{{"applicantInvoices.applicationName" | translate }}</span> {{ li.applications.applicationName }} </span>
                  <span>
                    <span class="ttl">UPI</span> {{ li.applications.upi }} </span>
              </div>
              <div class="tblist-item_status">
                <span class="bdg bdg-pend">{{ li.invoiceStatus?.name }}</span>
              </div>
              <div class="kbk-table-dropdown">
                <button class="kbk-link-btn">{{"applicantInvoices.more" | translate }}</button>
                <div class="kbk-table-dropdown_list">
                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View" *ngIf="
                      li.invoiceStatus?.code !== 'EXMTD' &&
                      li.invoiceStatus?.code !== 'PAD' &&
                      li.invoiceStatus?.code !== 'CCD' &&
                      (currentUser.data.user.userType.code === 'LO' ||
                        currentUser.data.user.userType.code === 'STF')
                    " [routerLink]="['/account/payment/invoice-detail', li.id]">{{ li.invoiceStatus?.code === "PND" ?
                    ('applicantInvoices.viewAndPay' | translate) : ('applicantInvoices.view' | translate) }} </a>
                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View" *ngIf="
                      (li.invoiceStatus?.code === 'PAD' ||
                        li.invoiceStatus?.code === 'EXMTD') &&
                      (currentUser.data.user.userType.code === 'LO' ||
                        currentUser.data.user.userType.code === 'STF')
                    " [routerLink]="[
                      '/account/payment/invoice-detail-water-mark',
                      li.id
                    ]"> {{ li.invoiceStatus?.code === "PND" ? ('applicantInvoices.viewAndPay' | translate) : ('applicantInvoices.view' | translate) }} </a>
                  <a *ngIf="
                    currentUser.data.user.role.code === 'DRCT' &&
                    li.invoiceStatus.code === 'PND'
                  " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                    data-bs-target="#showModal" (click)="openModal(deleteModel, 'lg', li)">{{"applicantInvoices.cancel" | translate }}</a>
                  <a *ngIf="
                    currentUser.data.user.role.code === 'DRCT' &&
                    li.invoiceStatus.code === 'PAD'
                  " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                    data-bs-target="#showModal" (click)="openModal(refundModel, 'lg', li)"> Refund </a>
                  <a *ngIf="
                    currentUser.data.user.role.code === 'DRCT' &&
                    li.invoiceStatus.code === 'PND'
                  " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                    data-bs-target="#showModal" (click)="openModalToUpdate(updateInvoice, 'lg', li)"> Update </a>
                  <a *ngIf="
                    currentUser.data.user.role.code === 'DRCT' &&
                    li.invoiceStatus.code === 'PND'
                  " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                    data-bs-target="#showModal" (click)="openModal(invoiceExtension, 'lg', li)"> Invoice Extension </a>
                </div>
              </div>
              <div class="tblist-item_xcn" style="display: none;">
                <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View" *ngIf="
                      li.invoiceStatus?.code !== 'EXMTD' &&
                      li.invoiceStatus?.code !== 'PAD' &&
                      li.invoiceStatus?.code !== 'CCD' &&
                      (currentUser.data.user.userType.code === 'LO' ||
                        currentUser.data.user.userType.code === 'STF')
                    " [routerLink]="['/account/payment/invoice-detail', li.id]">{{ li.invoiceStatus?.code === "PND" ?
                  ('applicantInvoices.viewAndPay' | translate) : ('applicantInvoices.view' | translate) }} </a>
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View"
                                      *ngIf="li.invoiceStatus?.code === 'PAD' && currentUser.data.user.userType.code === 'LO'"
                                      [routerLink]="['/account/payment/invoice-detail-water-mark', li.id]">
                                      {{li.invoiceStatus?.code
                                      !== 'PAD' ? 'View and pay' : 'View'}} </a> -->
                <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View" *ngIf="
                      (li.invoiceStatus?.code === 'PAD' ||
                        li.invoiceStatus?.code === 'EXMTD') &&
                      (currentUser.data.user.userType.code === 'LO' ||
                        currentUser.data.user.userType.code === 'STF')
                    " [routerLink]="[
                      '/account/payment/invoice-detail-water-mark',
                      li.id
                    ]"> {{ li.invoiceStatus?.code === "PND" ? ('applicantInvoices.viewAndPay' | translate) : ('applicantInvoices.view' | translate) }} </a>
                <a *ngIf="
                      currentUser.data.user.role.code === 'DRCT' &&
                      li.invoiceStatus.code === 'PND'
                    " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                  data-bs-target="#showModal" (click)="openModal(deleteModel, 'lg', li)">{{"applicantInvoices.cancel" | translate }}</a>
                <a *ngIf="
                      currentUser.data.user.role.code === 'DRCT' &&
                      li.invoiceStatus.code === 'PAD'
                    " class="kbk-link-btn hs-tp" data-kbk-tooltip="Cancel Invoice" data-bs-toggle="modal"
                  data-bs-target="#showModal" (click)="openModal(refundModel, 'lg', li)"> {{"applicantInvoices.refund" | translate }} </a>
                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="Delete">
                                      <img src="assets/ikons/colored/ikon-trash.svg" alt="">
                                  </a>
                                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View details" (click)="viewData(li)">
                                      <img src="assets/ikons/colored/ikon-eye.svg" alt="">
                                  </a> -->
              </div>
            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                <span class="ent">{{ totalRecords }}</span>
                <span class="cur">{{ startIndex }} - {{ endIndex }}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="getPremiumData()">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div>
      <div class="app-dash">
          <div class="container">
              <div class="app-main">
                  <div class="app-cont">
                      <div class="app-cont_title">
                          <span class="hder" aria-label="header tittle">Tittle</span>
                          <div class="btns">
                              <button type="button" class="kbk-btn kbk-btn-main">Button</button>
                          </div>
                      </div>
                      <div class="app-cont_list">
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <div class="app-dash">
          <div class="container">
              <div class="app-main">
                  <div class="app-lists">
                      <div class="app-tblist">
                          <div class="app-tblist_title">
                              <span class="hder" aria-label="header tittle">Tittle</span>
                          </div>
                          <div class="btns">
                              <button type="button" class="kbk-btn kbk-btn-main">Button</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div> -->
<ng-template #refundModel let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel"> {{"applicantInvoices.confirmRefund" | translate }} {{ cancelData.invoiceNumber }} ? </h2>
      <!-- <p>Deleting your {{cancelData.name}} will remove all of your information from our database.</p> -->
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-md">
        <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
      </div>
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close">{{"applicantInvoices.close" | translate }}</button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="confirmRefund()"> {{"applicantInvoices.yesRefund" | translate }}!
        </button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #deleteModel let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel"> You are about to cancel {{ cancelData.invoiceNumber }} ? </h2>
      <!-- <p>Deleting your {{cancelData.name}} will remove all of your information from our database.</p> -->
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-md">
        <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
      </div>
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close">{{"applicantInvoices.close" | translate }}</button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="confirmCancel()"> {{"applicantInvoices.yesDelete" | translate }}!
        </button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #updateInvoice role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"applicantInvoices.updateInvoice" | translate }}</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>

  <div class="step-panel_body">
    <form>
      <div class="">
        <div class="form-input">
          <label>{{"applicantInvoices.invoiceItem" | translate }}</label>
          <div>
            <select name="invoiceItemId" id="invoiceItemId" [(ngModel)]="editInvoiceDetails.invoiceItemId" required>
              <option *ngFor="let op of invoiceItems" [value]="op.id"> {{ op.name }} </option>
            </select>
          </div>
        </div>
        <div class="form-input" *ngIf="editInvoiceDetails.isChoosingPrice">
          <label>{{"applicantInvoices.chooseInvoicePrices" | translate }}</label>
          <div>
            <select name="invoicePriceId" id="invoicePriceId" [(ngModel)]="invoicePriceId" (change)="getPrice()"
              required>
              <option *ngFor="let op of invoicePrices" [value]="op.id">Min Sqm: {{ op.rangeInSqmMin }} - Max Sqm: {{
                op.rangeInSqmMax }} | Price {{ op.amount }} </option>
            </select>
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantInvoices.amount" | translate }} <span style="cursor: pointer; color: #155724;" data-bs-toggle="modal" id="create-btn"
              data-bs-target="#showModal" (click)="openPricePopup()">(Get price)</span> </label>
          <div>
            <input type="newAmount" name="newAmount" [(ngModel)]="editInvoiceDetails.newAmount" readonly required />
          </div>
        </div>
      </div>

    </form>
  </div>
  <div class="modol-content">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()">{{"applicantInvoices.cancel" | translate }}</button>
      <button class="kbk-btn kbk-btn-main" type="button" *ngIf="!submitted" (click)="updateInvoiceMethod()"> Update
      </button>
      <button class="kbk-btn kbk-btn-main" type="button" *ngIf="submitted"> {{"applicantInvoices.updating" | translate }}... </button>


    </div>
  </div>
</ng-template>
<ng-template #invoiceExtension role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"applicantInvoices.askForExtension" | translate }}</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="step-panel_body">
    <form>
      <div class="form-set">
        <div class="form-input">
          <label>{{"applicantInvoices.amount" | translate }}</label>
          <div>
            <input type="text" id="amount" name="amount" [(ngModel)]="iremboDetails.amount" required />
          </div>
        </div>
        <div class="form-input">
          <label>{{"applicantInvoices.expirationDate" | translate }}</label>
          <div>
            <input type="date" id="expirationTime" name="expirationTime" [(ngModel)]="iremboDetails.expirationTime"
              required />
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modol-content">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()">{{"applicantInvoices.cancel" | translate }}</button>
      <button class="kbk-btn kbk-btn-main" type="button" *ngIf="!submitted" (click)="saveToIrembo()"> {{"applicantInvoices.submit" | translate }} </button>
      <button class="kbk-btn kbk-btn-main" type="button" *ngIf="submitted"> {{"applicantInvoices.submit" | translate }} ..... </button>

    </div>
  </div>
</ng-template>



<!-- <ng-template #contentPrice role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Get prices</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content">
    <form autocomplete="off">
      <div class="form-set">
        <div class="form-input">
          <label>Invoice Prices</label>
          <div>
            <select name="invoicePriceId" id="invoicePriceId" [(ngModel)]="invoicePriceId"
            (change)="getPrice()" required>
              <option *ngFor="let op of invoicePrices" [value]="op.id">Range {{ op.range }} | Price {{ op.amount }} </option>
            </select>
          </div>
        </div>
      </div>

    </form>
  </div>
</ng-template> -->