const os = require('os');
const { exec } = require('child_process');

console.log('=== CPU Optimization Analysis ===\n');

// Get CPU information
const cpus = os.cpus();
const numCPUs = cpus.length;
const totalMemory = os.totalmem();
const freeMemory = os.freemem();
const usedMemory = totalMemory - freeMemory;

console.log(`System Information:`);
console.log(`- CPU Cores: ${numCPUs}`);
console.log(`- CPU Model: ${cpus[0].model}`);
console.log(`- Total Memory: ${(totalMemory / 1024 / 1024 / 1024).toFixed(2)} GB`);
console.log(`- Used Memory: ${(usedMemory / 1024 / 1024 / 1024).toFixed(2)} GB`);
console.log(`- Free Memory: ${(freeMemory / 1024 / 1024 / 1024).toFixed(2)} GB`);
console.log(`- Memory Usage: ${((usedMemory / totalMemory) * 100).toFixed(2)}%\n`);

// Calculate optimal worker count
const optimalWorkers = Math.max(1, Math.floor(numCPUs * 0.75));
const maxWorkers = numCPUs;
const minWorkers = 1;

console.log(`Worker Configuration Recommendations:`);
console.log(`- Minimum workers: ${minWorkers}`);
console.log(`- Optimal workers: ${optimalWorkers} (75% of cores)`);
console.log(`- Maximum workers: ${maxWorkers} (all cores)`);
console.log(`- Recommended: ${optimalWorkers} workers for best performance\n`);

// Check current Node.js processes
exec('ps aux | grep node | grep -v grep', (error, stdout, stderr) => {
  if (error) {
    console.log('No Node.js processes found running');
  } else {
    console.log('Current Node.js Processes:');
    console.log(stdout);
  }
  
  console.log('\n=== Optimization Commands ===');
  console.log('To start applications with optimal CPU usage:');
  console.log('');
  console.log('# Start all services with cluster mode:');
  console.log('docker compose up --build');
  console.log('');
  console.log('# Start individual services with advanced clustering:');
  console.log('pnpm run start:advanced-cluster:applications');
  console.log('pnpm run start:advanced-cluster:auth');
  console.log('pnpm run start:advanced-cluster:payments');
  console.log('pnpm run start:advanced-cluster:notifications');
  console.log('pnpm run start:advanced-cluster:documents');
  console.log('pnpm run start:advanced-cluster:integrations');
  console.log('');
  console.log('# Monitor CPU usage:');
  console.log('htop');
  console.log('top');
  console.log('');
  console.log('# Check Docker container resource usage:');
  console.log('docker stats');
  console.log('');
  console.log('=== End Analysis ===');
});

// Monitor CPU usage in real-time
let lastCPUUsage = os.cpus().map(cpu => ({
  idle: cpu.times.idle,
  total: Object.values(cpu.times).reduce((acc, time) => acc + time, 0)
}));

setInterval(() => {
  const currentCPUUsage = os.cpus().map(cpu => ({
    idle: cpu.times.idle,
    total: Object.values(cpu.times).reduce((acc, time) => acc + time, 0)
  }));

  const cpuUsagePercentages = currentCPUUsage.map((cpu, index) => {
    const idle = cpu.idle - lastCPUUsage[index].idle;
    const total = cpu.total - lastCPUUsage[index].total;
    return ((total - idle) / total) * 100;
  });

  const avgCPUUsage = cpuUsagePercentages.reduce((acc, usage) => acc + usage, 0) / cpuUsagePercentages.length;

  console.log(`\rCPU Usage: ${avgCPUUsage.toFixed(1)}% | Memory: ${((usedMemory / totalMemory) * 100).toFixed(1)}% | Workers: ${optimalWorkers}/${numCPUs}`, '');
  
  lastCPUUsage = currentCPUUsage;
}, 2000); 