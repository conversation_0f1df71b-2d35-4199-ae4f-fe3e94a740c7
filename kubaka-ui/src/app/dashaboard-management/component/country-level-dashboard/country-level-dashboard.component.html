<div class="app-main">
  <div class="app-tblist_title">
      <div class="tbleFilter">

        <div class="form-input clear-m w-aut">
          <label><Strong>{{"countryLevelDashboard.agency" | translate }}</Strong> </label>
          <div>
            <select name="filterByAgency" id="filterByAgency"
              [(ngModel)]="filterByAgency"
              (change)="changeByAgency()">
              <option value="all">{{"countryLevelDashboard.allAgency" | translate }}</option>
              <option *ngFor="let r of agencies" [value]="r.id"> {{ r.name }} </option>
            </select>
          </div>
        </div>
      </div>
    </div>
  <div class="app-stats">
      <div class="app-stats_card" (click)="goToApplication('')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.allApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.all }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('PAPRV')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.preApproval" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.preApproval }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('RVW')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.reviewedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.review }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('SUB')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.submittedApplications" | translate }} </span>
              <span class="dtail-num">{{ countingBoard?.submitted }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('CTFD')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.permitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.certified }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('CXL')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.rejectedApplications" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.rejected }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('RSMB')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.resubmitted" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.reSubmitted }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('NORHA')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">RHA</span>
              <span class="dtail-num">{{ countingBoard?.rha }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('UNRV')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.underReview" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.underReview }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('UNCRN')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <span class="dtail-title">{{"countryLevelDashboard.underCorrection" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.underCorrection }} </span>
          </div>
      </div>
      <div class="app-stats_card" (click)="goToApplication('NORVW')">
          <div class="icon">
              <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
          </div>
          <div class="dtail">
              <!-- <span class="dtail-title">RHA Under review application</span> -->
              <span class="dtail-title">{{"countryLevelDashboard.nonObjectionReturned" | translate }}</span>
              <span class="dtail-num">{{ countingBoard?.rhaReturned }} </span>
          </div>
      </div>
  </div>
</div>
