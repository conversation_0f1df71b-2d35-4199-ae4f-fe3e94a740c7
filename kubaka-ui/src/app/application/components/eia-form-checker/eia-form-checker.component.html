<div class="step-panel">
    <form [formGroup]="userForm" (ngSubmit)="EIAChecker()">
        <div class="step-panel_header">
            <h2>{{"eiaFormChecker.checkingEIA" | translate }}</h2>
            <h4 style="color: red;">{{userForm.value.message ? userForm.value.message : ''}}.
              <a (click)="openEIAclick()">{{"eiaFormChecker.eiaApplyMessage" | translate }}</a>
            </h4>
        </div>
        <div class="step-panel_body">
            <div class="kbk-x">
                <div class="form-set">
                    <div class="form-input">
                        <label>{{"eiaFormChecker.certificateNumber" | translate }}</label>
                        <div>
                            <input type="text" id="certificateNumber" name="certificateNumber"
                                formControlName="certificateNumber" readonly required>
                        </div>
                    </div>
                    <div class="form-input">
                        <label>{{"eiaFormChecker.upi" | translate }}</label>
                        <div>
                            <input type="text" id="upi" name="upi"
                                formControlName="upi" readonly>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="step-panel_footer">
            <button type="button" *ngIf="submitted"> Wait... </button>
            <button type="submit" *ngIf="!submitted"> Verify certificate </button>
        </div> -->
    </form>
</div>
