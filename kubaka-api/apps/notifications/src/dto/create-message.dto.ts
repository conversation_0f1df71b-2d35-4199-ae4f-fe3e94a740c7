import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMessageDto {
  @ApiProperty({ description: 'The field senderId is required' })
  @IsNotEmpty({ message: 'The field senderId cannot be empty' })
  readonly senderId: string;

  @ApiProperty({ description: 'The field receiverId is required' })
  @IsNotEmpty({ message: 'The field receiverId cannot be empty' })
  readonly receiverId: string;

  @ApiProperty({ description: 'The field content is required' })
  @IsNotEmpty({ message: 'The field content cannot be empty' })
  readonly content: string;
}
