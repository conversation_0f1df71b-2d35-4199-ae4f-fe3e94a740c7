{"name": "kubaka-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug 0.0.0.0:9229 --watch", "start:prod": "node dist/apps/kubaka-api/main", "start:cluster": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js", "start:cluster:applications": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js applications", "start:cluster:auth": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js auth", "start:cluster:payments": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js payments", "start:cluster:notifications": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js notifications", "start:cluster:documents": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js documents", "start:cluster:integrations": "node -r tsconfig-paths/register -r ts-node/register scripts/cluster.js integrations", "start:load-balanced": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js", "start:load-balanced:applications": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js applications", "start:load-balanced:auth": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js auth", "start:load-balanced:payments": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js payments", "start:load-balanced:notifications": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js notifications", "start:load-balanced:documents": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js documents", "start:load-balanced:integrations": "node -r tsconfig-paths/register -r ts-node/register scripts/load-balanced-cluster.js integrations", "start:optimized": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js", "start:optimized:applications": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js applications", "start:optimized:auth": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js auth", "start:optimized:payments": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js payments", "start:optimized:notifications": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js notifications", "start:optimized:documents": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js documents", "start:optimized:integrations": "node -r tsconfig-paths/register -r ts-node/register scripts/single-worker-cluster.js integrations", "cpu:analyze": "node scripts/cpu-optimizer.js", "ports:check": "node scripts/port-checker.js", "ports:fix": "node scripts/port-checker.js --fix", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/kubaka-api/test/jest-e2e.json"}, "dependencies": {"@jozefazz/nestjs-redoc": "^1.0.7", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^10.3.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.3", "@nestjs/swagger": "^7.2.0", "@nestjs/typeorm": "^10.0.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "minio": "^7.1.3", "multer": "1.4.5-lts.1", "nestjs-minio-client": "^2.2.0", "nodemailer": "^6.9.9", "passport": "^0.7.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "pnpm": "^10.13.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/passport": "^1.0.16", "@types/passport-http": "^0.3.11", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/libs/", "<rootDir>/apps/"], "moduleNameMapper": {"^y/common(|/.*)$": "<rootDir>/libs/common/src/$1"}}}