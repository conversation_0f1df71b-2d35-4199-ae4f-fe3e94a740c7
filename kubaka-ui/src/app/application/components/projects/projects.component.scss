// .app-dash .app-main .app-lists .tblist-item {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     width: 100%;
//     border: 1px solid #f1f5f9;
//     border-inline-color: transparent;
//     padding: 0.5rem 1rem;
//     flex-wrap: wrap; /* Allows wrapping to new lines if necessary */
// }

// .app-dash .app-main .app-lists .tblist-item > div {
//     flex: 1;
//     min-width: 0; /* Ensures the flex items shrink properly */
//     display: flex;
//     align-items: center;
//     max-width: 100%; /* Prevents items from growing too large */
// }

// .app-dash .app-main .app-lists .tblist-item .tblist-item_dt {
//     flex: 1; /* Makes each data item take up equal space */
//     padding: 0 0.5rem; /* Adjust padding as needed */
//     display: flex;
//     flex-direction: column; /* Stacks span and value vertically */
//     white-space: nowrap; /* Prevents text from breaking into a new line */
//     overflow: hidden; /* Hides overflow text */
//     text-overflow: ellipsis; /* Adds ellipsis to overflow text */
//     max-width: 100%; /* Ensures the item does not exceed container width */
//     box-sizing: border-box; /* Includes padding in width calculation */
// }

// .app-dash .app-main .app-lists .tblist-item .tblist-item_xcn {
//     flex: 0 0 auto; /* Prevents this section from growing or shrinking */
//     display: flex;
//     gap: 0.5rem; /* Adds space between action buttons */
// }

// .app-dash .app-main .app-lists .tblist-item .tblist-item_icon {
//     flex: 0 0 auto; /* Prevents this section from growing or shrinking */
//     margin-right: 1rem; /* Adds space after the icon */
// }




// .form-input_search.fsm {
//     height: 2rem !important;
//     font-size: 0.875rem;
//     letter-spacing: 0;
//     padding: 0;
//     margin-right: 8px;
//     border-radius: 0.25rem;
// }
