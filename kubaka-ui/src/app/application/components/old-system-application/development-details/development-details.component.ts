import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { jwtDecode } from "jwt-decode";
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../../services/application.service';
import { StepperService } from '../../../services/stepper.service';
@Component({
  selector: 'app-development-details',
  templateUrl: './development-details.component.html',
  styleUrls: ['./development-details.component.scss']
})
export class DevelopmentDetailsComponent {
  submitted!: boolean;
  applicationDetail: any = {};
  @Output() backToParent = new EventEmitter();
  userForm!: UntypedFormGroup;
  outputData: any;

  currentUser: any = {};
  upiInfo: any = {};



  constructor(
    private stepperService: StepperService,
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private sessionService: SessionService,
    private utilService: UtilService,
    private cdr: ChangeDetectorRef,
    private appConfig: AppConfig) {

    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_old_INFO) as any);
    this.initialiaze();

  }

  ngOnInit(): void {
    // When data fetched form old system
    // lkjflas
    if (this.upiInfo && this.upiInfo?.isFromOldSystem) {
      this.cdr.detectChanges();
      // let agencyCodeSplitted = this.upiInfo.Agency.split(' ');
      // this.getAgencyByCode(agencyCodeSplitted[0]?.toUpperCase()); jfaldkfa
      this.initializeDataFromOldSystem();
      this.checkIfProjectExits(this.upiInfo.Plot_No);
    }
    // else if (this.upiInfo && this.upiInfo.bpmisProjectId) {

    // }
    else if (this.upiInfo) {
      // Calling agency by district code from what you have selected to apply
      this.getAgencyByDistrictCode(this.upiInfo.parcelLocation.district.districtCode);
      this.initializeDataFromLandIntergration();
      this.checkIfProjectExits(this.upiInfo.upi);
    }
  }

  checkIfProjectExits(upi: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + upi)
      .subscribe(
        data => {

          if (data.items.length > 0 && data.items[0] && !this.upiInfo?.isFromOldSystem) {
            this.onNext()
            this.cdr.detectChanges();
          }
          // Means data does not exit in NEW BPMIS and first create it's project in bpmis
          else
            if (this.upiInfo?.isFromOldSystem) {

              // Call page to create project of UPI from old system
              // this.router.navigate([])
            }
        }
      )
  }

  // getAgencyByCode(agencyCode: any) {
  //   if (agencyCode) {
  //     this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=' + agencyCode)
  //       .subscribe(
  //         data => {
  //           this.userForm.controls['agencyId'].setValue(data?.items[0]?.id);
  //         },
  //         error => { }
  //       )
  //   }

  // }





  initializeDataFromOldSystem() {
    this.userForm = this.formBuilder.group({
      upi: [this.upiInfo.Plot_No],
      isFromOldSystem: [this.upiInfo.isFromOldSystem],
      isFromOldSystemDevelopersName: [this.upiInfo.Developers_Name],
      isFromOldSystemPermitNumber: [this.upiInfo.Permit_Number],
      isFromOldSystemInvoiceNumber: [this.upiInfo.Invoice_number],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      centralCoordinateX: [""],
      centralCoordinateY: [""],
      villageCode: [""],
      villageName: [""],
      cellCode: [""],
      cellName: [""],
      sectorCode: [""],
      sectorName: [""],
      districtCode: [""],
      districtName: [this.upiInfo.District],
      provinceCode: [""],
      provinceName: [""],
      ownerFullName: [""],
      ownerIdNo: [""],
      // selectedUse: [this.upiInfo.Building_Type],
      // selectedCategoryUse: [this.upiInfo.Building_Type],
      selectedUse: [this.upiInfo.Building_Type ? this.upiInfo.Building_Type : 'None'],
      selectedCategoryUse: [this.upiInfo.Building_Type ? this.upiInfo.Building_Type : 'None'],
      projectName: [this.upiInfo.Name_Of_Project],
      projectDescription: [this.upiInfo.Capacity_Info],
      plotSize: [this.upiInfo.Plot_size],
      originalPlotSize: [this.upiInfo.Plot_size],
      userId: [this.currentUser.userId],
      agencyId: [""],
      // technologySurveyId: [""],
      projectStatusId: [""],
      //   // from land ap
    }
    );
    this.getLocationsOwnerAndOtherPropertiesFromLand();
    // this.focusOutFunction();
    this.getProjectStatus();
  }


  // focusOutFunction() {
  //   this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / this.userForm.value.plotSize) * 100).toFixed(2));
  // }



  getLocationsOwnerAndOtherPropertiesFromLand() {
    this.applicationService.findAllWithPath(environment.landAPI + this.userForm.value.upi)
      .subscribe(
        data => {
          // if (data && data.data.isUnderRestriction) {
          //   this.utilService.showNotification(NOTIFICATION_COLOR.warning, "In the checking process, the UPI is under restriction.", " top", "right");
          // } else {
          if (data) {
             
            this.userForm.controls['isUnderMortgage'].setValue(data.data.isUnderMortgage);
            this.userForm.controls['plotSize'].setValue(data.data.size);
            this.userForm.controls['isUnderRestriction'].setValue(data.data.isUnderRestriction);
            this.userForm.controls['centralCoordinateX'].setValue(data.data.centralCoordinate.x);
            this.userForm.controls['centralCoordinateY'].setValue(data.data.centralCoordinate.y);
            this.userForm.controls['villageCode'].setValue("");
            this.userForm.controls['villageName'].setValue(data.data.parcelLocation.village.villageName);
            this.userForm.controls['cellCode'].setValue(data.data.parcelLocation.cell.cellCode);
            this.userForm.controls['cellName'].setValue(data.data.parcelLocation.cell.cellName);
            this.userForm.controls['sectorCode'].setValue(data.data.parcelLocation.sector.sectorCode);
            this.userForm.controls['sectorName'].setValue(data.data.parcelLocation.sector.sectorName);
            this.userForm.controls['districtCode'].setValue(data.data.parcelLocation.district.districtCode);
            this.userForm.controls['districtName'].setValue(data.data.parcelLocation.district.districtName);
            this.userForm.controls['provinceCode'].setValue(data.data.parcelLocation.province.provinceCode);
            this.userForm.controls['provinceName'].setValue(data.data.parcelLocation.province.provinceName);
            this.userForm.controls['ownerFullName'].setValue(data.data.owners[0].fullName);
            this.userForm.controls['ownerIdNo'].setValue(data.data.owners[0].idNo);
            this.getAgencyByDistrictCode(data.data.parcelLocation.district.districtCode);
          }
        }
        // }
      )

  }

  getAgencyByDistrictCode(districtCode: any) {
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + districtCode)
      .subscribe(
        data => {
          this.userForm.controls['agencyId'].setValue(data.id);
        },
        error => { }
      )
  }


  initializeDataFromLandIntergration() {
    this.userForm = this.formBuilder.group({
      // from land api
      upi: [this.upiInfo.upi],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      // from land api
      centralCoordinateX: [this.upiInfo.centralCoordinate.x],
      centralCoordinateY: [this.upiInfo.centralCoordinate.y],
      villageName: [this.upiInfo.parcelLocation.village.villageName],
      cellCode: [this.upiInfo.parcelLocation.cell.cellCode],
      sectorCode: [this.upiInfo.parcelLocation.sector.sectorCode],
      districtCode: [this.upiInfo.parcelLocation.district.districtCode],
      provinceCode: [this.upiInfo.parcelLocation.province.provinceCode],
      cellName: [this.upiInfo.parcelLocation.cell.cellName],
      sectorName: [this.upiInfo.parcelLocation.sector.sectorName],
      districtName: [this.upiInfo.parcelLocation.district.districtName],
      provinceName: [this.upiInfo.parcelLocation.province.provinceName],
      selectedUse: [this.upiInfo.applicationName],
      selectedCategoryUse: [this.upiInfo.seletedCategoryUse],

      // fill
      projectName: [""],
      projectDescription: [""], // from first page
      plotSize: [this.upiInfo.size, [Validators.required]],
      originalPlotSize: [this.upiInfo.size],
      userId: [this.currentUser.userId],
      agencyId: [""],   //this is for cok
      // technologySurveyId: [""],
      projectStatusId: [""],
      isFromOldSystem: [''],


      // send to application
      // buildUpArea: ["", [Validators.required]],
      // numberOfFloor: ["", [Validators.required]],    // help to calculate amount
      // grossFloorArea: ["", [Validators.required]],
      // numberOfParkingSpace: [""],
      // priceOfDwellingUnitRwf: ["", [Validators.required]],
      // capacityInformation: ["", [Validators.required]],
      // numberOfDwellingUnits: ["", [Validators.required]],
      // DescriptionOfOperation: [""],
      // percentageSpaceUse: ["", [Validators.required]],
      // waterConsumption: ["", [Validators.required]],
      // electricityConsumption: ["", [Validators.required]],
      // DistanceToTheNearestLandIn: ["", [Validators.required]],
      // ProjectCostInUSD: ["", [Validators.required]],
      // ProjectCostInRwf: ["", [Validators.required]],
      // send to application


    }
      // , { validator: this.buildUpAreaValidator }
    );

    this.getProjectStatus();
  }



  findNationalIdIsInUpiOwnersList(owners: any) {
    //  if (data.data.owners[0].idNo && (data.data.owners[0].idNo.replace(/\s+/g, "")) !== (this.currentUser.data.user.nationalId.replace(/\s+/g, "")
    // )
    let value = owners.filter((x: any) => x.idNo.replace(/\s+/g, "") === this.currentUser.data.user.nationalId.replace(/\s+/g, ""));
    if (value.length > 0) {
      return true;
    } else {
      return false;
    }
  }




  getProjectStatus() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus/code/search?search=' + VALUE.PROJECT_STATUS_CREATED_CODE)
      .subscribe(
        data => { this.userForm.controls['projectStatusId'].setValue(data.items[0].id); },
        error => { }
      )
  }









  onPrev(): void {
    this.stepperService.prevStep();
  }

  onNext(): void {
    this.stepperService.nextStep();
  }

  onSubmit() {
    if (this.userForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill username and password', "bottom", "center")
      return;
    } else {
      this.userForm.controls['isFromOldSystem'].setValue(this.upiInfo?.isFromOldSystem);
      this.userForm.controls['originalPlotSize'].setValue(+this.userForm.value.plotSize);
      this.userForm.value.plotSize = this.userForm.controls['plotSize'].setValue(this.userForm.value.plotSize);
      if (this.userForm.value.isFromOldSystem) {
        this.checkOldSystemAgencyIdFor();
      } else {
        // Save project detail, Means this project does not come from Old BPMIS
        this.confirmSave();
      }
    }
  }



  checkOldSystemAgencyIdFor() {
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistrict')
      .subscribe(
        data => {
          if (data.length > 0) {
            let angecyDistrict = data.find((x: any) => x.district.name.toLowerCase() === this.userForm.value.districtName.toLowerCase());
            if (angecyDistrict) {
              this.userForm.controls['agencyId'].setValue(angecyDistrict.agency.id);

              // save now
              this.confirmSave();
            } else {
              this.utilService.showNotification(NOTIFICATION_COLOR.error, this.userForm.value.districtName + " does not have agency, please merge agency with district", "top", "right");
            }
          } else {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "No agency districts found", "top", "right");
          }

        },
        error => { }
      )
  }



  confirmSave() {
    this.submitted = true;
    this.applicationService.saveWithPath(this.userForm.value, environment.applicationUrl + 'application/project')
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your project saved successfully", "bottom", "center");
          if (this.upiInfo?.isFromOldSystem) {
            // this.router.navigate(['/account/application/other-application/', this.userForm.value.upi]);
            this.router.navigate(['/account/application/other-application/', data.id]);
          } else {
            this.backToParent.emit(data);
          }
          this.submitted = false;
        }, error => {
          this.submitted = false;
          if (error?.error === 'UPI already exists') {
            this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.userForm.value.upi)
              .subscribe(
                dataExist => {
                  // this.router.navigate(['/account/application/new-application-development-detail/', dataExist.items[0].id]);
                  this.router.navigate(['/account/application/other-application/', dataExist.items[0].id]);
                }
              )
          }
        }
      )
  }


  initialiaze() {
    this.userForm = this.formBuilder.group({
      //   // from land api
      upi: [""],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      // from land api
      centralCoordinateX: [""],
      centralCoordinateY: [""],
      villageName: [""],
      cellCode: [""],
      sectorCode: [""],
      districtCode: [""],
      provinceCode: [""],
      cellName: [""],
      sectorName: [""],
      districtName: [""],
      provinceName: [""],
      selectedUse: [""],
      selectedCategoryUse: [""],

      //   // fill
      projectName: [""],
      projectDescription: [""], // from first page
      plotSize: ["", [Validators.required]],
      originalPlotSize: [""],

      userId: [""],                                   // catched from user token
      agencyId: [""],   //this is for cok                               // back-end to handle this

      projectStatusId: [""]
    }
      // , { validator: this.buildUpAreaValidator }
    );
  }
}
