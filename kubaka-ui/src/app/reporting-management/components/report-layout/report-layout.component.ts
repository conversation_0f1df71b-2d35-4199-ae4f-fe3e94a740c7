import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService, UserData } from 'src/app/authentication-services/session.service';
import { UtilService } from 'src/app/shared/services/util.service';

@Component({
  selector: 'app-report-layout',
  templateUrl: './report-layout.component.html',
  styleUrls: ['./report-layout.component.scss']
})
export class ReportLayoutComponent {
  currentUser: any;
  permissions: any;
  isRHAReviewer = false;

  constructor(
    private router: Router,
    private sessionService: SessionService,
    private utilService: UtilService,
    private applicationService: ApplicationService
  ) {

    if (this.sessionService.getSession() && this.sessionService.getSession().data && this.sessionService.getSession().data.user) {
      this.currentUser = this.sessionService.getSession();
      this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    }

    if (this.currentUser.data.user.userType.name === 'Engineer') {
      this.currentUser.permissions = UserData.find((x: any) => x.name === this.currentUser.data.user.userType.name)?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    } else if (this.currentUser.data.user.userType.name === 'Architect') {
      this.currentUser.permissions = UserData.find((x: any) => x.name === this.currentUser.data.user.userType.name)?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    } else {
      this.currentUser.permissions = UserData.find((x: any) => x.name === this.currentUser.data.user.role.name)?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    }
  }
  ngOnInit() {
   if (this.currentUser.data.user.agency.code === 'RHA' && this.currentUser.data.user.role.code !== 'DRCT') {
        this.isRHAReviewer = true;
      }
    }
}
