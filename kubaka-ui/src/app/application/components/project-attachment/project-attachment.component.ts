import { Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../services/application.service';
import { StepperService } from '../../services/stepper.service';

@Component({
  selector: 'app-project-attachment',
  templateUrl: './project-attachment.component.html',
  styleUrls: ['./project-attachment.component.scss']
})
export class ProjectAttachmentComponent {
  documentRequired: any[] = [];
  reservedDocumentRequired: any[] = [];
  userForm!: UntypedFormGroup;
  submitted: boolean = false;
  currentUser: any;
  isReadyToSubmit!: boolean;
  uploadedDocuments: any[] = [];
  fileData: any = {};
  isChecked: boolean = false;
  deleteData: any = {};
  existingApplication: any = {}
  upiInfo: any = {};
  formAccess: any = {};
  fileSubmit: boolean = false;
  pageController: any = {};
  isAssociated: boolean = false;
  outputData: any = {};

  allowToSubmit() {
    this.isChecked = !this.isChecked;
  }


  closeAssociatedPopup() {
    this.modalService.dismissAll();
    this.isAssociated = !this.isAssociated;

  }
  checkAssociated(event: any) {
    this.outputData.applicationId = this.userForm.value.applicationId;
    this.isAssociated = !this.isAssociated;
    this.openModal(event, 'md');
    // this.modalService.

    // (click)="openModal(associatedUpiContent, 'md')"
  }


  constructor(
    private stepperService: StepperService,
    private applicationService: ApplicationService,
    private utilService: UtilService,
    private formBuilder: UntypedFormBuilder,
    private route: ActivatedRoute,
    private sessionService: SessionService,
    private modalService: NgbModal,
    private appConfig: AppConfig,
    private router: Router) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;


    // route.params.subscribe((params: any) => {
    //   this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
    //     .subscribe(
    //       data => {
    //         this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + data.items[0].id)
    //           .subscribe(
    //             documents => {
    //               this.reservedDocumentRequired = documents;
    //             }
    //           )
    //       }
    //     )
    // })



    this.applicationService.findAllWithPath(environment.applicationUrl +
      'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe(
        data => {
          this.userForm.controls['usedPermitType'].setValue(data.items[0].id);
          this.getProject();
        },
        error => { }
      )


  }




  getProject() {
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_NEW_INFO) as any);
    // test
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=5/07/10/04/5711')
    // test



    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.upiInfo.upi)
      .subscribe(
        data => {
          this.userForm.controls['projectId'].setValue(data.items[0].id);
          // Call exiting application by project id and permit type
          this.getExistingApplicationAndPermitType();
          // Call exiting application by project id and permit type
        }
      )
  }



  getExistingApplicationAndPermitType() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.userForm.value.projectId + '/permit-type/' + this.userForm.value.usedPermitType)
      .subscribe(
        data => {
          if (data.length > 0) {
            // if data present means that application already existis
            this.formAccess.isApplicationExists = true;
            this.existingApplication = data[0];
            this.userForm.controls['permitTypeId'].setValue(this.existingApplication.permitTypes.id);
            this.userForm.controls['categoryTypeId'].setValue(this.existingApplication.categoryTypes.id);
            this.userForm.controls['buildTypeId'].setValue(this.existingApplication.buildTypes.id);
            this.userForm.controls['agencyId'].setValue(this.existingApplication.agencyId);
            this.userForm.controls['permitTypeCode'].setValue(this.existingApplication.permitTypeCode);
            this.userForm.controls['agencyCode'].setValue(this.existingApplication.agencyCode);
            this.userForm.controls['applicationId'].setValue(this.existingApplication.id);
            // load document uploaded for current application
            // this.loadUploadedDocuments();
            this.loadRequiredDocuments()
            // load document uploaded for current application
          }
        }
      )
  }


  loadRequiredDocuments() {
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + data.items[0].id)
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + this.userForm.value.permitTypeId + '/category/' + this.userForm.value.categoryTypeId)
      .subscribe(
        documents => {
          this.reservedDocumentRequired = documents;
          this.loadUploadedDocuments();
        }
      )
  }

  get form() {
    return this.userForm.controls;
  }

  ngOnInit() {
    this.userForm = this.formBuilder.group({
      documentRequiredId: ["", [Validators.required]],
      applicationId: [""],
      document: [""],
      userId: [this.currentUser.userId],
      applicationStatusId: [""],
      usedPermitType: [""],



      projectId: [""],
      permitTypeId: [""],
      categoryTypeId: [""],
      buildTypeId: [""],
      agencyId: [""],
      permitTypeCode: [""],
      agencyCode: [""],
    });
  }


  loadUploadedDocuments() {
    // test
    // this.applicationService.findAllWithPath(environment.documentUrl + 'DocMgt/afc0834b-215a-4a3e-966f-e4cd5480f82d')
    // test
    this.applicationService.findAllWithPath(environment.documentUrl + 'DocMgt/' + this.userForm.value.applicationId)
      .subscribe(
        documents => {
          this.uploadedDocuments = documents;

          if (this.uploadedDocuments.length === this.reservedDocumentRequired.length) {
            this.isReadyToSubmit = true;
          }

          // test
          this.documentRequired = this.reservedDocumentRequired;
          this.documentRequired.forEach((document: any) => {
            let findElement = this.uploadedDocuments.find((x: any) => x.requiredDocumentId === document.id);
            if (findElement) {
              document.uploaded = findElement
            }
          });
          // test

          // The below commented it is an option 1 used before
          // =================================================
          // this.documentRequired = [];
          // this.reservedDocumentRequired.forEach(document => {
          //   if (!this.uploadedDocuments.some(element => element.requiredDocumentId === document.id)) {
          //     this.documentRequired.push(document);
          //   }
          // });
          // The below commented it is an option 1 used before
          // =================================================
        }
      )
  }




  onPrev(): void {
    this.stepperService.prevStep();
  }

  onNext(): void {
    this.submitApplication();
  }

  submitApplication() {

    this.submitted = true;
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + VALUE.APPLICATION_STATUS_SUBMIT_CODE)
      .subscribe(
        data => {
          this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
          this.confirmSubmit();
        }, error => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
        });


    // })
  }


  confirmSubmit() {
    // let dataToSubmit = {
    //   "projectId": "string",
    //   "userId": "string",
    //   "permitTypeId": "string",
    //   "categoryTypeId": "string",
    //   "buildTypeId": "string",
    //   "agencyId": "string",
    //   "applicationStatusId": "string",
    //   "permitTypeCode": "string",
    //   "agencyCode": "string"
    // }
    this.applicationService.updateAssetWithoutParams(this.userForm.value, environment.applicationUrl + 'application/application/submit/' + this.userForm.value.applicationId)
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application submitted successfully", "bottom", "center");
          this.submitted = false;
          this.modalService.dismissAll();
          this.router.navigate(['/account/application/applications']);
        }, error => {
          this.submitted = false;
        }
      )
  }


  isNextButtonDisabled(): boolean {
    return this.stepperService.getCurrentStep() === 5;
  }


  getFileDetails(e: any, docInfo: any) {
    const fileInput: any = e.target as HTMLInputElement;
    this.pageController = docInfo.name
    if (docInfo.code === '1') {
      // Excel File to upload
      if (this.utilService.getCheckFileExtension(e).toLowerCase() === 'pdf') {
        this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please upload BOQ (Bill of Quantities file)', "bottom", "center");

      } else {
        this.fileData.fileName = e.target.files[0].name;
        this.fileData.fileNameDisplay = e.target.files[0].name;
        this.userForm.controls['documentRequiredId'].setValue(docInfo.id);
        this.handleFileSelected(e);
        this.fileData.myFiles = [];
        for (var i = 0; i < e.target.files.length; i++) {
          this.fileData.myFiles.push(e.target.files[i]);
        }
        this.fileData.file = e.target.files;
        this.onSubmit();
      }

      // Excel File to upload
    } else {
      this.fileData.fileName = e.target.files[0].name;
      this.fileData.fileNameDisplay = e.target.files[0].name;
      this.userForm.controls['documentRequiredId'].setValue(docInfo.id);
      this.handleFileSelected(e);
      if (this.fileData.fileType !== 'excel' && this.utilService.getCheckFileExtension(e).toLowerCase() === 'pdf') {
        this.fileData.myFiles = [];
        for (var i = 0; i < e.target.files.length; i++) {
          this.fileData.myFiles.push(e.target.files[i]);
        }
        if (this.fileData.size > this.utilService.fileSize) {
          this.utilService.showNotification(
            NOTIFICATION_COLOR.error,
            "Error: " + 'File will not be saved because it is too big, try 100MB of size',
            "bottom",
            "center"
          );
          fileInput.value = ''; // Reset input
          return;
        } else {
          this.fileData.file = e.target.files;
          this.onSubmit();
        }
      } else {
        this.utilService.showNotification(
          NOTIFICATION_COLOR.error,
          "Error: " + 'PDF File allowed only',
          "bottom",
          "center"
        );

        setTimeout(() => {
          fileInput.value = '';
        }, 0);
      }
    }

  }


  onSubmit() {
    // if (this.userForm.invalid) {
    //   this.submitted = false;
    //   this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill username and password', "bottom", "center")
    //   return;
    // } else {
    let formData = new FormData();
    const fileList: FileList = this.fileData.file;
    if (fileList && fileList[0]) {
      const file: File = fileList[0];
      this.fileData.fileNameDisplay = file.name
      formData.append('file', file, file.name);
    }
    formData.append('requiredDocumentId', this.userForm.value.documentRequiredId);
    formData.append('applicationId', this.userForm.value.applicationId);
    this.fileSubmit = true;
    this.applicationService.saveAssetWithPathFormData(formData, environment.documentUrl + 'DocMgt/upload')
      .subscribe(
        data => {
          this.fileSubmit = false;
          // this.utilService.showFewMinutesNotification(NOTIFICATION_COLOR.success, "Your file uploed successfully", "bottom", "center");
          this.loadUploadedDocuments();

        }, err => { this.fileSubmit = false; }
      )
    // }
  }



  async handleFileSelected(event: any) {
    const size = event.srcElement.files[0].size;
    if (size < 1000 * 1000 * 1000) {
      this.fileData.size = size / 1000 / 1000;
      this.fileData.unit = "mb";
    }
  }


  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  openDeleteModal(content: any, sizeParams: any, value: any) {
    this.deleteData = value;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  cancel() {
    this.modalService.dismissAll();
  }




  deleteFile() {
    this.applicationService.deleteWithPathNoId(environment.documentUrl + "DocMgt/" + this.deleteData.id)
      .subscribe(
        data => {
          this.cancel();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Document removed successfully", "bottom", "center");
          this.loadUploadedDocuments();
        },
        error => { }
      )
  }
}
