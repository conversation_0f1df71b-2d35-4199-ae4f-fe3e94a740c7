import { AbstractEntity } from '@app/common';
import { Entity, Column, OneToMany, ManyToOne } from 'typeorm';
import { AgencyDistrict } from '../agency/agencyDistrict.entity';
import { Province } from './province.entity';
import { Sector } from './sector.entity';

@Entity()
export class District extends AbstractEntity<District> {
  @Column()
  name: string;

  @Column()
  code: string;

  @OneToMany(() => AgencyDistrict, (agencyDistrict) => agencyDistrict.district)
  agencies: AgencyDistrict[];

  @ManyToOne(() => Province, (province) => province.districts)
  provinces: Province;

  @OneToMany(() => Sector, (sector) => sector.district)
  sectors: Sector[];
}
