<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"otherApplicationDocument.associatedUpiTitle" | translate }}</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
            (backToParent)="closeAssociatedPopup()"></app-associated-upi-application>
    </div>
</ng-template>
<div class="stepper-content" style="margin-top: 2rem;">
    <div class="step-panel">
        <form [formGroup]="userForm">
            <div class="step-panel_header">
                <h2>{{"otherApplicationDocument.projectAttachmentTitle" | translate }}</h2>
            </div>
            <!-- Required Documents list     -->
            <div class="kbk-updloadtable">
                <ul class="updload-list">
                    <div class="updload-list_header">
                        <li class="updload-list_item">
                            <div class="updload-list_item-dt">
                                <span class="text">{{"otherApplicationDocument.requiredTitle" | translate }} <a
                                        href="assets/files/BOQ-OF-REVIEW.xlsx">
                                        {{"otherApplicationDocument.requiredBoqLink" | translate }}</a></span>
                                <span class="kbk-warn"><img
                                        src="assets/ikons/colored/ikon-warn-o.svg">{{"otherApplicationDocument.requiredWarning"
                                    | translate }}</span>
                            </div>
                            <div class="updload-list_item-dt">
                                <span class="text">{{"otherApplicationDocument.uploadedTitle" | translate }}</span>
                            </div>
                        </li>
                    </div>
                    <div class="updload-list_body">
                        <li class="updload-list_item" *ngFor="let dr of documentRequired">
                            <div class="updload-list_item-dt">
                                <span class="text">{{dr.name}}</span>
                                <label *ngIf="!fileSubmit" class="kbk-btn kbk-btn-sm kbk-btn-sec">
                                    <input type="file" multiple (change)="getFileDetails($event, dr)" name="document"
                                        style="display: none;"> {{"otherApplicationDocument.upload" | translate }}
                                </label>
                                <label *ngIf="fileSubmit" class="kbk-btn kbk-btn-sm kbk-btn-sec">
                                    <input type="file" style="display: none;"> {{"otherApplicationDocument.wait" |
                                    translate }} </label>
                            </div>
                            <div class="updload-list_item-dt">
                                <div *ngIf="dr.uploaded">
                                    <span class="icon">
                                        <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                                    </span>
                                    <span class="text">{{dr.uploaded?.fileName}}</span>
                                </div>
                                <div class="nofile" *ngIf="!dr.uploaded">
                                    <span class="icon">
                                        <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                                    </span>
                                    <span class=" text">{{"otherApplicationDocument.nofile" | translate }}</span>
                                </div>
                                <button *ngIf="dr.uploaded && this.inputData.applicationSaved.applicationStatus.code !== 'CTFD'
                                    && this.inputData.applicationSaved.applicationStatus.code !== 'SUB'"
                                    class="kbk-btn kbk-btn-sm kbk-btn-error" type="button" data-bs-target="#showModal"
                                    (click)="openDeleteModal(deleteModel, 'md', dr.uploaded)">{{"otherApplicationDocument.delete"
                                    | translate }}</button>
                            </div>
                        </li>
                        <app-additional-upload-file *ngIf="inputData?.applicationSaved"
                            [inputData]="inputData?.applicationSaved">
                        </app-additional-upload-file>
                    </div>
                </ul>
            </div>
            <div class="step-panel_footer">
                <button (click)="onPrev()">{{"otherApplicationDocument.previous" | translate }}</button>
                <button class="kbk-btn kbk-btn-main" type="button" *ngIf="submitted"> {{"otherApplicationDocument.wait"
                    | translate }} </button>
                <!-- // The below commented it is an option 1 used before -->
                <!-- // ================================================= -->
                <!-- <button class="kbk-btn kbk-btn-main" type="submit" *ngIf="!isReadyToSubmit && !submitted"> Save the document </button> -->
                <!-- // The below commented it is an option 1 used before -->
                <!-- // ================================================= -->
                <button class="kbk-btn kbk-btn-main" data-bs-toggle="modal" type="button" data-bs-target="#showModal"
                    *ngIf="isReadyToSubmit && !submitted && this.inputData.applicationSaved.applicationStatus.code !== 'CTFD'
                    && this.inputData.applicationSaved.applicationStatus.code !== 'SUB'"
                    (click)="openModal(isAcceptThatTheInformationProvided, 'md')">
                    {{this.inputData.applicationSaved.applicationStatus.code === 'UNCRN'
                    ? ('otherApplicationDocument.resubmit' | translate) : ('otherApplicationDocument.submit' |
                    translate)' }} </button>
            </div>
        </form>
    </div>
</div>
<!-- Submit application modal -->
<ng-template #isAcceptThatTheInformationProvided role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel">{{"otherApplicationDocument.confirm" | translate }} </h2>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="step-panel_body">
        <div class="form-incheckbox">
            <label class="checkbox">
                <input type="checkbox" id="check" [checked]="isChecked" (click)="allowToSubmit()" />
                <span class="checkbox_box"></span>
                <span class="checkbox_txt">{{"otherApplicationDocument.certificationText" | translate }}</span>
            </label>
        </div>
    </div>
    <div class="modol-content">
        <div class="kbk-x-c sp-sm mt-md">
            <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> {{"otherApplicationDocument.cancel" | translate }} </button>
            <button *ngIf="this.inputData.applicationSaved.applicationStatus.code !== 'CTFD'
            && this.inputData.applicationSaved.applicationStatus.code !== 'SUB'" class="kbk-btn kbk-btn-main"
                type="button" [disabled]="!isChecked" (click)="onNext()">


                {{this.inputData.applicationSaved.applicationStatus.code === 'UNCRN' ? ('otherApplicationDocument.resubmit' | translate) : ('otherApplicationDocument.submit' | translate) }}



            </button>
        </div>
    </div>
</ng-template>
<!-- Submit application modal -->
<!-- Delete file modal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">{{"otherApplicationDocument.deleteFileTitle" | translate }} {{deleteData.fileName}} ?</h2>
            <p>{{"otherApplicationDocument.deleteFileBodyA" | translate }} {{deleteData.name}} {{"otherApplicationDocument.deleteFileBodyB" | translate }}</p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <!-- <h4>{{"otherApplicationDocument.deleteFileTitle" | translate }} {{deleteData.fileName}} ?</h4> -->
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                    (click)="modal.close('Close click')" id="deleteRecord-close">{{"otherApplicationDocument.close" | translate }}</button>
                <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="deleteFile()">{{"otherApplicationDocument.confirmDelete" | translate }}</button>
            </div>
        </div>
    </div>
</ng-template>
<!-- Delete file modal -->