import { Component, Input } from '@angular/core';
import { ApplicationService } from 'src/app/application/services/application.service';
import { AuthService } from 'src/app/auth-pages/services/auth.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-verification-reviewer',
  templateUrl: './verification-reviewer.component.html',
  styleUrls: ['./verification-reviewer.component.scss']
})
export class VerificationReviewerComponent {
  @Input() inputData: any;
  submitted: boolean = false;

  displayErrorMessage: any = {};


  responseData: any = {};
  constructor(
    private authService: AuthService,
    private applicationService: ApplicationService,
    private utilService: UtilService
  ) { }

  ngOnInit() {

    if (this.inputData.verifyType === 'NID') {
      this.checkNida();
    }
    if (this.inputData.verifyType === 'RRA') {
      this.checkRRA();
    }
    if (this.inputData.verifyType === 'EIA') {
      this.checkEIA()
    }

    if (this.inputData.verifyType === 'LIS') {
      this.checkLIS()
    }
  }



  checkLIS() {
    this.responseData = {};
    this.submitted = true;
    this.applicationService.findAllWithPath(environment.landAPI + this.inputData.projects.upi)
      // this.applicationService.findAllWithPath(environment.landAPI + '5/07/01/05/1662')
      .subscribe(
        data => {
          if (data && data.data && data.data.upi) {
            this.responseData = data.data;
            this.responseData.isLis = true;
            this.submitted = false;
          } else {
            this.responseData.isLis = false;
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error,
              "UPI not found in LIS", "bottom", "center");
          }
        },
        error => { this.submitted = false; }
      )
  }

  checkNida() {
    this.responseData = {};
    this.submitted = true;
    this.authService.saveAssetWithPath(null, environment.nidaUrl + this.inputData.userDetails.nationalId.replace(/\s+/g, ""))
      .subscribe(
        data => {
          if (data && data.error) {
            //verified
            this.responseData.errorMessage = data.error;
            this.responseData.isNida = false;
            this.submitted = false;
          } else {
            //verified
            this.responseData.errorMessage = false;


            this.inputData.names = data.response.surnames + ' ' + data.response.foreName;
            this.inputData.idNumber = data.response.documentNumber;
            this.inputData.dateOfBirth = data.response.dateOfBirth;
            this.inputData.sex = data.response.sex;
            this.inputData.maritalStatus = data.response.maritalStatus;
            this.inputData.district = data.response.district;
            this.inputData.photo = data.response.photo;
            this.responseData.errorMessage = '';
            this.responseData.isNida = true;
            this.submitted = false;
          }

        }, error => {
          this.submitted = false;

        })
  }

  checkRRA() {
    this.responseData = {};
    this.submitted = true;
    this.applicationService.findAllWithPath('https://apis.kubaka.gov.rw/apis/checkPropertyTaxTesting?upi=' + this.inputData.projects.upi)
    // this.applicationService.findAllWithPath(environment.integrationUrl + 'integration/rra?upi=' + this.inputData.projects.upi)
      .subscribe(
        data => {
          if (data && data.ResponseObject && (data.ResponseObject.StatusCode === 1 || data.ResponseObject.StatusCode === 4 )) {
          // if (data && data.ResponseObject && data.ResponseObject.StatusCode === 1) {
            // verified
            this.responseData.isRRA = true;
            this.submitted = false;
          } else {
            this.submitted = false;
            this.displayErrorMessage = 'This plot has unpaid taxes';
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "This plot has some unpaid taxes", "bottom", "center");
          }
        }, error => {
          this.displayErrorMessage = 'RRA Server is down'
          this.submitted = false;
        }
      )
  }



  checkEIA() {
    this.responseData = {};
    let dataToSubmit = {
      "certificateNumber": this.inputData
    }

    this.confirmToSearch(dataToSubmit)
  }


  verifyCertificate() {
    let dataToSubmit = {
      "certificateNumber": this.responseData.certificateNumber
    }

    this.confirmToSearch(dataToSubmit)
  }


  confirmToSearch(dataToSubmit: any) {
    // http://197.243.12.45:3007/integration/eia?upi=5%2F07%2F05%2F03%2F3352

    this.applicationService.saveAssetWithPathFormData(dataToSubmit, environment.integrationUrl + 'integration/eia?upi=' + this.inputData.projects.upi)
      .subscribe(
        data => {
          if (data.data && data.data.project_reference_number) {
            this.responseData.certificateNumber = data.data.project_reference_number;
            this.responseData.name = data.data.rep_name;
            this.responseData.upi = data.data.project_plot_number;
            this.responseData.isEIA = true;
          } else {
            this.responseData.isEIA = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, `Data not found.
        Please submit your request to RDB for an Environmental Impact Access certificate`, "bottom", "center");
          }
          this.submitted = false;
        }, error => {
          this.submitted = false;
        }
      )
    // this.applicationService.saveAssetWithPathFormData(dataToSubmit, environment.eia + '?certificateNumber=' + this.inputData.certificateNumber)
    // .subscribe(
    //   data => {
    //     if (data.data && data.data.document_status === 'Valid') {
    //       // VERIFIED
    //       this.responseData.isEIA = true;
    //     } else {
    //       this.utilService.showNotification(NOTIFICATION_COLOR.error, `Data not found.
    //     Please submit your request to RDB for an Environmental Impact Access certificate`, "bottom", "center");
    //     }
    //     this.submitted = false;
    //   }, error => {
    //     this.submitted = false;
    //   }
    // )
  }

}
