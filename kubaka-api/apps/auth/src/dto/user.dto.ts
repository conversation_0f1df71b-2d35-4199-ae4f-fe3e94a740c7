import {
  IsDateString,
  // IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MinLength,
} from 'class-validator';
import { RoleDto } from './user-management.dto';
import { ApiProperty } from '@nestjs/swagger';

const passwordRegEx = /((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;

// export const phoneNumberRegex = new RegExp(/^2507[2389]\d{7}/);

export class CreateUserDto {
  @ApiProperty({ description: 'The field first name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'The field last name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  // @IsEmail(null, { message: 'Please provide valid Email.' })
  email: string;

  @ApiProperty()
  @IsOptional()
  isEmailValid: boolean;

  // @Matches(phoneNumberRegex, {
  //   message: `Phone number must be registered in Rwanda`,
  // })
  @ApiProperty({ description: 'The field phone number is required' })
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty()
  @IsOptional()
  nationalId: string;

  @ApiProperty()
  @IsOptional()
  passport: string;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  dateOfBirth: Date;

  @ApiProperty()
  @IsOptional()
  @IsDateString()
  verifiedAt: Date;

  // @ApiProperty()
  // @IsOptional()
  // verified: boolean;

  @ApiProperty()
  @IsOptional()
  licenceIdArch: string;

  @ApiProperty()
  @IsOptional()
  licenceIdEng: string;

  @ApiProperty()
  @IsEnum(['Male', 'Female'])
  gender: string;

  @ApiProperty({ description: 'The field password is required' })
  @IsNotEmpty()
  @Matches(passwordRegEx, {
    message: `Password must contain Minimum 8 and maximum 20 characters, 
      at least one uppercase letter, 
      one lowercase letter, 
      one number and 
      one special character`,
  })
  password: string;

  // @ApiProperty()
  // @IsNotEmpty()
  // role: RoleDto;

  @IsNotEmpty({ message: 'The field roleId cannot be empty' })
  @ApiProperty()
  roleId: string;

  @IsNotEmpty({ message: 'The field userTypeId cannot be empty' })
  @ApiProperty()
  userTypeId: string;

  @IsOptional()
  @ApiProperty()
  civilStatusId: string;

  // @IsOptional()
  // @ApiProperty()
  // agencyId: string;
}

export class UpdateAnyUserDto {
  @ApiProperty({ description: 'The field first name is required' })
  @IsOptional()
  firstName: string;

  @ApiProperty({ description: 'The field last name is required' })
  @IsOptional()
  lastName: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsOptional()
  isEmailValid: boolean;

  @ApiProperty({ description: 'The field phone number is required' })
  @IsOptional()
  phoneNumber: string;

  @ApiProperty()
  @IsOptional()
  nationalId: string;

  @ApiProperty()
  @IsOptional()
  passport: string;

  @ApiProperty()
  @IsOptional()
  dateOfBirth: string;

  @ApiProperty()
  @IsOptional()
  verifiedAt: string;

  @ApiProperty()
  @IsOptional()
  licenceIdArch: string;

  @ApiProperty()
  @IsOptional()
  licenceIdEng: string;

  @ApiProperty()
  @IsOptional()
  @IsEnum(['Male', 'Female'])
  gender: string;

  @IsNotEmpty({ message: 'The field roleId cannot be empty' })
  @ApiProperty()
  roleId: string;

  @IsNotEmpty({ message: 'The field userTypeId cannot be empty' })
  @ApiProperty()
  userTypeId: string;

  @IsOptional()
  @ApiProperty()
  civilStatusId: string;

  @IsOptional()
  @ApiProperty()
  agencyId: string;

  @IsOptional()
  @ApiProperty()
  approvalLevelId: string;
}

// export class UpdateAnyUserDto {
//   @ApiProperty({ description: 'The field first name is required' })
//   @IsOptional()
//   firstName: string;

//   @ApiProperty({ description: 'The field last name is required' })
//   @IsOptional()
//   lastName: string;

//   @ApiProperty({ description: 'The field email is required' })
//   @IsOptional()
//   email: string;

//   @ApiProperty()
//   @IsOptional()
//   isEmailValid: boolean;

//   @ApiProperty({ description: 'The field phone number is required' })
//   @IsOptional()
//   phoneNumber: string;

//   @ApiProperty()
//   @IsOptional()
//   nationalId: string;

//   @ApiProperty()
//   @IsOptional()
//   passport: string;

//   @ApiProperty()
//   @IsOptional()
//   dateOfBirth: string;

//   @ApiProperty()
//   @IsOptional()
//   verifiedAt: string;

//   @ApiProperty()
//   @IsOptional()
//   licenceIdArch: string;

//   @ApiProperty()
//   @IsOptional()
//   licenceIdEng: string;

//   @ApiProperty()
//   @IsOptional()
//   @IsEnum(['Male', 'Female'])
//   gender: string;

//   @IsNotEmpty({ message: 'The field roleId cannot be empty' })
//   @ApiProperty()
//   roleId: string;

//   @IsNotEmpty({ message: 'The field userTypeId cannot be empty' })
//   @ApiProperty()
//   userTypeId: string;

//   @IsOptional()
//   @ApiProperty()
//   civilStatusId: string;
// }

export class CreateStaffDto {
  @ApiProperty({ description: 'The field first name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'The field last name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  email: string;

  @ApiProperty()
  @IsOptional()
  isEmailValid: boolean;

  @ApiProperty({ description: 'The field phone number is required' })
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty()
  @IsEnum(['Male', 'Female'])
  gender: string;

  @ApiProperty()
  @IsOptional()
  password: string;

  @IsNotEmpty({ message: 'The field role cannot be empty' })
  @ApiProperty()
  roleId: string;

  @IsNotEmpty()
  @ApiProperty({ description: 'The field userType is required' })
  userTypeId: string;

  @IsNotEmpty()
  @ApiProperty({ description: 'The field agency is required' })
  agencyId: string;

  @IsOptional()
  @ApiProperty({ description: 'The field sector' })
  sectorId: string;

  @IsNotEmpty()
  @ApiProperty({ description: 'The field approval Level is required' })
  approvalLevelId: string;
}

export class UpdateUserDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsString()
  @MinLength(2, { message: 'Name must have at least 2 characters.' })
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  phoneNumber: number;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  nationalId: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  passport: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  @IsDateString()
  dateOfBirth: Date;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  @IsDateString()
  verifiedAt: Date;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  verified: boolean;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  licenceIdArch: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsOptional()
  licenceIdEng: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsEnum(['Male', 'Female'])
  gender: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty()
  role: RoleDto;
}

export class UserProfileDto {
  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The field file is required' })
  @IsString()
  @IsNotEmpty()
  profilePicture: string;
}

export class UpdateUserProfileDto {
  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'The field email is required' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'The field phoneNumber is required' })
  @IsNotEmpty()
  phoneNumber: string;
}

export class UserDto {
  constructor(object: any) {
    this.firstName = object.firstName;
    this.lastName = object.lastName;
    this.email = object.email;
    this.phoneNumber = object.phoneNumber;
    this.nationalId = object.nationalId;
    this.approvalLevelId = object.approvalLevelId;
    this.role = object.role;
    this.agency = object.agency;
    this.sector = object.sector;
    this.isPasswordExpired = object.sector;
    this.isStaffPasswordChanged = object.sector;
  }
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phoneNumber: string;
  readonly nationalId: string;
  readonly isPasswordExpired: string;
  readonly isStaffPasswordChanged: string;
  readonly approvalLevelId: string;
  readonly role: string;
  readonly agency: string;
  readonly sector: string;
}

export class GetUserDto {
  constructor(object: any) {
    this.firstName = object.firstName;
    this.lastName = object.lastName;
    this.email = object.email;
    this.phoneNumber = object.phoneNumber;
    this.nationalId = object.nationalId;
    this.isPasswordExpired = object.isPasswordExpired;
    this.isStaffPasswordChanged = object.isStaffPasswordChanged;
    this.approvalLevelId = object.approvalLevelId;
    this.role = object.role;
    this.userType = object.userType;
    this.agency = object.agency;
    this.sector = object.sector;
  }
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phoneNumber: string;
  readonly nationalId: string;
  readonly isPasswordExpired: string;
  readonly isStaffPasswordChanged: string;
  readonly approvalLevelId: string;
  role: {
    id: string;
    name: string;
  };
  userType: {
    id: string;
    name: string;
  };
  agency: {
    id: string;
    name: string;
  };
  sector: {
    id: string;
    name: string;
    code: string;
  };
}
