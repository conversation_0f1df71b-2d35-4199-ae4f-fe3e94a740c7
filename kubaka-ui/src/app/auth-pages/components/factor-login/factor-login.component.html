<div class="login-container">
    <div class="login-content">
        <div class="illust">
            <img class="logo" [routerLink]="['/']" src="assets/ikons/logo/NavLogo.svg" alt="">
            <img class="ill" src="assets/imgs/SVG/illustration.svg" alt="">
        </div>
        <div class="dtials">
            <div class="pin-container">
            </div>
            <form class="form">
                <div class="form-header">
                    <h2 class="display-md">{{"factorLogin.verify" | translate }}</h2>
                </div>
                <!-- <div class="form-body">
                    <input id="input0" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[0]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 0)" />
                    <input id="input1" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[1]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 1)" />
                    <input id="input2" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[2]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 2)" />
                    <input id="input3" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[3]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 3)" />
                    <input id="input4" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[4]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 4)" />
                    <input id="input5" type="text" maxlength="1" class="pin-input" [(ngModel)]="pinDigits[5]"
                        [ngModelOptions]="{standalone: true}" (input)="onInput($event, 5)" />
                  </div>
                  
                <div class="form-footer">
                    <button class="kbk-btn kbk-btn-lg kbk-btn-main" [disabled]="!isComplete()"
                        (click)="confirm()">{{"factorLogin.confirm" | translate }}</button>
                    <a class="kbk-link" routerLink="/login">{{"factorLogin.login" | translate }}</a>
                </div> -->

                <div class="form-body">
                    <input id="input0" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 0)" (keydown)="onKeyDown($event, 0)" #pinInput />
                    <input id="input1" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 1)" (keydown)="onKeyDown($event, 1)" #pinInput />
                    <input id="input2" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 2)" (keydown)="onKeyDown($event, 2)" #pinInput />
                    <input id="input3" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 3)" (keydown)="onKeyDown($event, 3)" #pinInput />
                    <input id="input4" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 4)" (keydown)="onKeyDown($event, 4)" #pinInput />
                    <input id="input5" type="text" maxlength="1" class="pin-input" 
                        (input)="onInput($event, 5)" (keydown)="onKeyDown($event, 5)" #pinInput />
                  </div>
                  <div class="form-footer">
                    <button class="kbk-btn kbk-btn-lg kbk-btn-main" [disabled]="!isComplete()"
                        (click)="confirm()">{{"factorLogin.confirm" | translate }}</button>
                    <a class="kbk-link" routerLink="/login">{{"factorLogin.login" | translate }}</a>
                  </div>
            </form>
        </div>
    </div>

