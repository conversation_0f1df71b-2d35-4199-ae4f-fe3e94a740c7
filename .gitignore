# Ignore macOS and system files
.DS_Store

# Ignore environment files in both projects
kubaka-api/.env
kubaka-ui/.env
*.env
KUBAKA-SSL

# Ignore Angular environment files
kubaka-ui/src/environments/environment.ts
kubaka-ui/src/environments/environment.prod.ts
kubaka-ui/src/index.html 

kubaka-api/docker-compose.yml

kubaka-api/.env
kubaka-api/upload/
kubaka-api/apps/applications/.env
kubaka-api/apps/applications/config.ts
kubaka-api/apps/auth/.env
kubaka-api/apps/auth/config.ts

kubaka-api/apps/documents/.env
kubaka-api/apps/documents/config.ts

kubaka-api/apps/integrations/.env
kubaka-api/apps/integrations/config.ts

kubaka-api/apps/notifications/.env
kubaka-api/apps/notifications/config.ts

kubaka-api/apps/payments/.env
kubaka-api/apps/payments/config.ts

# Ignore logs, backups, temp files
*.log
*.bak
*.tmp
*.backup

# Ignore IDE/editor folders
.vscode/
.idea/

# Ignore node_modules in both subprojects
kubaka-api/node_modules/
kubaka-ui/node_modules/
