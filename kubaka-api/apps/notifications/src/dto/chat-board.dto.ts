import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ChatBoardDto {
  @ApiProperty({ description: 'The field phoneNumber is required' })
  @IsNotEmpty({ message: 'The field phoneNumber cannot be empty' })
  phoneNumber: string;

  @ApiProperty({ description: 'The field senderNames is required' })
  @IsNotEmpty({ message: 'The field senderNames cannot be empty' })
  senderNames: string;

  @ApiProperty({ description: 'The field agencyId is required' })
  @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
  agencyId: string;

  @ApiProperty({ description: 'The field content is required' })
  @IsNotEmpty({ message: 'The field content cannot be empty' })
  content: string;

  @ApiProperty({ description: 'The field issueCategoryId is required' })
  @IsNotEmpty({ message: 'The field issueCategoryId cannot be empty' })
  issueCategoryId: string;

  @ApiPropertyOptional({
    description: 'Array of Document UUIDs attached to this chat',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', {
    each: true,
    message: 'Each documentId must be a valid UUID',
  })
  documentIds?: string[];
  documents?: any[];
}
export class ChatBoardReplyDto {
  @ApiProperty({ description: 'The field phoneNumber is required' })
  @IsNotEmpty({ message: 'The field phoneNumber cannot be empty' })
  phoneNumber: string;

  @ApiProperty({ description: 'The field agencyId is required' })
  @IsNotEmpty({ message: 'The field agencyId cannot be empty' })
  agencyId: string;

  @ApiProperty({ description: 'The field content is required' })
  @IsNotEmpty({ message: 'The field content cannot be empty' })
  content: string;

  @ApiProperty({ description: 'The field issueCategoryId is required' })
  @IsNotEmpty({ message: 'The field issueCategoryId cannot be empty' })
  issueCategoryId: string;

  @ApiProperty({ description: 'The field isReply is required' })
  @IsNotEmpty({ message: 'The field isReply cannot be empty' })
  isReply: string;

  // Adding documents for attachment uploading scenario
  @ApiPropertyOptional({
    description: 'Array of Document UUIDs attached to this chat',
    type: [String],
    format: 'uuid',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('all', {
    each: true,
    message: 'Each documentId must be a valid UUID',
  })
  documentIds?: string[];
  documents?: any[];
}

export class IssueCategoryDto {
  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}
