<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"editProfile.header" | translate }} <!-- <span>3</span> / <span>4</span>  -->
        </div>
    </div>
    <div class="stepper-body">
        <div>
            <!-- Step content goes here -->
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"editProfile.title" | translate }}</h2>
                </div>
                <form [formGroup]="userForm" (ngSubmit)="onSubmit()" autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"editProfile.firstName" | translate }}<span>*</span></label>
                                <div>
                                    <input type="text" id="firstName" name="firstName" formControlName="firstName"
                                        readonly>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"editProfile.lastName" | translate }}<span>*</span></label>
                                <div>
                                    <input type="text" id="lastName" name="lastName" formControlName="lastName"
                                        readonly>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"editProfile.email" | translate }}</label>
                                <div>
                                    <input type="email" id="email" name="email" formControlName="email" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"editProfile.phoneNumber" | translate }}</label>
                                <div>
                                    <input type="text" id="phoneNumber" name="phoneNumber" formControlName="phoneNumber"
                                        required>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-panel_footer">
                        <button>{{"editProfile.cancel" | translate }}</button>
                        <button *ngIf="!submitted" type="submit">{{"editProfile.update" | translate }}</button>
                        <button *ngIf="submitted" type="button">{{"editProfile.updating" | translate }}...</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>