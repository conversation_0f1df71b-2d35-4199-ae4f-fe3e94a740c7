import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { AgencyDistrictComponent } from 'src/app/system-setting/agency-district/agency-district.component';
import { AgencyComponent } from 'src/app/system-setting/agency/agency.component';
import { DocumentRequiredComponent } from 'src/app/system-setting/document-required/document-required.component';
import { PermitChecklistComponent } from 'src/app/system-setting/permit-checklist/permit-checklist.component';
import { QuestionCatogoriesComponent } from 'src/app/system-setting/question-catogories/question-catogories.component';
import { RoleComponent } from '../role/role.component';
import { SystemAdminLayoutComponent } from '../system-admin-layout/system-admin-layout.component';
import { UsersComponent } from '../users/users.component';
import { UserMgtRoutingModule } from './user-mgt-routing.module';
import { PriceComponent } from 'src/app/system-setting/price/price.component';
import { PermissionComponent } from '../permission/permission.component';
import { ActivityComponent } from '../activity/activity.component';



@NgModule({
  declarations: [
    SystemAdminLayoutComponent,
    RoleComponent,
    UsersComponent,
    PermitChecklistComponent,
    DocumentRequiredComponent,
    AgencyComponent,
    AgencyDistrictComponent,
    QuestionCatogoriesComponent,
    PriceComponent,
    PermissionComponent,
    ActivityComponent
  ],
  imports: [
    CommonModule,
    UserMgtRoutingModule,
    SharedModule
  ]
})
export class UserMgtModule { }
