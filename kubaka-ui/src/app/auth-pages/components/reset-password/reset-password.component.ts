import { Component } from '@angular/core';
import { FormControl, FormGroupDirective, NgForm, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { AppConfig } from 'src/app/app.config';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { AuthService } from '../../services/auth.service';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { ErrorStateMatcher } from '@angular/material/core';


export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    return !!(control && control.valid && (control.dirty || control.touched) && form?.hasError('notMatched'));
  }
}


@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent {
  saveForm!: UntypedFormGroup;
  submitted!: boolean

  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private authService: AuthService,
    public utilService: UtilService,
    private route: ActivatedRoute,
    private appConfig: AppConfig
  ) {

    this.route.params.subscribe((params: any) => {
      this.saveForm = this.formBuilder.group({
        verified: [params.id],
        email: ["", [Validators.required]],
        // currentPassword: ["", [Validators.required]],
        password: ["", [Validators.required, Validators.pattern(this.utilService.passwordPattern)]],
        confirmPassword: ["", [Validators.required, Validators.pattern(this.utilService.passwordPattern)]],
        // newPasswordToken: [params.code]
        newPasswordToken: [""]
      });
    });

  }

  ngOnInit(): void {

  }


  onSubmit() {
    this.submitted = true;
    if (this.saveForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill all required fields', "bottom", "center")
      return;
    } else {

      let dataToSave = {
        "email": this.saveForm.value.email,
        "newPassword": this.saveForm.value.password,
        "currentPassword": this.saveForm.value.currentPassword,
        "newPasswordToken": this.saveForm.value.newPasswordToken,
      }
      this.authService.saveAssetWithPath(dataToSave, environment.authUrl + 'auth/reset-password')
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, 'bottom', 'center');
            this.router.navigate(['/login'])
          },
        )

    }
  }
}
