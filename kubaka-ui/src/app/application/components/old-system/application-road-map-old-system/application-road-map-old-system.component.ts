import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { StepModel } from '../../../models/step.model';
import { StepperService } from '../../../services/stepper.service';

@Component({
  selector: 'app-application-road-map-old-system',
  templateUrl: './application-road-map-old-system.component.html',
  styleUrls: ['./application-road-map-old-system.component.scss']
})
export class ApplicationRoadMapOldSystemComponent {
  currentStep!: number;
  steps!: StepModel[];
  outputData: any = {};


  constructor(
    private stepperService: StepperService,
    private route: ActivatedRoute,
  ) {

  }

  ngOnInit(): void {
    this.steps = this.stepperService.getSteps();

    this.stepperService.currentStep$.subscribe((step) => {
      this.currentStep = step;
    });
  }


  nextStep(): void {
    this.stepperService.nextStep();
  }

  prevStep(): void {
    this.stepperService.prevStep();
  }



  saveTheResponse(event: any) {
    this.outputData = event;
    this.outputData.isProjectDetails = true;
    this.nextStep();
  }
}
