import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccountHeaderComponent } from '../components/account-header/account-header.component';
import { ChangePasswordComponent } from '../components/change-password/change-password.component';
import { EditProfileComponent } from '../components/edit-profile/edit-profile.component';



const routes: Routes = [
    {
        path: '', component: AccountHeaderComponent,
        children: [
            { path: 'setting', loadChildren: () => import('../../user-management/modules/user-mgt.module').then(m => m.UserMgtModule) },
            { path: 'payment', loadChildren: () => import('../../payment/modules/payment.module').then(m => m.PaymentModule) },
            { path: 'application', loadChildren: () => import('../../application/module/application.module').then(m => m.ApplicationModule) },
            { path: 'dashboard', loadChildren: () => import('../../dashaboard-management/module/dasbaord.module').then(m => m.DasbaordModule) },
            { path: 'all-applications', loadChildren: () => import('../../application-review-management/modules/application-review.module').then(m => m.ApplicationReviewModule) },
            { path: 'certificate', loadChildren: () => import('../../../app/certificate-management/modules/certificate.module').then(m => m.CertificateModule) },
            { path: 'report', loadChildren: () => import('../../../app/reporting-management/modules/report.module').then(m => m.ReportModule) },
            { path: 'notification', loadChildren: () => import('../../../app/notification-management/modules/notification.module').then(m => m.NotificationModule) },


            { path: 'change-password', component: ChangePasswordComponent },
            { path: 'edit-profile', component: EditProfileComponent }
        ]
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class AccountRoutingModule { }
