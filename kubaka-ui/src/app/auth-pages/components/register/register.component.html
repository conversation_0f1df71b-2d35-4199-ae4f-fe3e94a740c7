<div class="login-container">
  <div class="login-content">
    <div class="illust">
      <img class="logo" src="assets/ikons/logo/NavLogo.svg" alt="" />
      <img class="ill" src="assets/imgs/SVG/illustration.svg" alt="" />
    </div>
    <!--  -->
    <div class="dtials" *ngIf="isChoosingAccountType">
      <form class="form" [formGroup]="accountTypeForm" (ngSubmit)="onSubmit()">
        <div class="form-header">
          <h2 class="display-md">{{"registerPage.title" | translate }}</h2>
        </div>
        <div class="form-body">
          <div class="form-input">
            <label>{{"registerPage.accountType" | translate }}<span class="estrx"> *</span></label>
            <div>
              <select class="flg" name="accountTypeId" formControlName="accountTypeId" [ngClass]="{
                  'is-invalid': submitted && f['accountTypeId'].errors
                }" (change)="checkAccountType()">
                <option value="">{{"registerPage.accountTypeOptions.placeholder" | translate }}</option>
                <option value="" *ngFor="let acc of accountTypes" [value]="acc.id"> {{ acc.name }} </option>
              </select>
              <div *ngIf="submitted && f['accountTypeId'].errors" class="invalid-feedback" align="left">
                <div *ngIf="f['accountTypeId'].errors['required']"> Account Type is required </div>
              </div>
            </div>
          </div>
          <div class="form-input" *ngIf="accountTypeForm.value.accountTypeId === '1'">
            <label>{{"registerPage.documentType" | translate }}<span class="estrx"> *</span></label>
            <div>
              <select class="flg" name="documentTypeId" formControlName="documentTypeId" (change)="cleanData()"
                [ngClass]="{
                  'is-invalid': submitted && f['documentTypeId'].errors
                }">
                <option value="">{{"registerPage.documentTypeOptions.placeholder" | translate }}</option>
                <option value="" *ngFor="let acc of documentTypes" [value]="acc.id"> {{ acc.name }} </option>
              </select>
              <div *ngIf="submitted && f['documentTypeId'].errors" class="invalid-feedback" align="left">
                <div *ngIf="f['documentTypeId'].errors['required']"> Document Type is required </div>
              </div>
            </div>
          </div>
          <div class="form-input" *ngIf="accountTypeForm.value.accountTypeId === '1'
           &&
           accountTypeForm.value.documentTypeId === '1'">
            <label>{{"registerPage.nationalIdLabel" | translate }}<span class="estrx"> *</span></label>
            <div>
              <input type="text" class="flg" id="nid" name="nid" appNoSpaces formControlName="nid" />
              <!--
                 [ngClass]="{ 'is-invalid': submitted && f['nid'].errors }"
                 <div *ngIf="submitted && f['nid'].errors" class="invalid-feedback" align="left">
                <div *ngIf="f['nid'].errors['required']">NID is required</div>
              </div> -->
            </div>
          </div>
          <div class="form-input" *ngIf="accountTypeForm.value.accountTypeId === '1' &&
          accountTypeForm.value.documentTypeId === '2'">
            <label>{{"registerPage.passportLabel" | translate }} <span class="estrx"> *</span></label>
            <div>
              <input type="text" class="flg" id="passport" name="passport" appNoSpaces formControlName="passport" />
              <!-- <div
                [ngClass]="{ 'is-invalid': submitted && f['passport'].errors }"
                 *ngIf="submitted && f['passport'].errors" class="invalid-feedback" align="left">
                <div *ngIf="f['passport'].errors['required']">NID is required</div>
              </div> -->
            </div>
          </div>
          <div class="form-input" *ngIf="
              accountTypeForm.value.accountTypeId === '2' ||
              accountTypeForm.value.accountTypeId === '3'
            ">
            <label>{{ "registerPage.licenseLabel" | translate }}</label>
            <div>
              <input type="text" class="flg" id="licenseNumber" name="licenseNumber" [placeholder]="'registerPage.licensePlaceholder' | translate"
                formControlName="licenseNumber" appNoSpaces [ngClass]="{
                  'is-invalid': submitted && f['licenseNumber'].errors
                }" />
              <div *ngIf="submitted && f['licenseNumber'].errors" class="invalid-feedback" align="left">
                <div *ngIf="f['licenseNumber'].errors['required']"> License is required </div>
              </div>
            </div>
          </div>
        </div>
        <div class="form-footer">
          <button *ngIf="!submitted" class="kbk-btn kbk-btn-lg kbk-btn-main" type="submit"> {{ "registerPage.proceedButton" | translate }} </button>
          <button *ngIf="submitted" class="kbk-btn kbk-btn-lg kbk-btn-main" type="button">{{ "registerPage.savingTxt" | translate }}...</button>
          <a class="kbk-link" routerLink="">{{ "registerPage.backHomeTxt" | translate }}</a>
        </div>
      </form>
    </div>
    <!-- *ngIf="formProcess.isRegisteringCredentials" -->
    <div class="dtials" *ngIf="isRegisteringCredentials">
      <app-register-info-detail [inputData]="this.accountTypeForm.value"
        (backToParent)="keepData($event)"></app-register-info-detail>
    </div>
  </div>
</div>
