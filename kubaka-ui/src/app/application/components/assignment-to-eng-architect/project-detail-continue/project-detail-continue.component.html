<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter">{{"projectDetailContinue.complete" | translate }} <span>{{"projectDetailContinue.application" | translate }}</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <!-- Step content goes here -->
            <div class="step-panel" *ngIf="!outputData.isDocumenting">
                <div class="step-panel_header">
                    <h2>{{"projectDetailContinue.projectInformation" | translate }}</h2>
                    <div class="form-incheckbox">
                        <label class="checkbox">
                            <input style="display: none;" type="checkbox" id="check" [checked]="isAssociated"
                                (click)="checkAssociated(associatedUpiContent)" data-bs-toggle="modal" type="button"
                                data-bs-target="#showModal" />
                            <span class="checkbox_box"></span>
                            <span class="checkbox_txt">{{"projectDetailContinue.checkToAddAssociatedUpi" | translate }}</span>
                        </label>
                    </div>
                </div>
                <form [formGroup]="userForm" *ngIf="!outputData.isDocumenting" (ngSubmit)="onSubmit()">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectDetailContinue.projectName" | translate }}</label>
                                <div>
                                    <textarea name="projectName" formControlName="projectName" id="projectName"
                                        cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.projectDescription" | translate }}</label>
                                <div>
                                    <textarea name="projectDescription" formControlName="projectDescription"
                                        id="projectDescription" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectDetailContinue.permitType" | translate }}</label>
                                <div>
                                    <select name="permitTypeId" id="permitTypeId" formControlName="permitTypeId"
                                        (change)="getPermitTypeObject($event)" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.buildingType" | translate }}</label>
                                <div>
                                    <select name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>P{{"projectDetailContinue.projectCategory" | translate }}</label>
                                <div>
                                    <!--  -->
                                    <select [class.disabled-select]="isSelectDisabled" name="categoryTypeId"
                                        id="categoryTypeId" formControlName="categoryTypeId" required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id"> {{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"projectDetailContinue.plotSize" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly required>
                                </div>
                            </div>
                            <div class="form-input"  *ngIf="userForm.value.combiningPlotSize">
                                <label>{{"projectDetailContinue.combiningPlotSize" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="combiningPlotSize" name="combiningPlotSize" formControlName="combiningPlotSize"
                                        readonly>
                                </div>
                            </div>


                            <div class="form-input">
                                <label>{{"projectDetailContinue.numberOfFloor" | translate }}<span>*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor" name="numberOfFloor" noNegative
                                        formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.buildUpArea" | translate }}<span>*</span></label>
                                <div>
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                        (keyup)="calculateNumberOfFlow()" required>
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> {{"projectDetailContinue.buildUpAreaError" | translate }} </div>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.grossFloorArea" | translate }}</label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                        formControlName="grossFloorArea" noNegative readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.numberOfParkingSpaces" | translate }}</label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.estimatedPriceOfDwellingUnit" | translate }}</label>
                                <div>
                                    <input type="text" id="priceOfDwellingUnitRwf" name="priceOfDwellingUnitRwf"
                                        formControlName="priceOfDwellingUnitRwf" appCommaSeparator required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.numberOfDwellingUnits" | translate }}</label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.descriptionOfOperations" | translate }}</label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.percentageSpaceUse" | translate }}</label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.estimatedWaterConsumption" | translate }}</label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.estimatedElectricityConsumption" | translate }}</label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.distanceToLandline" | translate }}</label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn" formControlName="DistanceToTheNearestLandIn"
                                        noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.estimatedProjectCostUSD" | translate }}</label>
                                <div>
                                    <input type="text" id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" appCommaSeparator required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.estimatedProjectCostRWF" | translate }}</label>
                                <div>
                                    <input type="text" id="ProjectCostInRwf" name="ProjectCostInRwf" appCommaSeparator
                                        formControlName="ProjectCostInRwf" required>
                                </div>
                            </div>
                            <!-- <div class="form-input">
                                <label>Technology Survey</label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId" required>
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div> -->
                            <div class="form-input">
                                <label>{{"projectDetailContinue.capacityInformation" | translate }}</label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation" noNegative
                                        (keyup)="calculateNumberOfPeople()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.selectedCategoryUse" | translate }}</label>
                                <div>
                                    <input type="text" id="selectedCategoryUse" name="selectedCategoryUse"
                                        formControlName="selectedCategoryUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"projectDetailContinue.selectedUse" | translate }}</label>
                                <div>
                                    <input type="text" id="selectedUse" name="selectedUse" formControlName="selectedUse"
                                        readonly required>
                                </div>
                            </div>
                        </div>
                        <div class="form-set" style="display: none;">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" formControlName="isFromOldSystem"
                                        [disabled]="isReadOnly" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"projectDetailContinue.isFromOldSystem" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" name="isUnderMortgage"
                                        formControlName="isUnderMortgage" [disabled]="isReadOnly" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"projectDetailContinue.isUnderMortgage" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" name="isUnderRestriction"
                                        formControlName="isUnderRestriction" [disabled]="isReadOnly" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"projectDetailContinue.isUnderRestriction" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="step-panel_footer">
                        <button (click)="cancel()">{{"projectDetailContinue.cancel" | translate }}</button>
                        <button type="submit" [disabled]="userForm.invalid">{{"projectDetailContinue.next" | translate }}</button>
                        <!-- <button *ngIf="applicationDetail.id" type="button" (click)="next()">{{"projectDetailContinue.next" | translate }}</button> -->
                    </div>
                </form>
            </div>
            <app-other-application-document *ngIf="outputData.isDocumenting"
            [inputData]="outputData"
                (backToParent)="closeDocument()"></app-other-application-document>

                <!-- <app-eia-form-checker *ngIf="
                    (userForm.value.categoryCode === 'CAT5' ||
                    userForm.value.categoryCode === 'CAT4') && this.projectDetails.upi"
                [inputData]="{upi: this.projectDetails.upi}"
                (backToParent)="comeAndAllowToAssign($event)"></app-eia-form-checker> -->
        </div>
    </div>
</div>
<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"projectDetailContinue.associatedUpi" | translate }}</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
            (backToParent)="closeAssociatedPopup($event)"
            (backToParentAndSubmitCombining)="getNewCombiningAssociated($event)"></app-associated-upi-application>
    </div>
</ng-template>
