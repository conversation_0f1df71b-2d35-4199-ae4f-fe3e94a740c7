import {
  Controller,
  Post,
  Get,
  Param,
  // Delete,
  Body,
  UploadedFile,
  UseInterceptors,
  Res,
  Delete,
  Put,
  HttpException,
  HttpStatus,
  UploadedFiles,
} from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { DocumentUpload } from '../entities/documentUpload.entity';
// import { MulterFile } from 'multer';
import {
  DocumentUploadAdditionalDto,
  DocumentUploadDto,
} from './dto/documentUpload.dto';
// import * as fs from 'fs';
// import * as path from 'path';
import axios from 'axios';
import { Documents, Entity_Type } from '../entities/documents.entity';
import { DocumentsDto } from './dto/documents.dto';

interface MulterFile {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
  fieldname: string;
}

@ApiTags('DocMgt')
@Controller('DocMgt')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        requiredDocumentId: { type: 'string' },
        applicationId: { type: 'string' },
        // Id: { type: 'integer' },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadFile(
    @UploadedFile() file: MulterFile,
    @Body() createFileDto: DocumentUploadDto,
  ) {
    const savedFile = await this.documentsService.create(
      createFileDto.requiredDocumentId,
      createFileDto.applicationId,
      file,
    );
    return savedFile;
  }
  // async uploadFile(
  //   @UploadedFile() file: MulterFile,
  //   @Body() createFileDto: DocumentUploadDto,
  // ): Promise<DocumentUpload> {
  //   const savedFile = await this.documentsService.create(
  //     createFileDto.requiredDocumentId,
  //     createFileDto.applicationId,
  //     file,
  //   );
  //   return savedFile;
  // }

  @ApiTags('settings')
  @Get('documents')
  async findAllDocuments() {
    return this.documentsService.findAllDocuments();
  }

  @Get('documents/:applicationId')
  async findAllUploadByApplicationId(
    @Param('applicationId') applicationId: string,
  ): Promise<DocumentUpload[]> {
    return await this.documentsService.findAllUploadByApplicationId(
      applicationId,
    );
  }

  @Get('url/:fileUrl')
  async findOneByUrl(
    @Param('fileUrl') fileUrl: string,
  ): Promise<DocumentUpload> {
    return await this.documentsService.findOneByUrl(fileUrl);
  }

  @Get(':fileName/base64')
  async getFileAsBase64(
    @Param('fileName') fileName: string,
    @Res() res,
  ): Promise<void> {
    const apiGatewayUrl = `https://api-gatway.kubaka.gov.rw/attachment/path/${fileName}`;

    try {
      // Fetch the file from the API Gateway
      const response = await axios.get(apiGatewayUrl, {
        responseType: 'arraybuffer',
      });
      const fileData = response.data;

      // Convert the file data to Base64
      const base64Data = Buffer.from(fileData).toString('base64');
      res.status(200).send({ base64Data });
    } catch (error) {
      console.error('Error fetching or processing the file:', error.message);

      if (error.response?.status === 404) {
        throw new HttpException(
          'File not found on the remote server.',
          HttpStatus.NOT_FOUND,
        );
      }

      throw new HttpException(
        'Failed to fetch file from the remote server.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // @Delete(':id')
  // async delete(@Param('id') id: string): Promise<void> {
  //   await this.documentsService.delete(id);
  // }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    await this.documentsService.delete(id);
  }

  @Put(':id/status')
  async updateDocumentStatus(@Param('id') id: string): Promise<DocumentUpload> {
    return await this.documentsService.updateDocumentStatus(id);
  }

  @Post('uploadAdditional')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        additionalDescription: { type: 'string' },
        uploadType: { type: 'string' },
        applicationId: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async createUploadAdditional(
    @UploadedFile() file: MulterFile,
    @Body() createFileDto: DocumentUploadAdditionalDto,
  ) {
    const savedFile = await this.documentsService.createUploadAdditional(
      createFileDto.additionalDescription,
      createFileDto.uploadType,
      createFileDto.applicationId,
      file,
    );
    return savedFile;
  }

  @Get('documents/uploadAdditional')
  async findAllUploadAdditional() {
    return this.documentsService.findAllUploadAdditional();
  }

  @Get('documents/uploadAdditional/:applicationId')
  async findAllUploadAdditionalByApplicationId(
    @Param('applicationId') applicationId: string,
  ): Promise<DocumentUpload[]> {
    return await this.documentsService.findAllUploadAdditionalByApplicationId(
      applicationId,
    );
  }

  @Post('chatBoard/uploadAttachment')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  async uploadChatBoardAttachment(
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    // first save the files with the document service
    // and return the documentIds

    let documentIds: string[] = [];
    const entityType = Entity_Type.CHAT;
    if (files && files.length > 0) {
      documentIds = await this.documentsService.saveBulkDocuments(
        files,
        entityType,
      );
    }

    return documentIds;
  }

  @Post('chatBoard/getAttachments')
  async getChatAttachments(@Body() chatMessages: any[]) {
    return await this.documentsService.getAttachmentsByIds(chatMessages);
  }

  // Getting document url for "Documents Entity"
  @Get('documentUrl/:fileUrl')
  async findDocumentUrl(@Param('fileUrl') fileUrl: string): Promise<Documents> {
    return await this.documentsService.findOneDocumentByUrl(fileUrl);
  }

  // Getting url by documents id 'Documents Entity'
  @Get('documentId/:documentId')
  async findDocumentUrlById(
    @Param('documentId') documentId: string,
  ): Promise<Documents> {
    return await this.documentsService.findOneDocumentById(documentId);
  }
}
