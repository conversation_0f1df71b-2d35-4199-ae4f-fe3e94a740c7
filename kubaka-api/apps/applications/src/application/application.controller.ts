import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpStatus,
  HttpException,
  HttpCode,
  Query,
  Put,
  Inject,
  UploadedFile,
  UseInterceptors,
  Req,
  DefaultValuePipe,
  ParseIntPipe,
  // UseGuards,
  // BadRequestException,
  // Put,
} from '@nestjs/common';
import { ApplicationService } from './application.service';
import {
  AnswerDto,
  ApplicationDto,
  ApplicationDtoUpdate,
  AssigneeDto,
  AssociatedUPIDto,
  // CreateChartDto,
  DataForChartDto,
  DeleteEngineerOnProjectDto,
  FoundationInspectionDto,
  LockApplicationDtoUpdate,
  OccupancyInspectionDto,
  OtherInfoApplicationDto,
  PermitQuestionDto,
  ProjectDto,
  // ProjectDtoGet,
  RejectProjectDto,
  ReviewersOnApplicationDto,
  UpdateAssigneeDto,
  ApplicationResubmitIremboDto,
  // UpdateApplicationDto,
} from '../dto/application.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import {
  ApplicationStatusDto,
  BuildTypeDto,
  CategoryRuleDto,
  CategoryTypeDto,
  // ConstructionMethodDto,
  DocumentTypeDto,
  // EquipmentCapacityDto,
  PermitTypeDto,
  ProjectStatusDto,
  QuestionCategoryDto,
  RequiredDocumentDto,
  TechnologySurveyDto,
} from '../dto/settings.dto';
// import { ApiPaginatedResponse } from '@app/common/decorators';
// import { PageOptionsDto } from '@app/common/dto/page-obptions.dto';
// import { PageDto } from '@app/common/dto/page.dto';
import { Application, Assignee, Project } from '../entities/application.entity';
import { Repository } from 'typeorm';
import {
  GenericSearch,
  GenericSearchWithMany,
  GenericSearchWithRelations,
  // GenericSearchWithRelations2,
} from './generic-search.service';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ApplicationStatus,
  BuildType,
  CategoryType,
  PermitType,
  DocumentType,
  ProjectStatus,
  // EquipmentCapacity,
  QuestionCategory,
  // ConstructionMethod,
} from '../entities/settings.entity';
import { ClientProxy, MessagePattern } from '@nestjs/microservices';
import { AUTH_SERVICE } from '@app/common/constants';

// interface AssigneeWithDetails extends Assignee {
//   details: any;
//   projectStatus: any;
// }
import { FileInterceptor } from '@nestjs/platform-express';

import { Request } from 'express';
import { ReviewerReportDto } from '../dto/approval.dto';
import { CalculateCategoryDto } from '../dto/calculatecategory.dto';
// import { JwtAuthGuard } from '@app/common/auth';

@Controller('application')
export class ApplicationController {
  constructor(
    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,

    private readonly applicationService: ApplicationService,

    @InjectRepository(Project)
    protected readonly projectRepository: Repository<Project>,
    protected readonly genericSearch: GenericSearch<Project>,
    protected readonly genericSearchWithManyOptions: GenericSearchWithMany<Project>,
    protected readonly genericSearchWithRelationsOptions: GenericSearchWithRelations<Project>,
    protected readonly genericSearchWithRelationsOptions2: GenericSearchWithRelations<Application>,

    @InjectRepository(PermitType)
    protected readonly permitTypeRepository: Repository<PermitType>,
    protected readonly genericSearch2: GenericSearch<PermitType>,

    @InjectRepository(CategoryType)
    protected readonly categoryTypeRepository: Repository<CategoryType>,
    protected readonly genericSearch3: GenericSearch<CategoryType>,

    @InjectRepository(BuildType)
    protected readonly buildTypeRepository: Repository<BuildType>,
    protected readonly genericSearch4: GenericSearch<BuildType>,

    @InjectRepository(ApplicationStatus)
    protected readonly applicationStatusRepository: Repository<ApplicationStatus>,
    protected readonly genericSearch5: GenericSearch<ApplicationStatus>,

    @InjectRepository(DocumentType)
    protected readonly documentTypeRepository: Repository<DocumentType>,
    protected readonly genericSearch6: GenericSearch<DocumentType>,

    @InjectRepository(Application)
    protected readonly applicationRepository: Repository<Application>,
    protected readonly genericSearch7: GenericSearchWithRelations<Application>,

    @InjectRepository(ProjectStatus)
    protected readonly projectStatusRepository: Repository<ProjectStatus>,
    protected readonly genericSearch8: GenericSearch<ProjectStatus>,

    @InjectRepository(Assignee)
    protected readonly assigneeRepository: Repository<Assignee>,
    protected readonly genericSearch9: GenericSearchWithRelations<Assignee>,

    // @InjectRepository(EquipmentCapacity)
    // protected readonly equipmentCapacityRepository: Repository<EquipmentCapacity>,
    // protected readonly genericSearch10: GenericSearch<EquipmentCapacity>,

    // @InjectRepository(ConstructionMethod)
    // protected readonly constructionMethodRepository: Repository<ConstructionMethod>,
    // protected readonly genericSearch11: GenericSearch<ConstructionMethod>,

    @InjectRepository(QuestionCategory)
    protected readonly questionCategoryRepository: Repository<QuestionCategory>,
    protected readonly genericSearch12: GenericSearch<QuestionCategory>,
  ) {}

  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }

  @MessagePattern('getRequiredDoc')
  async checkReqDoc(requiredDocumentId: string) {
    console.log(requiredDocumentId);
    // Check if the user exists in
    return await this.applicationService.findOneRequiredDocument(
      requiredDocumentId,
    );
  }

  @MessagePattern({ cmd: 'requiredDocumentData' })
  async getRequiredDocumentData(requiredDocumentId: string) {
    return this.applicationService.findOneRequiredDocument(requiredDocumentId);
  }

  // project
  @ApiTags('application')
  @Post('project')
  async CreateProject(@Body() projectDto: ProjectDto) {
    const upi = projectDto.upi;
    const searchResponse = this.applicationService.searchProject(upi);
    if ((await searchResponse).totalCount != 0) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          error: 'UPI already exists',
        },
        HttpStatus.BAD_REQUEST,
      );
    } else {
      // const userExists = await this.applicationService.checkUserExists(
      //   projectDto.userId,
      // );

      // if (!userExists) {
      //   throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      // }
      return this.applicationService.createProject(projectDto);
    }
  }

  // // project
  // @ApiTags('application')
  // @Post('project')
  // async CreateProject(@Body() projectDto: ProjectDto) {
  //   return this.applicationService.createProject(projectDto);
  // }

  // async CreateProject(@Body() projectDto: ProjectDto) {
  //   // const upiExists = await this.applicationService.checkUpiExists(
  //   //   projectDto.upi,
  //   // );
  //   // if (upiExists) {
  //   //   throw new HttpException(
  //   //     'Already this UPI has project',
  //   //     HttpStatus.NOT_FOUND,
  //   //   );
  //   // }

  //   const searchFields: (keyof Project)[] = ['upi'];
  //   await this.genericSearch.search(
  //     this.projectRepository,
  //     searchFields,
  //     projectDto.upi,
  //   );

  //   const userExists = await this.applicationService.checkUserExists(
  //     projectDto.userId,
  //   );

  //   if (!userExists) {
  //     throw new HttpException('User not found', HttpStatus.NOT_FOUND);
  //   }

  //   // const agencyExists = await this.applicationService.checkAgencyExists(
  //   //   projectDto.agencyId,
  //   // );
  //   // if (!agencyExists) {
  //   //   throw new HttpException('COMMON.Agency_NOT_FOUND', HttpStatus.NOT_FOUND);
  //   // }
  //   return this.applicationService.createProject(projectDto);
  // }

  @ApiTags('application')
  // @UseGuards(JwtAuthGuard)
  @Get('project')
  async findAllProjects() {
    return this.applicationService.findAllProjects();
  }

  // @ApiTags('application')
  // @Get('project/paginated')
  // @HttpCode(HttpStatus.OK)
  // @ApiPaginatedResponse(ProjectDtoGet)
  // async getProjectsPaginated(
  //   @Query() pageOptionsDto: PageOptionsDto,
  // ): Promise<PageDto<ProjectDtoGet>> {
  //   return this.applicationService.findAllProjectsPaginated(pageOptionsDto);
  // }

  // @ApiTags('application')
  // @Get('project/upi/search')
  // async searchProjectsByUPI(@Query('search') search: string) {
  //   const searchFields: (keyof Project)[] = ['upi'];
  //   // const searchFields: (keyof Project)[] = ['upi', 'projectName'];
  //   const dataToBeReturned = await this.genericSearch.search(
  //     this.projectRepository,
  //     searchFields,
  //     search,
  //   );
  //   if (!dataToBeReturned.items.length) {
  //     throw new HttpException('No content found', HttpStatus.NO_CONTENT);
  //   }

  //   return dataToBeReturned;
  // }

  @ApiTags('application')
  @Get('project/upi/search')
  async searchProjectsByUPI(@Query('upi') upi: string) {
    return this.applicationService.searchProject(upi);
  }

  @ApiTags('application')
  @Get('project/all-application/upi/search')
  async searchProjectsAllApplicationByUPI(@Query('upi') upi: string) {
    return this.applicationService.searchProjectAllApplication(upi);
  }

  // @ApiTags('application')
  // @Get('project/application/upi/search')
  // async searchProjectsAndApplicationsByUPI(@Query('search') search: string) {
  //   const searchFields: (keyof Project)[] = ['upi'];
  //   const relations: string[] = [
  //     'applications',
  //     // 'applications.applicationStatus',
  //   ];
  //   const dataToBeReturned =
  //     await this.genericSearchWithRelationsOptions.search(
  //       this.projectRepository,
  //       searchFields,
  //       search,
  //       relations,
  //     );

  //   if (!dataToBeReturned.items.length) {
  //     throw new HttpException('No content found', HttpStatus.NO_CONTENT);
  //   }

  //   // const applicationId = dataToBeReturned.items[0].applications[0].id;
  //   const applicationId = (await dataToBeReturned).items[0].applications[0].id;
  //   const dataToBeReturnedWithDetails =
  //     await this.findOneApplicationAllDetails(applicationId);

  //   return dataToBeReturnedWithDetails;
  // }

  @ApiTags('application')
  @Get('project/application/upi/search')
  async searchProjectsAndApplicationsByUPI(@Query('search') search: string) {
    const searchFields: (keyof Project)[] = ['upi'];
    const relations: string[] = [
      'applications',
      // 'applications.applicationStatus',
    ];
    const dataToBeReturned =
      await this.genericSearchWithRelationsOptions.search(
        this.projectRepository,
        searchFields,
        search,
        relations,
      );

    if (!dataToBeReturned.items.length) {
      throw new HttpException('No content found', HttpStatus.NO_CONTENT);
    }

    const project = dataToBeReturned.items[0];

    if (!project.applications.length) {
      // Return the project with empty applications array
      return { ...project, applications: [] };
    }

    const applicationId = project.applications[0].id;
    const dataToBeReturnedWithDetails =
      await this.findOneApplicationAllDetails(applicationId);

    return dataToBeReturnedWithDetails;
  }

  @ApiTags('application')
  @Get('project/agency/search')
  async searchProjectsByAgency(@Query('search') search: string) {
    const searchFields: (keyof Project)[] = ['agencyId'];
    // const searchFields: (keyof Project)[] = ['upi', 'projectName'];
    return this.genericSearch.search(
      this.projectRepository,
      searchFields,
      search,
    );
  }

  @ApiTags('application')
  @Get('project/many/search')
  async searchProjects(
    @Query('upi') upi: string,
    @Query('projectName') projectName: string,
    @Query('created_at') created_at: Date,
  ) {
    const searchQuery: any = {};
    if (upi) searchQuery.name = upi;
    if (projectName) searchQuery.description = projectName;
    if (created_at) searchQuery.created_at = new Date(created_at);

    return this.genericSearchWithManyOptions.searchProjects(
      this.projectRepository,
      searchQuery,
    );
  }

  @ApiTags('application')
  @Get('project/:id')
  async findOneProject(@Param('id') id: string) {
    return this.applicationService.findOneProject(id);
  }

  @ApiTags('application')
  @Get('project/user/:userId')
  async findProjectByUserId(@Param('userId') userId: string) {
    return this.applicationService.findProjectByUserId(userId);
  }

  @ApiTags('application')
  @Get('getProjectByUserIdIrembo')
  async getProjectByUserIdIrembo(@Query('userId') userId: string) {
    // console.log('Received userId:', userId);
    return this.applicationService.findProjectByUserId(userId);
  }

  @ApiTags('application')
  @Get('project/and/application/user/:userId')
  async findProjectAndApplicationByUserId(@Param('userId') userId: string) {
    return this.applicationService.findProjectAndApplicationByUserId(userId);
  }
  // fund application by UPI but group by project
  @ApiTags('application')
  @Get('application/upi/search')
  async searchApplicationsByUPI(@Query('upi') upi: string) {
    return this.applicationService.searchApplicationByUPI(upi);
  }

  @ApiTags('application')
  @Get('application/associated/upi/search')
  async searchApplicationsByUPIAssociated(@Query('upi') upi: string) {
    return this.applicationService.searchApplicationByUPIAssociated(upi);
  }

  @ApiTags('application')
  @Get('project/and/application/engORarch/:userId')
  async findProjectAndApplicationBySubmittedUserId(
    @Param('userId') userId: string,
  ) {
    return this.applicationService.findProjectAndApplicationBySubmittedUserId(
      userId,
    );
  }

  // Edit the project when it is needed
  @ApiTags('application')
  @Patch('project/:id')
  async updateProject(@Param('id') id: string, @Body() projectDto: ProjectDto) {
    return this.applicationService.updateProject(id, projectDto);
  }

  @ApiTags('application')
  @Delete('project/:id')
  async removeProject(@Param('id') id: string) {
    return this.applicationService.removeProject(id);
  }

  @ApiTags('application')
  @Get('project/landOwners/:agencyId')
  async findAllUsersByAgency(@Param('agencyId') agencyId: string) {
    return this.applicationService.findAllApplicantInTheAgency(agencyId);
  }

  generateAlphanumericPart(): string {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let alphabeticPart = '';
    for (let i = 0; i < 4; i++) {
      alphabeticPart += alphabet.charAt(
        Math.floor(Math.random() * alphabet.length),
      ); // Randomly select alphabetic character
    }
    const timestamp = Date.now().toString().slice(-5); // Get last 5 digits of current timestamp
    const numericPart = Math.floor(Math.random() * 100000); // Generate random number
    const numericString = (numericPart + `-` + timestamp).padStart(10, '0'); // Concatenate random number with timestamp and format as a 10-digit string
    return `${alphabeticPart}-${numericString}`;
  }
  // // application
  // @ApiTags('application')
  // @HttpCode(HttpStatus.OK)
  // @Post('application')
  // async CreateApplication(@Body() applicationDto: ApplicationDto) {
  //   return this.applicationService.createApplication(applicationDto);
  // }

  // application with ip, browser log
  // submit application
  @ApiTags('application')
  @HttpCode(HttpStatus.OK)
  @Post('application')
  async CreateApplication(
    @Body() applicationDto: ApplicationDto,
    @Req() request: Request,
  ) {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.applicationService.createApplication(
      applicationDto,
      clientIp,
      userAgent,
    );
  }

  @ApiTags('application')
  @Get('submissionLog')
  async findAllSubmissionLogs() {
    return this.applicationService.findAllSubmissionLogs();
  }

  @ApiTags('application')
  @Get('submissionLog/:applicationId')
  async findOneSubmissionLogByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.applicationService.findOneSubmissionLog(applicationId);
  }

  // getting application by date range
  @ApiTags('application')
  @Get('applicationBydateRange')
  async searchApplicationsByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    if (!startDate || !endDate) {
      throw new HttpException(
        'Start date and end date are required',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.applicationService.findAllApplicationsBySubmissionDateRange(
      new Date(startDate),
      new Date(endDate),
    );
  }
  // getting application by date range
  @ApiTags('application')
  @Get('applicationBydateRangeMonitor')
  async searchApplicationsByDateRangeFormonioting(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    if (!startDate || !endDate) {
      throw new HttpException(
        'Start date and end date are required',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.applicationService.searchApplicationsByDateRangeFormonioting(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // @ApiTags('applications')
  // @Post('application')
  // async CreateApplication(@Body() applicationDto: ApplicationDto) {
  //   // try {
  //   // const alphanumericPart = this.generateAlphanumericPart();
  //   //   applicationDto.applicationName = `${applicationDto.agencyCode}-${applicationDto.permitTypeCode}-${alphanumericPart}`;
  //   //   // this.applicationName = `COK-INS-${alphanumericPart}`;
  //   //   console.log(applicationDto.applicationName);
  //   // } catch (error) {}

  //   return this.applicationService.createApplication(applicationDto);
  // }

  // @ApiTags('application')
  // @Get('')
  // async findApplications() {
  //   return this.applicationService.findAllApplicationsWithDetails();
  // }

  // WithPagination
  @ApiTags('application')
  @Get('')
  async findApplications(@Query('page') page = 1, @Query('limit') limit = 100) {
    return this.applicationService.findAllApplicationsWithDetails(
      Number(page),
      Number(limit),
    );
  }

  // @ApiTags('application')
  // @Get('application')
  // async findAllApplications() {
  //   return this.applicationService.findAllApplications();
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application')
  async findAllApplications(
    @Query('page') page = 1,
    @Query('limit') limit = 100,
    @Query('search') search?: string,
    @Query('statusId') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start =
      startDate && !isNaN(Date.parse(startDate))
        ? new Date(startDate)
        : undefined;
    const end =
      endDate && !isNaN(Date.parse(endDate)) ? new Date(endDate) : undefined;

    return this.applicationService.findAllApplications(
      Number(page),
      Number(limit),
      search,
      status,
      start,
      end,
    );
  }

  @ApiTags('application')
  @Get('application/user/:userId')
  async findAllApplicationByUserId(@Param('userId') userId: string) {
    return this.applicationService.findAllApplicationsWithDetailsByUserId(
      userId,
    );
  }

  @ApiTags('application')
  @Get('application/landOwner/:userId')
  async findAllApplicationByLandOwnerUserId(@Param('userId') userId: string) {
    return this.applicationService.findAllApplicationsWithDetailsByLandOwnerUserId(
      userId,
    );
  }

  // @ApiTags('application')
  // @Get('application/agency/:agencyId')
  // async findAllApplicationByAgencyId(@Param('agencyId') agencyId: string) {
  //   return this.applicationService.findAllApplicationsWithDetailsByAgency(
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application/agency/:agencyId')
  async findAllApplicationByAgencyId(
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationsWithDetailsByAgency(
      agencyId,
      page,
      limit,
    );
  }

  // @ApiTags('application')
  // @Get('application/applicationStatus/:applicationStatusId')
  // async findAllApplicationByApplicationStatusId(
  //   @Param('applicationStatusId') applicationStatusId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsWithDetailsByApplicationStatus(
  //     applicationStatusId,
  //   );
  // }

  @ApiTags('application')
  @Get('application/applicationStatus/:applicationStatusId')
  async findAllApplicationByApplicationStatusId(
    @Param('applicationStatusId') applicationStatusId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationsWithDetailsByApplicationStatus(
      applicationStatusId,
      page,
      limit,
    );
  }

  // @ApiTags('application')
  // @Get('application/:applicationStatusId/agency/:agencyId')
  // async findAllApplicationByApplicationStatusIdAndAgencyId(
  //   @Param('applicationStatusId') applicationStatusId: string,
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
  //     applicationStatusId,
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application/:applicationStatusId/agency/:agencyId')
  async findAllApplicationByApplicationStatusIdAndAgencyId(
    @Param('applicationStatusId') applicationStatusId: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
      applicationStatusId,
      agencyId,
      page,
      limit,
    );
  }

  // @ApiTags('application')
  // @Get('inspectionApplication/:applicationStatusId/agency/:agencyId')
  // async findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
  //   @Param('applicationStatusId') applicationStatusId: string,
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
  //     applicationStatusId,
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('inspectionApplication/:applicationStatusId/agency/:agencyId')
  async findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
    @Param('applicationStatusId') applicationStatusId: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationWithInspectionByApplicationStatusIdAndAgencyId(
      applicationStatusId,
      agencyId,
      page,
      limit,
    );
  }

  // WithPagination
  @ApiTags('application')
  @Get('inspectionApplication/status/:applicationStatusCode/agency/:agencyId')
  async findAllApplicationWithInspectionByApplicationStatusCodeAndAgencyId(
    @Param('applicationStatusCode') applicationStatusCode: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationWithInspectionByApplicationStatusCodeAndAgencyId(
      applicationStatusCode,
      agencyId,
      page,
      limit,
    );
  }

  // @ApiTags('application')
  // @Get('withouInspectionApplication/:applicationStatusId/agency/:agencyId')
  // async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
  //   @Param('applicationStatusId') applicationStatusId: string,
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
  //     applicationStatusId,
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('withouInspectionApplication/:applicationStatusId/agency/:agencyId')
  async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
    @Param('applicationStatusId') applicationStatusId: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId(
      applicationStatusId,
      agencyId,
      page,
      limit,
    );
  }

  // WithPagination withCode
  @ApiTags('application')
  @Get(
    'withouInspectionApplication/status/:applicationStatusCode/agency/:agencyId',
  )
  async findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId2(
    @Param('applicationStatusCode') applicationStatusCode: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationWithoutInspectionByApplicationStatusIdAndAgencyId2(
      applicationStatusCode,
      agencyId,
      page,
      limit,
    );
  }

  // for director not in COK ==> REV, RESUB, SUB
  // @ApiTags('application')
  // @Get('application/director/agency/:agencyId')
  // async findAllApplicationForDirectorAndAgencyId(
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationForDirectorAndAgencyId(
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application/director/agency/:agencyId')
  async findAllApplicationForDirectorAndAgencyId(
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationForDirectorAndAgencyId(
      agencyId,
      page,
      limit,
    );
  }

  @ApiTags('application')
  @Post('applications/report')
  async findApplicationsByParamsInReport(
    @Body('applicationStatusId') applicationStatusId?: string,
    @Body('agencyId') agencyId?: string,
    @Body('permitTypeId') permitTypeId?: string,
  ) {
    return this.applicationService.findApplicationsByParams(
      applicationStatusId,
      agencyId,
      permitTypeId,
    );
  }

  // @ApiTags('application')
  // @Get('application/applicationStatus/:applicationStatusId')
  // async findAllApplicationByApplicationStatusId(
  //   @Param('applicationStatusId') applicationStatusId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsWithDetailsByApplicationStatusAndAgencyId(
  //     applicationStatusId,
  //   );
  // }

  @ApiTags('application')
  @Post('application/generalSearch')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        agencyId: { type: 'string', nullable: true },
        applicationName: { type: 'string', nullable: true },
        UPI: { type: 'string', nullable: true },
        applicationStatusId: { type: 'string', nullable: true },
        permitTypeId: { type: 'string', nullable: true },
        categoryTypeId: { type: 'string', nullable: true },
        buildTypeId: { type: 'string', nullable: true },
      },
    },
  })
  async searchApplications(
    @Body()
    bodyParams: {
      agencyId?: string;
      applicationName?: string;
      UPI?: string;
      applicationStatusId?: string;
      permitTypeId?: string;
      categoryTypeId?: string;
      buildTypeId?: string;
    },
  ) {
    return await this.applicationService.generalSearchApplications(bodyParams);
  }

  @ApiTags('application')
  @Get('application/nonObjections')
  async findAllApplicationsWithNonObjections() {
    return this.applicationService.findAllApplicationsWithNonObjections();
  }

  @ApiTags('application')
  @Get('application/nonObjections/agency/:agencyId')
  async findAllApplicationsWithNonObjectionInAgency(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.findAllApplicationsWithNonObjectionInAgency(
      agencyId,
    );
  }

  @ApiTags('application')
  @Get('application/categoryType/:categoryTypeId')
  async findProjectAndApplicationByCategoryTypeId(
    @Param('categoryTypeId') categoryTypeId: string,
  ) {
    return this.applicationService.findApplicationsWithDetailsByCategoryTypeId(
      categoryTypeId,
    );
  }

  @ApiTags('application')
  @Get('application/dashboard/user/:userId')
  async findAllApplicationByUserIdOnDashboard(@Param('userId') userId: string) {
    return this.applicationService.countApplicationsByStatus(userId);
  }

  @ApiTags('application')
  @Get('application/dashboard/submittedUser/:submittedUserId')
  async findAllApplicationBySubmittedUserOnDashboard(
    @Param('submittedUserId') submittedUserId: string,
  ) {
    return this.applicationService.countApplicationsByStatusSubmittedUser(
      submittedUserId,
    );
  }

  // Dashboard for inspection
  @ApiTags('application')
  @Get('dashboardInspectionAll/:agencyId')
  async findAllApplicationByAgencyIdOnDashboardInspection(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.countApplicationsForInspection(agencyId);
  }

  @ApiTags('application')
  @Get('application/dashboardAgency/:agencyId')
  async findAllApplicationByAgencyIdOnDashboard(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.countApplicationsByStatusByAgency(agencyId);
  }

  // Dashboard for foundation
  @ApiTags('application')
  @Get('application/dashboard/inspection/foundationAll/:agencyId')
  async DashboardForFoundationNotice(@Param('agencyId') agencyId: string) {
    return this.applicationService.countApplicationsByStatusByAgencyInspectionFoundation(
      agencyId,
    );
  }

  // Dashboard for occupancy
  @ApiTags('application')
  @Get('application/dashboard/inspection/occupancyAll/:agencyId')
  async DashboardForOccupancyPermit(@Param('agencyId') agencyId: string) {
    return this.applicationService.countApplicationsByStatusByAgencyInspectionOccupancy(
      agencyId,
    );
  }

  // All permits for inspection
  // @ApiTags('application')
  // @Get('application/allData/inspection/:agencyId')
  // async findAllApplicationByAgencyIdForInspection(
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsByAgencyIdForInspection(
  //     agencyId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application/allData/inspection/:agencyId')
  async findAllApplicationByAgencyIdForInspection(
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationsByAgencyIdForInspection(
      agencyId,
      page,
      limit,
    );
  }

  // // All permits for inspection by reviewers
  // @ApiTags('application')
  // @Get('application/allData/inspection/reviewer/:userId')
  // async findAllApplicationByAgencyIdForInspectionReviewer(
  //   @Param('userId') userId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsByAgencyIdForInspectionReviewer(
  //     userId,
  //   );
  // }

  // WithPagination
  @ApiTags('application')
  @Get('application/allData/inspection/reviewer/:userId')
  async findAllApplicationByAgencyIdForInspectionReviewer(
    @Param('userId') userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findAllApplicationsByAgencyIdForInspectionReviewer(
      userId,
      page,
      limit,
    );
  }

  //  All permits for foundation
  @ApiTags('application')
  @Get('application/foundation/inspection/:agencyId')
  async findAllApplicationsByAgencyIdForInspectionFoundation(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.findAllApplicationsByAgencyIdForInspectionFoundation(
      agencyId,
    );
  }

  //  All permits for occupancy
  @ApiTags('application')
  @Get('application/occupancy/inspection/:agencyId')
  async findAllApplicationsByAgencyIdForInspectionOccupancy(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.findAllApplicationsByAgencyIdForInspectionOccupancy(
      agencyId,
    );
  }

  // @ApiTags('application')
  // @Get('application/dashboardAgency/:agencyId')
  // async findAllApplicationByAgencyIdOnDashboard(
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   const validAgencyId = this.extractUUID({ id: agencyId });
  //   return this.applicationService.countApplicationsByStatusByAgency(
  //     validAgencyId,
  //   );
  // }

  @ApiTags('application')
  @Get('application/dashboard/Agency/:agencyId/role/:roleCode')
  async countApplicationsByStatusByAgencyAndRole(
    @Param('agencyId') agencyId: string,
    @Param('roleCode') roleCode: string,
  ) {
    return this.applicationService.countApplicationsByStatusByAgencyAndRole(
      agencyId,
      roleCode,
    );
  }

  extractUUID(input: { id: string }): string {
    return input.id;
  }

  @ApiTags('application')
  @Get('application/dashboard/all/')
  async findAllApplicationOnDashboard() {
    return this.applicationService.countAllApplicationsByStatus();
  }

  @ApiTags('application')
  @Get('application/:id')
  async findOneApplication(@Param('id') id: string) {
    return this.applicationService.findOneApplication(id);
  }

  // @ApiTags('application')
  // @HttpCode(HttpStatus.OK)
  // @Post('application/submit/:id')
  // async updateApplicationSubmitController(
  //   @Param('id') id: string,
  //   @Body() applicationDtoUpdate: ApplicationDtoUpdate,
  //   @Req() request: Request,
  // ) {
  //   const clientIp = request.ip;
  //   const userAgent = request.headers['user-agent'];

  //   return this.applicationService.updateApplicationSubmit(
  //     id,
  //     applicationDtoUpdate,
  //     clientIp,
  //     userAgent,
  //   );
  // }

  @ApiTags('application')
  @HttpCode(HttpStatus.OK)
  @Post('application/submit/:id')
  async updateApplicationSubmitController(
    @Param('id') id: string,
    @Body() applicationDtoUpdate: ApplicationDtoUpdate,
    @Req() request: Request,
  ) {
    const forwardedFor = request.headers['x-forwarded-for'];
    const clientIp = Array.isArray(forwardedFor)
      ? forwardedFor[0]
      : forwardedFor?.split(',')[0] || request.socket.remoteAddress;

    const userAgent = request.headers['user-agent'];

    return this.applicationService.updateApplicationSubmit(
      id,
      applicationDtoUpdate,
      clientIp,
      userAgent,
    );
  }

  private getClientIp(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded) {
      return (forwarded as string).split(',')[0].trim(); // Get first IP if multiple
    }
    return req.socket.remoteAddress || ''; // Fallback if header is missing
  }

  // @ApiTags('application')
  // @Put('application/submit/:id')
  // updateApplicationSubmitController(
  //   @Param('id') id: string,
  //   @Body() applicationDtoUpdate: ApplicationDtoUpdate,
  // ) {
  //   return this.applicationService.updateApplicationSubmit(
  //     id,
  //     applicationDtoUpdate,
  //   );
  // }

  // resubmit with submission logs
  @ApiTags('application')
  @HttpCode(HttpStatus.OK)
  @Put('application/resubmit/:id')
  async updateApplicationResubmitController(
    @Param('id') id: string,
    @Body() applicationDtoUpdate: ApplicationDtoUpdate,
    @Req() request: Request,
  ) {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.applicationService.updateApplicationResubmit(
      id,
      applicationDtoUpdate,
      clientIp,
      userAgent,
    );
  }
  // @ApiTags('application')
  // @Put('application/resubmit/:id')
  // updateApplicationResubmitController(
  //   @Param('id') id: string,
  //   @Body() applicationDtoUpdate: ApplicationDtoUpdate,
  // ) {
  //   return this.applicationService.updateApplicationResubmit(
  //     id,
  //     applicationDtoUpdate,
  //   );
  // }

  // Edit the project when it is needed for resubmission
  @ApiTags('application')
  @Patch('application/:id')
  async updateApplication(
    @Param('id') id: string,
    @Body() applicationDto: ApplicationDto,
  ) {
    return this.applicationService.updateApplicationToResubmit(
      id,
      applicationDto,
    );
  }
  // function to clear data
  cleanDto(obj: any): any {
    if (typeof obj !== 'object' || obj === null) return obj;

    return Object.fromEntries(
      Object.entries(obj)
        .filter(
          ([, value]) =>
            value !== null &&
            value !== '' &&
            value !== 'string' &&
            value !== undefined,
        )
        .map(([key, value]) => [key, this.cleanDto(value)]),
    );
  }

  // Edit the project when it is needed for resubmission but no email update only form of development
  @ApiTags('application')
  @Patch('application/data/:id')
  async updateApplicationToResubmitOnlyData(
    @Param('id') id: string,
    @Body() applicationDto: ApplicationDto,
  ) {
    return this.applicationService.updateApplicationToResubmitOnlyData(
      id,
      applicationDto,
    );
  }

  // resubmit on irembo.
  @ApiTags('application')
  @HttpCode(HttpStatus.OK)
  @Patch('resubmitIrembo')
  async updateApplicationResubmitIrembo(
    @Query('id') id: string,
    @Body() applicationDtoUpdate: ApplicationResubmitIremboDto,
    @Req() request: Request,
  ) {
    // fuction to clean the dto
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];
    const cleanedDto = this.cleanDto({
      ...applicationDtoUpdate,
    });

    // return cleanedDto;

    return this.applicationService.ApplicationResubmitIrembo(
      id,
      cleanedDto,
      clientIp,
      userAgent,
    );
  }

  // @ApiTags('applications')
  // @Get('application/:projectId')
  // async findOneApplicationByProjectId(@Param('projectId') projectId: string) {
  //   return this.applicationService.findOneApplicationByProjectId(projectId);
  // }

  @ApiTags('application')
  @Get('application/:projectId/permit-type/:permitTypeId')
  async getApplicationsByProjectAndPermitType(
    @Param('projectId') projectId: string,
    @Param('permitTypeId') permitTypeId: string,
  ) {
    return this.applicationService.getApplicationsByProjectAndPermitType(
      projectId,
      permitTypeId,
    );
  }

  @ApiTags('application')
  @Get('application/AllDetails/:id')
  async findOneApplicationAllDetails(@Param('id') id: string) {
    return this.applicationService.findOneApplicationAllDetails(id);
  }

  // @ApiTags('applications')
  // @Get('application/AllDetails/:applicationNumber')
  // async findOneApplicationAllDetailsByApplicationId(
  //   @Param('applicationNumber') applicationNumber: string,
  // ) {
  //   return this.applicationService.findOneApplicationAllDetails(
  //     applicationNumber,
  //   );
  // }

  @ApiTags('application')
  @Get('application/applicationName/search')
  async findOneApplicationByApplicationId(@Query('search') search: string) {
    const searchFields: (keyof Application)[] = ['applicationName'];
    const relations: string[] = [
      'projects',
      'permitTypes',
      'categoryTypes',
      'buildTypes',
      'applicationStatus',
    ];

    // Check if search is empty
    if (!search) {
      // return { message: 'Application not found' };
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Application not found',
      };
    }

    const dataToBeReturned =
      await this.genericSearchWithRelationsOptions2.search(
        this.applicationRepository,
        searchFields,
        search,
        relations,
      );

    // Check if no items are found
    if (!dataToBeReturned.items || dataToBeReturned.items.length === 0) {
      // return { message: 'Application not found' };
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Application not found',
      };
    }

    const applicationId = dataToBeReturned.items[0].id;
    const dataToBeReturnedWithDetails =
      await this.findOneApplicationAllDetails(applicationId);

    return dataToBeReturnedWithDetails;
  }

  @ApiTags('application')
  @Get('application/search/:applicationName')
  async searchApplicationByApplicationName(
    @Param('applicationName') applicationName: string,
  ) {
    return this.applicationService.searchApplicationByApplicationName(
      applicationName,
    );
  }

  @ApiTags('application')
  @Get('application/:applicationName/agency/:agencyId')
  async searchApplicationByApplicationNameInAgency(
    @Param('agencyId') agencyId: string,
    @Param('applicationName') applicationName: string,
  ) {
    return this.applicationService.searchApplicationByApplicationNameInAgency(
      applicationName,
      agencyId,
    );
  }

  // async findOneApplicationByApplicationId(@Query('search') search: string) {
  //   const searchFields: (keyof Application)[] = ['applicationName'];
  //   const relations: string[] = [
  //     'projects',
  //     'permitTypes',
  //     'categoryTypes',
  //     'buildTypes',
  //     'applicationStatus',
  //   ];
  //   const dataToBeReturned = this.genericSearchWithRelationsOptions2.search(
  //     this.applicationRepository,
  //     searchFields,
  //     search,
  //     relations,
  //   );

  //   const applicationId = (await dataToBeReturned).items[0].id;
  //   const dataToBeReturnedWithDetails =
  //     await this.findOneApplicationAllDetails(applicationId);

  //   return dataToBeReturnedWithDetails;
  // }

  // @ApiTags('applications')
  // @Get('application/applicationName/search')
  // async findOneApplicationByApplicationId(@Query('search') search: string) {
  //   const searchFields: (keyof Application)[] = ['applicationName'];
  //   return this.genericSearch7.search(
  //     this.applicationRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  // @ApiTags('applications')
  // @Patch('application/:id')
  // async updateApplication(
  //   @Param('id') id: string,
  //   @Body() applicationDto: FindUpdateApplicationDto,
  // ) {
  //   return this.applicationService.updateApplication(id, applicationDto);
  // }

  @ApiTags('application')
  @Delete('application/:id')
  async removeApplication(@Param('id') id: string) {
    return this.applicationService.removeApplication(id);
  }

  @ApiTags('application')
  @Delete('application/draft/:id')
  async deleteDraftApplication(@Param('id') id: string) {
    return this.applicationService.deleteDraftApplication(id);
  }

  @ApiTags('application')
  @HttpCode(HttpStatus.OK)
  @Put('application/lockApplication/:applicationId')
  async lockApplication(
    @Param('applicationId') applicationId: string,
    @Body() lockApplicationDtoUpdate: LockApplicationDtoUpdate,
    @Req() request: Request,
  ) {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.applicationService.lockApplication(
      applicationId,
      lockApplicationDtoUpdate,
      clientIp,
      userAgent,
    );
  }

  // associated upi
  @ApiTags('application')
  @Post('associatedUPI')
  async CreateAssociatedUPI(@Body() associatedUPIDto: AssociatedUPIDto) {
    return this.applicationService.createAssociatedUPI(associatedUPIDto);
  }

  // sychronize application owners and project name and sesciption
  // associated upi
  @ApiTags('application')
  @Post('synchronization')
  async synchronization() {
    return this.applicationService.synchronization();
  }

  @ApiTags('application')
  @Post('calculatecategory')
  async calculatecategory(@Body() calculateCategoryDto: CalculateCategoryDto) {
    return this.applicationService.calculateCategory(calculateCategoryDto);
  }

  @ApiTags('application')
  @Get('associatedUPI')
  async findAllAssociatedUPIs() {
    return this.applicationService.findAllAssociatedUPIs();
  }
  // geting associated upi by upi
  @ApiTags('application')
  @Get('checkassociatedUPI')
  async associatedUPIByUPI(@Query('upi') upi: string) {
    console.log('UPI received as query param:', upi);
    return this.applicationService.findAssociatedUPIByUPI(upi);
  }

  @ApiTags('application')
  @Get('associatedUPI/:id')
  async findOneAssociatedUPI(@Param('id') id: string) {
    return this.applicationService.findOneAssociatedUPI(id);
  }

  @ApiTags('application')
  @Get('associatedUPI/ByProject/:projectId')
  async findOneAssociatedUPIByProjectId(@Param('projectId') projectId: string) {
    return this.applicationService.findOneAssociatedUPIByProjectId(projectId);
  }

  @ApiTags('application')
  @Get('associatedUPI/upi/search')
  async checkUPIInAssociatedUPI(@Query('upi') upi: string) {
    return this.applicationService.checkUPIInAssociatedUPI(upi);
  }

  // @ApiTags('application')
  // @Get('associatedUPI/isUPIExist/')
  // async checkIfUPIExistInAssociatedUPI(@Query('upi') upi: string) {
  //   return this.applicationService.checkIfUPIExistInAssociatedUPI(upi);
  // }
  @ApiTags('application')
  @Get('associatedUPI/InListIsUPIExist')
  async checkIfUPIExistInAssociatedUPI(@Query('upi') upi: string) {
    return this.applicationService.checkIfUPIExistInAssociatedUPI(upi);
  }

  @ApiTags('application')
  @Patch('associatedUPI/:id')
  async updateAssociatedUPI(
    @Param('id') id: string,
    @Body() associatedUPIDto: AssociatedUPIDto,
  ) {
    return this.applicationService.updateAssociatedUPI(id, associatedUPIDto);
  }

  @ApiTags('application')
  @Delete('associatedUPI/:id')
  async removeAssociatedUPI(@Param('id') id: string) {
    return this.applicationService.removeAssociatedUPI(id);
  }

  // Settings
  // Permit type
  @ApiTags('settings')
  @Post('permitType')
  async CreatePermitType(@Body() permitTypeDto: PermitTypeDto) {
    return this.applicationService.createPermitType(permitTypeDto);
  }

  @ApiTags('settings')
  @Get('permitType')
  async findAllPermitTypes() {
    return this.applicationService.findAllPermitTypes();
  }

  @ApiTags('settings')
  @Get('permitType/:id')
  async findOnePermitType(@Param('id') id: string) {
    return this.applicationService.findOnePermitType(id);
  }

  @ApiTags('settings')
  @Get('permitType/:name')
  async findOnePermitTypeByName(@Param('name') name: string) {
    return this.applicationService.findOnePermitTypeByName(name);
  }

  @ApiTags('settings')
  @Get('permitType/code/search')
  async searchPermitTypeByCode(@Query('search') search: string) {
    const searchFields: (keyof PermitType)[] = ['code'];
    return this.genericSearch2.search(
      this.permitTypeRepository,
      searchFields,
      search,
    );
  }

  // @ApiTags('settings')
  // @Get('permitType/:code')
  // async findOnePermitTypeByCode(@Param('code') code: string) {
  //   console.log('fdsdfa');
  //   return this.applicationService.findOnePermitTypeByCode(code);
  // }

  @ApiTags('settings')
  @Patch('permitType/:id')
  async updatePermitType(
    @Param('id') id: string,
    @Body() permitTypeDto: PermitTypeDto,
  ) {
    return this.applicationService.updatePermitType(id, permitTypeDto);
  }

  @ApiTags('settings')
  @Delete('permitType/:id')
  async removePermitType(@Param('id') id: string) {
    return this.applicationService.removePermitType(id);
  }

  // category rule
  @ApiTags('settings')
  @Post('categoryRule')
  async CreateCategoryRule(@Body() categoryRuleDto: CategoryRuleDto) {
    return this.applicationService.createCategoryRule(categoryRuleDto);
  }

  @ApiTags('settings')
  @Get('categoryRule')
  async findAllCategoryRules() {
    return this.applicationService.findAllCategoryRules();
  }

  @ApiTags('settings')
  @Get('categoryRule/:id')
  async findOneCategoryRule(@Param('id') id: string) {
    return this.applicationService.findOneCategoryRule(id);
  }

  @ApiTags('settings')
  @Patch('categoryRule/:id')
  async updateCategoryRule(
    @Param('id') id: string,
    @Body() categoryRuleDto: CategoryRuleDto,
  ) {
    return this.applicationService.updateCategoryRule(id, categoryRuleDto);
  }

  @ApiTags('settings')
  @Delete('categoryRule/:id')
  async removeCategoryRule(@Param('id') id: string) {
    return this.applicationService.removeCategoryRule(id);
  }

  // category type
  @ApiTags('settings')
  @Post('categoryType')
  async CreateCategoryType(@Body() categoryTypeDto: CategoryTypeDto) {
    return this.applicationService.createCategoryType(categoryTypeDto);
  }

  @ApiTags('settings')
  @Get('categoryType')
  async findAllCategoryTypes() {
    return this.applicationService.findAllCategoryTypes();
  }

  @ApiTags('settings')
  @Get('categoryType/:id')
  async findOneCategoryType(@Param('id') id: string) {
    return this.applicationService.findOneCategoryType(id);
  }

  @ApiTags('settings')
  @Patch('categoryType/:id')
  async updateCategoryType(
    @Param('id') id: string,
    @Body() categoryTypeDto: CategoryTypeDto,
  ) {
    return this.applicationService.updateCategoryType(id, categoryTypeDto);
  }

  @ApiTags('settings')
  @Delete('categoryType/:id')
  async removeCategoryType(@Param('id') id: string) {
    return this.applicationService.removeCategoryType(id);
  }

  @ApiTags('settings')
  @Get('categoryType/code/search')
  async searchCategoryTypeByCode(@Query('search') search: string) {
    const searchFields: (keyof CategoryType)[] = ['code'];
    return this.genericSearch3.search(
      this.categoryTypeRepository,
      searchFields,
      search,
    );
  }

  // build type
  @ApiTags('settings')
  @Post('buildType')
  async CreateBuildType(@Body() buildTypeDto: BuildTypeDto) {
    return this.applicationService.createBuildType(buildTypeDto);
  }

  @ApiTags('settings')
  @Get('buildType')
  async findAllBuildTypes() {
    return this.applicationService.findAllBuildTypes();
  }

  @ApiTags('settings')
  @Get('buildType/:id')
  async findOneBuildType(@Param('id') id: string) {
    return this.applicationService.findOneBuildType(id);
  }

  @ApiTags('settings')
  @Patch('buildType/:id')
  async updateBuildType(
    @Param('id') id: string,
    @Body() buildTypeDto: BuildTypeDto,
  ) {
    return this.applicationService.updateBuildType(id, buildTypeDto);
  }

  @ApiTags('settings')
  @Delete('buildType/:id')
  async removeBuildType(@Param('id') id: string) {
    return this.applicationService.removeBuildType(id);
  }

  @ApiTags('settings')
  @Get('buildType/code/search')
  async searchBuildTypeByCode(@Query('search') search: string) {
    const searchFields: (keyof BuildType)[] = ['code'];
    return this.genericSearch4.search(
      this.buildTypeRepository,
      searchFields,
      search,
    );
  }

  // document type
  @ApiTags('settings')
  @Post('documentType')
  async CreateDocumentType(@Body() documentTypeDto: DocumentTypeDto) {
    return this.applicationService.createDocumentType(documentTypeDto);
  }

  @ApiTags('settings')
  @Get('documentType')
  async findAllDocumentTypes() {
    return this.applicationService.findAllDocumentTypes();
  }

  @ApiTags('settings')
  @Get('documentType/:id')
  async findOneDocumentType(@Param('id') id: string) {
    return this.applicationService.findOneDocumentType(id);
  }

  @ApiTags('settings')
  @Patch('documentType/:id')
  async updateDocumentType(
    @Param('id') id: string,
    @Body() documentTypeDto: DocumentTypeDto,
  ) {
    return this.applicationService.updateDocumentType(id, documentTypeDto);
  }

  @ApiTags('settings')
  @Delete('documentType/:id')
  async removeDocumentType(@Param('id') id: string) {
    return this.applicationService.removeDocumentType(id);
  }

  @ApiTags('settings')
  @Get('documentType/code/search')
  async searchDocumentTypeByCode(@Query('search') search: string) {
    const searchFields: (keyof DocumentType)[] = ['code'];
    return this.genericSearch6.search(
      this.documentTypeRepository,
      searchFields,
      search,
    );
  }

  // required document
  @ApiTags('settings')
  @Post('requiredDocument')
  async CreateRequiredDocument(
    @Body() requiredDocumentDto: RequiredDocumentDto,
  ) {
    return this.applicationService.createRequiredDocument(requiredDocumentDto);
  }

  @ApiTags('settings')
  @Get('requiredDocument')
  async findAllRequiredDocuments() {
    return this.applicationService.findAllRequiredDocuments();
  }

  @ApiTags('settings')
  @Get('requiredDocument/:id')
  async findOneRequiredDocument(@Param('id') id: string) {
    return this.applicationService.findOneRequiredDocument(id);
  }

  @ApiTags('settings')
  @Patch('requiredDocument/:id')
  async updateRequiredDocument(
    @Param('id') id: string,
    @Body() requiredDocumentDto: RequiredDocumentDto,
  ) {
    return this.applicationService.updateRequiredDocument(
      id,
      requiredDocumentDto,
    );
  }

  @ApiTags('settings')
  @Delete('requiredDocument/:id')
  async removeRequiredDocument(@Param('id') id: string) {
    return this.applicationService.removeRequiredDocument(id);
  }

  @ApiTags('settings')
  @Get('requiredDocument/permitType/:permitTypeId')
  async findAllRequiredDocumentByPermitTypeId(
    @Param('permitTypeId') permitTypeId: string,
  ) {
    return this.applicationService.findRequiredDocumentByPermitTypeId(
      permitTypeId,
    );
  }

  @ApiTags('settings')
  @Get('requiredDocument/permitType/:permitTypeId/category/:categoryId')
  async findAllRequiredDocumentByPermitTypeAndCategory(
    @Param('permitTypeId') permitTypeId: string,
    @Param('categoryId') categoryId: string,
  ) {
    return this.applicationService.findAllRequiredDocumentByPermitTypeAndCategory(
      permitTypeId,
      categoryId,
    );
  }

  // technology survey
  @ApiTags('settings')
  @Post('technologySurvey')
  async CreateTechnologySurvey(
    @Body() technologySurveyDto: TechnologySurveyDto,
  ) {
    return this.applicationService.createTechnologySurvey(technologySurveyDto);
  }

  @ApiTags('settings')
  @Get('technologySurvey')
  async findAllTechnologySurveys() {
    return this.applicationService.findAllTechnologySurveys();
  }

  @ApiTags('settings')
  @Get('technologySurvey/:id')
  async findOneTechnologySurvey(@Param('id') id: string) {
    return this.applicationService.findOneTechnologySurvey(id);
  }

  @ApiTags('settings')
  @Patch('technologySurvey/:id')
  async updateTechnologySurvey(
    @Param('id') id: string,
    @Body() technologySurveyDto: TechnologySurveyDto,
  ) {
    return this.applicationService.updateTechnologySurvey(
      id,
      technologySurveyDto,
    );
  }

  @ApiTags('settings')
  @Delete('technologySurvey/:id')
  async removeTechnologySurvey(@Param('id') id: string) {
    return this.applicationService.removeTechnologySurvey(id);
  }

  // Application Status
  @ApiTags('settings')
  @Post('applicationStatus')
  async CreateApplicationStatus(
    @Body() applicationStatusDto: ApplicationStatusDto,
  ) {
    return this.applicationService.createApplicationStatus(
      applicationStatusDto,
    );
  }

  @ApiTags('settings')
  @Get('applicationStatus')
  async findAllApplicationStatus() {
    return this.applicationService.findAllApplicationStatus();
  }

  @ApiTags('settings')
  @Get('applicationStatus/:id')
  async findOneApplicationStatus(@Param('id') id: string) {
    return this.applicationService.findOneApplicationStatus(id);
  }

  @ApiTags('settings')
  @Patch('applicationStatus/:id')
  async updateApplicationStatus(
    @Param('id') id: string,
    @Body() applicationStatusDto: ApplicationStatusDto,
  ) {
    return this.applicationService.updateApplicationStatus(
      id,
      applicationStatusDto,
    );
  }

  @ApiTags('settings')
  @Delete('applicationStatus/:id')
  async removeApplicationStatus(@Param('id') id: string) {
    return this.applicationService.removeApplicationStatus(id);
  }

  @ApiTags('settings')
  @Get('applicationStatus/code/search')
  async searchApplicationStatusByCode(@Query('search') search: string) {
    const searchFields: (keyof ApplicationStatus)[] = ['code'];
    return this.genericSearch5.search(
      this.applicationStatusRepository,
      searchFields,
      search,
    );
  }

  // Project Status
  @ApiTags('settings')
  @Post('projectStatus')
  async CreateProjectStatus(@Body() projectStatusDto: ProjectStatusDto) {
    return this.applicationService.createProjectStatus(projectStatusDto);
  }

  @ApiTags('settings')
  @Get('projectStatus')
  async findAllProjectStatus() {
    return this.applicationService.findAllProjectStatus();
  }

  @ApiTags('settings')
  @Get('projectStatus/:id')
  async findOneProjectStatus(@Param('id') id: string) {
    return this.applicationService.findOneProjectStatus(id);
  }

  @ApiTags('settings')
  @Patch('projectStatus/:id')
  async updateProjectStatus(
    @Param('id') id: string,
    @Body() projectStatusDto: ProjectStatusDto,
  ) {
    return this.applicationService.updateProjectStatus(id, projectStatusDto);
  }

  @ApiTags('settings')
  @Delete('projectStatus/:id')
  async removeProjectStatus(@Param('id') id: string) {
    return this.applicationService.removeProjectStatus(id);
  }

  @ApiTags('settings')
  @Get('projectStatus/code/search')
  async searchProjectStatusByCode(@Query('search') search: string) {
    const searchFields: (keyof ProjectStatus)[] = ['code'];
    return this.genericSearch8.search(
      this.projectStatusRepository,
      searchFields,
      search,
    );
  }

  // Assignee
  @ApiTags('application')
  @Post('assignee')
  async CreateAssignee(@Body() assigneeDto: AssigneeDto) {
    return this.applicationService.createAssignee(assigneeDto);
  }

  @ApiTags('application')
  @Get('assignee')
  async findAllAssignee() {
    return this.applicationService.findAllAssignee();
  }

  @ApiTags('application')
  @Get('assignee/:id')
  async findOneAssignee(@Param('id') id: string) {
    return this.applicationService.findOneAssignee(id);
  }

  @ApiTags('application')
  @Get('assignee/byProject/:projectId')
  async findAssigneeOfTheProject(@Param('projectId') projectId: string) {
    return this.applicationService.findAssigneeOfTheProject(projectId);
  }

  @ApiTags('application')
  @Patch('assignee/:id')
  async updateAssignee(
    @Param('id') id: string,
    @Body() updateAssigneeDto: UpdateAssigneeDto,
  ) {
    return this.applicationService.updateAssignee(id, updateAssigneeDto);
  }

  @ApiTags('application')
  @Post('assignee/deleteEngineerOnProject')
  async deleteEngineerOnProject(
    @Body() deleteEngineerOnProjectDto: DeleteEngineerOnProjectDto,
  ) {
    console.log('deleteEngineerOnProjectDto', deleteEngineerOnProjectDto);
    return this.applicationService.deleteEngineerOnProject(
      deleteEngineerOnProjectDto,
    );
  }

  @ApiTags('application')
  @Delete('assignee/:id')
  async removeAssignee(@Param('id') id: string) {
    return this.applicationService.removeAssignee(id);
  }

  // // add another reviewer
  // @ApiTags('application')
  // @Post('assignee/:projectUserId/reject/:licenseNumber/project/:projectId')
  // async rejectProjectAsAssignee(
  //   @Param('projectUserId') projectUserId: string,
  //   @Param('licenseNumber') licenseNumber: string,
  //   @Param('projectId') projectId: string,
  // ) {
  //   return this.applicationService.rejectProjectAsAssignee(
  //     projectUserId,
  //     licenseNumber,
  //     projectId,
  //   );
  // }
  // add another reviewer
  // @ApiTags('application')
  // @Post('assignee/:projectUserId/reject/:licenseNumber/project/:projectId')
  // async rejectProjectAssigned(
  //   @Param('projectUserId') projectUserId: string,
  //   @Param('licenseNumber') licenseNumber: string,
  //   @Param('projectId') projectId: string,
  // ) {
  //   return this.applicationService.rejectProjectAsAssignee(
  //     projectUserId,
  //     licenseNumber,
  //     projectId,
  //   );
  // }
  @ApiTags('application')
  @Post('assignee/rejectProjectAsEngOrArch')
  async rejectProjectAssigned(@Body() rejectProjectDto: RejectProjectDto) {
    return this.applicationService.rejectProjectAsAssignee(rejectProjectDto);
  }
  // // add another reviewer
  // @ApiTags('application')
  // @Post('assignee/:projectUserId/reject/:licenseNumber')
  // async rejectProjectAsAssignee(
  //   @Param('projectUserId') projectUserId: string,
  //   @Param('licenseNumber') licenseNumber: string,
  // ) {
  //   return this.applicationService.rejectProjectAsAssignee(
  //     projectUserId,
  //     licenseNumber,
  //   );
  // }

  @ApiTags('application')
  @Put('assignee/:id/status')
  async updateAssignStatus(@Param('id') id: string): Promise<Assignee> {
    return await this.applicationService.updateAssignStatus(id);
  }

  @ApiTags('application')
  @Put('assignee/reassign/:id')
  async reassignEngineer(@Param('id') id: string): Promise<Assignee> {
    return await this.applicationService.reaAssigneeStatus(id);
  }

  @ApiTags('application')
  @Get('assignee/licenseNumber/search')
  // async searchAssigneeByCode(@Query('search') search: string) {
  //   const searchFields: (keyof Assignee)[] = ['licenseNumber'];
  //   const relations: string[] = ['projects'];
  //   const dataToBeReturned = await this.genericSearch9.search(
  //     this.assigneeRepository,
  //     searchFields,
  //     search,
  //     relations,
  //   );

  //   // Check if any items are returned
  //   if (dataToBeReturned.items.length > 0) {
  //     // Iterate through each item and fetch details
  //     for (const item of dataToBeReturned.items) {
  //       const userId = item.projects.userId;
  //       const projectStatusId = item.projectStatusId;
  //       const projectStatus = await this.findOneProjectStatus(projectStatusId);
  //       const details = await this.checkUser(userId);

  //       (item as AssigneeWithDetails).details = details;
  //       (item as AssigneeWithDetails).projectStatus = projectStatus;
  //     }
  //   }

  //   return dataToBeReturned;
  // }
  // @ApiTags('application')
  // @Get('assignee/licenseNumber/search')
  // async searchAssigneeByCode(@Query('search') search: string) {
  //   const searchFields: (keyof Assignee)[] = ['licenseNumber'];
  //   const relations: string[] = ['projects'];
  //   const dataToBeReturned = await this.genericSearch9.search(
  //     this.assigneeRepository,
  //     searchFields,
  //     search,
  //     relations,
  //   );

  //   // Check if any items are returned
  //   if (dataToBeReturned.items.length > 0) {
  //     // Iterate through each item and fetch details
  //     for (const item of dataToBeReturned.items) {
  //       const userId = item.projects.userId;
  //       const projectId = item.projects.id;
  //       const projectStatus = this.applicationService.findOneProject(projectId);
  //       const details = await this.checkUser(userId);

  //       (item as AssigneeWithDetails).details = details;
  //       (item as AssigneeWithDetails).projectStatus = projectStatus;
  //     }
  //   }

  //   return dataToBeReturned;
  // }
  @ApiTags('application')
  @Get('assignee/licenseNumber/search')
  async searchAssigneeByCode(@Query('search') search: string) {
    const searchFields: (keyof Assignee)[] = ['licenseNumber'];
    const relations: string[] = ['projects'];
    const dataToBeReturned = await this.genericSearch9.search(
      this.assigneeRepository,
      searchFields,
      search,
      relations,
    );

    // Check if any items are returned
    if (dataToBeReturned.items.length > 0) {
      // Create an array of promises for fetching details and statuses
      const promises = dataToBeReturned.items.map(async (item) => {
        const userId = item.projects.userId;
        const projectId = item.projects.id;

        // Fetch project status and user details
        const [projectStatus, details] = await Promise.all([
          this.applicationService.findOneProject(projectId),
          this.checkUser(userId),
        ]);

        // Create a simplified version of projectStatus with only necessary fields
        const simplifiedProjectStatus = {
          id: projectStatus.id,
          name: projectStatus.projectStatus?.name,
          code: projectStatus.projectStatus?.code,
        };

        // Attach fetched details and simplified status to item
        return {
          ...item,
          details,
          projectStatus: simplifiedProjectStatus,
        };
      });

      // Resolve all promises
      dataToBeReturned.items = await Promise.all(promises);
    }

    return dataToBeReturned;
  }

  // async searchAssigneeByCode(@Query('search') search: string) {
  //   const searchFields: (keyof Assignee)[] = ['licenseNumber'];
  //   const relations: string[] = ['projects'];
  //   const dataToBeReturned = await this.genericSearch9.search(
  //     this.assigneeRepository,
  //     searchFields,
  //     search,
  //     relations,
  //   );

  //   // Check if any items are returned
  //   if ((await dataToBeReturned).items.length > 0) {
  //     const userId = (await dataToBeReturned).items[0].projects.userId;
  //     console.log(userId);
  //     const dataToBeReturnedWithDetails = await this.checkUser(userId);
  //     return {
  //       ...dataToBeReturned,
  //       details: dataToBeReturnedWithDetails,
  //     };
  //   } else {
  //     return dataToBeReturned;
  //   }
  // }

  // // assignUsersFoReview with submission logs
  @ApiTags('application')
  @Post('assignUsersFoReview/:applicationId/assign-users')
  async assignUsersFoReview(
    @Param('applicationId') applicationId: string,
    @Body() userIds: string[],
    @Req() request: Request,
  ): Promise<Application> {
    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'];

    return this.applicationService.assignUsersForReview(
      applicationId,
      userIds,
      clientIp,
      userAgent,
    );
  }

  // // // assignUsersFoReview
  // @ApiTags('application')
  // @Post('assignUsersFoReview/:applicationId/assign-users')
  // async assignUsersFoReview(
  //   @Param('applicationId') applicationId: string,
  //   @Body() userIds: string[],
  // ): Promise<Application> {
  //   return this.applicationService.assignUsersForReview(applicationId, userIds);
  // }

  // // // assignUsersFoReview
  // @ApiTags('application')
  // @Post('assignUsersFoReview/:applicationId/assign-users')
  // async assignUsersFoReview(
  //   @Param('applicationId') applicationId: string,
  //   @Body() userIds: string[],
  // ): Promise<Application> {
  //   return this.applicationService.assignUsersForReview(applicationId, userIds);
  // }

  // // get Reviewers by applicationId
  @ApiTags('application')
  @Get('getReviewers/:applicationId/review-user-ids')
  async getReviewUserIdsByApplicationId(
    @Param('applicationId') applicationId: string,
  ): Promise<string[]> {
    return this.applicationService.getReviewUserIdsByApplicationId(
      applicationId,
    );
  }

  // remove a reviewer on application
  @ApiTags('application')
  @Delete('removeReviewer/:applicationId/remove-review-user/:userId')
  async removeUserFromReview(
    @Param('applicationId') applicationId: string,
    @Param('userId') userId: string,
  ) {
    return this.applicationService.removeUserFromReview2(applicationId, userId);
  }

  // reassign a reviewer on application
  @ApiTags('application')
  @Delete(
    'reassignReviewer/:applicationId/reassign-review-user/:userId/doneBy/:doneByUserId',
  )
  async reassignUserFromReviewConsider(
    @Param('applicationId') applicationId: string,
    @Param('userId') userId: string,
    @Param('doneByUserId') doneByUserId: string,
  ) {
    return this.applicationService.reassignUserFromReviewRHAConsider(
      applicationId,
      userId,
      doneByUserId,
    );
  }
  // // reassign a reviewer on application
  @ApiTags('application')
  @Delete('reassignReviewer/:applicationId/reassign-review-user/:userId')
  async reassignUserFromReview(
    @Param('applicationId') applicationId: string,
    @Param('userId') userId: string,
  ) {
    return this.applicationService.reassignUserFromReview(
      applicationId,
      userId,
    );
  }

  // add onether reviewer
  @ApiTags('application')
  @Post('addReviewer/:applicationId/add-review-user/:userId')
  async addUserToReview(
    @Param('applicationId') applicationId: string,
    @Param('userId') userId: string,
  ): Promise<Application> {
    return this.applicationService.addUserToReview(applicationId, userId);
  }

  // get all application for review
  @ApiTags('application')
  @Get('allApplicationsForReview')
  async findAllWithAssignedUsers(): Promise<Application[]> {
    return this.applicationService.findAllWithAssignedUsers();
  }
  // get my application for review
  @ApiTags('application')
  @Get('by-assign-user-for-review/:userId')
  async findByAssignedUserForReview(
    @Param('userId') userId: string,
  ): Promise<Application[]> {
    return this.applicationService.findAllByAssignUsersForReview(userId);
  }

  // construction Method
  @ApiTags('settings')
  @Post('questionCategory')
  async CreateQuestionCategory(
    @Body() questionCategoryDto: QuestionCategoryDto,
  ) {
    return this.applicationService.createQuestionCategory(questionCategoryDto);
  }

  @ApiTags('settings')
  @Get('questionCategory')
  async findAllQuestionCategory() {
    return this.applicationService.findAllQuestionCategorys();
  }

  @ApiTags('settings')
  @Get('questionCategory/:id')
  async findOneQuestionCategory(@Param('id') id: string) {
    return this.applicationService.findOneQuestionCategory(id);
  }

  @ApiTags('settings')
  @Patch('questionCategory/:id')
  async updateQuestionCategory(
    @Param('id') id: string,
    @Body() questionCategoryDto: QuestionCategoryDto,
  ) {
    return this.applicationService.updateQuestionCategory(
      id,
      questionCategoryDto,
    );
  }

  @ApiTags('settings')
  @Delete('questionCategory/:id')
  async removeQuestionCategory(@Param('id') id: string) {
    return this.applicationService.removeQuestionCategory(id);
  }

  @ApiTags('settings')
  @Get('equipmentCapacity/code/search')
  async searchQuestionCategoryByCode(@Query('search') search: string) {
    const searchFields: (keyof QuestionCategory)[] = ['code'];
    return this.genericSearch12.search(
      this.questionCategoryRepository,
      searchFields,
      search,
    );
  }

  // // construction Method
  // @ApiTags('settings')
  // @Post('constructionMethod')
  // async CreateConstructionMethod(
  //   @Body() constructionMethodDto: ConstructionMethodDto,
  // ) {
  //   return this.applicationService.createConstructionMethod(
  //     constructionMethodDto,
  //   );
  // }

  // @ApiTags('settings')
  // @Get('constructionMethod')
  // async findAllConstructionMethod() {
  //   return this.applicationService.findAllConstructionMethods();
  // }

  // @ApiTags('settings')
  // @Get('constructionMethod/:id')
  // async findOneConstructionMethod(@Param('id') id: string) {
  //   return this.applicationService.findOneConstructionMethod(id);
  // }

  // @ApiTags('settings')
  // @Patch('constructionMethod/:id')
  // async updateConstructionMethod(
  //   @Param('id') id: string,
  //   @Body() constructionMethodDto: ConstructionMethodDto,
  // ) {
  //   return this.applicationService.updateConstructionMethod(
  //     id,
  //     constructionMethodDto,
  //   );
  // }

  // @ApiTags('settings')
  // @Delete('constructionMethod/:id')
  // async removeConstructionMethod(@Param('id') id: string) {
  //   return this.applicationService.removeEquipmentCapacity(id);
  // }

  // @ApiTags('settings')
  // @Get('equipmentCapacity/code/search')
  // async searchConstructionMethodByCode(@Query('search') search: string) {
  //   const searchFields: (keyof ConstructionMethod)[] = ['code'];
  //   return this.genericSearch10.search(
  //     this.equipmentCapacityRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  // // Equipment Capacity
  // @ApiTags('settings')
  // @Post('equipmentCapacity')
  // async CreateEquipmentCapacity(
  //   @Body() equipmentCapacityDto: EquipmentCapacityDto,
  // ) {
  //   return this.applicationService.createEquipmentCapacity(
  //     equipmentCapacityDto,
  //   );
  // }

  // @ApiTags('settings')
  // @Get('equipmentCapacity')
  // async findAllEquipmentCapacity() {
  //   return this.applicationService.findAllEquipmentCapacitys();
  // }

  // @ApiTags('settings')
  // @Get('equipmentCapacity/:id')
  // async findOneEquipmentCapacity(@Param('id') id: string) {
  //   return this.applicationService.findOneEquipmentCapacity(id);
  // }

  // @ApiTags('settings')
  // @Patch('equipmentCapacity/:id')
  // async updateEquipmentCapacity(
  //   @Param('id') id: string,
  //   @Body() equipmentCapacityDto: EquipmentCapacityDto,
  // ) {
  //   return this.applicationService.updateEquipmentCapacity(
  //     id,
  //     equipmentCapacityDto,
  //   );
  // }

  // @ApiTags('settings')
  // @Delete('equipmentCapacity/:id')
  // async removeEquipmentCapacity(@Param('id') id: string) {
  //   return this.applicationService.removeEquipmentCapacity(id);
  // }

  // @ApiTags('settings')
  // @Get('equipmentCapacity/code/search')
  // async searchEquipmentCapacityByCode(@Query('search') search: string) {
  //   const searchFields: (keyof EquipmentCapacity)[] = ['code'];
  //   return this.genericSearch10.search(
  //     this.equipmentCapacityRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  // Other info application
  @ApiTags('application')
  @Post('otherInfoApplication')
  async CreateOtherInfoApplication(
    @Body() otherInfoApplicationDto: OtherInfoApplicationDto,
  ) {
    return this.applicationService.createOtherInfoApplication(
      otherInfoApplicationDto,
    );
  }

  @ApiTags('application')
  @Get('otherInfoApplication')
  async findAllOtherInfoApplication() {
    return this.applicationService.findAllOtherInfoApplication();
  }

  @ApiTags('application')
  @Get('otherInfoApplication/:id')
  async findOneOtherInfoApplication(@Param('id') id: string) {
    return this.applicationService.findOneOtherInfoApplication(id);
  }

  @ApiTags('application')
  @Patch('otherInfoApplication/:id')
  async updateOtherInfoApplication(
    @Param('id') id: string,
    @Body() otherInfoApplicationDto: OtherInfoApplicationDto,
  ) {
    return this.applicationService.updateOtherInfoApplication(
      id,
      otherInfoApplicationDto,
    );
  }

  @ApiTags('application')
  @Delete('otherInfoApplication/:id')
  async removeOtherInfoApplication(@Param('id') id: string) {
    return this.applicationService.removeOtherInfoApplication(id);
  }

  // permitQuestion
  @ApiTags('application')
  @Post('permitQuestion')
  async CreatePermitQuestion(@Body() permitQuestionDto: PermitQuestionDto) {
    return this.applicationService.createPermitQuestion(permitQuestionDto);
  }

  @ApiTags('application')
  @Get('permitQuestion')
  async findAllPermitQuestion() {
    return this.applicationService.findAllPermitQuestion();
  }

  @ApiTags('application')
  @Get('permitQuestion/:id')
  async findOnePermitQuestion(@Param('id') id: string) {
    return this.applicationService.findOnePermitQuestion(id);
  }

  @ApiTags('application')
  @Get('permitQuestion/permitType/:permitTypeId')
  async findPermitQuestionByPermitType(
    @Param('permitTypeId') permitTypeId: string,
  ) {
    return this.applicationService.findAllPermitQuestionByPermitType(
      permitTypeId,
    );
  }

  @ApiTags('application')
  @Patch('permitQuestion/:id')
  async updatePermitQuestion(
    @Param('id') id: string,
    @Body() otherInfoApplicationDto: PermitQuestionDto,
  ) {
    return this.applicationService.updatePermitQuestion(
      id,
      otherInfoApplicationDto,
    );
  }

  @ApiTags('application')
  @Delete('permitQuestion/:id')
  async removePermitQuestion(@Param('id') id: string) {
    return this.applicationService.removePermitQuestion(id);
  }

  // Answer
  // Answer
  @ApiTags('application')
  @Post('answer')
  async CreateAnswer(@Body() answerDto: AnswerDto) {
    return this.applicationService.createAnswer(answerDto);
  }

  @ApiTags('application')
  @Get('answer')
  async findAllAnswer() {
    return this.applicationService.findAllAnswer();
  }

  @ApiTags('application')
  @Get('answer/:id')
  async findOneAnswer(@Param('id') id: string) {
    return this.applicationService.findOneAnswer(id);
  }

  @ApiTags('application')
  @Patch('answer/:id')
  async updateAnswer(@Param('id') id: string, @Body() answerDto: AnswerDto) {
    return this.applicationService.updateAnswer(id, answerDto);
  }

  @ApiTags('application')
  @Delete('answer/:id')
  async removeAnswer(@Param('id') id: string) {
    return this.applicationService.removeAnswer(id);
  }

  // reviewersOnApplication
  @ApiTags('application')
  @Post('reviewersOnApplication')
  async CreateReviewersOnApplication(
    @Body() reviewersOnApplicationDto: ReviewersOnApplicationDto,
  ) {
    return this.applicationService.createReviewersOnApplication(
      reviewersOnApplicationDto,
    );
  }

  @ApiTags('application')
  @Get('reviewersOnApplication')
  async findAllReviewersOnApplication() {
    return this.applicationService.findAllReviewersOnApplication();
  }

  @ApiTags('application')
  @Get('reviewersOnApplication/:id')
  async findOneReviewersOnApplication(@Param('id') id: string) {
    return this.applicationService.findOneReviewersOnApplication(id);
  }

  @ApiTags('application')
  @Get('reviewerOnApplication/:applicationId')
  async findReviewerOnApplicationByApplication(
    @Param('applicationId') applicationId: string,
  ) {
    return this.applicationService.findReviewersOnApplication2(applicationId);
  }

  // @ApiTags('application')
  // @Get('reviewersOnApplication/:applicationId')
  // async findReviewersOnApplicationByApplication(
  //   @Param('applicationId') applicationId: string,
  // ) {
  //   console.log(applicationId);
  //   return this.applicationService.findOneReviewersOnApplicationByApplicationId(
  //     applicationId,
  //   );
  // }

  @ApiTags('application')
  @Get('reviewersOnApplication/findReviewersOnApplication/:applicationId')
  async findOneReviewersOnApplicationByApplication(
    @Param('applicationId') applicationId: string,
  ) {
    console.log(applicationId);
    return this.applicationService.findReviewersOnApplication(applicationId);
  }

  @ApiTags('application')
  @Get('reviewersOnApplication/findReviewersOnApplication/:userId')
  async findOneReviewersOnApplicationByUserId(@Param('userId') userId: string) {
    return this.applicationService.findReviewersOnApplicationByUserId(userId);
  }

  @ApiTags('application')
  @Get('reviewersOnApplication/application/:applicationId/reviewer/:userId')
  async findTheReviewersOnSpecificApplication(
    @Param('applicationId') applicationId: string,
    @Param('userId') userId: string,
  ) {
    return this.applicationService.findTheReviewersOnSpecificApplication(
      applicationId,
      userId,
    );
  }

  @ApiTags('application')
  @Get('reviewersOnApplication/myBox/:userId')
  async countApplicationOfReviewer(@Param('userId') userId: string) {
    return this.applicationService.countApplicationOfReviewer(userId);
  }

  // @ApiTags('application')
  // @Get('reviewersOnApplication/myBox/allApplications/:userId')
  // async getApplicationsOfReviewer(@Param('userId') userId: string) {
  //   return this.applicationService.getApplicationsOfReviewer(userId);
  // }

  // WithPagination
  @ApiTags('application')
  @Get('reviewersOnApplication/myBox/allApplications/:userId')
  async getApplicationsOfReviewer(
    @Param('userId') userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.getApplicationsOfReviewer(
      userId,
      page,
      limit,
    );
  }

  // @ApiTags('application')
  // @Get('reviewersOnApplication/myBox/allApplications/all/:userId')
  // async getAllApplicationsOfReviewer(@Param('userId') userId: string) {
  //   return this.applicationService.getAllApplicationsOfReviewer(userId);
  // }

  // WithPagination
  @ApiTags('application')
  @Get('reviewersOnApplication/myBox/allApplications/all/:userId')
  async getAllApplicationsOfReviewer(
    @Param('userId') userId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.getAllApplicationsOfReviewer(
      userId,
      page,
      limit,
    );
  }

  @ApiTags('application')
  @Patch('reviewersOnApplication/:id')
  async updateReviewersOnApplication(
    @Param('id') id: string,
    @Body() reviewersOnApplicationDto: ReviewersOnApplicationDto,
  ) {
    return this.applicationService.updateReviewersOnApplication(
      id,
      reviewersOnApplicationDto,
    );
  }

  @ApiTags('application')
  @Delete('reviewersOnApplication/:id')
  async removeReviewersOnApplication(@Param('id') id: string) {
    return this.applicationService.removeReviewersOnApplication(id);
  }

  // // Occupancy Inspection
  // @ApiTags('application')
  // @Post('inspection/occupancy')
  // async CreateOccupancyInspection(
  //   @Body() occupancyInspectionDto: OccupancyInspectionDto,
  // ) {
  //   return this.applicationService.createOccupancyInspection(
  //     occupancyInspectionDto,
  //   );
  // }

  // Occupancy Inspection with a file
  @ApiTags('application')
  @Post('inspection/occupancy')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Create occupancy Inspection  with file upload',
    type: OccupancyInspectionDto,
  })
  async CreateOccupancyInspection(
    @Body() occupancyInspectionDto: OccupancyInspectionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.applicationService.createOccupancyInspection(
      occupancyInspectionDto,
      file,
    );
  }
  //Charts dashboard
  @ApiTags('application')
  @Post('applicationChartDashboard')
  async generateChart(@Body() chartDto: DataForChartDto) {
    console.log(`Received DTO:`, chartDto);

    if (!chartDto.year) {
      console.error(`Invalid year provided: ${chartDto.year}`);
      throw new HttpException('Year is required', HttpStatus.BAD_REQUEST);
    }

    return this.applicationService.generateCharts(chartDto);
  }

  @ApiTags('application')
  @Get('inspection/occupancy')
  async findAllOccupancyInspection() {
    return this.applicationService.findAllOccupancyInspection();
  }

  @ApiTags('application')
  @Get('inspection/occupancy/:id')
  async findOneOccupancyInspection(@Param('id') id: string) {
    return this.applicationService.findOneOccupancyInspection(id);
  }

  @ApiTags('application')
  @Patch('inspection/occupancy/:id')
  async updateOccupancyInspection(
    @Param('id') id: string,
    @Body() occupancyInspectionDto: OccupancyInspectionDto,
  ) {
    return this.applicationService.updateOccupancyInspection(
      id,
      occupancyInspectionDto,
    );
  }

  @ApiTags('application')
  @Delete('inspection/occupancy/:id')
  async removeOccupancyInspection(@Param('id') id: string) {
    return this.applicationService.removeOccupancyInspection(id);
  }

  @ApiTags('application')
  @Get('inspection/occupancy/ByApplication/:applicationId')
  async findOneOccupancyInspectionByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.applicationService.findOneOccupancyInspectionByApplicationId(
      applicationId,
    );
  }

  // // Foundation Inspection
  // @ApiTags('application')
  // @Post('inspection/foundation')
  // async CreateFoundationInspection(
  //   @Body() foundationInspectionDto: FoundationInspectionDto,
  // ) {
  //   return this.applicationService.createFoundationInspection(
  //     foundationInspectionDto,
  //   );
  // }

  // Foundation notice Inspection with a file
  @ApiTags('application')
  @Post('inspection/foundationNotice')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Create Foundation Notice Inspection  with file upload',
    type: FoundationInspectionDto,
  })
  async CreateFoundationInspectionWithAFile(
    @Body() foundationInspectionDto: FoundationInspectionDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.applicationService.createFoundationInspectionWithAFile(
      foundationInspectionDto,
      file,
    );
  }

  @ApiTags('application')
  @Get('inspection/foundation')
  async findAllFoundationInspection() {
    return this.applicationService.findAllFoundationInspection();
  }

  @ApiTags('application')
  @Get('inspection/foundation/:id')
  async findOneFoundationInspection(@Param('id') id: string) {
    return this.applicationService.findOneFoundationInspection(id);
  }

  @ApiTags('application')
  @Patch('inspection/foundation/:id')
  async updateFoundationInspection(
    @Param('id') id: string,
    @Body() foundationInspectionDto: FoundationInspectionDto,
  ) {
    return this.applicationService.updateFoundationInspection(
      id,
      foundationInspectionDto,
    );
  }

  @ApiTags('application')
  @Delete('inspection/foundation/:id')
  async removeFoundationInspection(@Param('id') id: string) {
    return this.applicationService.removeFoundationInspection(id);
  }

  @ApiTags('application')
  @Get('inspection/foundation/ByApplication/:applicationId')
  async findOneFoundationInspectionByApplicationId(
    @Param('applicationId') applicationId: string,
  ) {
    return this.applicationService.findOneFoundationInspectionByApplicationId(
      applicationId,
    );
  }

  // // Dashboard for COK Director case
  // @ApiTags('application')
  // @Get('dashboardWithoutInspection/:agencyId')
  // async findAllApplicationByAgencyWithoutInspection(
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.dashboardApplicationsWithoutInspection(
  //     agencyId,
  //   );
  // }
  // @ApiTags('application')
  // @Get('allApplicationWithoutInspection/:agencyId')
  // async allApplicationByAgencyWithoutInspection(
  //   @Param('agencyId') agencyId: string,
  // ) {
  //   return this.applicationService.findAllApplicationsWithoutInspection(
  //     agencyId,
  //   );
  // }

  // Dashboard for COK Director case remove draft
  @ApiTags('application')
  @Get('dashboardWithoutInspection/:agencyId')
  async findAllApplicationByAgencyWithoutInspection2(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.dashboardApplicationsWithoutInspection2(
      agencyId,
    );
  }
  @ApiTags('application')
  @Get('allApplicationWithoutInspection/:agencyId')
  async allApplicationByAgencyWithoutInspection2(
    @Param('agencyId') agencyId: string,
  ) {
    return this.applicationService.findAllApplicationsWithoutInspection2(
      agencyId,
    );
  }

  @ApiTags('application')
  @Post('reviewersOnApplication/staffReport/')
  async findApprovalsByUserIdInDateRange(
    @Body() reviewerReportDto: ReviewerReportDto,
  ) {
    return this.applicationService.findReviewersOnApplicationsByUserIdInDateRange(
      reviewerReportDto,
    );
  }

  // @ApiTags('application')
  // @Get('application/:applicationStatusCode/agency/:agencyId')
  // async findAllApplicationByApplicationStatusCodeAndAgencyId(
  //   @Param('applicationStatusCode') applicationStatusCode: string,
  //   @Param('agencyId') agencyId: string,
  //   @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  //   @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  // ) {
  //console.log("commented coment ");
  //   return this.applicationService.findAllApplicationsWithDetailsByApplicationStatusAndAgencyId2(
  //     applicationStatusCode,
  //     agencyId,
  //     page,
  //     limit,
  //   );
  // }

  // with pagination
  @Get('applicationStatus/:applicationStatusCode/agency/:agencyId')
  async findApplicationsByApplicationCodeAndAgencyId(
    @Param('applicationStatusCode') applicationStatusCode: string,
    @Param('agencyId') agencyId: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
  ) {
    return this.applicationService.findApplicationsByApplicationCodeAndAgencyId(
      applicationStatusCode,
      agencyId,
      page,
      limit,
    );
  }
}
