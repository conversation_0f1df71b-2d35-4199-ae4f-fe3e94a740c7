import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { jwtDecode } from 'jwt-decode';
import { interval, Subscription, takeWhile } from 'rxjs';
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import {
  SessionService,
  UserData,
} from 'src/app/authentication-services/session.service';
import {
  NOTIFICATION_COLOR,
  UtilService,
} from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { LanguageService } from 'src/app/services/language.service';
@Component({
  selector: 'app-account-header',
  templateUrl: './account-header.component.html',
  styleUrls: ['./account-header.component.scss'],
})
export class AccountHeaderComponent {
  currentUser: any;
  lists: any[] = [];
  inTokenData: any = {};
  permissions: any;
  private subscription!: Subscription;
  notificationNumber: any = {};

  constructor(
    private router: Router,
    private sessionService: SessionService,
    private utilService: UtilService,
    private applicationService: ApplicationService,
    private appConfig: AppConfig,
    private languageService: LanguageService
  ) {
    if (
      this.sessionService.getSession() &&
      this.sessionService.getSession().data &&
      this.sessionService.getSession().data.user
    ) {
      this.currentUser = this.sessionService.getSession();
      this.currentUser.userId = (
        jwtDecode(this.currentUser.data.token.access_token) as any
      ).UserId;

      if (
        this.currentUser.data.user.userType.code === 'STF' ||
        this.currentUser.data.user.role.code === 'STF'
      ) {
        this.startCheckingBoolean(
          this.currentUser.data.user.isStaffPasswordChanged,
          this.currentUser.data.user.isPasswordExpired
        );
      }

      localStorage.setItem(
        this.appConfig.PERMI_ACCESS,
        JSON.stringify(this.loadPermissions())
      );
    } else {
      this.unauthorizedUser();
    }

    this.inTokenData = this.sessionService.getTokenData();

    this.loadList();
  }

  // dynamically changing the active class depending on the selected lang for i18n
  defaultSelectedLang = this.languageService.getCurrentLanguage();
  switchLang(lang: string) {
    this.languageService.switchLang(lang);
    this.defaultSelectedLang = lang;
  }

  loadPermissions() {
    if (this.currentUser.data.user.userType.name === 'Engineer') {
      this.currentUser.permissions = UserData.find(
        (x: any) => x.name === this.currentUser.data.user.userType.name
      )?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    } else if (this.currentUser.data.user.userType.name === 'Architect') {
      this.currentUser.permissions = UserData.find(
        (x: any) => x.name === this.currentUser.data.user.userType.name
      )?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    } else if (this.currentUser.data.user.role.code === 'STF') {
      this.currentUser.permissions = UserData.find(
        (x: any) => x.name === this.currentUser.data.user.role.name
      )?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    } else {
      this.currentUser.permissions = UserData.find(
        (x: any) => x.code === this.currentUser.data.user.role.code
      )?.userActivities.reduce((acc: any, curr: any) => {
        return { ...acc, ...curr };
      }, {});
    }
    return this.currentUser.permissions;
  }

  getNotification(event: any) {
    this.notificationNumber = event;
  }

  startCheckingBoolean(booleanData: any, isPasswordExpired: boolean) {
    // this.subscription = interval(1000)  // emits every second
    //   .pipe(
    //     takeWhile(() =>
    //       booleanData === false)  // continue checking while myBoolean is false
    //   )
    //   .subscribe(() => {
    //     this.checkCondition(booleanData);
    //   });
    if (booleanData === false || isPasswordExpired === true) {
      this.subscription = interval(1000)
        .pipe(
          takeWhile(() => booleanData === false || isPasswordExpired === true) // Stop when both are false
        )
        .subscribe(() => {
          this.checkCondition(booleanData);
        });
    }
  }

  checkCondition(booleanData: any) {
    // Logic to check if the boolean is false
    if (!booleanData) {
      this.router.navigate(['/account/change-password']);
    } else {
      this.stopCheckingBoolean();
    }
  }

  stopCheckingBoolean() {
    if (this.subscription) {
      this.subscription.unsubscribe(); // Unsubscribe to stop checking
    }
  }

  // Cleanup the subscription when the component is destroyed
  ngOnDestroy() {
    this.stopCheckingBoolean();
  }

  loadList() {
    if (this.inTokenData.LicenseArchitect) {
      this.getPendingLists(this.inTokenData.LicenseArchitect);
    }
    if (this.inTokenData.LicenseEngineer) {
      this.getPendingLists(this.inTokenData.LicenseEngineer);
    }
  }

  getPendingLists(licenceNumber: any) {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
          'application/assignee/licenseNumber/search?search=' +
          licenceNumber
      )
      .subscribe((data) => {
        this.lists = data.items.filter(
          (x: any) => x.projectStatus.code !== 'PAPRV'
        );
      });
  }

  unauthorizedUser() {
    if (
      this.sessionService.getSession()?.data &&
      this.sessionService.getSession()?.data.message === 'Email not verified'
    ) {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        this.sessionService.getSession()?.data.message +
          ', please go to you email account and verify your account',
        'bottom',
        'center'
      );
      this.router.navigate(['/']);
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'Invalid username or password',
        'bottom',
        'center'
      );
      this.router.navigate(['/']);
    }
  }

  // convertPermissionsArrayToObject(array: any) {
  //   array.reduce;
  // }

  logout() {
    // this.router.navigate(['/']);
    this.sessionService.logout();
  }
}
