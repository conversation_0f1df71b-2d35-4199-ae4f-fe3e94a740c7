<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"newApplication.step3.step" | translate }} <span>3</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"newApplication.step3.projectDetails" | translate }}</h2>
                    <div class="form-incheckbox">
                        <label class="checkbox">
                            <input style="display: none;" type="checkbox" id="check" [checked]="isAssociated"
                                (click)="checkAssociated(associatedUpiContent)" data-bs-toggle="modal" type="button"
                                data-bs-target="#showModal" />
                            <span class="checkbox_box"></span>
                            <span class="checkbox_txt">{{"newApplication.step3.checkAddAssociatedUpi" | translate }}</span>
                        </label>
                    </div>
                </div>
                <form [formGroup]="pageForm" autocomplete="off" *ngIf="currentUser.data.user.userType.code === 'LO'">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToDoItYouself"
                                        (change)="changeForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"newApplication.step3.doItYourself" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToAssignToEngineer"
                                        (change)="changeAssignForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"newApplication.step3.assignToEngineerArchitect" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                <form [formGroup]="userForm" autocomplete="off" *ngIf="doYouWantToDoItYouself">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"newApplication.step3.permitType" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="permitTypeId"
                                        id="permitTypeId" formControlName="permitTypeId"
                                        (change)="checkTemporalPermit()" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.plotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly>
                                </div>
                            </div>
                            <div class="form-input" *ngIf="userForm.value.combiningPlotSize">
                                <label>{{"newApplication.step3.combiningPlotSize" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="combiningPlotSize" name="combiningPlotSize"
                                        formControlName="combiningPlotSize" readonly>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.buildingType" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>

                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.projectCategory" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <!--  -->
                                    <select [class.disabled-select]="isSelectDisabled" name="categoryTypeId"
                                        id="categoryTypeId" formControlName="categoryTypeId"
                                        [disabled]="isSelectDisabled" required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>

                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfFloorGPlus" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor" name="numberOfFloor" noNegative
                                        formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.builtUpAreaSqMeters" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <!--  -->
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                        (keyup)="calculateNumberOfFlow()" noNegative required
                                        [disabled]="userForm.value?.buildTypeId?.length < 0 || userForm.value?.buildTypeId?.length == 0">
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> {{"newApplication.step3.buildUpAreaRestriction" | translate }}. </div>
                                </div>
                            </div>

                            <div class="form-input">
                                <label>{{"newApplication.step3.grossFloorArea" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                        formControlName="grossFloorArea" noNegative readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfParkingSpaces" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedPriceDwellingRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="priceOfDwellingUnitRwf"
                                        name="priceOfDwellingUnitRwf" formControlName="priceOfDwellingUnitRwf" required
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.descriptionOperations" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.percentageSpaceUse" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.numberOfDwellingUnit" | translate }} <span class="estrx"></span></label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits" autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedMonthlyWaterM3" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedMonthlyElectricityWatts" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.distanceNearestLandLineFiberM" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn" formControlName="DistanceToTheNearestLandIn"
                                        noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedProjectCostUsd" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.estimatedProjectCostRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInRwf" name="ProjectCostInRwf"
                                        formControlName="ProjectCostInRwf" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.capacityInfoPeopleSeats" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation" noNegative
                                        (keyup)="calculateNumberOfPeople()" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step3.technologySurveyOptional" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId">
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-panel_footer" *ngIf="
                (((userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2') &&
                (!formAccess.doYouWantToShareToEngineer))
                && (!this.pageForm.value.doYouWantToAssignToEngineer)) && (currentUser.data.user.role.code === 'APP' &&
                currentUser.data.user.userType.code !== 'ENG'
                )">
                        <button type="button" (click)="onPrev()">{{"newApplication.step3.previous" | translate }}</button>
                        <button type="button" (click)="onNext()">{{"newApplication.step3.next" | translate }}</button>
                    </div>

                    <div class="step-panel_footer" *ngIf="
                (userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2' ||
                userForm.value.categoryCode === 'CAT3'||
                userForm.value.categoryCode === 'CAT4' ||
                userForm.value.categoryCode === 'CAT5') && (currentUser.data.user.userType.code === 'ENG' || currentUser.data.user.userType.code === 'ARC' )">
                        <button type="button" (click)="onPrev()">{{"newApplication.step3.previous" | translate }}</button>
                        <button type="button" (click)="onNext()">{{"newApplication.step3.next" | translate }}</button>
                    </div>

                    <!-- Fallback footer for all other cases -->
                    <div class="step-panel_footer" *ngIf="
                !((((userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2') &&
                (!formAccess.doYouWantToShareToEngineer))
                && (!this.pageForm.value.doYouWantToAssignToEngineer)) && (currentUser.data.user.role.code === 'APP' &&
                currentUser.data.user.userType.code !== 'ENG')) &&
                !((userForm.value.categoryCode === 'CAT1' ||
                userForm.value.categoryCode === 'CAT2' ||
                userForm.value.categoryCode === 'CAT3'||
                userForm.value.categoryCode === 'CAT4' ||
                userForm.value.categoryCode === 'CAT5') && (currentUser.data.user.userType.code === 'ENG' || currentUser.data.user.userType.code === 'ARC'))">
                        <button type="button" (click)="onPrev()">{{"newApplication.step3.previous" | translate }}</button>
                        <button type="button" (click)="onNext()">{{"newApplication.step3.next" | translate }}</button>
                    </div>

                </form>
                <h4 *ngIf="currentUser.data.user.role.code === 'APP' &&
                    (projectDetails?.projectStatus?.code === 'PAPRV'
                        || projectDetails?.projectStatus?.code === 'PASGD'
                    )" style="color: red;">{{"newApplication.step3.cannotContinueAssigned" | translate }} </h4>

                <app-assign-to-engineer-form *ngIf="
                    (
                        (doYouWantToAssignToEngineer ||
                        (userForm.value.categoryCode === 'CAT3' ||
                         userForm.value.categoryCode === 'CAT4' ||
                         userForm.value.categoryCode === 'CAT5')
                        || (formAccess.isRdbIAEchecked))
                        &&  (currentUser.data.user.userType.code !== 'ENG'))
                        && projectDetails?.projectStatus?.code !== 'PAPRV'
                        && projectDetails?.projectStatus?.code !== 'PASGD'
                            " [inputData]="{
                        projectId: userForm.value.projectId,
                        allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form>
            </div>
        </div>
    </div>
</div>
<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"newApplication.step3.associatedUpi" | translate }}</h2>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
        (backToParent)="closeAssociatedPopup($event)"
        (backToParentAndSubmitCombining)="getNewCombiningAssociated($event)"></app-associated-upi-application>
    </div>
</ng-template>
