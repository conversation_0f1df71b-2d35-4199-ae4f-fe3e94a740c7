<!-- Applications Request-->
<div class="app-dash">
  <div class="container">
    <div class="app-main">
      <div class="app-welnote">
        <div class="app-welnote_dtails">
          <span class="prim-nt">{{"assignedApplications.welcome" | translate }}</span>
          <h3>
            <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
          </h3>
        </div>
      </div>
      <div class="app-lists">
        <div class="app-tblist">
          <div class="app-tblist_title">
            <span class="hder" aria-label="header tittle">{{"assignedApplications.projectRequests" | translate }}</span>
            <!-- <div class="btns"> -->
            <div class="tbleFilter">
              <!-- <div class="form-input w-aut clear-m"> -->
              <div class="form-input_search">
                <input type="text" name="searchTerm" [(ngModel)]="applicationService.searchTerm"
                  (ngModelChange)="onSearchTermChange($event)" [placeholder]="'assignedApplications.searchPlaceholder' | translate"/>
                <button type="button" class="btn">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g data-name="Layer 2">
                      <g data-name="search">
                        <rect width="24" height="24" opacity="0" />
                        <path
                          d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                      </g>
                    </g>
                  </svg>
                </button>
              </div>
              <div class="form-input clear-m w-aut">
                <label><Strong>{{"assignedApplications.filterByStatusSize" | translate }}</Strong> </label>
                <div>
                  <select name="applicationStatusId" id="applicationStatusId"
                    [(ngModel)]="outputData.applicationStatusId" (change)="filterByStatus()">
                    <!-- <option disabled value="">All</option> -->
                    <option *ngFor="let r of applicationStatuses" [value]="r.id"> {{ r.name }} </option>
                  </select>
                </div>
              </div>
              <!-- </div> -->
            </div>
          </div>
          <ul class="tblist">
            <li class="tblist-item" *ngFor="let lst of lists">
              <div class="tblist-item_icon o_icon">
                <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.requestFrom" | translate }}</span> {{ lst?.details?.lastName }} {{ lst?.details?.firstName }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.projectName" | translate }}</span> {{ lst?.projects?.projectName | titlecase }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.upi" | translate }}</span> {{ lst?.projects?.upi }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.plotSize" | translate }}</span> {{ lst?.projects?.plotSize }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.timelineForSubmission" | translate }}</span> {{ lst?.timeLineDays }} days
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.appliedFor" | translate }}</span> {{ lst?.projects?.selectedUse }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.applicationDate" | translate }}</span> {{ lst?.projects?.created_at | date }}
              </div>
              <div class="tblist-item_dt">
                <span class="ttl">{{"assignedApplications.status" | translate }}</span> {{ lst.projectStatus.name }}
              </div>
              <div class="tblist-item_xcn">
                <!--  -->
                <a *ngIf="lst.projectStatus.code === 'PASGD' &&
                lst.projectStatus.code !== 'PRJCT'" class="kbk-link-btn hs-tp"
                  data-kbk-tooltip="Accept or reject application" data-bs-toggle="modal" id="create-btn"
                  data-bs-target="#showModal" (click)="openModal(requestContent, 'lg', lst)">
                  <!-- <img src="assets/ikons/colored/ikon-eye.svg" alt="" /> --> {{"assignedApplications.viewOffer" | translate }}</a>
                <a *ngIf="
                    lst.projectStatus.code ===
                    'PAPRV'
                  " class="kbk-link-btn hs-tp" [routerLink]="[
                    '/account/application/complete-assigned-project',
                    lst.projects.id
                  ]" data-kbk-tooltip="view details" id="create-btn">
                  <!-- <img src="assets/ikons/colored/ikon-eye.svg" alt="" /> --> {{"assignedApplications.applyForPermit" | translate }} </a>
              </div>
            </li>
          </ul>
          <div class="pagnation" *ngIf="lists.length > 0">
            <div class="pagnation-item">
              <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                <span class="ent">{{ totalRecords }}</span>
                <span class="cur">{{ startIndex }} - {{ endIndex }}</span>
              </div>
            </div>
            <div class="pagnation-item">
              <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                (pageChange)="getPremiumData()">
              </ngb-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #requestContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{"assignedApplications.request" | translate }}</h2>
    <p>{{"assignedApplications.applicationRequest" | translate }}</p>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <app-assigned-application-detail *ngIf="outputData.isViewing" [inputData]="outputData"
    (backToParent)="closeContent($event)"></app-assigned-application-detail>
</ng-template>