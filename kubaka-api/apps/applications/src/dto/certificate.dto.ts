import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsIn, IsNotEmpty, IsOptional } from 'class-validator';

export enum OrientationType {
  LEFT = 'left',
  RIGHT = 'right',
}

export class CertificateDto {
  @ApiProperty({ description: 'The field permitType Id required' })
  @IsNotEmpty({ message: 'The field permitTypeId cannot be empty' })
  permitTypeId: string;

  @ApiProperty({ description: 'The field agencyCode required' })
  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  agencyCode: string;

  @IsNotEmpty({ message: 'The field applicantUserId cannot be empty' })
  @ApiProperty({ description: 'The field applicantUserId is required' })
  applicantUserId: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field title cannot be empty' })
  @ApiProperty({ description: 'The field title is required' })
  title: string;

  @IsNotEmpty({ message: 'The field lows cannot be empty' })
  @ApiProperty({ description: 'The field lows is required' })
  lows: string;

  @IsOptional()
  certificateNumber: string;

  @IsOptional()
  statusId: string;

  @IsOptional()
  invoiceNumber: string;

  @IsOptional()
  expiredDate: Date;

  // @IsNotEmpty({ message: 'The field expiredDate cannot be empty' })
  // @ApiProperty({ description: 'The field expiredDate is required' })
  // expiredDate: Date;

  @IsNotEmpty({ message: 'The field backgroundUrl cannot be empty' })
  @ApiProperty({ description: 'The field backgroundUrl is required' })
  backgroundUrl: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;
}

export class SignatoryDto {
  @ApiProperty({ description: 'The field names required' })
  @IsNotEmpty({ message: 'The field names cannot be empty' })
  names: string;

  @IsNotEmpty({ message: 'The field signatureUrl cannot be empty' })
  @ApiProperty({ description: 'The field signatureUrl is required' })
  signatureUrl: string;

  @IsNotEmpty({ message: 'The field title cannot be empty' })
  @ApiProperty({ description: 'The field title is required' })
  title: string;

  @IsNotEmpty({ message: 'The field orientation cannot be empty' })
  @ApiProperty({ description: 'The field orientation is required' })
  orientation: string;

  @ApiProperty({ description: 'The field orientation is required' })
  @IsEnum(OrientationType)
  @IsIn([OrientationType.LEFT, OrientationType.RIGHT])
  public activityTpe: OrientationType;

  @IsNotEmpty({ message: 'The field isActive cannot be empty' })
  @ApiProperty({ description: 'The field isActive is required' })
  isActive: boolean;

  @IsNotEmpty({ message: 'The field certificateId cannot be empty' })
  @ApiProperty({ description: 'The field certificateId is required' })
  certificateId: string;
}

export class TransferCertificateDto {
  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @IsNotEmpty({ message: 'The field firstName cannot be empty' })
  @ApiProperty({ description: 'The field firstName is required' })
  firstName: string;

  @IsNotEmpty({ message: 'The field lastName cannot be empty' })
  @ApiProperty({ description: 'The field lastName is required' })
  lastName: string;

  @IsNotEmpty({ message: 'The field documentNumber cannot be empty' })
  @ApiProperty({ description: 'The field documentNumber is required' })
  documentNumber: string;

  @IsNotEmpty({ message: 'The field certificateNumber cannot be empty' })
  @ApiProperty({ description: 'The field certificateNumber is required' })
  certificateNumber: string;
}

export class DevelopmentStatusDto {
  @IsNotEmpty({ message: 'The field UPI cannot be empty' })
  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @IsNotEmpty({ message: 'The field UPI cannot be empty' })
  @ApiProperty({ description: 'The field UPI is required' })
  UPI: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field certificateId cannot be empty' })
  @ApiProperty({ description: 'The field certificateId is required' })
  certificateId: string;

  @IsNotEmpty({ message: 'The field startingDate cannot be empty' })
  @ApiProperty({ description: 'The field startingDate is required' })
  startingDate: string;

  @IsNotEmpty({ message: 'The field siteStatusId cannot be empty' })
  @ApiProperty({ description: 'The field siteStatusId is required' })
  siteStatusId: string;
}

export class RandomInspectionDto {
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @IsNotEmpty({ message: 'The field UPI cannot be empty' })
  @ApiProperty({ description: 'The field UPI is required' })
  UPI: string;

  @IsNotEmpty({ message: 'The field reason cannot be empty' })
  @ApiProperty({ description: 'The field reason is required' })
  reason: string;

  @IsNotEmpty({ message: 'The field certificateId cannot be empty' })
  @ApiProperty({ description: 'The field certificateId is required' })
  certificateId: string;

  @IsNotEmpty({ message: 'The field siteStatusId cannot be empty' })
  @ApiProperty({ description: 'The field siteStatusId is required' })
  siteStatusId: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'The uploaded file',
  })
  @IsOptional()
  file?: any;
}
