import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class PermitTypeDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field issuedDays is required' })
  @IsOptional()
  issuedDays: number;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class CategoryRuleDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field GrossFloorArea is required' })
  @IsNotEmpty({ message: 'The field GrossFloorArea cannot be empty' })
  GrossFloorArea: number;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field GPlus cannot be empty' })
  GPlus: number;

  @ApiProperty({ description: 'The field NumberOfPeople is required' })
  @IsNotEmpty({ message: 'The field NumberOfPeople cannot be empty' })
  NumberOfPeople: number;
}

export class CategoryTypeDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  // @IsNotEmpty({ message: 'The field categoryRule cannot be empty' })
  // @Type(() => CategoryRuleDto)
  // categoryRule: CategoryRuleDto;

  @IsNotEmpty({ message: 'The field categoryRule cannot be empty' })
  @ApiProperty()
  categoryRuleId: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class BuildTypeDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field description is required' })
  @IsNotEmpty({ message: 'The field description cannot be empty' })
  description: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class DocumentTypeDto {
  @IsOptional({ message: 'The field id cannot be empty' })
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class RequiredDocumentDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @IsOptional()
  code: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'The field documentType cannot be empty' })
  documentTypeId: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'The field permitType cannot be empty' })
  permitTypeId: string;

  @ApiProperty()
  @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
  categoryTypeId: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class TechnologySurveyDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class ApplicationStatusDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

// export class NonObjectionStatusDto {
//   @IsOptional()
//   id: string;
//   @ApiProperty({ description: 'The field name required' })
//   @IsNotEmpty({ message: 'The field name cannot be empty' })
//   name: string;

//   @ApiProperty({ description: 'The field code is required' })
//   @IsNotEmpty({ message: 'The field code cannot be empty' })
//   code: string;
// }

export class ProjectStatusDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;

  @ApiProperty({ description: 'The field createdBy' })
  @IsOptional({ message: 'The field createdBy ' })
  createdBy: string;

  @ApiProperty({ description: 'The field updatedBy' })
  @IsOptional({ message: 'The field updatedBy ' })
  updatedBy: string;
}

export class QuestionCategoryDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class EquipmentCapacityDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class ConstructionMethodDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field name is required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}
