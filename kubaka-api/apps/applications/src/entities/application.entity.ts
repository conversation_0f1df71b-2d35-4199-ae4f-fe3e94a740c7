import { AbstractEntity } from '@app/common';
import {
  // BeforeInsert,
  Column,
  CreateDateColumn,
  Entity,
  // JoinColumn,
  ManyToOne,
  OneToMany,
  UpdateDateColumn,
  // OneToOne,
} from 'typeorm';
import {
  ApplicationStatus,
  BuildType,
  CategoryType,
  PermitType,
  ProjectStatus,
  QuestionCategory,
  TechnologySurvey,
} from './settings.entity';
import {
  ApplicationApproval,
  ApplicationApprovalCheckList,
  ApprovalDocument,
} from './approval.entity';
import { Invoice } from './invoice.entity';
import { Certificate, DevelopmentStatus } from './certificate.entity';
// import { ApplicationApproval } from './approval.entity';

@Entity()
export class Project extends AbstractEntity<Project> {
  @Column({ type: 'varchar' })
  // @Column({ type: 'varchar', unique: true })
  upi: string;

  @Column({ default: false })
  isRRAVerified: boolean;

  @Column({ default: false })
  isAssociatedUpi: boolean;

  @Column({ nullable: true })
  ownerFullName: string;

  @Column({ nullable: true })
  ownerIdNo: string;

  @Column({ default: false })
  isFromOldSystem: boolean;

  @Column({ type: 'varchar', nullable: true })
  isFromOldSystemDevelopersName: string;

  @Column({ type: 'varchar', nullable: true })
  isFromOldSystemPermitNumber: string;

  @Column({ type: 'varchar', nullable: true })
  isFromOldSystemInvoiceNumber: string;

  @Column({ default: false })
  isUnderMortgage: boolean;

  @Column({ default: false })
  isUnderRestriction: boolean;

  @Column({ default: '523096.26218253', nullable: true })
  centralCoordinateX: string;

  @Column({ default: '4784974.42535000', nullable: true })
  centralCoordinateY: string;

  @Column({ default: '1021101', nullable: true })
  villageCode: string;

  @Column({ default: 'Mukagarama', nullable: true })
  villageName: string;

  @Column({ default: '1021101', nullable: true })
  cellCode: string;

  @Column({ default: '1021101', nullable: true })
  cellName: string;

  @Column({ default: '10211', nullable: true })
  sectorCode: string;

  @Column({ default: '10211', nullable: true })
  sectorName: string;

  @Column({ default: '102', nullable: true })
  districtCode: string;

  @Column({ default: '102', nullable: true })
  districtName: string;

  @Column({ default: '01', nullable: true })
  provinceCode: string;

  @Column({ default: '01', nullable: true })
  provinceName: string;

  @Column()
  selectedUse: string;

  @Column()
  selectedCategoryUse: string;

  @Column({ type: 'varchar' })
  agencyId: string;

  @Column()
  projectName: string;

  @Column()
  projectDescription: string;

  @Column()
  plotSize: number;

  @Column({ nullable: true })
  originalPlotSize: number;

  // @OneToOne(() => User)
  // @JoinColumn()
  // userId: User;
  @Column({ type: 'varchar' })
  userId: string;

  @OneToMany(() => Application, (application) => application.projects)
  applications: Application[];

  // @ManyToOne(() => ProjectStatus, (projectStatus) => projectStatus.projects)
  // projectStatus: ProjectStatus[];

  @ManyToOne(() => ProjectStatus, (projectStatus) => projectStatus.projects)
  projectStatus: ProjectStatus;

  @OneToMany(() => AssociatedUPI, (associatedUPI) => associatedUPI.projects)
  associatedUPIs: AssociatedUPI[];

  @OneToMany(() => Assignee, (assignee) => assignee.projects)
  assignees: Assignee[];

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}
// @Entity()
// export class Project extends AbstractEntity<Project> {
//   @Column({ type: 'varchar' })
//   upi: string;

//   @Column({ default: false })
//   isFromOldSystem: boolean;

//   @Column({ default: false })
//   isUnderMortgage: boolean;

//   @Column({ default: false })
//   isUnderRestriction: boolean;

//   @Column({ default: '523096.26218253', nullable: true })
//   centralCoordinateX: string;

//   @Column({ default: '4784974.42535000', nullable: true })
//   centralCoordinateY: string;

//   @Column({ default: '1021101', nullable: true })
//   villageCode: string;

//   @Column({ default: 'Mukagarama', nullable: true })
//   villageName: string;

//   @Column({ default: '1021101', nullable: true })
//   cellCode: string;

//   @Column({ default: '1021101', nullable: true })
//   cellName: string;

//   @Column({ default: '10211', nullable: true })
//   sectorCode: string;

//   @Column({ default: '10211', nullable: true })
//   sectorName: string;

//   @Column({ default: '102', nullable: true })
//   districtCode: string;

//   @Column({ default: '102', nullable: true })
//   districtName: string;

//   @Column({ default: '01', nullable: true })
//   provinceCode: string;

//   @Column({ default: '01', nullable: true })
//   provinceName: string;

//   @Column()
//   selectedUse: string;

//   @Column()
//   selectedCategoryUse: string;

//   @Column({ type: 'varchar' })
//   agencyId: string;

//   @Column()
//   projectName: string;

//   @Column()
//   projectDescription: string;

//   @Column()
//   plotSize: number;

//   @Column()
//   buildUpArea: number;

//   @Column()
//   numberOfFloor: number;

//   @Column()
//   grossFloorArea: number;

//   @Column()
//   numberOfParkingSpace: number;

//   @Column()
//   priceOfDwellingUnitRwf: number;

//   @Column()
//   capacityInformation: number;

//   @Column()
//   numberOfDwellingUnits: string;

//   @Column()
//   DescriptionOfOperation: string;

//   @Column()
//   percentageSpaceUse: string;

//   // @OneToOne(() => User)
//   // @JoinColumn()
//   // userId: User;
//   @Column({ type: 'varchar' })
//   userId: string;

//   // project estimate
//   @Column()
//   waterConsumption: number;

//   @Column()
//   electricityConsumption: number;

//   @Column()
//   DistanceToTheNearestLandIn: number;

//   @Column()
//   ProjectCostInUSD: number;

//   @Column()
//   ProjectCostInRwf: number;

//   @ManyToOne(() => TechnologySurvey, { cascade: false })
//   @JoinTable()
//   technologySurvey: TechnologySurvey[];

//   @OneToMany(() => Application, (application) => application.projects)
//   applications: Application[];

//   // @ManyToOne(() => ProjectStatus, (projectStatus) => projectStatus.projects)
//   // projectStatus: ProjectStatus[];

//   @ManyToOne(() => ProjectStatus, (projectStatus) => projectStatus.projects)
//   projectStatus: ProjectStatus;

//   @OneToMany(() => Assignee, (assignee) => assignee.projects)
//   assignees: Assignee[];

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;

//   @UpdateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public updated_at!: Date;
// }

@Entity()
export class Application extends AbstractEntity<Application> {
  // Data from project

  @Column({ type: 'varchar', nullable: true })
  upi: string;
  @Column({ type: 'varchar', nullable: true, unique: true })
  applicationNumberForIremboHub: string;

  @Column({ default: false, nullable: true })
  isLocked: boolean;

  @Column({ default: false, nullable: true })
  isResubmitted: boolean;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  combiningPlotSize: number;

  @Column({ default: '0', nullable: true })
  isNonObjection: string;

  @Column({ default: '0', nullable: true })
  isNonObjectionReturned: string;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public isNonObjectionRequested!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public isNonObjectionReturnedDate!: Date;

  @Column({ default: '0', nullable: true })
  isInspected: string;

  @Column({ default: false })
  isAssociatedUpi: boolean;

  @Column({ default: false })
  isEIAVerified: boolean;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  buildUpArea: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  numberOfFloor: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  grossFloorArea: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  numberOfParkingSpace: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  priceOfDwellingUnitRwf: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  capacityInformation: number;

  @Column({ nullable: true })
  numberOfDwellingUnits: string;

  @Column({ nullable: true })
  DescriptionOfOperation: string;

  @Column({ nullable: true })
  percentageSpaceUse: string;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  waterConsumption: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  electricityConsumption: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  DistanceToTheNearestLandIn: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  ProjectCostInUSD: number;

  @Column({ nullable: true, type: 'decimal', precision: 15, scale: 2 })
  ProjectCostInRwf: number;

  @ManyToOne(() => Project, (project) => project.applications)
  projects: Project;

  @Column({ type: 'varchar' })
  agencyId: string;

  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar', nullable: true })
  submittedByUserId: string;

  @Column({ type: 'varchar', nullable: true })
  certificateNumberEIA: string;

  @ManyToOne(
    () => TechnologySurvey,
    (technologySurvey) => technologySurvey.applications,
  )
  technologySurveys: TechnologySurvey;

  @ManyToOne(() => PermitType, (permitType) => permitType.applications)
  permitTypes: PermitType;

  @ManyToOne(() => CategoryType, (categoryType) => categoryType.applications)
  categoryTypes: Project;

  @ManyToOne(() => BuildType, (buildType) => buildType.applications)
  buildTypes: BuildType;

  @ManyToOne(
    () => ApplicationStatus,
    (applicationStatus) => applicationStatus.applications,
  )
  applicationStatus: ApplicationStatus;

  @OneToMany(() => Answer, (answer) => answer.applications)
  answers: Answer[];

  @Column({ type: 'varchar', nullable: true })
  applicationName: string;

  @Column({ type: 'varchar' })
  permitTypeCode: string;

  @Column({ type: 'varchar' })
  agencyCode: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public submittedDate: Date;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public resubmittedDate: Date;

  @Column({ default: false, nullable: true })
  isCountingActive: boolean;

  @Column({ default: 0, nullable: true })
  numberOfDayItTakes: number;

  @OneToMany(() => Invoice, (invoice) => invoice.applications)
  invoices: Invoice[];

  @OneToMany(() => Certificate, (certificate) => certificate.applications)
  certificates: Certificate[];

  @OneToMany(
    () => DevelopmentStatus,
    (developmentStatus) => developmentStatus.applications,
  )
  developmentStatus: DevelopmentStatus[];

  @OneToMany(
    () => ApplicationApproval,
    (applicationApproval) => applicationApproval.applications,
  )
  applicationApprovals: ApplicationApproval[];

  @OneToMany(
    () => ApprovalDocument,
    (approvalDocument) => approvalDocument.applications,
  )
  approvalDocuments: ApprovalDocument[];

  @OneToMany(
    () => ApplicationApprovalCheckList,
    (applicationApprovalCheckList) => applicationApprovalCheckList.applications,
  )
  applicationApprovalCheckLists: ApplicationApprovalCheckList[];

  @OneToMany(
    () => OtherInfoApplication,
    (otherInfoApplication) => otherInfoApplication.applications,
  )
  otherInfoApplications: OtherInfoApplication[];

  @OneToMany(
    () => ReviewersOnApplication,
    (reviewersOnApplication) => reviewersOnApplication.applications,
  )
  reviewersOnApplications: ReviewersOnApplication[];

  @OneToMany(
    () => FoundationInspection,
    (foundationInspection) => foundationInspection.application,
  )
  foundationInspections: FoundationInspection[];

  @OneToMany(
    () => OccupancyInspection,
    (occupancyInspection) => occupancyInspection.application,
  )
  occupancyInspections: OccupancyInspection[];

  @OneToMany(() => SubmissionLog, (submissionLog) => submissionLog.application)
  submissionLogs: SubmissionLog[];

  // @OneToMany(
  //   () => SubmissionLog,
  //   (submissionLog) => submissionLog.application,
  //   { cascade: ['remove'] },
  // )
  // submissionLogs: SubmissionLog[];

  @Column({ type: 'simple-array', nullable: true })
  assignUsersFoReview: string[];

  @Column('jsonb', { nullable: true })
  other: Record<string, any>;
  // additional fields for better controlling application
  @Column({ default: false })
  isRRAVerified: boolean;
  @Column({ default: false })
  isUnderMortgage: boolean;
  @Column({ default: false })
  isUnderRestriction: boolean;
  @Column({ type: 'simple-array', nullable: true })
  parcelOwners: string[];
  @Column({ type: 'simple-array', nullable: true })
  parcelOwnersIds: string[];
  @Column({ type: 'simple-array', nullable: true })
  parcelRepresentatives: string;
  @Column({ type: 'simple-array', nullable: true })
  parcelRepresentativesIds: string;
  @Column({ default: '523096.26218253', nullable: true })
  centralCoordinateX: string;

  @Column({ default: '4784974.42535000', nullable: true })
  centralCoordinateY: string;

  @Column({ default: '1021101', nullable: true })
  villageCode: string;

  @Column({ default: 'Mukagarama', nullable: true })
  villageName: string;

  @Column({ default: '1021101', nullable: true })
  cellCode: string;

  @Column({ default: '1021101', nullable: true })
  cellName: string;

  @Column({ default: '10211', nullable: true })
  sectorCode: string;

  @Column({ default: '10211', nullable: true })
  sectorName: string;

  @Column({ default: '102', nullable: true })
  districtCode: string;

  @Column({ default: '102', nullable: true })
  districtName: string;

  @Column({ default: '01', nullable: true })
  provinceCode: string;

  @Column({ default: '01', nullable: true })
  provinceName: string;
  @Column({ nullable: true })
  originalPlotSize: number;

  @Column({ type: 'varchar', nullable: true })
  projectName: string;
  @Column({ type: 'varchar', nullable: true })
  projectDescription: string;
}

@Entity()
export class OtherInfoApplication extends AbstractEntity<OtherInfoApplication> {
  @Column({ type: 'varchar' })
  userId: string;

  @ManyToOne(() => PermitType, (permitType) => permitType.otherInfoApplications)
  permitTypes: PermitType;

  @ManyToOne(
    () => Application,
    (application) => application.otherInfoApplications,
  )
  applications: Application;

  @Column({ nullable: true })
  doYouHaveTheOccupancy: string;

  @Column({ nullable: true })
  licenseNumber: string;

  @Column({ nullable: true })
  isFastAidBox: string;

  @Column({ nullable: true })
  disabilityToiletsFlipUpGrabBars: string;

  @Column({ nullable: true })
  paraLighteningSystem: string;

  @Column({ nullable: true })
  equipmentCapacity: string;

  @Column({ nullable: true })
  constructionMethod: string;

  @Column({ nullable: true })
  fireAlarmSystemWithAnAlarmBellOnEach: string;

  @Column({ nullable: true })
  whyNotFireAlarmSystemWithAnAlarmBellOnEach: string;

  @Column({ nullable: true })
  fireExtinguishersEvery50mOnEachFloor: string;

  @Column({ nullable: true })
  whyNotFireExtinguishersEvery50mOnEachFloor: string;

  @Column({ nullable: true })
  functioningExitSignsOnEachFloor: string;

  @Column({ nullable: true })
  whyNotfunctioningExitSignsOnEachFloor: string;

  @Column({ nullable: true })
  anEmergencyExitOnEachFloor: string;

  @Column({ nullable: true })
  whyNotanEmergencyExitOnEachFloor: string;

  @Column({ nullable: true })
  floorPlanOnEachLevel: string;

  @Column({ nullable: true })
  whyNotfloorPlanOnEachLevel: string;

  @Column({ nullable: true })
  numberSignOnEachFloor: string;

  @Column({ nullable: true })
  whyNotnumberSignOnEachFloor: string;

  @Column({ nullable: true })
  signForbiddingTheUseOfElevatorsInCaseOfFire: string;

  @Column({ nullable: true })
  whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire: string;

  @Column({ nullable: true })
  landingSpaceOnTopOfTheBuildingForHelicopters: string;

  @Column({ nullable: true })
  whyNotlandingSpaceOnTopOfTheBuildingForHelicopters: string;

  @Column({ nullable: true })
  CCTVCameras: string;

  @Column({ nullable: true })
  whyNotCCTVCameras: string;

  @Column({ nullable: true })
  WalkThroughAndHeldMetalDetect: string;

  @Column({ nullable: true })
  whyNotWalkThroughAndHeldMetalDetect: string;

  @Column({ nullable: true })
  UnderSearchMirror: string;

  @Column({ nullable: true })
  whyNotUnderSearchMirror: string;

  @Column({ nullable: true })
  LuggageScanners: string;

  @Column({ nullable: true })
  whyNotLuggageScanners: string;

  @Column({ nullable: true })
  PlatesIndicatingEmergencyResponseUnitsPhoneNumbers: string;

  @Column({ nullable: true })
  whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers: string;

  @Column({ nullable: true })
  EmergencyEvacuationPlan: string;

  @Column({ nullable: true })
  whyNotEmergencyEvacuationPlan: string;

  @Column({ nullable: true })
  SecurityManagerAndStaffCameras: string;

  @Column({ nullable: true })
  whyNotSecurityManagerAndStaffCameras: string;

  @Column({ nullable: true })
  AnInternalCommunicationSystem: string;

  @Column({ nullable: true })
  whyNotAnInternalCommunicationSystem: string;

  @Column({ nullable: true })
  BroadBandInternetServices: string;

  @Column({ nullable: true })
  whyNotBroadBandInternetServices: string;

  @Column({ nullable: true })
  StaffAndVisitorAccessCards: string;

  @Column({ nullable: true })
  whyNotStaffAndVisitorAccessCards: string;

  @Column({ nullable: true })
  applicationForFixedTelephoneLineConnection: string;

  @Column({ nullable: true })
  areThereAnyFacilitiesForTheDisabledProvidedBuilding: string;

  @Column({ nullable: true })
  whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding: string;

  @Column({ nullable: true })
  stageOfConstruction: string;

  @Column({ nullable: true })
  supervisingFirmSiteEngineer: string;

  @Column({ nullable: true })
  remarks: string;

  @Column({ nullable: true })
  dateForRequestedInspection: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}

@Entity()
export class AssociatedUPI extends AbstractEntity<AssociatedUPI> {
  @Column({ type: 'varchar' })
  upi: string;

  @Column({ type: 'varchar', nullable: true })
  plotSize: string;

  @Column({ type: 'varchar', nullable: true })
  ownerFullName: string;

  @Column({ type: 'varchar', nullable: true })
  ownerIdNo: string;

  @Column({ default: false })
  isRRAVerified: boolean;

  @Column({ type: 'varchar', nullable: true })
  userId: string;

  @ManyToOne(() => Project, (project) => project.associatedUPIs)
  projects: Project;
}

// @Entity()
// export class ProjectLocation extends AbstractEntity<ProjectLocation> {
//   @Column()
//   villageName: string;

//   @ManyToOne(() => Application, (application) => application.associatedUPIs)
//   applications: Application;
// }

@Entity()
export class Assignee extends AbstractEntity<Assignee> {
  @Column({ type: 'varchar' })
  userIdForAssignee: string;

  @Column({ type: 'varchar', nullable: true })
  EIACertificateNumber: string;

  @Column({ type: 'varchar', nullable: true })
  timeLineDays: string;

  @Column({ default: '0' })
  assigneeStatusId: string;

  @Column({ type: 'varchar' })
  userTypeId: string;

  @Column({ type: 'varchar', nullable: true })
  projectStatusId: string;

  @Column({ type: 'varchar' })
  licenseNumber: string;

  @ManyToOne(() => Project, (project) => project.assignees)
  projects: Project;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}

@Entity()
export class PermitQuestion extends AbstractEntity<PermitQuestion> {
  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'varchar' })
  code: string;

  @ManyToOne(() => PermitType, (permitType) => permitType.questions)
  permitTypes: PermitType;

  @ManyToOne(
    () => QuestionCategory,
    (questionCategory) => questionCategory.questions,
  )
  questionCategorys: QuestionCategory;

  @OneToMany(() => Answer, (answer) => answer.permitQuestions)
  answers: Answer[];

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}

@Entity()
export class Answer extends AbstractEntity<Answer> {
  @Column({ type: 'varchar' })
  userId: string;

  @ManyToOne(() => PermitQuestion, (permitQuestion) => permitQuestion.answers)
  permitQuestions: PermitQuestion;

  @Column({ type: 'varchar' })
  comment: string;

  @ManyToOne(() => Application, (application) => application.answers)
  applications: Application;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}

@Entity()
export class ReviewersOnApplication extends AbstractEntity<ReviewersOnApplication> {
  @Column({ type: 'varchar' })
  userId: string;

  @Column({ type: 'varchar', default: 0 })
  status: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  operatingSystem: string;

  @ManyToOne(
    () => Application,
    (application) => application.reviewersOnApplications,
  )
  applications: Application;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}

// inspections

@Entity()
export class FoundationInspection extends AbstractEntity<FoundationInspection> {
  @Column({ type: 'varchar', nullable: true })
  geotechnicalReportRespected: string;

  @Column({ type: 'varchar', nullable: true })
  foundationType: string;

  @Column({ type: 'varchar', nullable: true })
  proceduresComply: string;

  @Column({ type: 'varchar', nullable: true })
  soilTreatmentComply: string;

  @Column({ type: 'varchar', nullable: true })
  erosionControlMeasures: string;

  @Column({ type: 'varchar', nullable: true })
  footingSizeComply: string;

  @Column({ type: 'varchar', nullable: true })
  locationComply: string;

  @Column({ type: 'varchar', nullable: true })
  reinforcementSizeComply: string;

  @Column({ type: 'varchar', nullable: true })
  spacingReinforcementsComply: string;

  @Column({ type: 'varchar', nullable: true })
  concretePlacementComply: string;

  @Column({ type: 'varchar', nullable: true })
  plumbingComply: string;

  @Column({ type: 'varchar', nullable: true })
  comments: string;

  @Column({ type: 'varchar', nullable: true })
  userId: string;

  @ManyToOne(
    () => Application,
    (application) => application.foundationInspections,
  )
  application: Application;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @Column({ type: 'text', nullable: true })
  fileBase64: string;
}

@Entity()
export class OccupancyInspection extends AbstractEntity<OccupancyInspection> {
  @Column({ type: 'varchar' })
  fireExtinguishersInResidence: string;

  @Column({ type: 'varchar' })
  fireBlanketInKitchen: string;

  @Column({ type: 'varchar' })
  twoExitsPerResidence: string;

  @Column({ type: 'varchar' })
  emergencyExit: string;

  @Column({ type: 'varchar' })
  fireAlarmOnEachFloor: string;

  @Column({ type: 'varchar' })
  smokeDetectorsSprinklers: string;

  @Column({ type: 'varchar' })
  hoseReelsEachFloor: string;

  @Column({ type: 'varchar' })
  lightningArrestor: string;

  @Column({ type: 'varchar' })
  fireExtinguishersEvery30m: string;

  @Column({ type: 'varchar' })
  exitSignsEachFloor: string;

  @Column({ type: 'varchar' })
  emergencyExitEachFloor: string;

  @Column({ type: 'varchar' })
  floorPlanEachLevel: string;

  @Column({ type: 'varchar' })
  signForElevators: string;

  @Column({ type: 'varchar' })
  landingSpaceHelicopter: string;

  @Column({ type: 'varchar' })
  evacuationPlan: string;

  @Column({ type: 'varchar' })
  cctvCameras: string;

  @Column({ type: 'varchar' })
  metalDetectors: string;

  @Column({ type: 'varchar' })
  luggageScanners: string;

  @Column({ type: 'varchar' })
  emergencyPhoneNumbers: string;

  @Column({ type: 'varchar' })
  fireHydrantNearby: string;

  @Column({ type: 'varchar' })
  evacuationPlanSecurity: string;

  @Column({ type: 'varchar' })
  securityManagerStaff: string;

  @Column({ type: 'varchar' })
  restrictedAccess: string;

  @Column({ type: 'varchar' })
  insuranceBuilding: string;

  @Column({ type: 'varchar' })
  comments: string;

  @Column({ type: 'varchar' })
  userId: string;

  @ManyToOne(
    () => Application,
    (application) => application.occupancyInspections,
  )
  application: Application;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @Column({ type: 'text', nullable: true })
  fileBase64: string;
}

@Entity()
export class SubmissionLog extends AbstractEntity<SubmissionLog> {
  @ManyToOne(() => Application, (application) => application.submissionLogs)
  application: Application;

  @Column()
  userId: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column({ nullable: true })
  applicationStatusId: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  operatingSystem: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}
