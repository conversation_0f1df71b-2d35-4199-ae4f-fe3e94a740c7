<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-lists">
                <div class="app-tblist">

                    <div class="app-tblist_title">
                        <span class="hder" aria-label="header tittle">Weekly Report</span>
                        <div class="tbleFilter">
                          <div class="form-input_search" *ngIf="(currentUser.data.user.role.code !== 'STF'
                          )">
                            <!-- <label>Find a user</label> -->
                            <input type="text" name="searchTerm" [(ngModel)]="reportService.searchTerm"
                              (ngModelChange)="onSearchTermChange($event)" placeholder="Find a user"
                              (keyup)="open()"/>
                            <button type="button" class="btn">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                <g data-name="Layer 2">
                                  <g data-name="search">
                                    <rect width="24" height="24" opacity="0" />
                                    <path
                                      d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                                  </g>
                                </g>
                              </svg>
                            </button>
                          </div>
                          <div class="form-input clear-m w-aut">
                            <div><label for="">From</label>
                              <input class="w-aut" type="date" name="filterByDate" [(ngModel)]="reportService.filterByDate"
                                (ngModelChange)="onSearchByDateChange($event)" required />
                            </div>
                          </div>
                          <div class="form-input clear-m w-aut">
                            <div>
                                <label for="">To</label>
                              <input class="w-aut" type="date" name="filterEndDate" [(ngModel)]="reportService.filterEndDate"
                                (ngModelChange)="onSearchByDateChange($event)" required />
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- (gridReady)="onGridReady($event)" -->
                    <button type="button" (click)="onBtnExport()">Export to Excel</button>



                    <ul class="tblist" *ngIf="users.length > 0 && reportService.searchTerm.length > 0 &&
                        isUserShow">
                        <li class="tblist-item" *ngFor="let li of users">

                            <div class="tblist-item_icon">
                                <!-- <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" /> -->
                                <div class="tblist-item_ck">
                                    <label class="form-checkbox">
                                        <input type="checkbox"
                                        [checked]="li.id === this.userId"
                                        (click)="chooseId(li)">
                                    </label>
                                </div>
                            </div>
                            <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">First name</span> {{li.firstName}}</span>
                                <span>
                                    <span class="ttl">Last Name</span> {{li.lastName}}</span>
                            </div>

                            <div class="tblist-item_dt txt-l">
                                <span>
                                    <span class="ttl">Email</span> {{li.email}} </span>
                                <span>
                                    <span class="ttl">Phone number</span> {{li.phoneNumber}}</span>
                            </div>
                            <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">Role</span> {{li.role?.name}}</span>
                                <span>
                                    <span class="ttl">Agency</span> {{li?.agency?.name}}</span>
                            </div>

                            <div class="tblist-item_xcn" *ngIf="users.length > 0">

                                <!-- <a *ngIf="li.applicationStatus.code !== 'UNCRN'" class="kbk-link-btn hs-tp" data-kbk-tooltip="view details"
                                    [routerLink]="['/account/application/application-detail', li.id]">
                                    <img src="assets/ikons/colored/ikon-eye.svg" alt="" /> Application Details
                                </a> -->


                            </div>
                        </li>
                    </ul>



                    <ag-grid-angular class="ag-theme-quartz" style="height: 500px;" [rowData]="rowData"
                        [columnDefs]="colDefs" [columnDefs]="colDefs" [defaultColDef]="defaultColDef"
                        [pagination]="true" [rowSelection]="'multiple'" (gridReady)="onGridReady($event)"
                        (cellValueChanged)="onCellValueChanged($event)" (selectionChanged)="onSelectionChanged($event)">
                    </ag-grid-angular>
                </div>
            </div>
        </div>
    </div>
</div>
