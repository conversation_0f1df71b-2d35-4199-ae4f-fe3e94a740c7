$blue-darker: #0067ab !default;
$blue-dark: #0082d4 !default;
$blue-root: #0099e8 !default;
$blue-light: #def1ff !default;
$blue-lighter: #eff9ff !default;

// .chat-container {
//     height: 400px;
//     overflow-y: auto;
//     border: 1px solid #ccc;
//     padding: 10px;
//   }
//   .message {
//     margin: 5px 0;
//   }

.chatbox-bdy {
  height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.selected-files-preview {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin: 0.5rem 1.5rem;
  padding: 1rem;
  background-color: $blue-root;
  .file-preview-wrapper {
    position: relative;

    .file-thumb {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      transition: transform 0.2s ease;
      background-color: #f0f0f0;

      &:hover {
        transform: scale(1.1);

        .file-name {
          opacity: 1;
          visibility: visible;
        }

        .remove-btn {
          opacity: 1;
          visibility: visible;
        }
      }

      .file-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .file-name {
        position: absolute;
        bottom: 2px;
        left: 2px;
        right: 2px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        font-size: 0.65rem;
        padding: 0.2rem;
        border-radius: 3px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease;
      }

      .remove-btn {
        position: absolute;
        bottom: 2px;
        left: 2px;
        background: rgba(255, 0, 0, 0.8);
        color: white;
        border: none;
        border-radius: 99%;
        width: 8px;
        height: 6px;
        font-size: 12px;
        line-height: 14px;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.kbkchat-reply {
  justify-content: flex-end;
}

.kbkchat-nonreply {
  justify-content: flex-start;
}


.kbkchat-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.5rem;
  .kbkchat-attachment-item {
    max-width: 160px;
    min-width: 120px;
    flex: 1 1 auto;
    border: 1px solid $blue-light;
    border-radius: 10px;
    background-color: $blue-lighter;
    box-shadow: 0 1px 4px rgba(0, 105, 171, 0.15);
    overflow: hidden;
    transition: box-shadow 0.25s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 130, 212, 0.4);
      .kbkchat-download-icon {
        opacity: 1;
        visibility: visible;
        color: $blue-root;
      }
    }

    .kbkchat-attachment-content {
      position: relative;
      padding: 0.5rem;
      cursor: pointer;
      text-align: right;
      color: $blue-darker;
    }

    .file-image,
    .file-icon-preview img {
      width: 100%;
      height: 100px;
      object-fit: cover;
      border-radius: 6px;
      margin-bottom: 0.4rem;
      border: 1px solid $blue-light;
      background-color: $blue-lighter;
    }

    .file-icon-preview {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100px;
      background: $blue-light;
      border-radius: 6px;

      img {
        width: 48px;
        height: 48px;
        object-fit: contain;
        filter: drop-shadow(0 0 2px rgba(0, 105, 171, 0.3));
      }
    }

    .kbkchat-file-info {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      font-size: 0.75rem;
      color: $blue-darker;

      .kbkchat-file-icon {
        width: 20px;
        height: 20px;
        object-fit: contain;
        margin-top: 2px;
        filter: drop-shadow(0 0 1px rgba(0, 105, 171, 0.4));
      }

      .kbkchat-file-name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
      }
    }

    .kbkchat-download-icon {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 14px;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease;
      background: $blue-lighter;
      padding: 4px;
      border-radius: 50%;
      box-shadow: 0 0 6px rgba(0, 153, 232, 0.6);
      color: $blue-dark;
      cursor: pointer;
      user-select: none;
    }
  }
}
