import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-invoices',
  templateUrl: './invoices.component.html',
  styleUrls: ['./invoices.component.scss']
})
export class InvoicesComponent {
  lists: any[] = [];
  currentUser: any = {};
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  cancelData: any = {};
  refundData: any = {};
  fullList: any[] = [];
  filteredList: any[] = [];

  iremboDetails: any = {};
  editInvoiceDetails: any = {};
  invoiceItems: any[] = [];
  invoicePrices: any[] = [];
  invoicePriceId: any = {};
  submitted: boolean = false;
  searchData: any = {};

  constructor(
    public applicationService: ApplicationService,
    private sessionService: SessionService,
    private router: Router,
    private modalService: NgbModal,
    private userService: UserMgtService,
    public utilService: UtilService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any)?.UserId;
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'invoice/invoice/applicantUserId/search?search=' + this.currentUser.userId)
    //   .subscribe(
    //     data => {
    //       this.lists = data.items;
    //     }
    //   )

    this.lookups();

    this.loadLists();
  }


  lookups() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'invoice/invoiceItem')
    .subscribe(
      data => { this.invoiceItems = data; }
    )


  this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.INVOICEPRICES)
    .subscribe(
      data => {

        this.invoicePrices = data
      }
    )
  }


  onSearchByDateChange(date: string): void {

    if (this.searchData.dateFrom && this.searchData.dateTo) {
      const from = this.searchData.dateFrom ? new Date(this.searchData.dateFrom) : null;
      const to = this.searchData.dateTo ? new Date(this.searchData.dateTo) : null;

      if (from) {
        // Convert 'from' to UTC start of the day
        from.setHours(0, 0, 0, 0);
      }
    
      if (to) {
        // Convert 'to' to UTC end of the day
        to.setHours(23, 59, 59, 999);
      }
      if (to) {
        // Add one day to the 'to' date to include the entire day
        to.setDate(to.getDate() + 1);
      }

      const filtered = this.fullList.filter(item => {
        const createdAt = new Date(item.created_at);
        return (!from || createdAt >= from) && (!to || createdAt < to);
      });




      this.filteredList = filtered;
      this.totalRecords = filtered.length;
      this.startIndex = (this.page - 1) * this.pageSize + 1;
      this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
      if (this.endIndex > this.totalRecords) {
        this.endIndex = this.totalRecords;
      }
      this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    }
  }


  ngOnDestroy() {
    this.applicationService.searchTerm = '';
    this.userService.searchTerm = '';
  }


  loadLists() {
    if (this.currentUser?.data?.user?.userType?.code !== "STF" && this.currentUser?.data?.user?.role?.code !== "ADM") {
      this.applicationService
        .findAllWithPath(environment.applicationUrl + 'invoice/invoice/applicantUserId/search?search=' + this.currentUser.userId)
        .subscribe(
          (data) => {
            this.fullList = data.items;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
          });
    } else if (this.currentUser?.data?.user?.userType?.code === "STF" && this.currentUser?.data?.user?.role?.code == "ADM") {
      this.applicationService
        .findAllWithPath(environment.applicationUrl + 'invoice/invoice')
        .subscribe(
          (data) => {
            this.fullList = data;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();


          });
    } else {
      this.applicationService
        .findAllWithPath(environment.applicationUrl + 'invoice/invoice/agency/' + this.currentUser.data?.user?.agency?.code)
        .subscribe(
          (data) => {
            this.fullList = data;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();

          });
    }
  }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    // if (!this.userService.searchTerm) return true;
    // const term = this.userService.searchTerm.toLowerCase();
    // return Object.values(item).some(val =>
    //   String(val).toLowerCase().includes(term)
    // );

    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';
  
    // Flattened search: you explicitly check main and nested properties
    const matchesTerm = term
      ? (
          Object.values(item).some(val =>
            String(val).toLowerCase().includes(term)
          ) ||
          (item.applications?.applicationName &&
            item.applications.applicationName.toLowerCase().includes(term)) ||
            (item.applications?.upi &&
              item.applications.upi.toLowerCase().includes(term))
        )
      : true;
  
    const matchesDate = dateFilter
      ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter
      : true;
  
    return matchesTerm && matchesDate;
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    // this.filterAndPaginate();
    if (this.searchData.dateFrom && this.searchData.dateTo) {
      this.onSearchByDateChange('');
    } else {
      this.filterAndPaginate();
    }
  }


  changePageSize() {
    this.pageSize = (+this.pageSize);
    this.filterAndPaginate();
  }


  viewData(event: any) {
    this.router.navigate(['/account/payment/invoice-detail/',]);
  }

  // getPremiumData() {

  //   this.lists = this.content
  //     .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);

  // }


  refundModal(content: any, sizeParams: any, event: any) {
    this.refundData = event;
    this.modalService.open(content, { size: sizeParams, centered: true });

  }

  openModal(content: any, sizeParams: any, event: any) {
    this.cancelData = event;
    this.iremboDetails = event;

    // this.saveForm.controls['certificateNumber'].setValue(event.certificateNumber);
    this.modalService.open(content, { size: sizeParams, centered: true });
  }





  confirmCancel() {
    this.applicationService.patchWithPath(JSON.stringify({}), environment.applicationUrl + 'invoice/invoice/cancel/' + this.cancelData.id)
      .subscribe(
        data => {
          this.cancel()
          this.loadLists();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "top", "right");
        }
      )
  }

  confirmRefund() {
    this.applicationService.patchWithPath(JSON.stringify({}), environment.applicationUrl + 'invoice/invoice/refunded/' + this.refundData.id)
      .subscribe(
        data => {
          this.cancel()
          this.loadLists();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "top", "right");
        }
      )
  }



  saveToIrembo() {

    this.iremboDetails.invoiceId = this.iremboDetails.externalReferenceNumber;

    let dataToSave = {
      "invoiceId": this.iremboDetails.invoiceId,
      "amount": +this.iremboDetails.amount,
      "expirationTime": this.iremboDetails.expirationTime
    }

    this.submitted = true;
    this.applicationService.saveAssetWithPath(dataToSave, environment.applicationUrl + 'invoice/updateToIrembo')
      .subscribe(
        data => {
          this.submitted = false
          this.cancel()
          this.loadLists();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "top", "right");
        }, error => {
          this.submitted = false
        }
      )

  }

  openPricePopup() {
    this.editInvoiceDetails.isChoosingPrice = true;
  }

  getPrice() {
    this.editInvoiceDetails.newAmount = +(this.invoicePrices.find((invoice: any) => invoice.id === this.invoicePriceId).amount);
    this.editInvoiceDetails.isChoosingPrice = false;
  }


  openModalToUpdate(content: any, sizeParams: any, event: any) {
    this.editInvoiceDetails = event;
    this.editInvoiceDetails.newAmount = this.editInvoiceDetails.amount;
    this.modalService.open(content, { size: sizeParams, centered: true });


    this.applicationService.findAllWithPath(environment.applicationUrl + 'invoice/invoiceStatus/code/search?search=' + VALUE.INVOICE_PENDING_STATUS_CODE)
      .subscribe(
        invoiceTypeData => {
          this.editInvoiceDetails.invoiceStatusId = invoiceTypeData.items[0].id;

        }
      )
  }

  updateInvoiceMethod() {
    let dataToSave = {
      "applicationId": this.editInvoiceDetails.applications.id,
      "applicantUserId": this.editInvoiceDetails.applicantUserId,
      "userId": this.currentUser.userId,
      "agencyCode": this.editInvoiceDetails.agencyCode,
      "paymentAccountIdentifier": "string",
      "amount": this.editInvoiceDetails.newAmount,
      "transactionNumber": "string",
      "invoiceTypeId": this.editInvoiceDetails.invoiceTypeId,
      "invoiceItemId": this.editInvoiceDetails.invoiceItemId,
      "invoiceStatusId": this.editInvoiceDetails.invoiceStatusId
    }
    this.submitted = true
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=' + dataToSave.agencyCode)
      .subscribe(
        data => {
          if (data && data.items) {
            dataToSave.paymentAccountIdentifier = data.items[0].id;
            this.applicationService.findAllWithPath(environment.applicationUrl + 'invoice/invoiceType/code/search?search=' + VALUE.INVOICE_TYPE_PERMIT_CODE)
              .subscribe(
                invoiceTypeData => {
                  if (invoiceTypeData && invoiceTypeData.items.length > 0) {
                    dataToSave.invoiceTypeId = invoiceTypeData.items[0].id;



                    this.applicationService.patchWithPath(dataToSave, environment.applicationUrl + 'invoice/invoice/' + this.editInvoiceDetails.id)
                      .subscribe(
                        data => { 
                          this.submitted = false
                          this.cancel()
                          this.loadLists();
                          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "top", "right");
                        }
                      )




                  } else {
                    this.submitted = false;
                    this.utilService.showNotification(NOTIFICATION_COLOR.error, "Please set invoice type", "bottom", "center");
                  }
                }, error => { this.submitted = false} 
              )
          } else {
            this.submitted = false;
          }
        }, error => { this.submitted = false; }
      )

  }



  cancel() {
    this.modalService.dismissAll();
  }
}
