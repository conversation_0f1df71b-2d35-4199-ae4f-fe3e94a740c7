const cluster = require('cluster');
const os = require('os');
const { spawn } = require('child_process');

// Get number of CPU cores dynamically
const numCPUs = os.cpus().length;
const appName = process.argv[2] || 'applications';

console.log(`System has ${numCPUs} CPU cores available`);

if (cluster.isMaster) {
  console.log(`Master process ${process.pid} is running`);
  console.log(`Starting ${appName} with optimized settings`);

  // Fork a single worker for the main application
  const worker = cluster.fork({
    WORKER_ID: 1,
    CPU_CORES: numCPUs,
    APP_NAME: appName,
    IS_MAIN_WORKER: 'true'
  });
  
  console.log(`Main worker ${worker.process.pid} started`);

  // Handle worker exit and restart
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
    const newWorker = cluster.fork({
      WORKER_ID: 1,
      CPU_CORES: numCPUs,
      APP_NAME: appName,
      IS_MAIN_WORKER: 'true'
    });
    console.log(`New worker ${newWorker.process.pid} started`);
  });

  // Monitor cluster health
  cluster.on('online', (worker) => {
    console.log(`Worker ${worker.process.pid} is online`);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('Master received SIGTERM, shutting down workers gracefully...');
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    process.exit(0);
  });

  process.on('SIGINT', () => {
    console.log('Master received SIGINT, shutting down workers gracefully...');
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    process.exit(0);
  });

} else {
  // Worker process
  const workerId = process.env.WORKER_ID;
  const cpuCores = process.env.CPU_CORES;
  const appName = process.env.APP_NAME;
  
  console.log(`Worker ${process.pid} (ID: ${workerId}) started for ${appName}`);
  console.log(`Optimizing for ${cpuCores} CPU cores`);

  // Start the NestJS application with optimized settings
  const child = spawn('pnpm', [
    'run',
    'start:dev',
    appName
  ], {
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      WORKER_ID: workerId,
      CPU_CORES: cpuCores,
      UV_THREADPOOL_SIZE: Math.max(4, cpuCores * 2), // Optimize thread pool
      NODE_OPTIONS: '--max-old-space-size=2048 --expose-gc'
    }
  });

  child.on('exit', (code) => {
    console.log(`Worker ${process.pid} exited with code ${code}`);
    process.exit(code);
  });

  // Handle signals for graceful shutdown
  process.on('SIGTERM', () => {
    console.log(`Worker ${process.pid} received SIGTERM`);
    child.kill('SIGTERM');
  });

  process.on('SIGINT', () => {
    console.log(`Worker ${process.pid} received SIGINT`);
    child.kill('SIGINT');
  });
} 