<div class="app-gen appl-details">
    <div class="app-gen-header genvline">
        <section class="genline">
            <div>
                <span class="hder" aria-label="header tittle">{{"applicationDetails.title" | translate }}</span>
                <span class="hder-sm" aria-label="header tittle">{{"applicationDetails.applicationsDetails" | translate }}</span>
            </div>
            <div class="btns">
                <button *ngIf="applicationDetail?.applicationStatus?.code === 'PND'" type="button"
                    class="kbk-btn kbk-btn-main" (click)="clickToContinue()">{{"applicationDetails.clickToContinue" | translate }}</button>
                <button type="button" class="kbk-btn kbk-btn-sec"
                    [routerLink]="['/account/application/applications']">{{"applicationDetails.back" | translate }}</button>
                <!-- <button type="button" class="kbk-btn kbk-btn-sec" [routerLink]="currentUser.data.role.code === 'STF' &&
                    currentUser.data.userType.code === 'STF' ? '/account/application/my-box' : '/account/application/applications'"> Bauick
                </button> -->
            </div>
            <!-- <ul class="kbk-tabs">
                <li class="kbk-tabs-item">
                    <a class="kbk-tabs-link" routerLinkActive="active">User</a>
                </li>
                <li class="kbk-tabs-item">
                    <a class="kbk-tabs-link" routerLinkActive="active">Role</a>
                </li>
            </ul> -->
        </section>
        <hr>
        <section class="kbk-x-s kbk-aend sp-2">
            <div>
                <div class="track-info">
                    <div class="track-profile">
                        <img src="assets/imgs/profile1.svg" alt="" />
                    </div>
                    <div class="track-dtail">
                        <label>{{"applicationDetails.owners" | translate }} </label>
                        <span class="track-user" *ngFor="let name of applicationDetail?.parcelOwners">
                          {{name}}
                            <!-- {{applicationDetail?.userDetails?.lastName}} -->

                          </span>
                        <!-- <span class="track-user">{{applicationDetail?.projects?.ownerFullName}}
                                </span> -->
                        <span class="track-usersub">{{applicationDetail?.projects?.upi}}</span>
                    </div>
                </div>
            </div>
            <div class="land-info">
                <div class="land-info-item">
                    <h3>{{"applicationDetails.parcelDetails" | translate }}</h3>
                    <div class="aline">
                        <div class="form-out">
                            <label>{{"applicationDetails.appliedFor" | translate }}</label>
                            <span>{{applicationDetail?.projects?.selectedUse}}</span>
                        </div>
                        <div class="form-out">
                            <label>{{"applicationDetails.province" | translate }}</label>
                            <span>{{applicationDetail?.projects?.provinceName}}</span>
                        </div>
                        <div class="form-out">
                            <label>{{"applicationDetails.district" | translate }}</label>
                            <span>{{applicationDetail?.projects?.districtName}}</span>
                        </div>
                        <div class="form-out">
                            <label>{{"applicationDetails.sector" | translate }}</label>
                            <span>{{applicationDetail?.projects?.sectorName}}</span>
                        </div>
                        <div class="form-out">
                            <label>{{"applicationDetails.cell" | translate }}</label>
                            <span>{{applicationDetail?.projects?.cellName}}</span>
                        </div>
                        <div class="form-out">
                            <label>{{"applicationDetails.village" | translate }}</label>
                            <span>{{applicationDetail?.projects?.villageName}}</span>
                        </div>
                    </div>
                </div>
                <div class="land-info-item kbk-x-e kbk-aend">
                    <div class="form-out">
                        <span class="bdg bdg-pend">{{applicationDetail?.applicationStatus?.name}}</span>
                    </div>
                    <!-- <h3>Application Status</h3> -->
                    <!-- <div>
                        <div class="aline">

                          <div class="form-out">
                              <label>Progress</label>
                              <div class="progr-bar">
                                  <div class="progr-bar_item" role="progressbar">50%</div>
                              </div>
                          </div>
                          <div class="form-out">

                          </div>
                      </div>
                  </div> -->
              </div>
          </div>
      </section>
  </div>
  <div class="app-gen-content">
      <div class="appl-info">
          <section>
              <div class="kbk-x-s sp-2 appl-info-fx">
                  <div class="appl-info-card">
                      <h3>{{"applicationDetails.projectDetails" | translate }}  </h3>
                      <div>
                          <div class="form-out">
                              <label>{{"applicationDetails.projectCategory" | translate }}</label>
                              <span>{{applicationDetail?.categoryTypes?.name}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.buildingType" | translate }}</label>
                              <span>{{applicationDetail?.buildTypes?.name}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.permitType" | translate }}</label>
                              <span>{{applicationDetail?.permitTypes?.name}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.projectBrief" | translate }}: {{applicationDetail.projectDescription}}</label>
                              <span class="form-out_txtarea">{{applicationDetail?.projects?.projectDescription}}</span>
                          </div>
                      </div>
                  </div>
                  <div class="appl-info-card">
                      <h3>{{"applicationDetails.projectEstimatesDetails" | translate }} </h3>
                      <div>
                          <div class="form-out">
                              <label>{{"applicationDetails.estimatedMonthlyWaterM3" | translate }}</label>
                              <span>{{applicationDetail?.waterConsumption}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.distanceNearestLandLineFiberM" | translate }}</label>
                              <span>{{applicationDetail?.DistanceToTheNearestLandIn}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.estimatedMonthlyElectricityKwh" | translate }}</label>
                              <span>{{applicationDetail?.electricityConsumption}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.estimatedProjectCostUsd" | translate }}</label>
                              <span>${{applicationDetail?.ProjectCostInUSD}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.estimatedProjectCostRwf" | translate }}</label>
                              <span>{{applicationDetail?.ProjectCostInRwf}} Rwf</span>
                          </div>
                          <!-- <div class="form-out">
                              <label>Any other comment</label>
                              <span>--</span>
                          </div> -->
                      </div>
                  </div>
              </div>
              <div class="kbk-x-s sp-2 appl-info-fx">
                  <div class="appl-info-card fx-2">
                      <h3>{{"applicationDetails.developmentDetails" | translate }}</h3>
                      <div class="kbk-x kbk-wrap-4">
                          <div class="form-out">
                              <label>{{"applicationDetails.projectCategory" | translate }}</label>
                              <span>{{applicationDetail.categoryTypes?.name}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.plotSizeSquareMeters" | translate }}</label>
                              <span>{{applicationDetail.projects?.plotSize}} m²</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.combinedPlotSizeSquareMeters" | translate }}</label>
                              <span>{{applicationDetail?.combiningPlotSize}} m²</span>
                          </div>
                          <!-- <div class="form-out">
                              <label>Building Coverage</label>
                              <span> {{applicationDetail.projects.percentageSpaceUse}}%</span>
                          </div> -->
                          <!-- <div class="form-out">
                              <label>{{"applicationDetails.proposedNumberOfFloorsGPlus" | translate }}</label>
                              <span>3</span>
                          </div> -->
                          <div class="form-out">
                              <label>{{"applicationDetails.grossFloorArea" | translate }}</label>
                              <span>{{applicationDetail?.grossFloorArea}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.proposedNumberOfFloorsGPlus" | translate }}</label>
                              <span>G+{{applicationDetail?.numberOfFloor}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.numberOfParkingSpaces" | translate }}</label>
                              <span>{{applicationDetail?.numberOfParkingSpace}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.priceOfDwellingUnit" | translate }}</label>
                              <span>{{applicationDetail?.priceOfDwellingUnitRwf}}</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.builtUpArea" | translate }}</label>
                              <span>{{applicationDetail?.buildUpArea}} </span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.grossFloorArea" | translate }}</label>
                              <span>{{applicationDetail?.grossFloorArea}}</span>
                          </div>
                          <!-- <div class="form-out">
                              <label>Estimate % of space</label>
                              <span>60%</span>
                          </div>
                          <div class="form-out">
                              <label>{{"applicationDetails.priceOfDwellingUnit" | translate }}</label>
                              <span>$60</span>
                          </div> -->
                          <div class="form-out">
                              <label>{{"applicationDetails.capacityInfoPeopleSeats" | translate }}</label>
                              <span>{{applicationDetail?.capacityInformation}}</span>
                          </div>
                          <!-- <div class="form-out">
                              <label>For Industrial projects</label>
                              <span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut excepturi hic, eaque
                                  velit a quod adipisci accusamus exercitationem? Qui, libero.</span>
                          </div> -->
                      </div>
                  </div>
              </div>
              <div class="appl-info-card fx-2">
                <h3>{{"applicationDetails.submittedBy" | translate }}</h3>
                <div class="kbk-x kbk-wrap-4">
                  <div class="form-out">
                    <label>{{"applicationDetails.names" | translate }}</label>
                    <span> {{ applicationDetail.senderDetails?.firstName | titlecase }} {{
                      applicationDetail.senderDetails?.lastName | titlecase }}</span>
                  </div>
                  <div class="form-out">
                    <label>{{"applicationDetails.phoneNumber" | translate }}</label>
                    <span>{{ applicationDetail.senderDetails?.phoneNumber }}</span>
                  </div>

                    <div class="form-out">
                      <label>{{"applicationDetails.userType" | translate }}</label>
                      <span>{{ applicationDetail?.senderDetails?.userType?.name }}</span>
                    </div>
                  </div>
                </div>
                <div class="kbk-x-s sp-2 appl-info-fx">
                    <div class="appl-info-card">
                        <h3>{{"applicationDetails.projectAttachmentDetails" | translate }}</h3>
                        <div>
                            <ul class="uploaded-list">
                                <li class="uploaded-file" *ngFor="let dc of documents">
                                    <div class="kbk-x-s kbk-ac">
                                        <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                                        <div>
                                            <p>{{dc.requiredDocumentName?.name}}</p>
                                            <span class="bdg bdg-pend">{{dc.fileName}}</span>
                                            <!-- <span class="bdg bdg-pend">{{dc.documentStatusId === '1' ? 'Approved' : ''}}</span> -->
                                        </div>
                                    </div>
                                    <div class="kbk-vd-btn">
                                        <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                                            (click)="viewDocument(viewContent, dc, 'ap-stas')"> {{"applicationDetails.viewDocument" | translate }} </a>
                                        <a *ngIf="loading" class="kbk-link hs-tp"> {{"applicationDetails.loading" | translate }} ... </a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="appl-info-card">
                      <h3>{{"applicationDetails.projectAttachmentAdditionalFiles" | translate }}</h3>
                      <div>
                        <ul class="uploaded-list">
                          <li class="uploaded-file" *ngFor="let dc of additionalDocuments">
                            <div class="kbk-x-s kbk-ac">
                              <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                              <div>
                                <p>{{ dc.additionalDescription }}</p>
                                <span class="bdg bdg-pend" style="width: 90% !important; padding-right: 1rem !important;">{{dc.fileName}}</span>
                              </div>
                            </div>
                            <div class="kbk-vd-btn">
                              <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                                (click)="viewDocument(viewContent, dc, 'ap-stas')"> {{"applicationDetails.viewDocument" | translate }} </a>
                              <a *ngIf="loading" class="kbk-link hs-tp"> {{"applicationDetails.loading" | translate }} ... </a>
                            </div>
                          </li>
                        </ul>
                      </div>
                  </div>
                  <div class="appl-info-card">
                    <h3>{{"applicationDetails.projectAttachmentAdditionalFiles" | translate }}</h3>
                    <div>
                      <ul class="uploaded-list">
                        <li class="uploaded-file" *ngFor="let dc of additionalDocuments">
                          <div class="kbk-x-s kbk-ac">
                            <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                            <div>
                              <p>{{ dc.additionalDescription }} / {{ dc.fileName }}</p>
                            </div>
                          </div>
                          <div class="">
                            <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                              (click)="viewDocument(viewContent, dc, 'ap-stas')"> {{"applicationDetails.viewDocument" | translate }} </a>
                            <a *ngIf="loading" class="kbk-link hs-tp"> {{"applicationDetails.loading" | translate }}.... </a>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
              </div>
              <div class="kbk-x-s sp-2 appl-info-fx">
                <div class="appl-info-card" *ngIf="associatedUpis.length > 0">
                  <h3>{{"applicationDetails.associatedUpi" | translate }}</h3>
                  <div>
                    <ul class="uploaded-list">
                      <li class="uploaded-file" *ngFor="let dcs of associatedUpis">
                        <div class="kbk-x-s kbk-ac">
                          <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                          <div>
                            <p>{{ dcs?.upi }}</p>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div class="kbk-x-c sp-sm">
                  <!-- <button class="kbk-btn kbk-btn-main">Process Application</button>
                  <button class="kbk-btn kbk-btn-main">Add Comment</button> -->
              </div>
          </section>
      </div>
  </div>
</div>
<ng-template #viewContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"applicationDetails.viewDocument" | translate }}</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-view-document [inputData]="outputData"></app-view-document>
    </div>
</ng-template>
