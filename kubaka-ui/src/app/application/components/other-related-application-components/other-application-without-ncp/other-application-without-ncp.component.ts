import { Component, ElementRef, Input, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';


@Component({
  selector: 'app-other-application-without-ncp',
  templateUrl: './other-application-without-ncp.component.html',
  styleUrls: ['./other-application-without-ncp.component.scss']
})
export class OtherApplicationWithoutNcpComponent {
  @ViewChild('mySelect') mySelect!: ElementRef<HTMLSelectElement>;
  @ViewChild('otherApplicationModel', { static: false }) otherApplicationModel!: TemplateRef<any>;

  isSelectDisabled = true;
  @Input() inputData: any = {};
  userForm!: UntypedFormGroup;
  categoryTypes: any[] = [];
  buildingTypes: any[] = [];
  permitTypes: any[] = [];
  pageForm!: UntypedFormGroup;
  doYouWantToDoItYouself: boolean = true;
  doYouWantToAssignToEngineer: boolean = false;
  projectDetails: any = {};
  existingProject: any = {};

  submitted: boolean = false;
  loading: boolean = false;
  currentUser: any;
  documents: any[] = [];
  outputData: any = {};
  paramsId: any = {};
  agencies: any[] = [];
  private modalRef!: NgbModalRef;
  otherInfoData: any = {};
  isAccessQuestion: boolean = false;
  technologySurveys: any[] = [];
  otherParams: any = {};
  formAccess: any = {};
  otherApplicationValue: any = {};

  constructor(
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private appConfig: AppConfig,
    private sessionService: SessionService,
    private route: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    route.params.subscribe((params: any) => {
      this.paramsId = params.id;
      this.otherParams = params;
    })
    this.outputData.isFromAssigned = false;
    this.callLookups();




  }


  changeForm() {
    this.doYouWantToAssignToEngineer = false;
    this.pageForm.controls['doYouWantToAssignToEngineer'].setValue(false);
    this.doYouWantToDoItYouself = true;
    this.pageForm.controls['doYouWantToDoItYouself'].setValue(true);

  }

  changeAssignForm() {
    this.doYouWantToAssignToEngineer = true;
    this.pageForm.controls['doYouWantToAssignToEngineer'].setValue(true);
    this.doYouWantToDoItYouself = false;
    this.pageForm.controls['doYouWantToDoItYouself'].setValue(false);

  }

  comeAndAllowToAssign(event: any) {
    if (event.found) {
      this.formAccess.isIAEverified = true;
      this.formAccess.isEIAVerified = event.isEIAVerified;
      this.formAccess.isRdbIAEchecked = true;
      this.formAccess.isRetreived = 1;
      this.userForm.controls['certificateNumberEIA'].setValue(event?.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event?.isEIAVerified);

    } else {
      this.formAccess.isIAEverified = false;
      this.formAccess.isEIAVerified = event.isEIAVerified;
      this.formAccess.isRdbIAEchecked = true;
      // this.formAccess.isRetreived = 0;
      this.formAccess.isRetreived = 1;
      this.userForm.controls['certificateNumberEIA'].setValue(event?.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event?.isEIAVerified);

    }
  }

  formAccessData() {
    this.pageForm = this.formBuilder.group({
      doYouWantToDoItYouself: [false],
      doYouWantToAssignToEngineer: [false],
    })
  }

  calculateNumberOfFlow() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    if (this.userForm.value.buildUpArea && this.userForm.value.numberOfFloor) {
      this.userForm.controls['grossFloorArea'].setValue((this.userForm.value.buildUpArea) * ((+this.userForm.value.numberOfFloor) + 1));
    }
  }


  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / (this.userForm.value.plotSize)) * 100).toFixed(2));
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  checkTemporalPermit() {
    let permitType = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId);
    if (this.paramsId !== '0' && (permitType.code !== 'NCP')) {
      this.router.navigate(['/account/application/other-application/' + permitType.id + '/' + this.paramsId]);
    } else {
      this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
      this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    }
  }

  calculateNumberOfPeople() {

    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  ngOnInit(): void {
    this.formAccessData();
    this.userForm = this.formBuilder.group({
      projectName: [""],
      projectDescription: [""],
      applicationId: [""],
      permitTypeId: ["", [Validators.required]],
      categoryTypeId: ["", [Validators.required]],
      buildTypeId: ["", [Validators.required]],
      buildTypeCode: [""],
      permitTypeCode: [""],
      applicationStatusId: [""], // hard coded
      applicationStatusCode: [""],
      userId: [this.currentUser.userId],
      // hardcoded
      technologySurveyId: ["13b8abbd-042c-455c-a91f-0540643140d9"],
      // From existing project
      agencyId: [""],
      projectId: ["", [Validators.required]],
      agencyCode: [""],
      categoryCode: [''],

      plotSize: [0],
      buildUpArea: ["", [Validators.required]],
      numberOfFloor: ["", [Validators.required]],
      grossFloorArea: ["", [Validators.required]],
      numberOfParkingSpace: [""],
      priceOfDwellingUnitRwf: [],
      DescriptionOfOperation: [""],
      percentageSpaceUse: ["", [Validators.required]],
      numberOfDwellingUnits: ["", [Validators.required]],
      waterConsumption: ["", [Validators.required]],
      electricityConsumption: ["", [Validators.required]],
      DistanceToTheNearestLandIn: ["", [Validators.required]],
      ProjectCostInUSD: ["", [Validators.required]],
      ProjectCostInRwf: ["", [Validators.required]],
      capacityInformation: ["", [Validators.required]]

    });

    this.loadData();
  }


  loadData() {
    this.loading = true;
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/' + this.paramsId)
      .subscribe(
        data => {
          if (data) {
            this.loading = false;
            this.projectDetails = data;
            if (this.projectDetails.isFromOldSystem) {
              this.isSelectDisabled = false;
            } else {
              this.isSelectDisabled = true;
            }
            this.outputData.project = this.projectDetails;
            this.userForm.controls['projectId'].setValue(this.projectDetails.id);
            this.userForm.controls['agencyId'].setValue(this.projectDetails.agencyId);
            this.userForm.controls['plotSize'].setValue(this.projectDetails.plotSize);
            this.userForm.controls['projectDescription'].setValue(this.projectDetails.projectDescription);
            this.userForm.controls['projectName'].setValue(this.projectDetails.projectName);
            // checking routeing params
            if (this.otherParams && this.otherParams.permitTypeId) {
              this.ifRouteContainsPermitType();
            } else {
              this.ifRouteDoesNotContainPemitType();
            }
            // checking routeing params

          } else {
            this.loading = false;
          }


        }, error => {
          this.loading = false;
        }
      )
  }


  ifRouteContainsPermitType() {
    this.userForm.controls['permitTypeId'].setValue(this.otherParams.permitTypeId);
    this.getExistingApplicationByProjectIdAndPermitType(this.otherParams.permitTypeId);
    this.isAccessQuestion = true;
  }


  ifRouteDoesNotContainPemitType() {
    // call permit type id by code
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe(
        data => {
          this.getExistingApplicationByProjectIdAndPermitType(data.items[0].id);
        }
      )

  }



  getExistingApplicationByProjectIdAndPermitType(permitTypeId: any) {
    this.otherApplicationValue.isLoadingApplication = false;
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.userForm.value.projectId + '/permit-type/' +
      permitTypeId
    ).subscribe(
      dataItem => {
        // fadflajkdlf

        this.userForm.controls['categoryTypeId'].setValue(dataItem[0]?.categoryTypes.id);
        this.userForm.controls['buildTypeId'].setValue(dataItem[0]?.buildTypes.id);
        // this.userForm.controls['typeId'].se
        if (!this.otherParams.permitTypeId) {
          this.userForm.controls['permitTypeId'].setValue(dataItem[0]?.permitTypes.id);
        }


        if (dataItem && dataItem.length > 0) {
          this.assignValuesToForm(dataItem[0]);
          this.userForm.controls['applicationStatusId'].setValue(dataItem[0].applicationStatus.id);
          this.userForm.controls['applicationStatusCode'].setValue(dataItem[0].applicationStatus.code);

          this.otherApplicationValue.other = dataItem[0]?.other;
          this.otherApplicationValue.isOtherReady = true;
          this.otherApplicationValue.isLoadingApplication = true;
        } else {
          this.otherApplicationValue.other = dataItem[0]?.other;
          this.otherApplicationValue.isOtherReady = false;
          this.otherApplicationValue.isLoadingApplication = true;
          this.alsoYouCanCheckTheOtherApplicationHasCompletedWithNCP();

        }


        this.projectDetails.waterConsumption = dataItem[0]?.waterConsumption;
        this.projectDetails.DistanceToTheNearestLandIn = dataItem[0]?.DistanceToTheNearestLandIn;
        this.projectDetails.ProjectCostInUSD = dataItem[0]?.ProjectCostInUSD;
        this.projectDetails.ProjectCostInRwf = dataItem[0]?.ProjectCostInRwf;
        this.projectDetails.electricityConsumption = dataItem[0]?.electricityConsumption;
        this.projectDetails.grossFloorArea = dataItem[0]?.grossFloorArea;
        this.projectDetails.numberOfFloor = dataItem[0]?.numberOfFloor;
        this.projectDetails.numberOfParkingSpace = dataItem[0]?.numberOfParkingSpace;
        this.projectDetails.priceOfDwellingUnitRwf = dataItem[0]?.priceOfDwellingUnitRwf;
        this.projectDetails.buildUpArea = dataItem[0]?.buildUpArea;
        this.projectDetails.grossFloorArea = dataItem[0]?.grossFloorArea;
        this.projectDetails.capacityInformation = dataItem[0]?.capacityInformation;
        // This is used to check if permit certificate is expired
        this.checkIfCertificateIsExpired(dataItem[0]?.certificates?.expiredDate);
        // This is used to check if permit certificate is expired
        // jfa

        this.loadRequiredDocumentByPermitType('');
      }, error => {
        this.otherApplicationValue.isOtherReady = false;
        this.otherApplicationValue.isLoadingApplication = true;
      }
    )

  }





  alsoYouCanCheckTheOtherApplicationHasCompletedWithNCP() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe(
        data => {
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.paramsId + '/permit-type/' + data.items[0]?.id)
            .subscribe(
              dataItem => {
                if (dataItem && dataItem.length > 0) {
                  this.assignValuesToForm(dataItem[0]);
                } else {

                }
              }
            )
        },
        error => { }
      )
  }


  assignValuesToForm(reponseValue: any) {
    this.userForm.controls['applicationId'].setValue(reponseValue.id);
    this.userForm.controls['buildUpArea'].setValue(reponseValue.buildUpArea);
    this.userForm.controls['numberOfFloor'].setValue(reponseValue.numberOfFloor);
    this.userForm.controls['grossFloorArea'].setValue(reponseValue.grossFloorArea);
    this.userForm.controls['numberOfParkingSpace'].setValue(reponseValue.numberOfParkingSpace);
    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(reponseValue.priceOfDwellingUnitRwf);
    this.userForm.controls['DescriptionOfOperation'].setValue(reponseValue.DescriptionOfOperation);
    this.userForm.controls['percentageSpaceUse'].setValue(reponseValue.percentageSpaceUse);
    this.userForm.controls['numberOfDwellingUnits'].setValue(reponseValue.numberOfDwellingUnits);
    this.userForm.controls['waterConsumption'].setValue(reponseValue.waterConsumption);
    this.userForm.controls['electricityConsumption'].setValue(reponseValue.electricityConsumption);
    this.userForm.controls['DistanceToTheNearestLandIn'].setValue(reponseValue.DistanceToTheNearestLandIn);
    this.userForm.controls['ProjectCostInUSD'].setValue(reponseValue.ProjectCostInUSD);
    this.userForm.controls['ProjectCostInRwf'].setValue(reponseValue.ProjectCostInRwf);
    this.userForm.controls['capacityInformation'].setValue(reponseValue.capacityInformation);
  }


  loadRequiredDocumentByPermitType(event: any) {
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + event.id)
    if (this.userForm.value.permitTypeId && this.userForm.value.categoryTypeId) {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + this.userForm.value.permitTypeId + '/category/' + this.userForm.value.categoryTypeId)
        .subscribe(
          data => {
            if (data.length > 0) {
              this.outputData.document = data;
            } else {
              // this.userForm.controls['permitTypeId'].setValue('');
              this.utilService.showNotification(NOTIFICATION_COLOR.error, "Please contact the admin to provide the documents needed for the permit you've chosen.", "bottom", "center");
            }
          }
        )
    } else {
      this.utilService.showNotification(NOTIFICATION_COLOR.warning, "Please verify the application category and permitted type are provided.", "bottom", "center");
    }

  }


  checkIfCertificateIsExpired(expiredDate: any) {
    if (expiredDate && this.isExpired(expiredDate)) {
      const code = 'ROP';
      const index = this.permitTypes.findIndex((valueData: any) => valueData.code === code);
      if (index !== -1) {
        this.permitTypes.splice(index, 1);
      }
    }
  }

  isExpired(expiredDate: string): boolean {
    const currentDate = new Date();
    const expirationDate = new Date(expiredDate);
    return expirationDate < currentDate;
  }







  openVerticallyCentered(content: any) {
    this.modalService.open(content, {
      backdrop: 'static',
      centered: true,
      windowClass: 'modalClass',
      keyboard: false
    });
  }


  loadingTrigger() {
    this.outputData.text = 'Loading data .....';
    this.outputData.isLoading = this.outputData.isLoading;
  }


  getPermitTypeObject(event: any) {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    // if (this.userForm.value.capacityInformation && this.userForm.value.capacityInformation > 500) {
    //   this.findCategoryByCode('CAT5');
    //   this.userForm.controls['categoryCode'].setValue('CAT5');

    // }

    this.isAccessQuestion = false;
    if (this.otherParams.permitTypeId !== this.userForm.value.permitTypeId) {
      this.router.navigate(['/account/application/other-application/' + this.userForm.value.permitTypeId + '/' + this.otherParams.id]
        , { relativeTo: this.route }
      );
      this.loadData();
    } else {
      let codeValue = this.permitTypes.find((x: any) => x.id == this.userForm.value.permitTypeId);
      // when it is project modification
      if (codeValue.code === "PMP") {
        // when it is project modification
        this.router.navigate(['/account/application/project-modification/', this.projectDetails.id]);
      } else if (codeValue.code === "ROP") {
        this.applicationService.saveAssetWithPath({
          "upi": this.projectDetails.upi
        }, environment.applicationUrl + 'certificate/check-IfRenewalOfNCPCertificateValidity')
          .subscribe(
            data => {
              if (data.answer) {
                this.findApplication(codeValue);
              } else {
                this.utilService.showNotification(NOTIFICATION_COLOR.error,
                  `You cannot renew your construction permit. The renewal of new construction permits is
                 possible if the permit expires within 30 days or if it has already expired.`,
                  "bottom", "center"
                )
              }
            },
            error => { }
          )
      } else {
        this.userForm.controls['permitTypeId'].setValue(codeValue.id);
        this.loadRequiredDocumentByPermitType('');
        this.findApplication(codeValue);
      }
    }

  }


  findApplication(codeValue: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + codeValue.code)
      .subscribe(
        data => {
          this.isAccessQuestion = true;
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + data.items[0].id)
            .subscribe(
              data => {
                if (data.length > 0) {
                  // If data present means that application already existis with the permit type category exists
                  this.utilService.showNotification(NOTIFICATION_COLOR.warning, "An application already exists. Please consider applying for a different one, or ensure that all required documents are uploaded. You can also upload any missing documents.", "bottom", "center");
                  this.outputData.applicationSaved = data[0];
                  this.projectDetails.applyOtherApplication = false;
                  this.modalRef = this.modalService.open(this.otherApplicationModel, { size: 'md' });
                } else {
                  this.projectDetails.applyOtherApplication = true;
                  this.outputData.isDocumenting = false;
                }
              })
        })
  }


  backAndCloseDocument() {
    this.outputData.isDocumenting = false;
  }

  closeModal() {
    if (this.modalRef) {
      this.modalRef.close();
      this.userForm.controls['permitTypeId'].setValue('');
      // this.isChecked = !this.isChecked;
    }
  }

  yesContinue() {
    this.outputData.isDocumenting = true;
    this.modalService.dismissAll();
  }







  getBuildTypeObject(event: any) {
    let buildType = this.buildingTypes.find((x: any) => x.id == this.userForm.value.buildTypeId);
    this.outputData.buildType = buildType;
    this.userForm.controls['buildTypeCode'].setValue(buildType.code);
    let permitType = this.permitTypes.find((x: any) => x.id == this.userForm.value.permitTypeId);
    this.userForm.controls['permitTypeCode'].setValue(permitType.code);
    // if (buildType.code === 'FAI' ||
    //   buildType.code === 'EDU' ||
    //   buildType.code === 'ASMBLY'
    //   || buildType.code === 'ASMB'
    //   || buildType.code === 'MEM'
    //   || buildType.code === 'STRG'
    //   || buildType.code === 'MISLNS'
    //   || buildType.code === 'INSTNAL'
    //   || buildType.code === 'INST') {
    //   // this.findCategoryByCode('CAT5');
    //   this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    //   this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    // } else {
    this.calculateToGetCategory()
    // }
  }


  calculateToGetCategory() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }






  findCategoryByCode(code: any) {
    if (code) {
      this.userForm.controls['categoryTypeId'].setValue(this.categoryTypes.find((x: any) => x.code === code)?.id);
    }

  }



  chooseCategory(event: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => {
          this.categoryTypes = data;
        }
      )
  }

  cancel(): void {
    this.router.navigate(['/account/application/applications']);
  }





  updateOtherInfo(event: any) {
    this.otherInfoData = event;
  }

  onSubmit() {
    let permitTypeCode = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId).code;
    this.userForm.controls['permitTypeCode'].setValue(permitTypeCode);
    this.userForm.controls['agencyCode'].setValue(this.agencies.find((x: any) => x.id = this.userForm.value.agencyId).code);
    if (this.userForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Fill all required', "bottom", "center")
      return;
      // update application
    } else if (this.userForm.value.applicationId && (this.userForm.value.applicationStatusCode === 'UNCRN'
      || this.userForm.value.applicationStatusCode === 'PND'
    )) {
      this.updateApplication();
    } else {
      this.submitted = true;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + 'PND')
        .subscribe(
          data => {
            this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
            this.confirmSubmit();
          }, error => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
          });
    }
  }


  updateApplication() {
    let dataToSubmit = {
      "waterConsumption": +this.userForm.value.waterConsumption,
      "electricityConsumption": +this.userForm.value.electricityConsumption,
      "DistanceToTheNearestLandIn": +this.userForm.value.DistanceToTheNearestLandIn,
      "ProjectCostInUSD": +this.userForm.value.ProjectCostInUSD,
      "ProjectCostInRwf": +this.userForm.value.ProjectCostInRwf,
      "buildUpArea": +this.userForm.value.buildUpArea,
      "numberOfFloor": +this.userForm.value.numberOfFloor,
      "grossFloorArea": +this.userForm.value.grossFloorArea,
      "numberOfParkingSpace": +this.userForm.value.numberOfParkingSpace,
      "priceOfDwellingUnitRwf": +this.userForm.value.priceOfDwellingUnitRwf,
      "capacityInformation": +this.userForm.value.capacityInformation,
      "numberOfDwellingUnits": this.userForm.value.numberOfDwellingUnits ? this.userForm.value.numberOfDwellingUnits : 0,
      "DescriptionOfOperation": this.userForm.value.DescriptionOfOperation,
      "percentageSpaceUse": this.userForm.value.percentageSpaceUse,
      "userId": this.currentUser.userId,
      "certificateNumberEIA": this.userForm.value.certificateNumberEIA,
      "projectId": this.userForm.value.projectId,
      "permitTypeId": this.userForm.value.permitTypeId,
      "categoryTypeId": this.userForm.value.categoryTypeId,
      "buildTypeId": this.userForm.value.buildTypeId,
      "technologySurveyId": this.userForm.value.technologySurveyId,
      "agencyId": this.userForm.value.agencyId,
      "applicationStatusId": this.userForm.value.applicationStatusId,
      "permitTypeCode": this.userForm.value.permitTypeCode,
      "agencyCode": this.userForm.value.agencyCode,
      "upi": this.userForm.value.upi,
      "other": this.otherInfoData
    }

    this.applicationService.patchAssetWithoutParams(dataToSubmit, environment.applicationUrl + 'application/application/' + this.userForm.value.applicationId)
      .subscribe(data => {
        // this.onNext();
        this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application updated successfully", "bottom", "center");
        this.refreshAndGetUpdatedApplication();
      })
  }


  confirmSubmit() {
    let dataToSave = this.userForm.value;
    this.otherInfoData.permitTypeId = this.userForm.value.permitTypeId;
    this.otherInfoData.userId = this.currentUser.userId;
    dataToSave.other = this.otherInfoData;
    dataToSave.submittedByUserId = this.currentUser.userId;
    // dataToSave.userId = this.userForm.value.projectId;
    dataToSave.userId = this.projectDetails.userId;
    this.applicationService.saveWithPath(dataToSave, environment.applicationUrl + 'application/application')
      // this.applicationService.saveWithPath(this.userForm.value, environment.applicationUrl + 'application/application')
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application saved successfully", "bottom", "center");
          this.refreshAndGetUpdatedApplication();
          this.submitted = false;
        }, error => {
          this.submitted = false;
        }
      )
  }


  refreshAndGetUpdatedApplication() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + this.userForm.value.permitTypeId)
      .subscribe(
        data => {
          if (data.length > 0) {
            // If data present means that application already existis with the permit type category exists
            this.outputData.applicationSaved = data[0];
            this.projectDetails.applyOtherApplication = false;
            this.outputData.isDocumenting = true;

          } else {
            this.projectDetails.applyOtherApplication = true;
          }
        })
  }




  callLookups() {
    this.applicationService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => {
          this.agencies = data;
        }
      )


    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.BUILDING_TYPE)
      .subscribe(
        data => { this.buildingTypes = data; },
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => {
          this.categoryTypes = data;
        }
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => {
          this.permitTypes = data;
        },
      )


    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.TECHNOLOGYSURVEY)
      .subscribe(
        data => {
          this.technologySurveys = data;
          this.userForm.controls['technologySurveyId'].setValue(this.technologySurveys[0].id);
        }
      )
  }
}
