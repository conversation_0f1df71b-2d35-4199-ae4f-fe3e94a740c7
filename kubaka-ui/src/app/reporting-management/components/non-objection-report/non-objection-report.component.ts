import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {
  CellValueChangedEvent,
  ColDef,
  GridReadyEvent,
  SelectionChangedEvent,
  ValueFormatterParams,
  GridApi, // Import GridApi
  CsvExportParams
} from 'ag-grid-community';
import { AgGridAngular } from 'ag-grid-angular';
import { environment } from 'src/environments/environment';
import { GlobalRefreshService } from 'src/app/shared/services/global-refresh.service';


@Component({
  selector: 'app-non-objection-report',
  templateUrl: './non-objection-report.component.html',
  styleUrls: ['./non-objection-report.component.scss']
})
export class NonObjectionReportComponent implements OnInit, OnDestroy {

  // Variables to store the grid API and column API
  private gridApi!: GridApi;
  private refreshSub: any;

  rowData: any[] = [];
  colDefs: ColDef[] = [
    { field: 'agencyCode', headerName:'Agency', width: 150, checkboxSelection: true },
    { field: 'projects.projectName', headerName:'Project Name', width: 150, checkboxSelection: true },
    { field: 'projects.upi', headerName:'UPI', width: 130 },
    { field: 'projects.plotSize', headerName:'Plot Size', width: 130 },
    { field: 'projects.ownerFullName', headerName:'ownerFullName ', width: 130 },
    { field: 'projects.ownerIdNo', headerName:'ownerIdNo ', width: 130 },
    { field: 'projects.plotSize', headerName:'plotSize ', width: 130 },
    { field: 'projects.isUnderMortgage', headerName:'isUnderMortgage ', width: 130 },
    { field: 'projects.isUnderRestriction', headerName:'isUnderRestriction ', width: 130 },
    { field: 'categoryTypes.name', headerName:'Category', width: 225 },
    { field: 'permitTypes.name', headerName:'Permit Type', },
    { field: 'buildUpArea', headerName:'Build Up Area', width: 130 },
    { field: 'numberOfFloor', headerName:'Number Of Floor', width: 120 },
    { field: 'waterConsumption', headerName:'waterConsumption', },
    { field: 'electricityConsumption', headerName:'electricityConsumption', },
    { field: 'DistanceToTheNearestLandIn', headerName:'DistanceToTheNearestLandIn', },
    { field: 'ProjectCostInUSD', headerName:'ProjectCostInUSD', },
    { field: 'ProjectCostInRwf', headerName:'ProjectCostInRwf', },
    { field: 'grossFloorArea', headerName:'grossFloorArea', },
    { field: 'capacityInformation', headerName:'capacityInformation', },
    { field: 'created_at', headerName:'created_at', },
    { field: 'submittedDate', headerName:'submittedDate', },
    { field: 'isNonObjectionRequested', headerName:'Requested Date', },
    { field: 'isNonObjectionReturnedDate', headerName:'Returned Date', },
    { field: 'applicationName', headerName:'applicationStatus', },
    { field: 'applicationStatus.name', headerName:'applicationName', },
    { field: 'isNonObjection', headerName:'isNonObjection', },
    { field: 'isAssociatedUpi', headerName:'isAssociatedUpi', },
  ];

  defaultColDef: ColDef = {
    filter: true,
    editable: true,
  };

  constructor(private http: HttpClient, private globalRefresh: GlobalRefreshService) { }

  ngOnInit(): void {
    this.refreshSub = this.globalRefresh.refreshInterval$.subscribe(() => {
      if (this.gridApi) {
        this.http
          .get<any[]>(environment.applicationUrl + 'application/application/nonObjections')
          .subscribe((data) => (this.rowData = data));
      }
    });
  }

  ngOnDestroy(): void {
    if (this.refreshSub) {
      this.refreshSub.unsubscribe();
    }
  }

  onGridReady(params: GridReadyEvent) {
    // Save the grid API reference
    this.gridApi = params.api;

    this.http
      .get<any[]>(environment.applicationUrl + 'application/application/nonObjections')
      .subscribe((data) => (this.rowData = data));
  }

  onSelectionChanged(event: SelectionChangedEvent) {

  }

  onCellValueChanged(event: CellValueChangedEvent) {
    console.log(`New Cell Value: ${event.value}`);
  }

  dateFormatter(params: ValueFormatterParams) {
    return new Date(params.value).toLocaleDateString('en-us', {
      weekday: 'long',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // // Export to Excel using the grid API
  // onBtnExport() {
  //   const params = {
  //     fileName: 'user-report.xlsx',
  //     onlySelected: true, // Export only selected rows
  //     columnKeys: ['firstName', 'lastName', 'email'], // Export specific columns
  //   };
  //   // Use the grid API to export data as Excel
  //   this.gridApi.exportDataAsExcel(params);
  // }
    // Export to Excel using the grid API
    onBtnExport() {
       const params: CsvExportParams = {
            fileName: 'application.csv',
            columnSeparator: ',',
          };
          this.gridApi.exportDataAsCsv(params);
    }
}
