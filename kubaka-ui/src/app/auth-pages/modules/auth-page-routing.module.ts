import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ForgotPasswordComponent } from '../components/forgot-password/forgot-password.component';
import { LandingPageComponent } from '../components/landing-page/landing-page.component';
import { LoginComponent } from '../components/login/login.component';
import { RegisterComponent } from '../components/register/register.component';
import { ResetPasswordComponent } from '../components/reset-password/reset-password.component';
import { VerifyEmailComponent } from '../components/verify-email/verify-email.component';
import { MessageAfterLoginComponent } from '../components/message-after-login/message-after-login.component';
import { FactorLoginComponent } from '../components/factor-login/factor-login.component';


const routes: Routes = [
    {
        path: "", component: LandingPageComponent,
    },
    {
        path: "register", component: RegisterComponent,
    },
    {
        path: "login", component: LoginComponent,
    },

    {
        path: 'forgot-password', component: ForgotPasswordComponent
    },

    // {
    //     path: 'reset-password/:code', component: ResetPasswordComponent
    // },

    {
        path: 'reset-password', component: ResetPasswordComponent
    },
    // {
    //     path: 'email/verify/:code', component: VerifyEmailComponent
    // }

    {
        path: 'email/verify', component: VerifyEmailComponent
    },
    {
        path: 'login-message', component: MessageAfterLoginComponent
    },

    {
        path: '2-factor-auth', component: FactorLoginComponent
    }







];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],

    exports: [RouterModule]
})
export class AuthPageRoutingModule { }
