import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-country-level-dashboard',
  templateUrl: './country-level-dashboard.component.html',
  styleUrls: ['./country-level-dashboard.component.scss']
})
export class CountryLevelDashboardComponent {
  countingBoard!: any;
  currentUser: any;
  agencies: any[] = [];
  filterByAgency: any;
  constructor(
    private sessionService: SessionService,
    private applicationService: ApplicationService,
    private router: Router
  ) {
    this.currentUser = this.sessionService.getSession();

    this.applicationService.findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        data => {
          this.agencies = data;
        }
      )

  }


  goToApplication(event: any) {
    // if (this.currentUser.data.user.role.code === 'APP' ||
    //   this.currentUser.data.user.userType.code === 'ENG' ||
    //   this.currentUser.data.user.userType.code === 'ARC'
    // ) {
    //   // this.router.navigate(['/account/application/applications']);
    //   this.router.navigate(['/account/application/applications/' + event]);
    // } else {
    this.router.navigate(['/account/all-applications/lists-agency/' + event, this.filterByAgency]);
    // }
  }


  changeByAgency() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/dashboardAgency/' + this.filterByAgency)
      .subscribe(
        data => {
          this.countingBoard = data;
        }
      )
  }
}
