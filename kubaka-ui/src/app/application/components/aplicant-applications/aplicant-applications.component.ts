import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../services/application.service';


@Component({
  selector: 'app-aplicant-applications',
  templateUrl: './aplicant-applications.component.html',
  styleUrls: ['./aplicant-applications.component.scss']
})
export class AplicantApplicationsComponent {
  @ViewChild("yourSelfContent") modalContent!: TemplateRef<any>;
  @ViewChild("isNewPermit") modalNewPermitContent!: TemplateRef<any>
  currentUser: any = {};
  applicationDetail: any = {};
  lists: any[] = [];
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];
  paramsId: any;
  comments: any[] = [];
  applicationStatusId: any = '';
  applicationStatuses: any[] = [];
  deleteData: any = {};
  loading: any = false;



  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    private applicationService: ApplicationService,
    private router: Router,
    private appConfig: AppConfig,
    private route: ActivatedRoute,
    public utilService: UtilService,
    public userService: UserMgtService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    route.params.subscribe((params: any) => {
      this.paramsId = params.status;
    })


    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus')
      .subscribe(
        data => {
          this.applicationStatuses = data;
        }
      )

    this.loadList();
  }

  onSearchByDateChange(date: string): void {
    this.userService.filterByDate = date;
    this.filterAndPaginate();
  }




  filterByStatus() {
    this.fullList = this.content.filter((x: any) => x.applicationStatus.id === this.applicationStatusId);
    this.totalRecords = this.fullList.length;
    this.filterAndPaginate();
  }



  loadList() {
    if (this.currentUser.data.user.userType.code === 'ENG' || this.currentUser.data.user.userType.code === 'ARC') {
      this.userService
        .findAllWithPath(environment.applicationUrl + 'application/project/and/application/engORarch/' + this.currentUser.userId)
        .subscribe(
          (data) => {
            // this.renderData(data);
            // this.fullList = data;
            this.content = data;
            if (this.paramsId) {
              this.fullList = data.filter((item: any) => item.applicationStatus.code === this.paramsId);
            } else {
              this.fullList = data;
            }
            this.fullList.forEach((element: any) => {
              element.projectName = element.projects.projectName,
                element.categoryTypeName = element.categoryTypes.name;
              element.buildTypeName = element.buildTypes.name;
              element.permitTypeName = element.permitTypes.name;
              element.upi = element.projects.upi;

            });
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
          });

    }
    if (this.currentUser.data.user.userType.code === 'LO') {
      this.userService
        .findAllWithPath(environment.applicationUrl + 'application/application/landOwner/' + this.currentUser.userId)
        .subscribe(
          (data) => {
            // this.renderData(data);
            // this.fullList = data;
            this.content = data;
            if (this.paramsId) {
              this.fullList = data.filter((item: any) => item.applicationStatus.code === this.paramsId);
            } else {
              this.fullList = data;
            }
            this.fullList.forEach((element: any) => {
              element.projectName = element.projects.projectName,
                element.categoryTypeName = element.categoryTypes.name;
              element.buildTypeName = element.buildTypes.name;
              element.permitTypeName = element.permitTypes.name;
              element.upi = element.projects.upi;

            });
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
          });

    }
     else {
      this.userService
        .findAllWithPath(environment.applicationUrl + 'application/application/user/' + this.currentUser.userId)
        .subscribe(
          (data) => {
            // this.renderData(data);
            this.content = data;
            if (this.paramsId) {
              this.fullList = data.filter((item: any) => item.applicationStatus.code === this.paramsId);
            } else {
              this.fullList = data;
            }

            this.fullList.forEach((element: any) => {
              element.projectName = element.projects.projectName,
                element.categoryTypeName = element.categoryTypes.name;
              element.buildTypeName = element.buildTypes.name;
              element.permitTypeName = element.permitTypes.name;
              element.upi = element.projects.upi;

            });
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
          });

    }

    // this.userService
    //   .findAllWithPath(environment.applicationUrl + 'application/application/user/' + this.currentUser.userId)
    //   .subscribe(
    //     (data) => {
    //       this.lists = data;
    //       this.content = data;
    //       setTimeout(() => {
    //         document.getElementById("elmLoader")?.classList.add("d-none");

    //       }, 1200);
    //       this.collectionSize = this.lists.length;
    //       this.total = this.lists.length;
    //       this.totalRecords = this.lists.length;
    //       this.startIndex = (this.page - 1) * this.pageSize + 1;
    //       this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    //       if (this.endIndex > this.totalRecords) {
    //         this.endIndex = this.totalRecords;
    //       }
    //       this.lists = this.lists.slice(
    //         this.startIndex - 1,
    //         this.endIndex
    //       );
    //     });
  }


  // renderData(data: any) {
  //   this.fullList = data;
  //   this.totalRecords = this.fullList.length;
  //   this.filterAndPaginate();
  // }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes(term)
    );
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    this.filterAndPaginate();
  }



  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }



  otherApplication(data: any) {
    this.router.navigate(['/account/application/other-application', data.projects.id]);
  }



  getChoosenSelectedUse(event: any) {
    if (!event.isFromOldSystem) {
      this.checKUpiHasAlreayApplicationInBPMIS(event)
      // let existingUpiInApplication = this.lists.find((l: any) => l.projects.upi === event.upi);
      // this.applicationDetail = event;
      // if (existingUpiInApplication) {
      //   this.applicationDetail.isUpiExists = true;
      //   // Make it temporary here
      //   this.goToApplication();
      // } else {
      //   this.applicationDetail.isUpiExists = false;
      //   this.goToApplication();
      // }
    } else {
      this.applicationDetail.isUpiExists = false;
      this.applicationDetail = event;
      this.goToApplication();
    }
  }



  checKUpiHasAlreayApplicationInBPMIS(event: any) {
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/application/upi/search?search=' + event.upi)
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + event.upi)
      .subscribe(
        data => {
          //
          if (data && data.items.length > 0) {
            // this.router.navigate(['account/application/resume-application', data.items[0].id]);
            // this.router.navigate(['account/application/resume-application', data.items[0].id]);
            this.router.navigate(['account/application/new-application-project/', data.items[0].id]);
          } else {

            // new application
            localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(event));
            // localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(event));
            this.router.navigate(['account/application/new-application', 0]);
          }
        }, error => {
          localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(event));
          // localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(event));
          this.utilService.showNotification(
            NOTIFICATION_COLOR.error, error + '', "bottom", "center"
          )
        }
      )
  }


  // isNewPermitM(event: any) {
  //   if (event === 'yes') {
  //     this.applicationDetail.isNewPermit = false;
  //     this.cancel();
  //     this.goToApplication();
  //   } else {
  //     this.applicationDetail.isNewPermit = true;
  //   }
  // }

  goToApplication() {
    localStorage.removeItem(this.appConfig.UPI_old_INFO);
    localStorage.setItem(this.appConfig.UPI_old_INFO, JSON.stringify(this.applicationDetail));
    this.router.navigate(['/account/application/new-application/0/0']);
  }

  ngOnDestroy() {
    this.userService.searchTerm = '';
    this.applicationService.searchTerm = '';
  }



  searchEngineerByLicenseNumber() {
    // search existing certificate number through this.applicationDetail.certificateNumber properties
    // then
    this.checkIsNewPermit();


    // this.cancel();
    // this.router.navigate(['/account/application/new-application/1/0']);
  }


  checkIsNewPermit() {
    this.cancel();
    this.modalService.open(this.modalNewPermitContent, { size: 'md', centered: true });
  }




  cancel() {
    this.modalService.dismissAll();
  }






  submitCertificateNumber() {
    this.goToApplication();
    // applicationDetail.isNewPermit
    // applicationDetail.certificateNumber
  }



  // chooseOwner(event: any) {
  //   // // this.applicationDetail = event;
  //   // fasdf
  //   // this.modalService.open(this.modalContent, { size: 'md', centered: true });
  // }



  viewComment(event: any, content: any) {
    this.modalService.open(content, { size: "lg", centered: true });
    this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/applicationApproval/' + event.id + '/status/' + event.applicationStatus.id)
      .subscribe(
        data => {
          this.comments = data;
        },
        error => {

        })
  }




  openToDelete(event: any, content: any) {
    this.deleteData = event;
    this.modalService.open(content, { size: "lg", centered: true });
  }


  deleteFile() {
    this.applicationService.deleteWithPath(this.deleteData.id, environment.applicationUrl + 'application/application/draft')
      .subscribe(
        data => {
          this.loadList();
          this.modalService.dismissAll();
        }
      )
  }
}
