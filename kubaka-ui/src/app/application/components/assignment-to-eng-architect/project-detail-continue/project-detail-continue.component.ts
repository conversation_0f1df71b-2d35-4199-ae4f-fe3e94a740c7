import { Component } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { omit } from 'lodash';


@Component({
  selector: 'app-project-detail-continue',
  templateUrl: './project-detail-continue.component.html',
  styleUrls: ['./project-detail-continue.component.scss']
})
export class ProjectDetailContinueComponent {
  categoryTypes: any[] = [];
  buildingTypes: any[] = [];
  permitTypes: any[] = [];
  currentUser: any;
  projectDetails: any = {};
  isSelectDisabled = true;
  userForm!: UntypedFormGroup;
  outputData: any = {};
  submitted!: boolean;
  paramsId: any = {};
  isReadOnly: boolean = true;
  isAssociated: boolean = false;


  constructor(
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private sessionService: SessionService,
    private route: ActivatedRoute,
    private router: Router,
    private modalService: NgbModal,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    route.params.subscribe((params: any) => {
      this.paramsId = params.id;
    })
    this.outputData.isFromAssigned = true;
    this.callLookups();
  }


  get form() {
    return this.userForm.controls;
  }

  ngOnInit(): void {
    this.initialize('');
  }


  calculateNumberOfFlow() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
    if (this.userForm.value.buildUpArea && this.userForm.value.numberOfFloor) {
      this.userForm.controls['grossFloorArea'].setValue((this.userForm.value.buildUpArea) * ((+this.userForm.value.numberOfFloor) + 1));
    }
  }


  checkTemporalPermit() {
    let permitType = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId);
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }
  getBuildTypeObject(event: any) {
    let buildType = this.buildingTypes.find((x: any) => x.id == this.userForm.value.buildTypeId);
    this.userForm.controls['buildTypeCode'].setValue(buildType.code);
    this.calculateToGetCategory()
  }


  getPermitTypeObject(event: any) {
    let codeValue = this.permitTypes.find((x: any) => x.id == this.userForm.value.permitTypeId);
    this.findCategoryByCode(codeValue.code);
    this.userForm.controls['permitTypeCode'].setValue(codeValue.code);
    if (codeValue.code === 'TSAP') {
      this.userForm.controls['categoryCode'].setValue('CAT1');
      this.userForm.controls['categoryTypeId'].setValue(this.categoryTypes.find((x: any) => x.code === 'CAT1').id);
      this.calculateToGetCategory()
    }
    if ((codeValue.code !== 'NCP') && (codeValue.code !== 'TSAP')) {
      this.router.navigate(['/account/application/other-application/' + codeValue.id + '/' + this.paramsId]);
    } else {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + codeValue.code)
        .subscribe(
          data => {
            this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + data.items[0].id)
              .subscribe(
                data => {
                  if (data && data.length > 0) {
                    this.userForm.controls['buildTypeId'].setValue(data[0].buildTypes.id);
                    this.getBuildTypeObject(this.userForm.value.buildTypeId);
                    this.outputData.applicationSaved = data[0];
                    this.projectDetails.applyOtherApplication = true;
                    this.outputData.isDocumenting = false;
                  } else {
                    this.projectDetails.applyOtherApplication = true;
                    this.outputData.isDocumenting = false;
                    this.userForm.controls['id'].setValue(null);
                    this.checkTemporalPermit();
                  }
                  // this.checkTemporalPermit();
                })
          })
    }
  }

  calculateToGetCategory() {

    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  calculateNumberOfPeople() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  findCategoryByCode(code: any) {
    if (code) {
      this.userForm.controls['categoryTypeId'].setValue(this.categoryTypes.find((x: any) => x.code === code)?.id);
      this.loadRequiredDocumentByPermitType('');
    }
  }

  loadRequiredDocumentByPermitType(event: any) {
    if (this.userForm.value.permitTypeId && this.userForm.value.categoryTypeId) {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/requiredDocument/permitType/' + this.userForm.value.permitTypeId + '/category/' + this.userForm.value.categoryTypeId)
        .subscribe(
          data => {
            this.outputData.document = data;
          }
        )
    }

  }





  callLookups() {
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.BUILDING_TYPE)
      .subscribe(
        data => { this.buildingTypes = data; },
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => {
          this.categoryTypes = data;
        }
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => {
          this.permitTypes = data;
          let permitType = this.permitTypes.find((permit: any) => permit.code === 'NCP');

          if (permitType) {
            this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.paramsId +
              '/permit-type/' + permitType.id
            )
              .subscribe(
                data => {
                  if (data && data.length > 0) {
                    this.outputData.applicationSaved = data[0];
                    this.projectDetails = data[0].projects;
                    this.outputData.project = this.projectDetails;
                    this.userForm.controls['projectId'].setValue(this.projectDetails.id);
                    this.userForm.controls['agencyId'].setValue(this.projectDetails.agencyId);
                    this.userForm.controls['agencyCode'].setValue(data[0].agencyCode);
                    this.userForm.controls['certificateNumberEIA'].setValue(data[0].certificateNumberEIA);
                    this.userForm.controls['upi'].setValue(this.projectDetails.upi);
                    this.userForm.controls['isFromOldSystem'].setValue(this.projectDetails.isFromOldSystem);
                    this.userForm.controls['isUnderMortgage'].setValue(this.projectDetails.isUnderMortgage);
                    this.userForm.controls['isUnderRestriction'].setValue(this.projectDetails.isUnderRestriction);
                    this.userForm.controls['centralCoordinateX'].setValue(this.projectDetails.centralCoordinateX);
                    this.userForm.controls['centralCoordinateY'].setValue(this.projectDetails.centralCoordinateY);
                    this.userForm.controls['villageName'].setValue(this.projectDetails.villageName);
                    this.userForm.controls['cellCode'].setValue(this.projectDetails.cellCode);
                    this.userForm.controls['cellName'].setValue(this.projectDetails.cellName);
                    this.userForm.controls['sectorCode'].setValue(this.projectDetails.sectorCode);
                    this.userForm.controls['sectorName'].setValue(this.projectDetails.sectorName);
                    this.userForm.controls['districtCode'].setValue(this.projectDetails.districtCode);
                    this.userForm.controls['districtName'].setValue(this.projectDetails.districtName);
                    this.userForm.controls['provinceCode'].setValue(this.projectDetails.provinceCode);
                    this.userForm.controls['provinceName'].setValue(this.projectDetails.provinceName);
                    this.userForm.controls['selectedUse'].setValue(this.projectDetails.selectedUse);
                    this.userForm.controls['selectedCategoryUse'].setValue(this.projectDetails.selectedCategoryUse);
                    this.userForm.controls['projectName'].setValue(this.projectDetails.projectName);
                    this.userForm.controls['projectDescription'].setValue(this.projectDetails.projectDescription);
                    this.userForm.controls['plotSize'].setValue(this.projectDetails.plotSize);
                    this.userForm.controls['userId'].setValue(this.projectDetails.userId);
                    this.userForm.controls['villageCode'].setValue(this.projectDetails.villageCode);
                    this.userForm.controls['isEIAVerified'].setValue(this.projectDetails.isEIAVerified);
                    this.userForm.controls['permitTypeId'].setValue(data[0].permitTypes.id);
                    this.userForm.controls['permitTypeCode'].setValue(data[0].permitTypes.code);
                    this.userForm.controls['id'].setValue(data[0]?.id);
                    this.userForm.controls['buildTypeId'].setValue(data[0]?.buildTypes?.id);
                    this.userForm.controls['categoryTypeId'].setValue(data[0]?.categoryTypes?.id);
                    this.userForm.controls['categoryCode'].setValue(data[0]?.categoryTypes?.code);
                    this.userForm.controls['numberOfFloor'].setValue(data[0]?.numberOfFloor);
                    this.userForm.controls['numberOfParkingSpace'].setValue(data[0].numberOfParkingSpace);
                    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(data[0]?.priceOfDwellingUnitRwf);
                    this.userForm.controls['numberOfDwellingUnits'].setValue(data[0]?.numberOfDwellingUnits);
                    this.userForm.controls['DescriptionOfOperation'].setValue(data[0]?.DescriptionOfOperation);
                    this.userForm.controls['percentageSpaceUse'].setValue(data[0]?.percentageSpaceUse);
                    this.userForm.controls['waterConsumption'].setValue(data[0]?.waterConsumption);
                    this.userForm.controls['waterConsumption'].setValue(data[0]?.waterConsumption);

                    this.userForm.controls['electricityConsumption'].setValue(data[0]?.electricityConsumption);

                    this.userForm.controls['DistanceToTheNearestLandIn'].setValue(data[0]?.DistanceToTheNearestLandIn);

                    this.userForm.controls['ProjectCostInUSD'].setValue(data[0]?.ProjectCostInUSD);

                    this.userForm.controls['ProjectCostInRwf'].setValue(data[0]?.ProjectCostInRwf);

                    this.userForm.controls['capacityInformation'].setValue(data[0]?.capacityInformation);
                    this.userForm.controls['buildUpArea'].setValue(data[0]?.buildUpArea);
                    this.userForm.controls['grossFloorArea'].setValue(data[0]?.grossFloorArea);
                    this.userForm.controls['combiningPlotSize'].setValue(

                      data[0]?.combiningPlotSize ? (
                        +(data[0]?.combiningPlotSize) +
                        +(this.userForm.value.plotSize)) :
                        this.userForm.value.plotSize);
                    this.userForm.controls['isAssociated'].setValue(data[0]?.isAssociated);


                    this.getAgencyCodeForProject(false);
                    // application details
                    this.loadRequiredDocumentByPermitType(data[0].permitTypes);
                    // Calling agency by district code from what you have selected to apply
                  } else {
                    this.checkIfProjectExist();
                  }
                }
              )
          }


        },
      )
  }



  checkIfProjectUPIHasAssociatedUpi(projectDetails: any) {
    if (projectDetails.isAssociatedUpi && this.paramsId !== '0') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/associatedUPI/ByProject/${projectDetails.id}`)
        .subscribe(dataItem => {
          // Initialize combiningPlotSize to zero
          let totalPlotSize = 0;

          // Accumulate plot sizes
          dataItem.forEach((element: any) => {
            totalPlotSize += +element.plotSize;
          });
          // Update the combiningPlotSize and percentageSpaceUse once
          this.userForm.controls['combiningPlotSize'].setValue(totalPlotSize + this.userForm.value.plotSize);

          const buildUpArea = this.userForm.value.buildUpArea || 1; // Avoid division by zero
          const percentageSpaceUse = ((buildUpArea / (totalPlotSize + this.userForm.value.plotSize)) * 100).toFixed(2);
          this.userForm.controls['percentageSpaceUse'].setValue(percentageSpaceUse);
        });
    }
  }



  getAgencyCodeForProject(isProject: boolean) {
    if (!isProject) {
      this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + this.projectDetails.districtCode)
        .subscribe(
          agencyData => {
            this.userForm.controls['agencyCode'].setValue(agencyData.code);
          },
          error => { }
        )
    } else {
      this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/' + this.projectDetails.agencyId)
        .subscribe(
          agencyData => {
            this.userForm.controls['agencyCode'].setValue(agencyData.code);
          },
          error => { }
        )
    }

  }



  checkIfProjectExist() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/' + this.paramsId)
      .subscribe(
        data => {
          this.projectDetails = data;

          this.userForm.controls['projectId'].setValue(this.projectDetails.id);

          this.userForm.controls['plotSize'].setValue(this.projectDetails.plotSize);
          this.userForm.controls['upi'].setValue(this.projectDetails.upi);
          this.userForm.controls['isFromOldSystem'].setValue(this.projectDetails.isFromOldSystem);
          this.userForm.controls['isUnderMortgage'].setValue(this.projectDetails.isUnderMortgage);
          this.userForm.controls['isUnderRestriction'].setValue(this.projectDetails.isUnderRestriction);
          this.userForm.controls['centralCoordinateX'].setValue(this.projectDetails.centralCoordinateX);
          this.userForm.controls['centralCoordinateY'].setValue(this.projectDetails.centralCoordinateY);
          this.userForm.controls['villageName'].setValue(this.projectDetails.villageName);
          this.userForm.controls['cellCode'].setValue(this.projectDetails.cellCode);
          this.userForm.controls['cellName'].setValue(this.projectDetails.cellName);
          this.userForm.controls['sectorCode'].setValue(this.projectDetails.sectorCode);
          this.userForm.controls['sectorName'].setValue(this.projectDetails.sectorName);
          this.userForm.controls['districtCode'].setValue(this.projectDetails.districtCode);
          this.userForm.controls['districtName'].setValue(this.projectDetails.districtName);
          this.userForm.controls['provinceCode'].setValue(this.projectDetails.provinceCode);
          this.userForm.controls['provinceName'].setValue(this.projectDetails.provinceName);
          this.userForm.controls['selectedUse'].setValue(this.projectDetails.selectedUse);
          this.userForm.controls['selectedCategoryUse'].setValue(this.projectDetails.selectedCategoryUse);
          this.userForm.controls['projectName'].setValue(this.projectDetails.projectName);
          this.userForm.controls['projectDescription'].setValue(this.projectDetails.projectDescription);
          this.userForm.controls['agencyId'].setValue(this.projectDetails.agencyId);
          this.getAgencyCodeForProject(true);
          this.checkIfProjectUPIHasAssociatedUpi(this.projectDetails);
        },
        error => { }
      )
  }

  closeDocument() {
    this.projectDetails.applyOtherApplication = true;
    this.outputData.isDocumenting = false;
  }

  onSubmit() {
    if (this.userForm.value.id) {
      this.projectDetails.applyOtherApplication = false;
      this.outputData.isDocumenting = true;
    } else {
      if (this.userForm.invalid) {
        this.submitted = false;
        this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Fill all required', "bottom", "center")
        return;
      } else {
        this.submitted = true;
        this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + 'PND')
          .subscribe(
            data => {
              this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
              this.confirmSubmit();
            }, error => {
              this.submitted = false;
              this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
            });
      }
    }
  }


  confirmSubmit() {
    let permitTypeCode = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId).code;
    this.userForm.controls['permitTypeCode'].setValue(permitTypeCode);
    this.userForm.controls['technologySurveyId'].setValue("13b8abbd-042c-455c-a91f-0540643140d9");
    let { id, ...dataWithoutId } = this.userForm.value;

    let dataToSave = dataWithoutId;
    dataToSave.ProjectCostInUSD = this.userForm.value.ProjectCostInUSD;
    dataToSave.ProjectCostInRwf = this.userForm.value.ProjectCostInRwf;
    dataToSave.priceOfDwellingUnitRwf = this.userForm.value.priceOfDwellingUnitRwf;
    dataToSave.buildUpArea = (+dataToSave.buildUpArea);
    dataToSave.numberOfFloor = (+dataToSave.numberOfFloor);
    dataToSave.waterConsumption = (+dataToSave.waterConsumption);
    dataToSave.electricityConsumption = (+dataToSave.electricityConsumption);
    dataToSave.isAssociated = dataToSave.combiningPlotSize ? true : false;
    dataToSave.combiningPlotSize = dataToSave.combiningPlotSize ? (+dataToSave.combiningPlotSize) : 0;
    dataToSave.numberOfParkingSpace = (+dataToSave.numberOfParkingSpace);
    dataToSave.capacityInformation = (+dataToSave.capacityInformation);
    dataToSave.technologySurveyId = this.userForm.value.technologySurveyId;
    dataToSave.permitTypeCode = this.userForm.value.permitTypeCode;
    dataToSave.agencyId = this.userForm.value.agencyId;
    dataToSave.DistanceToTheNearestLandIn = (+this.userForm.value.DistanceToTheNearestLandIn);
    dataToSave.numberOfDwellingUnits = this.userForm.value.numberOfDwellingUnits ? (+this.userForm.value.numberOfDwellingUnits) : 0;
    dataToSave.other = this.returnOtherInfo();
    dataToSave.submittedByUserId = this.currentUser.userId;
    dataToSave.userId = this.projectDetails.userId;




    const filteredData = omit(dataToSave, ['projectName', 'projectDescription']);

    this.applicationService.saveWithPath(filteredData, environment.applicationUrl + 'application/application')
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application saved successfully", "bottom", "center");
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.projectDetails.id + '/permit-type/' + this.userForm.value.permitTypeId)
            .subscribe(
              data => {
                if (data.length > 0) {
                  // If data present means that application already existis with the permit type category exists
                  this.outputData.applicationSaved = data[0];
                  this.projectDetails.applyOtherApplication = false;
                  this.outputData.isDocumenting = true;
                } else {
                  this.projectDetails.applyOtherApplication = true;
                }
              }, error => {
                this.utilService.showNotification(NOTIFICATION_COLOR.error, error.message, "bottom", "center");
              })
          this.submitted = false;
        }, error => {
          this.submitted = false;
        }
      )
  }



  cancel(): void {
    this.router.navigate(['/account/application/applications']);
  }


  comeAndAllowToAssign(event: any) {
    if (event.found) {
      this.userForm.controls['certificateNumberEIA'].setValue(event.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event.isEIAVerified);
    } else {
      this.userForm.controls['certificateNumberEIA'].setValue(event.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event.isEIAVerified);
    }
  }


  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / this.userForm.value.plotSize) * 100).toFixed(2));
  }

  initialize(event: any) {
    if (event === '') {

      this.userForm = this.formBuilder.group({
        buildTypeId: ["", [Validators.required]],
        buildTypeCode: [""],
        permitTypeCode: [""],
        permitTypeId: [""],
        applicationStatusId: [""],
        projectId: ["", [Validators.required]],
        agencyCode: ["", [Validators.required]],
        certificateNumberEIA: [''],
        upi: [""],
        categoryTypeId: ["", [Validators.required]],
        isUnderMortgage: [false],
        isUnderRestriction: [false],
        centralCoordinateX: [""],
        centralCoordinateY: [""],
        villageName: ["",],
        villageCode: [""],
        cellCode: ["",],
        sectorCode: ["",],
        districtCode: ["",],
        provinceCode: ['',],
        cellName: ["",],
        sectorName: ["",],
        districtName: ["",],
        provinceName: ["",],
        id: [""],
        selectedUse: ["",],
        selectedCategoryUse: [""],
        projectName: [""],
        projectDescription: [""],
        plotSize: [""],
        userId: [this.currentUser.userId],
        agencyId: [""],
        technologySurveyId: [""],
        projectStatusId: [""],
        isFromOldSystem: [""],
        buildUpArea: [""],
        numberOfFloor: [""],
        grossFloorArea: [""],
        numberOfParkingSpace: [""],
        priceOfDwellingUnitRwf: [""],
        capacityInformation: [""],
        numberOfDwellingUnits: [""],
        DescriptionOfOperation: [""],
        percentageSpaceUse: [""],
        waterConsumption: [""],
        electricityConsumption: [""],
        DistanceToTheNearestLandIn: [""],
        ProjectCostInUSD: [""],
        ProjectCostInRwf: [""],
        categoryCode: [""],
        isEIAVerified: [false],
        combiningPlotSize: [""],
        isAssociated: [""],
      }, { validator: this.buildUpAreaValidator });
    } else {


    }

    // this.getProjectStatus();
  }




  buildUpAreaValidator: ValidatorFn = (control: AbstractControl): { [key: string]: any } | null => {
    const plotSize = control.get('plotSize');
    const combiningPlotSize = control.get('combiningPlotSize');
    // const buildUpArea = control.get('buildUpArea');
    // const percentageSpaceUse: any = control.get('percentageSpaceUse');
    // if (plotSize && buildUpArea && plotSize.value && buildUpArea.value) {
    //   if (buildUpArea.value > plotSize.value) {
    //     return { 'greaterThanPlotSize': true };
    //   }
    // }
    // return null;

    const buildUpArea = control.get('buildUpArea');
    const percentageSpaceUse: any = control.get('percentageSpaceUse');
    if (combiningPlotSize && combiningPlotSize?.value && buildUpArea && combiningPlotSize?.value > plotSize?.value) {
      if (buildUpArea.value > combiningPlotSize.value) {
        return { 'greaterThanPlotSize': true };
      }
    } else
      if (plotSize && buildUpArea && plotSize.value && buildUpArea.value) {
        if (buildUpArea.value > plotSize.value) {
          return { 'greaterThanPlotSize': true };
        }
      }
    return null;
  };




  checkAssociated(event: any) {
    if (this.paramsId !== '0') {
      this.outputData.applicationId = this.paramsId;
      this.outputData.combiningPlotSize = this.userForm.value.combiningPlotSize;
      this.outputData.plotSize = this.userForm.value.plotSize;
      this.isAssociated = !this.isAssociated;
      this.openModal(event, 'lg');
    } else {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Project identifier is not yet generated", "top", "right");
    }
  }

  closeAssociatedPopup(event: any) {
    this.modalService.dismissAll();
    this.isAssociated = !this.isAssociated;
    // update value of percentage
    // this.checkIfUPIhasAlreadyProject();
  }

  getNewCombiningAssociated(event: any) {
    if (event.isCombining) {
      this.userForm.controls['combiningPlotSize'].setValue(event.combining + this.userForm.value.plotSize);
    }
  }

  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }


  returnOtherInfo() {
    return {
      "userId": "",
      "licenseNumber": "",
      "permitTypeId": "",
      "applicationId": "",
      "doYouHaveTheOccupancy": "",
      "isFastAidBox": "",
      "disabilityToiletsFlipUpGrabBars": "",
      "paraLighteningSystem": "",
      "equipmentCapacity": "",
      "constructionMethod": "",
      "fireAlarmSystemWithAnAlarmBellOnEach": "",
      "whyNotFireAlarmSystemWithAnAlarmBellOnEach": "",
      "fireExtinguishersEvery50mOnEachFloor": "",
      "whyNotFireExtinguishersEvery50mOnEachFloor": "",
      "functioningExitSignsOnEachFloor": "",
      "whyNotfunctioningExitSignsOnEachFloor": "",
      "anEmergencyExitOnEachFloor": "",
      "whyNotanEmergencyExitOnEachFloor": "",
      "floorPlanOnEachLevel": "",
      "whyNotfloorPlanOnEachLevel": "",
      "numberSignOnEachFloor": "",
      "whyNotnumberSignOnEachFloor": "",
      "signForbiddingTheUseOfElevatorsInCaseOfFire": "",
      "whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire": "",
      "landingSpaceOnTopOfTheBuildingForHelicopters": "",
      "whyNotlandingSpaceOnTopOfTheBuildingForHelicopters": "",
      "CCTVCameras": "",
      "whyNotCCTVCameras": "",
      "WalkThroughAndHeldMetalDetect": "",
      "whyNotWalkThroughAndHeldMetalDetect": "",
      "UnderSearchMirror": "",
      "whyNotUnderSearchMirror": "",
      "LuggageScanners": "",
      "whyNotLuggageScanners": "",
      "PlatesIndicatingEmergencyResponseUnitsPhoneNumbers": "",
      "whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers": "",
      "EmergencyEvacuationPlan": "",
      "whyNotEmergencyEvacuationPlan": "",
      "SecurityManagerAndStaffCameras": "",
      "whyNotSecurityManagerAndStaffCameras": "",
      "AnInternalCommunicationSystem": "",
      "whyNotAnInternalCommunicationSystem": "",
      "BroadBandInternetServices": "",
      "whyNotBroadBandInternetServices": "",
      "StaffAndVisitorAccessCards": "",
      "whyNotStaffAndVisitorAccessCards": "",
      "applicationForFixedTelephoneLineConnection": "",
      "areThereAnyFacilitiesForTheDisabledProvidedBuilding": "",
      "whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding": "",
      "stageOfConstruction": "",
      "supervisingFirmSiteEngineer": "",
      "remarks": "",
      "dateForRequestedInspection": ""
    }
  }
}
