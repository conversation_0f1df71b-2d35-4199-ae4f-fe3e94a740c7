import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { AuthService } from 'src/app/auth-pages/services/auth.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { environment } from 'src/environments/environment';
import { GlobalRefreshService } from 'src/app/shared/services/global-refresh.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  currentUser: any;
  countingBoard: any = {}
  outputData: any = {};
  inTokenData: any = {}
  lists: any[] = [];
  // searchApplicationId = '';
  searchData: any = {};
  loading: boolean = false;
  private refreshSub: any;
  upiData: any;

  constructor(
    private authService: AuthService,
    private sessionService: SessionService,
    private applicationService: ApplicationService,
    private modalService: NgbModal,
    private router: Router,
    private globalRefresh: GlobalRefreshService
  ) {
    this.searchData.isGeneralSearchUpi = false;
    this.searchData.isGeneralSearchUpiAssociated = false;
    this.searchData.isGeneralSearchUpiInLIS = false;
    this.currentUser = this.sessionService.getSession();
    this.inTokenData = this.sessionService.getTokenData();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;

    if (this.currentUser.data.user.role.code === 'APP') {
      this.applicationService.findAllWithPath(environment.applicationUrl +
        'application/application/dashboard/user/' + this.currentUser.userId)
        .subscribe(
          data => { this.countingBoard = data; }
        )
    } else if (this.currentUser.data?.user?.agency?.code === 'RHA' &&
      this.currentUser.data.user.userType.code === 'STF') {

      this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/application/dashboardAgency/RHA')
        .subscribe(
          data => { this.countingBoard = data; }
        )

    } else if (this.currentUser.data.user.role.code === 'SINSP' || this.currentUser.data.user.role.code === 'DINSP' ||
      this.currentUser.data.user.role.code === 'INSP') {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/dashboardInspectionAll/' +
        this.currentUser.data.user.agency.id)
        .subscribe(
          data => {
            this.countingBoard = data;
          })
    } else if (this.currentUser.data.user.role.code === 'DRCT' && this.currentUser.data.user?.agency?.code === 'RHA') {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/application/dashboardAgency/RHA')
        .subscribe(
          data => { this.countingBoard = data; }
        )
    } else if (
      this.currentUser.data.user.role.code === 'STF'
      || this.currentUser.data.user.role.code === 'DRCT'
      || this.currentUser.data.user.role.code === 'RVWR' ||
      this.currentUser.data.user.role.code === 'OFCMNG' ||
      this.currentUser.data.user.role.code === 'OSCM' ||
      this.currentUser.data.user.role.code === 'TMLD'
      // || this.currentUser.data.user.role.code === 'SINSP'
      // || this.currentUser.data.user.role.code === 'DINSP'
    ) {
      if (this.currentUser.data.user.agency.code === 'COK') {
        this.applicationService.findAllWithPath(environment.applicationUrl + 'application/dashboardWithoutInspection/' + this.currentUser.data.user.agency.id)
          .subscribe(
            data => {
              this.countingBoard = data;
              this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=NORVW')
                .subscribe(
                  statusData => {
                    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/applicationStatus/' + statusData.items[0]?.id)
                      .subscribe(
                        contentData => {
                          this.countingBoard.rhaUnderReview = contentData.length;
                        })
                  }
                )


            }
          )
      } else {
        this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/dashboardAgency/' + this.currentUser.data.user.agency.id)
          .subscribe(
            data => {
              this.countingBoard = data;
              this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=NORVW')
                .subscribe(
                  statusData => {
                    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/applicationStatus/' + statusData.items[0]?.id)
                      .subscribe(
                        contentData => {
                          this.countingBoard.rhaUnderReview = contentData.length;
                        })
                  }
                )


            }
          )
      }

    }


    else if (this.currentUser.data.user.userType.code === 'ENG' || this.currentUser.data.user.userType.code === 'ARC') {
      if (this.inTokenData.LicenseArchitect) { this.getPendingLists(this.inTokenData.LicenseArchitect); }
      if (this.inTokenData.LicenseEngineer) { this.getPendingLists(this.inTokenData.LicenseEngineer) };
    }
    else {
      if (this.currentUser.data.user.role.code !== 'APP' && this.currentUser.data.user.role.code === 'ADM') {
        this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/dashboard/all')
          .subscribe(
            data => { this.countingBoard = data; }
          )
      }
    }


    this.loadList();
  }

  ngOnInit(): void {
    this.refreshSub = this.globalRefresh.refreshInterval$.subscribe(() => {
      // this.loadList();
    });
  }

  ngOnDestroy(): void {
    if (this.refreshSub) {
      this.refreshSub.unsubscribe();
    }
  }

  // ngOnInit(): void {
  //   this.loadList(); // Initial fetch
  //   // Use correct interval — 60 seconds = 60000 ms
  //   this.refreshIntervalId = setInterval(() => {
  //     this.loadList();
  //   }, 60000);
  // }

  // ngOnDestroy(): void {
  //   if (this.refreshIntervalId) {
  //     clearInterval(this.refreshIntervalId);
  //   }
  // }


  getPendingLists(licenceNumber: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/dashboard/submittedUser/' + this.currentUser.userId)
      .subscribe(
        (data) => {

          this.countingBoard = data;
        });
  }

  searchUpiEvent(content: any, sizeParams: any) {

    if (this.searchData.searchUpi) {
      this.searchData.isGeneralSearchUpi = false;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/upi/search?upi=' + this.searchData.searchUpi)
        .subscribe(
          (data) => {
            this.loading = false;
            if (data) {
              this.searchData.listOfUpi = data;
              this.modalService.open(content, { size: sizeParams, centered: true });
              this.searchData.isGeneralSearchUpi = true;
            }
          }, error => { this.loading = false;  this.searchData.isGeneralSearchUpi = false; });
    }
  }

  searchUpiInAssociatedEvent(content: any, sizeParams: any) {

    if (this.searchData.searchAssociatedUpi) {
      this.searchData.isGeneralSearchUpiAssociated = false;
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/associated/upi/search?upi=' + this.searchData.searchAssociatedUpi)
        .subscribe(
          (data) => {
            this.loading = false;
            if (data) {
              this.searchData.listOfAssociatedUpi = data;
              this.modalService.open(content, { size: sizeParams, centered: true });
              this.searchData.isGeneralSearchUpiAssociated = true;
            }
          }, error => { this.loading = false;  this.searchData.isGeneralSearchUpiAssociated = false; });
    }
  }

  searchUpiInLISEvent(content: any, sizeParams: any) {

    if (this.searchData.searchUpiInLIS) {
      this.searchData.isGeneralSearchUpiInLIS = false;
      this.applicationService.findAllWithPath(environment.landAPI + this.searchData.searchUpiInLIS)
        .subscribe(
          (data) => {
            this.loading = false;
            if (data) {
              this.searchData.listOfUpiInLIS = data;
              this.upiData = this.searchData.searchUpiInLIS;
              this.modalService.open(content, { size: sizeParams, centered: true });
              this.searchData.isGeneralSearchUpiInLIS = true;
            }
          }, error => { this.loading = false;  this.searchData.isGeneralSearchUpiInLIS = false; });
    }
  }



  searchTermEvent() {
    if (this.currentUser.data.user.role === 'ADM') {

    } else {

    }
    this.loading = true;
    if (this.searchData.searchApplicationId) {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/search/' + this.searchData.searchApplicationId)
        .subscribe(
          (data) => {
            this.loading = false;
            if (data) {
              this.router.navigate(['/account/all-applications/application-detail/', data.id]);
            }
          }, error => { this.loading = false; });
    }
  }


  openGeneralSearchComponent(content: any, sizeParams: any) {
    this.outputData.isGeneralSearch = true;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  closeAndGeneralSearch(event: any) {
    this.applicationService.saveAssetWithPath(event, environment.applicationUrl + 'application/application/generalSearch')
      .subscribe(
        (data) => {
          this.loading = false;
          if (data) {
            this.router.navigate(['/account/all-applications/application-detail/', data.id]);
          }
        }, error => { this.loading = false; });
  }




  goToApplication(event: any) {
    console.log(event);
    if (this.currentUser.data.user.role.code === 'APP' ||
      this.currentUser.data.user.userType.code === 'ENG' ||
      this.currentUser.data.user.userType.code === 'ARC'
    ) {
      // this.router.navigate(['/account/application/applications']);
      this.router.navigate(['/account/application/applications/' + event]);
    } else {
      this.router.navigate(['/account/all-applications/lists/' + event]);
    }


  }

  loadList() {

    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/user/' + this.currentUser.userId)
      .subscribe(
        data => {
          this.lists = data
        },
      )
  }



  viewItOnMap(event: any) {
    this.outputData = event;
    this.outputData.isMap = true;
  }

  closeMap(event: any) {
    this.outputData.isMap = false;
  }
}
