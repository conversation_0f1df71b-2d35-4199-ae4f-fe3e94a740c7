import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AplicantApplicationsComponent } from '../components/aplicant-applications/aplicant-applications.component';
import { ApplicationDetailComponent } from '../components/application-detail/application-detail.component';
import { ApplicationRoadMapComponent } from '../components/application-road-map/application-road-map.component';
import { AssignedApplicationsComponent } from '../components/assignment-to-eng-architect/assigned-applications/assigned-applications.component';
import { ProjectDetailContinueComponent } from '../components/assignment-to-eng-architect/project-detail-continue/project-detail-continue.component';
import { ContinuePendingApplicationComponent } from '../components/continue-application/continue-pending-application/continue-pending-application.component';
import { ContinuePendingAttachementComponent } from '../components/continue-application/continue-pending-attachement/continue-pending-attachement.component';
import { NewApplicationDetailComponent } from '../components/new-application/new-application-detail/new-application-detail.component';
import { NewApplicationComponent } from '../components/new-application/new-application/new-application.component';
import { ApplicationRoadMapOldSystemComponent } from '../components/old-system/application-road-map-old-system/application-road-map-old-system.component';
import { OtherApplicationComponent } from '../components/other-related-application-components/other-application/other-application.component';
import { ProjectAttachmentComponent } from '../components/project-attachment/project-attachment.component';
import { NewInfoApplicationComponent } from '../components/new-application/new-info-application/new-info-application.component';
import { ProjectsComponent } from '../components/projects/projects.component';
import { ProjectDetailsComponent } from '../components/old-system-application/project-details/project-details.component';
import { ProjectModificationComponent } from '../components/new-application/project-modification/project-modification.component';
import { MyBoxComponent } from 'src/app/application-review-management/components/my-box/my-box.component';
import { RelatedPermitTypeApplicationComponent } from '../components/new-application/related-permit-type-application/related-permit-type-application.component';
import { OtherApplicationWithoutNcpComponent } from '../components/other-related-application-components/other-application-without-ncp/other-application-without-ncp.component';


const routes: Routes = [
    { path: "new-application/:page/:id", component: ApplicationRoadMapComponent, },
    { path: "new-old-application/:page/:id", component: ApplicationRoadMapOldSystemComponent },



    { path: "applications", component: AplicantApplicationsComponent },
    { path: "applications/:status", component: AplicantApplicationsComponent },
    { path: "application-detail/:id", component: ApplicationDetailComponent },
    { path: "engineers-tasks", component: AssignedApplicationsComponent },
    { path: "complete-assigned-project/:id", component: ProjectDetailContinueComponent },
    { path: 'attachment', component: ProjectAttachmentComponent },
    { path: "other-application/:id", component: OtherApplicationComponent },
    { path: "other-application/:permitTypeId/:id", component: OtherApplicationWithoutNcpComponent },

    { path: 'project-modification/:projectId', component: ProjectModificationComponent},

    { path: 'new-application/:id', component: NewInfoApplicationComponent},
    { path: 'new-application-project/:id', component: NewApplicationComponent },
    { path: 'new-application-development-detail/:id', component: NewApplicationDetailComponent },

    { path: 'related-permit-type-application-development-detail/:permitTypeId/:projectId', component: RelatedPermitTypeApplicationComponent},

    { path: 'resume-application/:id', component: ContinuePendingApplicationComponent },
    { path: 'resume-application-attachment/:permitType/:id', component: ContinuePendingAttachementComponent },

    { path: 'projects', component: ProjectsComponent},
    { path: 'project-details/:id', component: ProjectDetailsComponent},

    { path: 'my-box', component: MyBoxComponent},

];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ApplicationRoutingModule { }
