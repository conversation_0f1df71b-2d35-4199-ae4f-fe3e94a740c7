import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
declare function makePayment(params: any): any;
@Component({
  selector: 'app-invoice-detail',
  templateUrl: './invoice-detail.component.html',
  styleUrls: ['./invoice-detail.component.scss']
})
export class InvoiceDetailComponent {

  invoiceDetails: any;
  currentUser: any = {};
  rootAPIKey = environment.iremboPayKey;


  constructor(
    public sessionService: SessionService,
    private applicationService: ApplicationService,
    private router: Router,
    private utilService: UtilService,
    private route: ActivatedRoute) {
    this.currentUser = this.sessionService.getSession();

    route.params.subscribe((params: any) => {
      this.applicationService.findAllWithPath(
        environment.applicationUrl + 'invoice/invoice/allData/' + params.id + '/' + params.applicationId
      )
        .subscribe(
          data => { this.invoiceDetails = data; },
        )

    })

    this.realTimeChecker();

  }


  makePaymentTS(event: any) {
    let data = {
      "invoiceNumber": this.invoiceDetails.externalReferenceNumber,
      "publicKey": this.rootAPIKey
    }
    makePayment(data);
  }
  // makePaymentTS(event: any) {
  //   let data = {
  //     "invoiceNumber": this.invoiceDetails.invoiceNumber,
  //     "publicKey": this.rootAPIKey
  //   }
  //   makePayment(data);
  // }

  payInvoiceSimilator() {
    // let data = {
    //   "externalReferenceNo": "df2b95a2-ccff-43cd-aa9d-5fafa483c6c6",
    //   "invoiceId": this.inputData.id,
    //   "amount": this.inputData.amount
    // }
    // this.loading = true;
    // this.applicationService.saveInvoicePayment(data)
    //   .subscribe(
    //     data => {
    //       this.loading = false;
    //       this.alertService.Success('Invoice Well paid');
    //       this.backToInvoices.emit(false);
    //     },
    //     error => { this.loading = false; this.alertService.Error(error + '') }
    //   )
  }


  realTimeChecker() {
    setInterval(() => {
      // if(this.inputData.paymentStatusId === 2 ) {
      //   this.successPaid = true;
      //   this.onPayment = true;
      // }
      // else {
      //   this.successPaid = false;
      //   this.onPayment = false;
      // }
    }, 1000);
  }




  // payInvoice() {
  //   let submitData = {};
  //   this.applicationService.saveWithPath(submitData, environment.applicationUrl + '')
  //     .subscribe(
  //       data => {
  //         this.utilService.showNotification(NOTIFICATION_COLOR.success, "Invoice paid successfully", "bottom", "center");
  //         this.router.navigate(['/account/payment']);
  //       }
  //     )
  // }
}
