<div class="kbk-table">
    <div class="kbk-table_hder p-cont">
        <span class="hder" aria-label="header tittle">Users</span>
        <button type="button" class="kbk-btn kbk-btn-main" id="create-btn" (click)="expiredPassword()">Expire all
            password for staff</button>
        <div class="btns sp-xs">
            <button type="button" class="kbk-btn kbk-btn-main" data-bs-toggle="modal" id="create-btn"
                data-bs-target="#showModal" (click)="openModal(content)">Create User</button>
            <!-- <button type="button" class="kbk-btn kbk-btn-outline" (click)="csvFileExport()">Export</button> -->
        </div>
    </div>
    <div class="kbk-table_body">
        <!-- Nav tabs -->
        <ul ngbNav #nav="ngbNav" [activeId]="1" class="kbk-tabs">
            <li [ngbNavItem]="1" class="kbk-tabs-item">
                <!-- <a ngbNavLink class="kbk-tabs-link"> All User </a> -->
                <ng-template ngbNavContent>
                    <div class="kbk-table_flt">
                        <div class="form-input">
                            <div class="form-input_search fsm">
                                <input type="text" name="searchTerm" placeholder="Search for something..."
                                    [(ngModel)]="userService.searchTerm" (ngModelChange)="onSearchTermChange($event)">
                                <button type="button" class="btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                        <g data-name="Layer 2">
                                            <g data-name="search">
                                                <rect width="24" height="24" opacity="0" />
                                                <path
                                                    d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                                            </g>
                                        </g>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="form-input">
                            <div>
                                <select class="fsm" name="status" id="status" [(ngModel)]="userService.status"
                                    (change)="filterByRole()">
                                    <option value="" selected>Filter by Role</option>
                                    <option value="all" selected>All</option>
                                    <option *ngFor="let role of roles" [value]="role.id">{{role.name}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-input">
                            <div>
                                <select class="fsm" name="filteredAgency" id="filteredAgency"
                                    [(ngModel)]="filteredAgency" (change)="filterByAgency()">
                                    <option value="" selected>Filter by Agency</option>
                                    <option value="all" selected>All</option>
                                    <option *ngFor="let role of agencies" [value]="role.id">{{role.name}}</option>
                                </select>
                            </div>
                            <!-- <div>
                                <select class="fsm" name="filteredAgency"
                                    id="status" [(ngModel)]="userService.status" (change)="filterByAgency()">
                                    <option value="" selected>Filter by Agency</option>
                                    <option value="all" selected>All</option>
                                    <option *ngFor="let role of agencies" [value]="role.id">{{role.name}}</option>
                                </select>
                            </div> -->
                        </div>
                        <!-- <div class="form-input">
                            <div>
                                <select class="fsm" data-choices data-choices-search-false name="choices-single-default"
                                    id="idPayment">
                                    <option value="" selected>Filter by Department</option>
                                </select>
                            </div>
                        </div> -->
                        <!-- <button type="button" class="kbk-btn kbk-btn-main kbk-btn-sm">Filter</button> -->
                    </div>
                    <div class="kbk-table-responsive">
                        <table>
                            <thead>
                                <tr>
                                    <th scope="col" style="width: 25px;">
                                        <label class="form-checkbox form-checkbox-dft">
                                            <input type="checkbox" id="checkAll" value="option"
                                                [(ngModel)]="masterSelected" (change)="checkUncheckAll($event)">
                                        </label>
                                    </th>
                                    <th data-sort="firstName">Firstname</th>
                                    <th data-sort="lastName">Lastname</th>
                                    <th data-sort="email">Email</th>
                                    <th data-sort="phone">Phone</th>
                                    <th data-sort="role">Role </th>
                                    <th data-sort="agency">Agency </th>
                                    <th data-sort="userType">User Type </th>
                                    <th data-sort="verified">Verified At </th>
                                    <th data-sort="isActive">Active </th>
                                    <th data-sort="lockUntil">Locked Until </th>
                                    <th data-sort="">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let data of lists| genericfilter: userService.searchTerm"
                                    id="o_{{data._id}}">
                                    <td scope="row">
                                        <label class="form-checkbox form-checkbox-dft">
                                            <input type="checkbox" name="checkAll" value="{{data._id}}"
                                                [(ngModel)]="data.state" (change)="onCheckboxChange($event)">
                                        </label>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.firstName | titlecase"
                                            [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.lastName | titlecase"
                                            [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.email" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.phoneNumber" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.role.name" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data?.agency?.name" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.userType.name" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.verifiedAt | date"
                                            [term]="userService.searchTerm"></ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.isActive ? 'Active' : 'Deactivated' "
                                            [term]="userService.searchTerm"></ngb-highlight>
                                    </td>
                                    <td>
                                        <ngb-highlight [result]="data.lockUntil" [term]="userService.searchTerm">
                                        </ngb-highlight>
                                    </td>
                                    <td>
                                        <!-- <ul class="list-inline hstack gap-2 mb-0">
                                            <li class="list-inline-item edit" data-bs-toggle="tooltip"
                                                data-bs-trigger="hover" ngbTooltip="Edit" placement="top">
                                                <a href="javascript:void(0);" data-bs-toggle="modal"
                                                    class="text-primary d-inline-block edit-item-btn"
                                                    data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                                                    (click)="editDataGet(data.id,content)">
                                                    <i class="ri-pencil-fill fs-16"></i>
                                                </a>
                                            </li>
                                        </ul> -->
                                        <div class="kbk-table-dropdown">
                                            <button class="kbk-link-btn">More</button>
                                            <div class="kbk-table-dropdown_list">



                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="deactivate(data, confirmModel, '0')">Deactivate </a>

                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="unlockUser(data, confirmModel, '2')">Unlock User </a>



                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="editDataGet(data.email,content)">Edit</a>


                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="activate(data, confirmModel, '1')">Activate</a>

                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="verifyUser(data, confirmModel, '4')">Verify</a>
                                                <!-- <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="verifyUser(data, verifyContent)">Verify</a> -->

                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="expiredPasswordOnePerson(data)">Expire psw </a>

                                                <a class="kbk-btn kbk-btn-sec kbk-btn-sm"
                                                    (click)="resendCode(data)">Resend Code</a>



                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- when there is no content message/icon -->
                                <tr *ngIf="(lists | genericfilter: userService.searchTerm)?.length === 0">
                                <td colspan="100%" class="text-center py-8">
                                    <div class="flex flex-col items-center justify-center text-muted">
                                    <img src="../../../assets/ikons/SVG/file-wrong-1.svg" alt="No data found" width="80" height="80" />
                                    <p class="mt-2 text-gray-500">No data found</p>
                                    </div>
                                </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagnation" *ngIf="lists.length > 0">
                        <div class="pagnation-item">
                            <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                                <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} -
                                    {{endIndex}}</span>
                            </div>
                        </div>
                        <div class="pagnation-item">
                            <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                                (pageChange)="getPremiumData();">
                            </ngb-pagination>
                        </div>
                    </div>
                </ng-template>
            </li>
        </ul>
        <!-- Tab panes -->
        <div class="tab-content text-muted">
            <div [ngbNavOutlet]="nav"></div>
            <div id="elmLoader">
                <div class="load-cont" role="status">
                    <div class="preload">
                        <div class="line-cont">
                            <span class="line1"></span>
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Create Model -->
<ng-template #content role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel" class="modal-title">New User</h2>
        <p class="modal-title">Create new user</p>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modol-content">
        <form (ngSubmit)="onSubmit()" [formGroup]="userForm" autocomplete="off">
            <div class="form-set">
                <div class="form-input">
                    <label>Firstname</label>
                    <div>
                        <input type="text" name="firstName" formControlName="firstName" required>
                    </div>
                </div>
                <div class="form-input">
                    <label>Lastname</label>
                    <div>
                        <input type="text" name="lastName" formControlName="lastName" required>
                    </div>
                </div>
                <div class="form-input">
                    <label>Email</label>
                    <div>
                        <input type="email" name="email" formControlName="email"
                            [class.is-invalid]="userForm.get('email')?.invalid && userForm.get('email')?.touched"
                            required>
                        <div
                            *ngIf="(userForm.get('email')?.invalid && userForm.get('email')?.touched) || userForm.get('email')?.dirty">
                            <small *ngIf="userForm.get('email')?.hasError('required')" class="text-danger">Email is
                                required</small>
                            <small *ngIf="userForm.get('email')?.hasError('pattern')" class="text-danger">Please provide
                                a valid email address</small>
                        </div>
                    </div>
                </div>
                <div class="form-input">
                    <label>Phone number</label>
                    <div>
                        <input type="text" name="phoneNumber" formControlName="phoneNumber" required>
                        <div class="wedu-warntip"
                            *ngIf="userForm.get('phoneNumber')?.invalid && userForm.get('phoneNumber')?.touched">
                            <small *ngIf="userForm.get('phoneNumber')?.hasError('required')" class="text-danger">Phone
                                number is required</small>
                            <small *ngIf="userForm.get('phoneNumber')?.hasError('pattern')" class="text-danger"> Please
                                provide a valid phone number starting with 078, 079, 073, or 072 </small>
                        </div>
                    </div>
                </div>
                <div class="form-input">
                    <label>Gender</label>
                    <div>
                        <select name="gender" id="gender" formControlName="gender" required>
                            <option disabled value="">Choose gender</option>
                            <option *ngFor="let r of genders" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="form-input">
                    <label>Agency</label>
                    <div>
                        <select name="agencyId" id="agencyId" formControlName="agencyId" required>
                            <option disabled value="">Choose agency</option>
                            <option *ngFor="let r of agencies" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="form-input">
                    <label>User Type</label>
                    <div>
                        <select name="userTypeId" id="userTypeId" formControlName="userTypeId" required>
                            <option disabled value="">Choose user type</option>
                            <option *ngFor="let r of userTypes" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="form-input">
                    <label>Level</label>
                    <div>
                        <select name="approvalLevelId" id="approvalLevelId" formControlName="approvalLevelId" required>
                            <option disabled value="">Choose level</option>
                            <option *ngFor="let r of levels" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>
                <div class="form-input">
                    <label>Role</label>
                    <div>
                        <select name="roleId" id="roleId" formControlName="roleId" required>
                            <option disabled value="">Choose role</option>
                            <option *ngFor="let r of roles" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>

                <div class="form-input">
                    <label>District </label>
                    <div>
                        <select name="districtId" id="districtId" formControlName="districtId"
                            (change)="findSectorsByDistrict()" required>
                            <option disabled value="">Choose district</option>
                            <option *ngFor="let r of districts" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>

                <div class="form-input">
                    <label>Sector (Optional)</label>
                    <div>
                        <select name="sectorId" id="sectorId" formControlName="sectorId" required>
                            <option disabled value="">Choose sector</option>
                            <option *ngFor="let r of sectors" [value]="r.id">{{r.name}}</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec">Cancel</button>
                <button *ngIf="!submitted" type="submit" class="kbk-btn kbk-btn-main">{{userForm.value.id ? 'Update' :
                    'Create user' }}</button>
                <button *ngIf="submitted" type="button" class="kbk-btn kbk-btn-main">Saving...</button>
            </div>
        </form>
    </div>
</ng-template>
<ng-template #verifyContent role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel" class="modal-title">User Verification</h2>
        <p class="modal-title">Verify Token</p>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modol-content">
        <form (ngSubmit)="onVerify()" [formGroup]="verfiyForm" autocomplete="off">
            <div class="form-set">
                <div class="form-input">
                    <label>OTP</label>
                    <div>
                        <input type="text" name="otp" formControlName="otp" required>
                    </div>
                </div>
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec">Cancel</button>
                <button *ngIf="!submitted" type="submit" class="kbk-btn kbk-btn-main">Submit</button>
                <button *ngIf="submitted" type="button" class="kbk-btn kbk-btn-main">Sending...</button>
            </div>
        </form>
    </div>
</ng-template>
<!--End Modal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">You are about to delete a user ?</h2>
            <p>Deleting your user will remove all of your information from our database.</p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <h4>You are about to delete a User ?</h4>
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                    (click)="modal.close('Close click')" id="deleteRecord-close">Close</button>
                <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="deleteData('')"
                    (click)="modal.close('Close click')">Yes, Delete It!</button>
            </div>
        </div>
    </div>
</ng-template>



<ng-template #confirmModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">Confirm message</h2>
            <!-- <p>Deleting your {{outPutData.name}} will remove all of your information from our database.</p> -->
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <h4>Are you sure you want to {{confirmData.name}} a user ?</h4>
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                    (click)="modal.close('Close click')" id="deleteRecord-close">Close</button>
                <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="doAction()"
                    (click)="modal.close('Close click')">Yes, {{confirmData.name}}!</button>
            </div>
        </div>
    </div>
</ng-template>
