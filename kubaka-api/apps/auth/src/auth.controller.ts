import {
  <PERSON>,
  Post,
  Body,
  HttpStatus,
  HttpCode,
  Get,
  Param,
  Inject,
  HttpException,
  UseGuards,
  // NotFoundException,
  BadRequestException,
  ForbiddenException,
  Patch,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Api<PERSON>ody, ApiTags } from '@nestjs/swagger';
import { IResponse } from '@app/common/interfaces/response.interface';
import {
  ResponseError,
  ResponseError2,
  ResponseSuccess,
} from '@app/common/dto/response.dto';
import { Login } from './interfaces/login.interface';
import { CreateStaffDto, CreateUserDto, UserDto } from './dto/user.dto';
import {
  ChangePasswordDto,
  ResetPasswordDto,
  SetPasswordDto,
} from './dto/resetPassword.dto';
import { UserManagementService } from './user-management/user-management.service';

import { NOTIFICATIONS_SERVICE } from '@app/common/constants';
import { ClientProxy, MessagePattern } from '@nestjs/microservices';
import { JwtAuthGuard } from './jwt.auth.guard';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userManagementService: UserManagementService,

    @Inject(NOTIFICATIONS_SERVICE)
    private readonly notificationsService: ClientProxy,
  ) {}

  @MessagePattern({ cmd: 'userExists' })
  async userExists(userId: string): Promise<boolean> {
    return this.authService.userExists(userId);
  }

  @MessagePattern({ cmd: 'userData' })
  async userData(userId: string) {
    return this.authService.userData(userId);
  }

  @MessagePattern({ cmd: 'checkAgencyDataByDistrictCode' })
  async checkAgencyDataByDistrictCode(districtCode: string) {
    return this.authService.checkAgencyDataByDistrictCode(districtCode);
  }
  @MessagePattern({ cmd: 'checkAgencyDataById' })
  async checkAgencyDataById(agencyId: string) {
    return this.authService.checkAgencyDataById(agencyId);
  }

  @MessagePattern({ cmd: 'getAllUserByAgency' })
  async getAllUserByAgency(agencyId: string) {
    return this.authService.getAllUserByAgency(agencyId);
  }

  @MessagePattern({ cmd: 'getAllApplicants' })
  async getAllApplicants() {
    return this.authService.getAllApplicants();
  }

  @Post('login')
  @UseGuards(AuthGuard('api-key'))
  public async login(@Body() login: Login): Promise<IResponse> {
    try {
      const response = await this.authService.validateLogin(
        login.email,
        login.password,
      );
      return new ResponseSuccess('Successfully logged in', response);
    } catch (error) {
      // return new ResponseError2('Login Error', error);
      const errorMessage =
        error?.response?.message || error?.message || 'Login Error';

      return new ResponseError2(errorMessage, error);
    }
  }

  //
  // @Post('verify-otp')
  // @ApiBody({
  //   description: 'Email and OTP required for verification',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       email: { type: 'string', example: '<EMAIL>' },
  //       otp: { type: 'string', example: '123456' },
  //     },
  //     required: ['email', 'otp'],
  //   },
  // })
  // public async verifyOtp(
  //   @Body() { email, otp }: { email: string; otp: string },
  // ): Promise<IResponse> {
  //   const { token, user } = await this.authService.verifyOtp(email, otp);
  //   if (!token) {
  //     throw new ForbiddenException('Invalid OTP');
  //   }
  //   return new ResponseSuccess('OTP verified successfully', {
  //     token,
  //     user,
  //   });
  // }
  @Post('verify-otp')
  @ApiBody({
    description: 'Email and OTP required for verification',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', example: '<EMAIL>' },
        otp: { type: 'string', example: '123456' },
      },
      required: ['email', 'otp'],
    },
  })
  public async verifyOtp(
    @Body() { email, otp }: { email: string; otp: string },
  ): Promise<IResponse> {
    const result = await this.authService.verifyOtp(email, otp);

    // Ensure the structure matches the desired response format
    return new ResponseSuccess('OTP verified successfully', result);
  }

  // @Post('register')
  // @UseGuards(AuthGuard('api-key'))
  // async register(@Body() createUserDto: CreateUserDto): Promise<IResponse> {
  //   try {
  //     const newUser = new UserDto(
  //       await this.userManagementService.createNewUser(createUserDto),
  //     );

  //     await this.authService.createEmailToken(newUser.email);
  //     console.log(newUser.email);
  //     const sent = await this.authService.sendEmailVerification(
  //       newUser.email,
  //       newUser.phoneNumber,
  //     );

  //     if (sent) {
  //       return new ResponseSuccess('User registered successfully');
  //     } else {
  //       return new ResponseError('Email not sent ');
  //     }
  //   } catch (error) {
  //     return new ResponseError('Registration Error', error);
  //   }
  // }
  @Post('register')
  @UseGuards(AuthGuard('api-key'))
  async register(@Body() createUserDto: CreateUserDto) {
    try {
      const newUser = new UserDto(
        await this.userManagementService.createNewUser(createUserDto),
      );

      await this.authService.createEmailToken(newUser.email);
      console.log(newUser.email);
      const sent = await this.authService.sendEmailVerification(
        newUser.email,
        newUser.phoneNumber,
      );

      if (sent) {
        return new ResponseSuccess('User registered successfully');
      } else {
        return new ResponseError2('Email not sent ');
      }
    } catch (error) {
      return new ResponseError2('Registration Error', error);
    }
  }

  // @Post('registerUserWithIrembo')
  // @UseGuards(AuthGuard('api-key'))
  // async registerUserWithIrembo(@Body() createUserDto: CreateUserDto) {
  //   try {
  //     const user =
  //       await this.userManagementService.createNewUser(createUserDto);

  //     await this.authService.createEmailToken(user.email);
  //     const sent = await this.authService.sendEmailVerification(
  //       user.email,
  //       user.phoneNumber,
  //     );

  //     // Sanitize user data (remove password)
  //     const { password, ...userWithoutPassword } = user;
  //     console.log(password);

  //     if (sent) {
  //       return new ResponseSuccess(
  //         'User registered successfully',
  //         userWithoutPassword,
  //       );
  //     } else {
  //       return new ResponseError2('Email not sent', userWithoutPassword);
  //     }
  //   } catch (error) {
  //     return new ResponseError2('Registration Error', error);
  //   }
  // }

  @Post('registerUserWithIrembo')
  @UseGuards(AuthGuard('api-key'))
  async registerUserWithIrembo(@Body() createUserDto: CreateUserDto) {
    try {
      const user =
        await this.userManagementService.createNewUserIrembo(createUserDto);

      // Sanitize user data (remove password)
      const { password, ...userWithoutPassword } = user;
      console.log(password);
      return new ResponseSuccess(
        'User registered successfully',
        userWithoutPassword,
      );
    } catch (error) {
      return new ResponseError2('Registration Error', error);
    }
  }

  // @Post('createStaff')
  // // @UseGuards(JwtAuthGuard)
  // // @UseGuards(AuthGuard('api-key'))
  // async createStaff(
  //   @Body() createStaffDto: CreateStaffDto,
  // ): Promise<IResponse> {
  //   try {
  //     // const newUser = new UserDto(
  //     //   await this.authService.createNewStaffWithRandomPSW(createStaffDto),
  //     // );
  //     const newUser = new UserDto(
  //       await this.userManagementService.createNewStaffWithDefaultPSW(
  //         createStaffDto,
  //       ),
  //     );

  //     await this.authService.createEmailToken(newUser.email);

  //     const sent =
  //       await this.authService.sendEmailVerificationForStaffWithDefaultPassword(
  //         newUser.email,
  //         newUser.phoneNumber,
  //       );

  //     if (sent) {
  //       return new ResponseSuccess('User registered successfully');
  //     } else {
  //       return new ResponseError('Email not sent ');
  //     }
  //   } catch (error) {
  //     return new ResponseError('Registration Error', error);
  //   }
  // }

  @Post('createStaff')
  async createStaff(@Body() createStaffDto: CreateStaffDto): Promise<any> {
    try {
      // Attempt to create a new staff member
      const newUser = new UserDto(
        await this.userManagementService.createNewStaffWithDefaultPSW(
          createStaffDto,
        ),
      );

      // Create an email token for verification
      await this.authService.createEmailToken(newUser.email);

      // Send email verification
      const sent =
        await this.authService.sendEmailVerificationForStaffWithDefaultPassword(
          newUser.email,
          newUser.phoneNumber,
        );

      if (sent) {
        return {
          status: 'success',
          message: 'User registered successfully',
        };
      } else {
        throw new BadRequestException('Email not sent');
      }
    } catch (error) {
      console.error('Error in staff registration:', error);

      // Handle known errors explicitly
      if (error instanceof HttpException) {
        if (
          error.getStatus() === 403 &&
          error.message.includes('User already registered')
        ) {
          throw new ForbiddenException('User already registered');
        }
        throw error;
      }

      // For other errors, return a generic message
      throw new BadRequestException(
        'Error in staff registration: ' + error.message,
      );
    }
  }

  // @Get('verify/:token')
  // // public async verifyEmail(@Param() params): Promise<IResponse> {
  // public async verifyEmail(@Param() params) {
  //   try {
  //     const isEmailVerified = await this.authService.verifyEmail(params.token);
  //     return new ResponseSuccess('Email verified', isEmailVerified);
  //   } catch (error) {
  //     // return new ResponseError('Email not verified Error', error);
  //     return new HttpException(
  //       'Email not verified Error',
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  // }

  @Get('getToken/:email')
  async getTokeByEmail(@Param('email') email: string) {
    return await this.authService.getTokenByEmail(email);
  }

  @Get('setPasswordExpired/:email')
  async setPasswordExpired(@Param('email') email: string) {
    return await this.userManagementService.setPasswordExpired(email);
  }

  @Get('verify/:token')
  @UseGuards(AuthGuard('api-key'))
  public async verifyEmail(@Param() params) {
    try {
      const isEmailVerified = await this.authService.verifyEmail(params.token);
      console.log(isEmailVerified);
      return new ResponseSuccess('Email verified', isEmailVerified);
    } catch (error) {
      return new HttpException(
        'Email not verified Error',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // @Get('resend-verification/:email')
  // public async resendEmailVerification(@Param() params): Promise<IResponse> {
  //   try {
  //     await this.authService.createEmailToken(params.email);
  //     const isEmailSent = await this.authService.resendEmailVerification(
  //       params.email,
  //     );
  //     if (isEmailSent) {
  //       return new ResponseSuccess('Email resent', null);
  //     } else {
  //       return new ResponseError('Email not sent');
  //     }
  //   } catch (error) {
  //     return new ResponseError('Error in sending email', error);
  //   }
  // }

  @ApiBody({
    description: 'Email to resend verification link',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The email address to resend the verification link to',
        },
      },
    },
  })
  @Post('resend-verification')
  public async resendEmailVerification(@Body('email') email: string) {
    try {
      await this.authService.createEmailToken(email);
      const isEmailSent = await this.authService.resendEmailVerification(email);
      if (isEmailSent) {
        return new ResponseSuccess('Email resent', null);
      } else {
        return new ResponseError('Email not sent');
      }
    } catch (error) {
      return new ResponseError('Error in sending email', error);
    }
  }

  @Get('forgot-password/:email')
  @UseGuards(AuthGuard('api-key'))
  public async sendEmailForgotPassword(@Param() params): Promise<IResponse> {
    try {
      const isEmailSent = await this.authService.sendEmailForgotPassword(
        params.email,
      );
      if (isEmailSent) {
        return new ResponseSuccess('Email resent', null);
      } else {
        return new ResponseError('Email not sent');
      }
    } catch (error) {
      console.error(
        'Error sending email:',
        error.response?.data || error.message,
      );
      return new ResponseError('Error in sending email', error);
    }
  }

  // @Post('reset-password')
  // @HttpCode(HttpStatus.OK)
  // public async RetNewPassword(
  //   @Body() resetPassword: ResetPasswordDto,
  // ): Promise<IResponse> {
  //   try {
  //     let isNewPasswordChanged: boolean = false;
  //     if (resetPassword.email && resetPassword.currentPassword) {
  //       const isValidPassword = await this.authService.checkPassword(
  //         resetPassword.email,
  //         resetPassword.currentPassword,
  //       );
  //       if (isValidPassword) {
  //         isNewPasswordChanged = await this.userManagementService.setPassword(
  //           resetPassword.email,
  //           resetPassword.newPassword,
  //         );
  //       } else {
  //         return new ResponseError(
  //           'Reset password error, wrong current password',
  //         );
  //       }
  //     } else if (resetPassword.newPasswordToken) {
  //       const forgottenPasswordModel =
  //         await this.authService.getForgottenPassword(
  //           resetPassword.newPasswordToken,
  //         );
  //       isNewPasswordChanged = await this.userManagementService.setPassword(
  //         forgottenPasswordModel.email,
  //         resetPassword.newPassword,
  //       );
  //       if (isNewPasswordChanged) await forgottenPasswordModel.remove();
  //     } else {
  //       return new ResponseError('Change password error');
  //     }
  //     return new ResponseSuccess('Password changed', isNewPasswordChanged);
  //   } catch (error) {
  //     return new ResponseError('Change password error', error);
  //   }
  // }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  public async RetNewPassword(
    @Body() resetPassword: ResetPasswordDto,
  ): Promise<IResponse> {
    try {
      // let isNewPasswordChanged: boolean = false;

      // Validate reset using the new password token only
      if (resetPassword.newPasswordToken) {
        const forgottenPasswordModel =
          await this.authService.getForgottenPassword(
            resetPassword.newPasswordToken,
          );

        // Set new password
        const isNewPasswordChanged =
          await this.userManagementService.setPassword(
            forgottenPasswordModel.email,
            resetPassword.newPassword,
          );

        // Remove the token after successful password change
        if (isNewPasswordChanged) await forgottenPasswordModel.remove();
      } else {
        return new ResponseError(
          'Change password error: invalid token or missing data',
        );
      }

      return new ResponseSuccess('Password changed');
    } catch (error) {
      return new ResponseError('Change password error', error);
    }
  }

  @Post('setNew-password')
  @HttpCode(HttpStatus.OK)
  public async setNewPassword(
    @Body() resetPassword: SetPasswordDto,
  ): Promise<IResponse> {
    try {
      let isNewPasswordChanged: boolean = false;

      // Validate reset using the new password token only
      if (resetPassword.newPasswordToken) {
        const forgottenPasswordModel =
          await this.authService.getForgottenPassword(
            resetPassword.newPasswordToken,
          );

        // Set new password
        isNewPasswordChanged = await this.userManagementService.setPassword(
          forgottenPasswordModel.email,
          resetPassword.newPassword,
        );

        // Remove the token after successful password change
        if (isNewPasswordChanged) await forgottenPasswordModel.remove();
      } else {
        return new ResponseError(
          'Change password error: invalid token or missing data',
        );
      }

      return new ResponseSuccess('Password changed', isNewPasswordChanged);
    } catch (error) {
      return new ResponseError('Change password error', error);
    }
  }

  // @Post('change-password')
  // @HttpCode(HttpStatus.OK)
  // public async changePassword(
  //   @Body() changePassword: ChangePasswordDto,
  // ): Promise<IResponse> {
  //   try {
  //     const isValidPassword = await this.authService.checkPassword(
  //       changePassword.email,
  //       changePassword.currentPassword,
  //     );
  //     console.log(isValidPassword);
  //     if (isValidPassword) {
  //       const setNewPassword = await this.userManagementService.setPassword(
  //         changePassword.email,
  //         changePassword.newPassword,
  //       );
  //       return new ResponseSuccess('Password changed', setNewPassword);
  //     } else {
  //       return new ResponseError(
  //         'Change password error, wrong current password',
  //       );
  //     }
  //   } catch (error) {
  //     return new ResponseError('Change password error', error);
  //   }
  // }
  @Post('change-password')
  @UseGuards(AuthGuard('api-key'))
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  public async changePassword(
    @Body() changePassword: ChangePasswordDto,
  ): Promise<IResponse> {
    try {
      const setNewPassword =
        await this.userManagementService.setPasswordForChanging(
          changePassword.email,
          changePassword.newPassword,
        );
      return new ResponseSuccess('Password changed', setNewPassword);
    } catch (error) {
      return new ResponseError('Change password error', error);
    }
  }
  // public async changePassword(
  //   @Body() changePassword: ChangePasswordDto,
  // ): Promise<IResponse> {
  //   try {
  //     let isNewPasswordChanged: boolean = false;
  //     if (changePassword.email && changePassword.currentPassword) {
  //       const isValidPassword = await this.authService.checkPassword(
  //         changePassword.email,
  //         changePassword.currentPassword,
  //       );
  //       if (isValidPassword) {
  //         isNewPasswordChanged = await this.userManagementService.setPassword(
  //           changePassword.email,
  //           changePassword.newPassword,
  //         );
  //       } else {
  //         return new ResponseError(
  //           'Change password error, wrong current password',
  //         );
  //       }
  //     }
  //     return new ResponseSuccess('Password changed', isNewPasswordChanged);
  //   } catch (error) {
  //     return new ResponseError('Change password error', error);
  //   }
  // }
  @Post('change-password-staff')
  @UseGuards(AuthGuard('api-key'))
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  public async changePasswordStaff(
    @Body() changePassword: ChangePasswordDto,
  ): Promise<IResponse> {
    try {
      const setNewPassword =
        await this.userManagementService.setPasswordForChangingStaff(
          changePassword.email,
          changePassword.newPassword,
        );
      return new ResponseSuccess('Password changed', setNewPassword);
    } catch (error) {
      return new ResponseError('Change password error', error);
    }
  }
  @ApiTags('user-management')
  @Get('user/:agencyId')
  async findUserByAgency(@Param('agencyId') agencyId: string) {
    return this.userManagementService.findUserByAgency(agencyId);
  }
  @ApiTags('user-management')
  @Get('user/:userTypeId')
  async findUserByUserType(@Param('userTypeId') userTypeId: string) {
    console.log(userTypeId);
    return this.userManagementService.findUsersByUserType(userTypeId);
  }
  @ApiTags('user-management')
  @Get('user/:userTypeId')
  async findByAssignedUserForReview(@Param('userTypeId') userTypeId: string) {
    console.log(userTypeId);
    return this.userManagementService.findUsersByUserType(userTypeId);
  }

  @ApiBody({
    description: 'User email to deactivated',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The User email to deactivated',
        },
      },
    },
  })
  @Post('deactivate-user')
  public async deactivateUser(@Body('email') email: string) {
    return await this.authService.deactivateUser(email);
  }

  @ApiBody({
    description: 'User email to deactivated',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The User email to deactivated',
        },
      },
    },
  })
  @Post('activate-user')
  public async activateUser(@Body('email') email: string) {
    return await this.authService.activateUser(email);
  }
  @ApiBody({
    description: 'User email to unlocked',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The User email to unlocked',
        },
      },
    },
  })
  @Post('unlock-user')
  public async unlockUser(@Body('email') email: string) {
    return await this.authService.unlockUser(email);
  }

  // expire password for one user
  @ApiBody({
    description: 'User email to unlocked',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The User email to unlocked',
        },
      },
    },
  })
  @Patch('expire-password')
  async expirePasswordForSpecificUser(@Body('email') email: string) {
    return this.authService.expireAPasswordForASpecificUser(email);
  }

  // expire password for one user
  @ApiBody({
    description: 'User email to unlocked',
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'The User email to unlocked',
        },
      },
    },
  })
  @Patch('unExpire-password')
  async UnExpirePasswordForSpecificUser(@Body('email') email: string) {
    return this.authService.unExpireAPasswordForASpecificUser(email);
  }

  // expire all password of the users
  @ApiTags('user-management')
  @Get('expire-staff-passwords')
  async expireAllStaffPasswords() {
    return this.authService.expireAllStaffPasswords();
  }
}
