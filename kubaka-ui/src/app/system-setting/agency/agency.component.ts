import { Component, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ngxCsv } from 'ngx-csv';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NgbdAdvancedSortableHeader } from "src/app/shared/directives/sortable.directive";
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-agency',
  templateUrl: './agency.component.html',
  styleUrls: ['./agency.component.scss']
})
export class AgencyComponent {
  userForm!: UntypedFormGroup;
  submitted = false;
  lists: any[] = [];
  total!: number;
  @ViewChildren(NgbdAdvancedSortableHeader)
  headers!: QueryList<NgbdAdvancedSortableHeader>;
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  econtent: any = {};
  masterSelected!: boolean;
  outPutData: any = {};
  roles: any[] = [];
  fullList: any[] = [];
  filteredList: any[] = [];
  currentUser: any =
    {};

  constructor(
    private modalService: NgbModal,
    public userService: UserMgtService,
    private formBuilder: UntypedFormBuilder,
    private sessionService: SessionService,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.initiliazeForm();
    this.loadList();
  }


  ngOnDestroy() {
    this.userService.searchTerm = '';
  }

  loadList() {
    this.userService
      .findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe(
        (data) => {
          // this.lists = data;
          // this.content = data;
          setTimeout(() => {
            document.getElementById("elmLoader")?.classList.add("d-none");

          }, 1200);

          this.fullList = data; // Store full data for search
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate(); // Apply filter and pagination

        });
  }


  ngOnInit(): void {
    this.outPutData.name = 'Agency';
  }


  initiliazeForm() {
    this.userForm = this.formBuilder.group({
      id: [""],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      paymentAccountIdentifier: ['', [Validators.required]],
    });
  }

  get form() {
    return this.userForm.controls;
  }



  openModal(content: any) {
    this.outPutData.isOpen = true;
    this.outPutData.type = 'add';
    this.outPutData.name = 'Agency';
    this.outPutData.url = environment.authUrl + 'user-management/agency';
    this.outPutData.method = '0';
    this.initiliazeForm()
    this.outPutData.userForm = this.userForm;
    this.submitted = false;
    this.modalService.open(content, { size: "md", centered: true });
  }

  checkedValGet: any[] = [];
  onCheckboxChange(e: any) {
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }



  filterAndPaginate() {
    // Apply filtering on the full list
    const filtered = this.fullList.filter(item => this.applyFilter(item));

    // Update filtered list for search
    this.filteredList = filtered;
    this.totalRecords = filtered.length;

    // Pagination logic
    this.startIndex = (this.page - 1) * this.pageSize;
    this.endIndex = this.startIndex + this.pageSize;

    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }

    // Slice the filtered list for pagination
    this.lists = filtered.slice(this.startIndex, this.endIndex);
  }



  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.page = 1; // Reset to the first page when searching
    this.filterAndPaginate();
  }

  applyFilter(item: any): boolean {
    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';
    // const selectedAgency = this.filteredAgency && this.filteredAgency !== 'all' ? this.filteredAgency : null;

    // Check if the item matches the search term (including nested fields)
    const matchesTerm = term ? (
      Object.values(item).some(val => String(val).toLowerCase().includes(term)) ||
      (item.role?.name && item.role.name.toLowerCase().includes(term)) ||
      (item.agency?.name && item.agency.name.toLowerCase().includes(term)) ||
      (item.userType?.name && item.userType.name.toLowerCase().includes(term))
    ) : true;


    // const matchesAgency = selectedAgency ? item.agency?.id === selectedAgency : true;
    return matchesTerm;
  }


  getPremiumData() {
    this.filterAndPaginate();
    // this.lists = this.content
    //   .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);

  }


  editDataGet(id: any, content: any) {
    this.submitted = false;

    this.modalService.open(content, { size: "md", centered: true });

    this.userService.findOneWithPath(id, environment.authUrl + 'user-management/agency').subscribe({
      next: (data: any) => {
        const users = data;
        this.econtent = users;
        this.userForm.controls['code'].setValue(this.econtent.code);
        this.userForm.controls['name'].setValue(this.econtent.name);
        this.userForm.controls['paymentAccountIdentifier'].setValue(this.econtent.paymentAccountIdentifier);
        this.userForm.controls["id"].setValue(this.econtent.id);
        this.outPutData.type = 'edit';
        this.outPutData.userForm = this.userForm
        this.outPutData.name = 'Build Type';
        this.outPutData.url = environment.authUrl + 'user-management/agency';
        this.outPutData.isOpen = true;

      },
    });
  }


  deleteData(id: any) {
    if (id) {
      this.userService.deleteWithPath(id, environment.authUrl + 'user-management/agency').subscribe({
        next: data => { this.loadList(); },
        error: err => {
        }
      });

    } else {

    }
  }


  deleteMultiple(content: any) {
    var checkboxes: any = document.getElementsByName("checkAll");
    var result;
    var checkedVal: any[] = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        result = checkboxes[i].value;
        checkedVal.push(result);
      }
    }
    if (checkedVal.length > 0) {
      this.modalService.open(content, { centered: true });
    } else {
      Swal.fire({
        text: "Please select at least one checkbox",
        confirmButtonColor: "#239eba",
      });
    }
    this.checkedValGet = checkedVal;
  }

  checkUncheckAll(ev: any) {
    this.lists.forEach((x: { state: any }) => (x.state = ev.target.checked));
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }


  saveValue(event: any) {
    this.loadList();
    this.modalService.dismissAll();
  }



  saveUser() {
    if (this.userForm.invalid) {
    } else {
      if (this.userForm.get("id")?.value) {
        let dataToSave = this.userForm.value;
        dataToSave.updatedBy = this.currentUser.userId;
        this.submitted = true;
        this.userService.patchAssetWithPath(this.userForm.value.id, dataToSave, environment.authUrl + 'user-management/agency')
          .subscribe(
            (data: any) => {
              // this.backToParent.emit({ message: 'Record updated successfully!', status: true });
              this.submitted = false;
            },)
      } else {
        let dataToSubmit = {
          name: this.userForm.value.name,
          code: this.userForm.value.code,
          paymentAccountIdentifier: this.userForm.value.paymentAccountIdentifier,
          createdBy: this.currentUser.userId
        }
        this.userService.saveAssetWithPath(dataToSubmit, environment.authUrl + 'user-management/agency')
          .subscribe(
            (data: any) => {
              // this.backToParent.emit({ message: 'Record inserted successfully!', status: true });
            },
          )
      }
      // setTimeout(() => {
      //   // this.userForm.reset();
      // }, 2000);
      // this.submitted = true;
    }
  }


  // Csv File Export
  csvFileExport() {
    var users = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalseparator: ".",
      showLabels: true,
      showTitle: true,
      title: "Agency Data",
      useBom: true,
      noDownload: false,
      headers: [
        "ID",
        "name",
        "code",
        "paymentAccountIdentifier",
      ],
    };
    new ngxCsv(this.content, "role", users);
  }
}
