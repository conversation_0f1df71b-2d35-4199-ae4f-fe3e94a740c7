import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  // Put,
  Query,
} from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import {
  InvoiceDto,
  InvoiceItemDto,
  InvoiceStatusDto,
  InvoiceTypeDto,
  PriceDto,
  ReceiptDto,
  UpdateInvoiceDto,
} from '../dto/invoice.dto';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Invoice,
  InvoiceStatus,
  InvoiceType,
} from '../entities/invoice.entity';
import { Repository } from 'typeorm';
import {
  GenericSearch,
  GenericSearchWithRelations,
} from './generic-search.service';
// import {
//   IremboResponseGetDto,
//   IremboPaymentRequestInputDto,
// } from '../dto/invoiceIrembo.dto';

@ApiTags('invoice')
@Controller('invoice')
export class InvoiceController {
  constructor(
    private readonly invoiceService: InvoiceService,

    @InjectRepository(InvoiceStatus)
    protected readonly invoiceStatusRepository: Repository<InvoiceStatus>,
    protected readonly genericSearch: GenericSearch<InvoiceStatus>,

    @InjectRepository(InvoiceType)
    protected readonly invoiceTypeRepository: Repository<InvoiceType>,
    protected readonly genericSearch2: GenericSearch<InvoiceType>,

    @InjectRepository(Invoice)
    protected readonly invoiceSearchRepository: Repository<Invoice>,
    protected readonly genericSearch3: GenericSearchWithRelations<Invoice>,
  ) {}

  // invoice Status
  @Post('invoiceStatus')
  async CreateInvoiceStatus(@Body() invoiceStatusDto: InvoiceStatusDto) {
    return this.invoiceService.createInvoiceStatus(invoiceStatusDto);
  }

  @Get('invoiceStatus')
  async findAllInvoiceStatus() {
    return this.invoiceService.findAllInvoiceStatus();
  }

  @Get('invoiceStatus/:id')
  async findOneInvoiceStatus(@Param('id') id: string) {
    return this.invoiceService.findOneInvoiceStatus(id);
  }

  @Patch('invoiceStatus/:id')
  async updateInvoiceStatus(
    @Param('id') id: string,
    @Body() invoiceStatusDto: InvoiceStatusDto,
  ) {
    return this.invoiceService.updateInvoiceStatus(id, invoiceStatusDto);
  }

  @Delete('invoiceStatus/:id')
  async removeInvoiceStatus(@Param('id') id: string) {
    return this.invoiceService.removeInvoiceStatus(id);
  }

  // @Get('invoiceStatus/code/search')
  // async searchInvoiceStatusByCode(@Query('search') search: string) {
  //   const searchFields: (keyof InvoiceStatus)[] = ['code'];
  //   return this.genericSearch.search(
  //     this.invoiceStatusRepository,
  //     searchFields,
  //     search,
  //   );
  // }

  @Get('invoiceStatus/code/search')
  async searchInvoiceStatusByCode(@Query('search') search: string) {
    const searchFields: (keyof InvoiceStatus)[] = ['code'];

    try {
      return await this.genericSearch.search(
        this.invoiceStatusRepository,
        searchFields,
        search,
      );
    } catch (error) {
      // Check specific error message or type
      if (
        error.message &&
        error.message.includes('Invoice with transaction ID') &&
        error.message.includes('not found')
      ) {
        console.warn(`Skipped missing invoice: ${error.message}`);
        return []; // or return partial results if available
      }

      throw error; // re-throw if it's a different kind of error
    }
  }

  // price
  @Post('price')
  async CreatePrice(@Body() priceDto: PriceDto) {
    return this.invoiceService.createPrice(priceDto);
  }

  @Get('price')
  async findAllPrice() {
    return this.invoiceService.findAllPrices();
  }

  @Get('price/:id')
  async findOnePrice(@Param('id') id: string) {
    return this.invoiceService.findOnePrice(id);
  }

  @Patch('price/:id')
  async updatePrice(@Param('id') id: string, @Body() priceDto: PriceDto) {
    return this.invoiceService.updatePrice(id, priceDto);
  }

  @Delete('price/:id')
  async removePrice(@Param('id') id: string) {
    return this.invoiceService.removePrice(id);
  }

  // invoice Type
  @Post('invoiceType')
  async CreateInvoiceType(@Body() invoiceTypeDto: InvoiceTypeDto) {
    return this.invoiceService.createInvoiceType(invoiceTypeDto);
  }

  @Get('invoiceType')
  async findAllInvoiceType() {
    return this.invoiceService.findAllInvoiceType();
  }

  @Get('invoiceType/:id')
  async findOneInvoiceType(@Param('id') id: string) {
    return this.invoiceService.findOneInvoiceType(id);
  }

  @Patch('invoiceType/:id')
  async updateInvoiceType(
    @Param('id') id: string,
    @Body() invoiceTypeDto: InvoiceTypeDto,
  ) {
    return this.invoiceService.updateInvoiceType(id, invoiceTypeDto);
  }

  @Delete('invoiceType/:id')
  async removeInvoiceType(@Param('id') id: string) {
    return this.invoiceService.removeInvoiceType(id);
  }

  @Get('invoiceType/code/search')
  async searchInvoiceByCode(@Query('search') search: string) {
    const searchFields: (keyof InvoiceType)[] = ['code'];
    return this.genericSearch.search(
      this.invoiceTypeRepository,
      searchFields,
      search,
    );
  }

  // invoice Item
  @Post('invoiceItem')
  async CreateInvoiceItem(@Body() invoiceItemDto: InvoiceItemDto) {
    return this.invoiceService.createInvoiceItem(invoiceItemDto);
  }

  @Get('invoiceItem')
  async findAllInvoiceItem() {
    return this.invoiceService.findAllInvoiceItem();
  }

  @Get('invoiceItem/:id')
  async findOneInvoiceItem(@Param('id') id: string) {
    return this.invoiceService.findOneInvoiceItem(id);
  }

  @Patch('invoiceItem/:id')
  async updateInvoiceItem(
    @Param('id') id: string,
    @Body() invoiceItemDto: InvoiceItemDto,
  ) {
    return this.invoiceService.updateInvoiceItem(id, invoiceItemDto);
  }

  @Delete('invoiceItem/:id')
  async removeInvoiceItem(@Param('id') id: string) {
    return this.invoiceService.removeInvoiceItem(id);
  }

  // invoice
  @Post('invoice')
  async CreateInvoice(@Body() invoiceDto: InvoiceDto) {
    return this.invoiceService.createInvoice(invoiceDto);
  }

  @Get('invoice')
  async findAllInvoices() {
    return this.invoiceService.findAllInvoices();
  }

  @Get('invoice/:id')
  async findOneInvoice(@Param('id') id: string) {
    return this.invoiceService.findOneInvoice(id);
  }

  // @Get('invoice/allData/:id')
  // async findOneInvoiceAllData(@Param('id') id: string) {
  //   return this.invoiceService.findOneInvoiceAllData(id);
  // }
  @Get('invoice/allData/:id?/:applicationId?')
  async findOneInvoiceAllData(
    @Param('id') id?: string,
    @Param('applicationId') applicationId?: string,
  ) {
    return this.invoiceService.findOneInvoiceAllData(id, applicationId);
  }

  @Get('application/:applicationId')
  async findAllInvoiceOfApplication(
    @Param('applicationId') applicationId: string,
  ) {
    return this.invoiceService.findAllInvoiceOfApplication(applicationId);
  }

  @Get('zeroAmount/application/:applicationId')
  async findAllInvoiceWithZeroAmount(
    @Param('applicationId') applicationId: string,
  ) {
    return this.invoiceService.findAllInvoiceWithZeroAmount(applicationId);
  }

  @Get('withZeroAmount/application/:applicationId')
  async findInvoiceWithZeroAmount(
    @Param('applicationId') applicationId: string,
  ) {
    return this.invoiceService.findInvoiceWithZeroAmount(applicationId);
  }

  @Get('invoice/applicant/:applicantUserId/invoiceStatus/:invoiceStatusId')
  async findAllInvoiceWithInvoiceStatusOfAnUser(
    @Param('applicantUserId') applicantUserId: string,
    @Param('invoiceStatusId') invoiceStatusId: string,
  ) {
    return this.invoiceService.findAllInvoiceWithInvoiceStatusOfAnUser(
      applicantUserId,
      invoiceStatusId,
    );
  }

  @Get('invoice/agency/:agencyCode')
  async findAllInvoiceByAgency(@Param('agencyCode') agencyCode: string) {
    return this.invoiceService.findAllInvoiceByAgency(agencyCode);
  }

  @Patch('invoice/:id')
  async updateInvoice(@Param('id') id: string, @Body() invoiceDto: InvoiceDto) {
    return this.invoiceService.updateInvoice(id, invoiceDto);
  }

  @Patch('invoice/cancel/:id')
  async cancelInvoice(@Param('id') id: string) {
    return this.invoiceService.cancelInvoice(id);
  }

  @Patch('invoice/refunded/:id')
  async refundedInvoice(@Param('id') id: string) {
    return this.invoiceService.refundedInvoice(id);
  }

  @Patch('invoice/invoiceStatus/:id')
  async updateInvoiceStatusOnInvoice(
    @Param('id') id: string,
    @Body() updateInvoiceDto: UpdateInvoiceDto,
  ) {
    return this.invoiceService.updateInvoiceStatusOnInvoice(
      id,
      updateInvoiceDto,
    );
  }

  @Delete('invoice/:id')
  async removeInvoice(@Param('id') id: string) {
    return this.invoiceService.removeInvoice(id);
  }

  @Get('invoice/invoiceNumber/search')
  async findInvoiceByInvoiceNumber(@Query('search') search: string) {
    const searchFields: (keyof Invoice)[] = ['invoiceNumber'];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const relations: string[] = [
      'invoiceStatus',
      'invoiceItems',
      'applications',
    ];
    return this.genericSearch3.search(
      this.invoiceSearchRepository,
      searchFields,
      search,
      relations,
    );
  }

  // @Get('invoice/:applicantUserId')
  // async getInvoicesByUserId(@Param('applicantUserId') applicantUserId: string) {
  //   return this.invoiceService.getInvoicesByApplicantUserId(applicantUserId);
  // }
  // @Get('invoice/:applicantUserId')
  // async getInvoicesByUserId(@Param('applicantUserId') applicantUserId: string) {
  //   return this.invoiceService.getInvoicesByApplicantUserId(applicantUserId);
  // }

  @Get('invoice/applicantUserId/search')
  async getInvoicesByUserId(@Query('search') search: string) {
    const searchFields: (keyof Invoice)[] = ['applicantUserId'];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const relations: string[] = [
      'invoiceStatus',
      'invoiceItems',
      'applications',
    ];
    return this.genericSearch3.search(
      this.invoiceSearchRepository,
      searchFields,
      search,
      relations,
    );
  }

  // receipt
  @Post('receipt')
  async CreateReceipt(@Body() receiptDto: ReceiptDto) {
    return this.invoiceService.createReceipt(receiptDto);
  }

  @Get('receipt')
  async findAllReceipt() {
    return this.invoiceService.findAllReceipt();
  }

  @Get('receipt/:id')
  async findOneReceipt(@Param('id') id: string) {
    return this.invoiceService.findOneReceipt(id);
  }

  @Patch('receipt/:id')
  async updateReceipt(@Param('id') id: string, @Body() receiptDto: ReceiptDto) {
    return this.invoiceService.updateReceipt(id, receiptDto);
  }

  @Delete('receipt/:id')
  async removeReceipt(@Param('id') id: string) {
    return this.invoiceService.removeReceipt(id);
  }

  // // Irembo
  // @Post('generate-invoice')
  // async generateInvoiceIrembo(
  //   @Body() invoice: Invoice,
  //   @Body('serviceNumber') serviceNumber: string,
  // ): Promise<[IremboResponseGetDto, IremboPaymentRequestInputDto]> {
  //   return this.invoiceService.generateInvoiceIrembo(invoice, serviceNumber);
  // }

  // @Put('update-invoice/:id')
  // async updateInvoiceIrembo(
  //   @Body() invoice: Invoice,
  //   @Param('id') serviceNumber: string,
  // ): Promise<IremboResponseGetDto> {
  //   return this.invoiceService.updateInvoiceIrembo(invoice, serviceNumber);
  // }

  // @Get('get-invoice/:id')
  // async getInvoiceIrembo(
  //   @Param('id') invoiceNumber: string,
  // ): Promise<IremboResponseGetDto> {
  //   return this.invoiceService.getInvoiceIrembo(invoiceNumber);
  // }

  @Post('updateToIrembo')
  @ApiTags('Invoices')
  @ApiBody({
    description: 'Invoice update payload',
    type: UpdateInvoiceDto,
    examples: {
      example: {
        value: {
          invoiceId: '12345',
          amount: 10000,
          expirationTime: '2024-11-28T16:04:30.281Z',
        },
      },
    },
  })
  async updateInvoiceToIrembo(
    @Body('invoiceId') invoiceId: string,
    @Body('amount') amount: number,
    @Body('expirationTime') expirationTime: string,
  ) {
    try {
      const result = await this.invoiceService.updateInvoiceToIrembo(
        invoiceId,
        amount,
        expirationTime,
      );
      return {
        message: 'Invoice updated successfully',
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('Error in InvoiceController:', error.message);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'An unexpected error occurred while updating the invoice.',
      );
    }
  }

  @Get('get-price-invoice')
  async getPrice(
    @Query('permitTypeId') permitTypeId: string,
    @Query('rangeNumber') rangeNumber: string,
  ): Promise<{ amount: number }> {
    // Validate inputs
    if (!permitTypeId) {
      throw new BadRequestException('permitTypeId is required');
    }
    if (!rangeNumber || isNaN(Number(rangeNumber))) {
      throw new BadRequestException('rangeNumber must be a valid number');
    }

    // Call the service method
    const amount = await this.invoiceService.getPriceByPermitTypeAndRange(
      permitTypeId,
      parseFloat(rangeNumber),
    );

    return { amount };
  }
}
