<div class="login-container">
  <div class="login-content">
    <div class="illust">
      <img class="logo" [routerLink]="['/']" src="assets/ikons/logo/NavLogo.svg" alt="">
      <img class="ill" src="assets/imgs/SVG/illustration.svg" alt="">
    </div>
    <div class="dtials">
      <form class="form" [formGroup]="saveForm" (ngSubmit)="onSubmit()">
        <div class="form-header">
          <h2 class="display-md">{{ "loginPage.title" | translate }}</h2>
        </div>
        <div class="form-body">
          <div class="form-input">
            <label>{{ "loginPage.email" | translate }} / {{ "loginPage.username" | translate }}</label>
            <div>
              <input class="flg" type="text" name="username" formControlName="username"
              appNoSpaces
                placeholder="<EMAIL>" />
            </div>
          </div>
          <div class="form-input">
            <label>{{ "loginPage.password" | translate }}</label>
            <div>
              <input class="flg" type="password" name="password" formControlName="password" 
              appNoSpaces
              placeholder="**********" />
            </div>
          </div>
        </div>
        <div class="form-footer">
          <a class="kbk-link" routerLink="/forgot-password">{{ "loginPage.forgotPassword" | translate }}</a>

          <button class="kbk-btn kbk-btn-lg kbk-btn-main" *ngIf="submitted" type="button">{{ "loginPage.waitmsg" | translate }}....</button>
          <button class="kbk-btn kbk-btn-lg kbk-btn-main" *ngIf="!submitted"
          [disabled]="saveForm.invalid"
            type="submit">{{ "loginPage.loginButton" | translate }}</button>
            <a class="kbk-link" routerLink="/register">{{ "loginPage.createAccount" | translate }}</a>
            <a class="kbk-link" routerLink="">{{ "loginPage.backHome" | translate }}</a>
        </div>
      </form>
    </div>
  </div>
</div>
