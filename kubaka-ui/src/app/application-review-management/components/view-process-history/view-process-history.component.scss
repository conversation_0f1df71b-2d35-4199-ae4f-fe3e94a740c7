// Define your SCSS variables
$blue-root: #007bff;
$radius-sm: 4px;
$slate-500: #64748b;
$blue-lighter: #eff9ff;
$blue-light: #def1ff;

.process-history-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #dee2e6;
  border-radius: $radius-sm;
  overflow: hidden;
  background-color: #fff;

  thead {
    background-color: #fff;

    th {
      padding: 1rem;
      text-align: left;
      font-size: 0.85rem;
      font-weight: 400;
      color: $slate-500;
      opacity: 0.75;
      border-bottom: 1px solid #dee2e6;

      &.action-column {
        width: 50px;
        text-align: center;
      }
    }
  }

  tbody {
    tr.process-row {
      cursor: pointer;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #dee2e6;

      &:hover {
        background-color: $blue-lighter;
      }

      // &.odd {
      //   background-color: $blue-light;
      // }

      &.expanded {
        background-color: $blue-light;
      }

      td {
        padding: 0.5rem;
        vertical-align: middle;
        font-size: 0.875rem;
        border-right: 1px solid #eee;

        &:last-child {
          border-right: none;
        }

        &.timeline-cell {
          position: relative;
          padding-left: 2.5rem;

          .timeline-indicator {
            position: absolute;
            left: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            border-radius: $radius-sm;
            border: calc(10px / 4) solid $blue-root;
            background: #f1f5f9;
            z-index: 2;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              top: 10px;
              width: 2px;
              height: 100vh;
              background-color: #f1f5f9;
              z-index: 1;
            }
          }
        }

        &.action-cell {
          text-align: center;
          width: 50px;

          .toggle-icon {
            width: 16px;
            height: 16px;
          }
        }
      }
    }

    tr.expanded-details {
      td {
        padding: 0;
        border-bottom: 1px solid #dee2e6;

        .expanded-content {
          padding: 1rem 2.5rem;
          background-color: #f8f9fa;

          .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));

            gap: 1rem;

            .detail-item {
              font-size: 0.875rem;

              strong {
                color: $slate-500;
                font-weight: 500;
              }

              span {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
                gap: 0.5rem;

                strong {
                  font-weight: 400;
                  color: $slate-500;
                }
              }
            }
            .checklist-list {
              font-size: 0.875rem;

              strong {
                color: $slate-500;
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }
}




.process-history-table tbody tr.process-row:nth-child(4n+2) td.timeline-cell .timeline-indicator {
  border-color: #40d1ff;
}

.process-history-table tbody tr.process-row:nth-child(4n+4) td.timeline-cell .timeline-indicator {
  border-color: #6366f1;
}

.process-history-table tbody tr.process-row:nth-child(4n+6) td.timeline-cell .timeline-indicator {
  border-color: #a855f7;
}

.process-history-table tbody tr.process-row:nth-child(4n+8) td.timeline-cell .timeline-indicator {
  border-color: #46ef76;
}

// Status badges
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: $radius-sm;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0e0e0;
  color: #333; 

  &.status-draft {
    background-color: #d1fae5;
    color: #065f46;
  }

  &.status-submitted {
    background-color: #fef3c7;
    color: #92400e;
  }

  &.status-accepted {
    background-color: #fee2e2;
    color: #991b1b;
  }

  &.status-pre-approval {
    background-color: #fef9c3;
    color: #b45309;
  }

  &.status-permitted {
    background-color: #d1e7dd;
    color: #0f5132;
  }

  &.status-assigned {
    background-color: #7c7fb4;
    color: #fff
  }

  &.status-request-for-action {
  background-color: #f117da;
  color: #721c24;
}
}

.process-steps {
  .step-list {
    &_item {
      $pos-x: 1rem;
      $poz-y: 0.5rem;
      $poz-color: #f1f5f9;
      $poz-st-color: $blue-root;
      $point-size: 10px;
      position: relative;
      // padding-left: $pos-x;
      padding: $poz-y $pos-x;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0.25rem;
        display: block;
        width: $point-size;
        aspect-ratio: 1;
        border-radius: $radius-sm;
        border: calc($point-size / 4) solid $poz-st-color;
        background: $poz-color;
        z-index: 2;
        margin-top: 0.15rem;
      }

      &::after {
        content: "";
        position: absolute;
        left: calc($point-size / 2);
        transform: translateX(-50%);
        top: 0.25rem;
        display: block;
        width: 2px;
        height: 100%;
        border-radius: $radius-sm;
        background-color: $poz-color;
        z-index: 1;
      }
    }

    &_row {
      margin-bottom: 0.2rem;

      h4 {
        font-size: 0.875rem;
        font-weight: 500;
      }

      span {
        display: block;
        color: #2b2b2b;
        font-size: 0.875rem;
      }

      &.step-ln {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
      }
    }

    &_dscb {
      padding: 0.5rem 0.75rem;
      border-radius: $radius-sm;
      background-color: #f1f5f9;

      p {
        font-size: 0.75rem;
        font-weight: 500;
        opacity: 0.75;
        line-height: 1.45;
      }
    }

    &_item:nth-child(2) {
      &::before {
        border-color: #40d1ff;
      }
    }

    &_item:nth-child(3) {
      &::before {
        border-color: #6366f1;
      }
    }

    &_item:nth-child(4) {
      &::before {
        border-color: #a855f7;
      }
    }

    &_item:nth-child(5) {
      &::before {
        border-color: #d946ef;
      }
    }

    &_item:nth-child(6) {
      &::before {
        border-color: #ec4899;
      }
    }

    &_item:nth-child(7) {
      &::before {
        border-color: #14b2ff;
      }
    }

    &_item:nth-child(8) {
      &::before {
        border-color: #818cf8;
      }
    }

    &_item:nth-child(9) {
      &::before {
        border-color: #c084fc;
      }
    }

    &_item:last-child {
      padding-bottom: 0;

      &::before {
        border-color: #10b981;
      }
    }
  }

  .action-cell img {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .process-steps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 0.5rem 0rem;

    h3 {
      margin-bottom: 0.75rem;
      margin-left: 0.5rem;
      font-size: 0.875rem;
      color: #0099e8;
    }

    img {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }
}

.step-list_row {
  margin-bottom: 0;
  width: 100%;
  line-height: normal;

  &.step-ln {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.5rem;
    align-items: center;
  }

  h5 {
    font-size: 0.75rem;
    font-weight: 500;
    color: $slate-500;
    line-height: 1.4;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
  }

  h5 span {
    display: inline;
    color: #2b2b2b;
    font-size: 0.875rem;
    font-weight: normal;
    white-space: normal;
  }
}

.hder {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 15px;
  display: block;
  text-align: center;
}

.three-dots-button {
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  line-height: 1;

  &:hover {
    color: #343a40;
  }
}

@media (max-width: 768px) {
  .process-history-table {
    thead {
      display: none;
    }

    tbody tr.process-row {
      display: block;
      border: 1px solid #dee2e6;
      border-radius: $radius-sm;
      margin-bottom: 0.5rem;

      td {
        display: block;
        border: none;
        padding: 0.5rem;

        &:before {
          content: attr(data-label) ": ";
          font-weight: 500;
          color: $slate-500;
        }

        &.timeline-cell {
          padding-left: 0;

          .timeline-indicator {
            display: none;
          }
        }

        &.action-cell {
          text-align: right;
          margin-top: 0.5rem;
        }
      }
    }
  }
}