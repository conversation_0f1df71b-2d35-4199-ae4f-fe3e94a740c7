import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { ChatMessageComponent } from '../chat-message/chat-message.component';
import { ChatBotComponent } from '../chat-bot/chat-bot.component';


const routes: Routes = [
    {
        path: "chat-room", component: ChatMessageComponent,
    },
    // {
    //     path: "chat-bot", component: ChatBotComponent,
    // },
];


@NgModule({
    declarations: [
        ChatMessageComponent
    ],
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        SharedModule
    ]
})
export class NotificationModule { }
