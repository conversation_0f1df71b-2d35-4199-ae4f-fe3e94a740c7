<div class="app-dash">
    <div class="container">
        <div class="dash-flex">

            <div class="app-main">
                <div class="app-stats" *ngFor="let dt of inputData">
                    <div class="app-stats_card">
                    <!-- <div class="app-stats_card" (click)="goToApplication(dt)"> -->
                        <div class="icon">
                            <img src="assets/ikons/colored/ikon-folder.svg" alt="" />
                        </div>
                        <div class="dtail">
                            <span class="dtail-num">Associated UPI: {{dt.upi}}</span>
                            <span class="dtail-title">Plot size:{{dt.plotSize}} </span>
                            <span class="dtail-title">Owner:{{dt.ownerFullName}}</span>
                            <span class="dtail-title">National ID: {{dt.ownerIdNo}} </span>
                            <span class="dtail-num">Main UPI: {{dt.projects.upi}}</span>
                            <span class="dtail-title">Main UPI's owner names: {{dt.projects.ownerFullName}} </span>
                            <span class="dtail-title">Main UPI's Id: {{dt.projects.ownerIdNo}} </span>
                            <span class="dtail-title">Project Name: {{dt.projects.projectName}} </span>
                            <span class="dtail-title">Project Description: {{dt.projects.projectDescription}} </span>
                        </div>
                        <!-- <div class="dtail">
                            <span class="dtail-title">Owner:{{dt.ownerFullName}}</span>
                            <span class="dtail-num">ID{{dt.ownerIdNo}} </span>
                        </div>
                        <div class="dtail">
                            <span class="dtail-title">Main UPI{{dt.projects.upi}}</span>
                            <span class="dtail-num">Main UPI's owner names:{{dt.projects.ownerFullName}} </span>
                            <span class="dtail-num">Main UPI's Id'{{dt.projects.ownerIdNo}} </span>
                        </div>
                        <div class="dtail">
                            <span class="dtail-num">Project Name{{dt.projects.projectName}} </span>
                            <span class="dtail-num">Project Description{{dt.projects.projectDescription}} </span>
                        </div> -->
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
