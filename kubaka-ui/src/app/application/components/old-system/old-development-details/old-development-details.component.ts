import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { jwtDecode } from "jwt-decode";
import { AppConfig } from 'src/app/app.config';
import { ApplicationService } from 'src/app/application/services/application.service';
import { StepperService } from 'src/app/application/services/stepper.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-old-development-details',
  templateUrl: './old-development-details.component.html',
  styleUrls: ['./old-development-details.component.scss']
})
export class OldDevelopmentDetailsComponent {
  submitted!: boolean;
  applicationDetail: any = {};
  @Output() backToParent = new EventEmitter();
  userForm!: UntypedFormGroup;
  outputData: any;
  technologySurveys: any[] = [];
  currentUser: any = {};
  upiInfo: any = {};



  constructor(
    private stepperService: StepperService,
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private sessionService: SessionService,
    private utilService: UtilService,
    private cdr: ChangeDetectorRef,
    private appConfig: AppConfig) {

    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;




    this.initialiaze();
    this.loadLookups();

  }

  ngOnInit(): void {
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_old_INFO) as any);
    if (this.upiInfo && this.upiInfo?.isFromOldSystem && this.upiInfo.plan_id) {
      this.cdr.detectChanges();
      let agencyCodeSplitted = this.upiInfo.Agency.split(' ');
      this.getAgencyByCode(agencyCodeSplitted[0].toUpperCase());
      this.initializeDataFromOldSystem();
      this.checkIfProjectExits(this.upiInfo.Plot_No);
    }
    
  }

  checkIfProjectExits(upi: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + upi)
      .subscribe(
        data => {
          if (data.items.length > 0 && data.items[0] && !this.upiInfo?.isFromOldSystem) {
            this.onNext()
            this.cdr.detectChanges();
          }
          // Means data does not exit in NEW BPMIS and first create it's project in bpmis
          else
            if (this.upiInfo?.isFromOldSystem) {

              // Call page to create project of UPI from old system
              // this.router.navigate([])
            }
        }
      )
  }

  getAgencyByCode(agencyCode: any) {
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agency/code/search?search=' + agencyCode)
      .subscribe(
        data => {
          this.userForm.controls['agencyId'].setValue(data.items[0]?.id);
        },
        error => { }
      )
  }

  getAgencyByDistrictCode(districtCode: any) {
    this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + districtCode)
      .subscribe(
        data => {
          this.userForm.controls['agencyId'].setValue(data.id);
        },
        error => { }
      )
  }



  initializeDataFromOldSystem() {
    this.userForm = this.formBuilder.group({
      upi: [this.upiInfo.Plot_No],
      isFromOldSystem: [this.upiInfo.isFromOldSystem],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      centralCoordinateX: [""],
      centralCoordinateY: [""],
      villageCode: [""],
      villageName: [""],
      cellCode: [""],
      cellName: [""],
      sectorCode: [""],
      sectorName: [""],
      districtCode: [""],
      districtName: [this.upiInfo.District],
      provinceCode: [""],
      provinceName: [""],
      selectedUse: [this.upiInfo.Building_Type],
      selectedCategoryUse: [this.upiInfo.Building_Type],
      projectName: [this.upiInfo.Name_Of_Project],
      projectDescription: [this.upiInfo.Capacity_Info],
      plotSize: [this.upiInfo.Plot_size],
      originalPlotSize: [this.upiInfo.Plot_size],
      userId: [this.currentUser.userId],
      agencyId: [""],
      technologySurveyId: [""],
      projectStatusId: [""],
    }
    );
    this.getProjectStatus();
  }



  initializeDataFromLandIntergration() {
    this.userForm = this.formBuilder.group({
      // from land api
      upi: [this.upiInfo.upi],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      // from land api
      centralCoordinateX: [this.upiInfo.centralCoordinate.x],
      centralCoordinateY: [this.upiInfo.centralCoordinate.y],
      villageName: [this.upiInfo.parcelLocation.village.villageName],
      cellCode: [this.upiInfo.parcelLocation.cell.cellCode],
      sectorCode: [this.upiInfo.parcelLocation.sector.sectorCode],
      districtCode: [this.upiInfo.parcelLocation.district.districtCode],
      provinceCode: [this.upiInfo.parcelLocation.province.provinceCode],
      cellName: [this.upiInfo.parcelLocation.cell.cellName],
      sectorName: [this.upiInfo.parcelLocation.sector.sectorName],
      districtName: [this.upiInfo.parcelLocation.district.districtName],
      provinceName: [this.upiInfo.parcelLocation.province.provinceName],
      selectedUse: [this.upiInfo.applicationName],
      selectedCategoryUse: [this.upiInfo.seletedCategoryUse],

      // fill
      projectName: [""],
      projectDescription: [""], // from first page
      plotSize: [this.upiInfo.size, [Validators.required]],
      originalPlotSize: [this.upiInfo.size,],
      userId: [this.currentUser.userId],
      agencyId: [""],   //this is for cok
      technologySurveyId: [""],
      projectStatusId: [""],
      isFromOldSystem: [''],
    }
    );

    this.getProjectStatus();
  }




  getProjectStatus() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus/code/search?search=' + VALUE.PROJECT_STATUS_CREATED_CODE)
      .subscribe(
        data => { this.userForm.controls['projectStatusId'].setValue(data.items[0].id); },
        error => { }
      )
  }









  onPrev(): void {
    this.stepperService.prevStep();
  }

  onNext(): void {
    this.stepperService.nextStep();
  }


  loadLookups() {
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.TECHNOLOGYSURVEY)
      .subscribe(
        data => { this.technologySurveys = data; }
      )
  }


  initialiaze() {
    this.userForm = this.formBuilder.group({
      upi: [""],
      isUnderMortgage: [false],
      isUnderRestriction: [false],
      centralCoordinateX: [""],
      centralCoordinateY: [""],
      villageName: [""],
      cellCode: [""],
      sectorCode: [""],
      districtCode: [""],
      provinceCode: [""],
      cellName: [""],
      sectorName: [""],
      districtName: [""],
      provinceName: [""],
      selectedUse: [""],
      selectedCategoryUse: [""],
      projectName: [""],
      projectDescription: [""],
      plotSize: ["", [Validators.required]],
      originalPlotSize: [""],
      userId: [""],
      agencyId: [""],
      technologySurveyId: [""],
      projectStatusId: [""]
    }
      // , { validator: this.buildUpAreaValidator }
    );
  }






  onSubmit() {
    if (this.userForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill username and password', "bottom", "center")
      return;
    } else {

      this.userForm.controls['isFromOldSystem'].setValue(this.upiInfo?.isFromOldSystem);
      this.userForm.controls['originalPlotSize'].setValue(+this.userForm.value.plotSize);
      this.userForm.value.plotSize = this.userForm.controls['plotSize'].setValue(this.userForm.value.plotSize);
      this.submitted = true;
      this.applicationService.saveWithPath(this.userForm.value, environment.applicationUrl + 'application/project')
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your project saved successfully", "bottom", "center");
            if (this.upiInfo?.isFromOldSystem) {
              // this.router.navigate(['/account/application/other-application/', this.userForm.value.upi]);
              this.router.navigate(['/account/application/other-application/', data.id]);
            } else {
              this.backToParent.emit(data);
            }

            this.submitted = false;
          }, error => {
            this.submitted = false;
            if (error?.error === 'UPI already exists') {
              this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.userForm.value.upi)
                .subscribe(
                  data => {
                    this.router.navigate(['/account/application/new-application-development-detail/', data.items[0].id]);
                  }
                )
            }
          }
        )
    }
  }
}
