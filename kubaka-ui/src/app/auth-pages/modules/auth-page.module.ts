import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { ForgotPasswordComponent } from '../components/forgot-password/forgot-password.component';
import { LandingPageComponent } from '../components/landing-page/landing-page.component';
import { LoginComponent } from '../components/login/login.component';
import { MessageAfterLoginComponent } from '../components/message-after-login/message-after-login.component';
import { RegisterInfoDetailComponent } from '../components/register-info-detail/register-info-detail.component';
import { RegisterComponent } from '../components/register/register.component';
import { ResetPasswordComponent } from '../components/reset-password/reset-password.component';
import { ServiceInfoPageComponent } from '../components/service-info-page/service-info-page.component';
import { TrackApplicationStatusComponent } from '../components/track-application-status/track-application-status.component';
import { AuthPageRoutingModule } from './auth-page-routing.module';
import { FactorLoginComponent } from '../components/factor-login/factor-login.component';



@NgModule({
  declarations: [
    LandingPageComponent,
    LoginComponent,
    FactorLoginComponent,
    RegisterComponent,
    ForgotPasswordComponent,
    ServiceInfoPageComponent,
    TrackApplicationStatusComponent,
    RegisterInfoDetailComponent,
    ResetPasswordComponent,
    MessageAfterLoginComponent
  ],
  imports: [
    CommonModule,
    AuthPageRoutingModule,
    SharedModule
  ]
})
export class AuthPageModule { }
