import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { DashboardComponent } from '../component/dashboard/dashboard.component';
import { AnalyticDashboardComponent } from '../component/analytic-dashboard/analytic-dashboard.component';
import { CountryLevelDashboardComponent } from '../component/country-level-dashboard/country-level-dashboard.component';

@NgModule({
  declarations: [
    DashboardComponent,
    AnalyticDashboardComponent,
    CountryLevelDashboardComponent
  ],
  imports: [
    CommonModule,
    DashboardRoutingModule,
    SharedModule
  ]
})
export class DasbaordModule { }
