import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateReadStatusDto {
  @ApiProperty({ description: 'The field messageId is required' })
  @IsNotEmpty({ message: 'The field messageId cannot be empty' })
  readonly messageId: string;

  @ApiProperty({ description: 'The field isRead is required' })
  @IsNotEmpty({ message: 'The field isRead cannot be empty' })
  readonly isRead: boolean;
}
