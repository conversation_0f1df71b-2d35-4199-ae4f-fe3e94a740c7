// import { UpiInfoComponent } from 'src/app/auth-pages/components/upi-info/upi-info.component';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Observable, catchError, map } from 'rxjs';
import axios from 'axios';
import config from '../config';
@Injectable()
export class IntegrationsService {
  private accessTokenRRA: string;
  private refreshedTokenRRA: string;
  private accessTokenExpirationTime: Date;

  private token: string;
  private tokenExpiration: Date;

  constructor(private readonly httpService: HttpService) {}

  // Check in old BPMIS with application Number  and status approved
  // Check in new BPMIS

  // search in old BPMIS
  searchInOldBPMIS(number: string) {
    const url = `${config.host.urlOLDBPMIS}/application-by-number-and-status?number=${number}&status=Approved`;
    // const url = `http://testing.kubaka.gov.rw/bpmis/application-by-number-and-status?number=${number}&status=Approved`;
    return this.httpService.get(url).pipe(
      map((response) => {
        if (!response.data || response.data.length === 0) {
          throw new HttpException('No Data found', HttpStatus.BAD_REQUEST);
        }
        return response.data;
      }),
      catchError((error) => {
        if (
          error instanceof HttpException &&
          error.getStatus() === HttpStatus.BAD_REQUEST
        ) {
          throw error;
        }
        throw new HttpException(
          `Error searching certificate: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }

  // Land search
  async searchUPIOnLIS(UPI: string): Promise<any> {
    const url = `${config.host.urlLIS}/land?upi=${UPI}`;
    return this.httpService.get(url).pipe(
      map((response) => response.data),
      catchError((error) => {
        throw new HttpException(
          `Error searching upi: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }

  // Regulations search
  async searchZone(zone: string): Promise<any> {
    const url = `${config.host.urlZoning}/land/zones/${zone}/regulations`;
    return this.httpService.get(url).pipe(
      map((response) => response.data),
      catchError((error) => {
        throw new HttpException(
          `Error searching zoning: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }

  // Arch search
  async getAllArch() {
    try {
      const ArchitectResponse = await axios.get(
        `${config.host.urlArchitect}/wp-admin/admin-ajax.php?action=wp_ajax_ninja_tables_public_action&table_id=1650&target_action=get-all-data&default_sorting=old_first&skip_rows=0&limit_rows=0&ninja_table_public_nonce=d49fd55f98`,
      );
      return ArchitectResponse.data;
    } catch (error) {
      throw new HttpException('Server Down', HttpStatus.OK);
    }
  }

  // Arch search with registration number
  async searchArchitectByRegIndex(registrationIndex: string) {
    try {
      const ArchitectResponse = await axios.get(
        `${config.host.urlArchitect}/wp-admin/admin-ajax.php?action=wp_ajax_ninja_tables_public_action&table_id=1650&target_action=get-all-data&default_sorting=old_first&skip_rows=0&limit_rows=0&ninja_table_public_nonce=d49fd55f98`,
      );
      const architects = ArchitectResponse.data;
      const filteredArchitect = architects.find(
        (arch) => arch.value.registrationindex === registrationIndex,
      );
      if (!filteredArchitect) {
        throw new HttpException('Architect not found', HttpStatus.BAD_REQUEST);
      }
      return filteredArchitect;
    } catch (error) {
      throw new HttpException('Server Down', HttpStatus.OK);
    }
  }

  // Arch search with registration number with condition
  async searchArchitectByRegIndexWithCondition(registrationIndex: string) {
    try {
      const ArchitectResponse = await axios.get(
        `${config.host.urlArchitect}/wp-admin/admin-ajax.php?action=wp_ajax_ninja_tables_public_action&table_id=1650&target_action=get-all-data&default_sorting=old_first&skip_rows=0&limit_rows=0&ninja_table_public_nonce=d49fd55f98`,
      );
      const architects = ArchitectResponse.data;
      const filteredArchitect = architects.find(
        (arch) => arch.value.registrationindex === registrationIndex,
      );
      if (!filteredArchitect) {
        throw new HttpException('Architect not found', HttpStatus.BAD_REQUEST);
      }
      if (
        !filteredArchitect.value.number ||
        filteredArchitect.value.number.trim() === ''
      ) {
        throw new HttpException(
          'Please contact the institution and update your identification number',
          HttpStatus.BAD_REQUEST,
        );
      }
      return filteredArchitect;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Engineer search
  searchEngineer(regNumber: string): Observable<boolean> {
    const url = `${config.host.urlEngineer}/members/memberships/byRegNumber?registrationNumber=${regNumber}`;
    return this.httpService.get(url).pipe(
      map((response) => response.data),
      catchError((error) => {
        throw new HttpException(
          `Error searching engineer: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }

  // Engineer search with some conditions
  searchEngineerWithConditions(regNumber: string): Observable<boolean> {
    const url = `${config.host.urlEngineer}/members/memberships/byRegNumber?registrationNumber=${regNumber}`;
    return this.httpService.get(url).pipe(
      map((response) => {
        const data = response.data;
        if (!data.identificationNumber) {
          throw new HttpException(
            'Please contact the institution and update your identification number',
            HttpStatus.BAD_REQUEST,
          );
        }
        return data;
      }),
      catchError((error) => {
        throw new HttpException(
          `Error searching engineer: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }

  //NIDA integration from Mininfra & at RISA
  async getNIDAInfo(documentNumber: string): Promise<any> {
    try {
      const NiwResponse = await axios.post(
        `${config.host.urlNIDA}/nidInfo`,
        // 'https://testing.kubaka.gov.rw/testing-api/nidInfo',
        { documentNumber },
      );
      return NiwResponse.data;
    } catch (error) {
      console.error('Error getting data:', error);
      throw new HttpException(
        'Error getting data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  //RRA integration from Mininfra with no good practice coz of body in get
  async getRRAInfo(upi: string): Promise<any> {
    try {
      const RRAResponse = await axios.get(
        `${config.host.urlRRA}/checkPropertyTax`,
        // `https://testing.kubaka.gov.rw/testing-api/checkPropertyTax`,
        {
          data: {
            upi: upi,
          },
        },
      );
      return RRAResponse.data;
    } catch (error) {
      console.error('Error getting data:', error);
      throw new HttpException('Server Down', HttpStatus.OK);
    }
  }

  // //RRA integration from Mininfra with no good practice coz of body in get
  // async getRRAInfo(upi: string): Promise<any> {
  //   try {
  //     const RRAResponse = await axios.get(
  //       `${config.host.urlRRA}/checkPropertyTax`,
  //       // `https://testing.kubaka.gov.rw/testing-api/checkPropertyTax`,
  //       {
  //         data: {
  //           upi: upi,
  //         },
  //       },
  //     );
  //     return RRAResponse.data;
  //   } catch (error) {
  //     console.error('Error getting data:', error);
  //     throw new HttpException(
  //       'Error getting data',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  //RDB integration from Mininfra with no good practice coz of body in post
  async searchEIAInfo(upi: string) {
    try {
      const EIAResponse = await axios.post(
        'http://testing.kubaka.gov.rw/testing-api/verify/eia/',
        { upi },
      );
      return EIAResponse.data;
    } catch (error) {
      throw new HttpException('Server Down', HttpStatus.OK);
    }
  }

  // //RDB integration from Mininfra with no good practice coz of body in post
  // async searchEIAInfo(upi: string) {
  //   try {
  //     const EIAResponse = await axios.post(
  //       // `${config.host.urlRDB}/verify/eia/`,
  //       'http://testing.kubaka.gov.rw/testing-api/verify/eia/',
  //       { upi },
  //     );
  //     return EIAResponse.data;
  //   } catch (error) {
  //     throw new HttpException(
  //       'Error getting data',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // PhoneNumber search
  searchPhone(phoneNumber: string): Observable<boolean> {
    const url = `${config.host.urlRURA}/` + phoneNumber;
    return this.httpService.get(url).pipe(
      map((response) => response.data),
      catchError((error) => {
        throw new HttpException(
          `Error searching phoneNumber: ${error.message}`,
          HttpStatus.BAD_REQUEST,
        );
      }),
    );
  }
}
