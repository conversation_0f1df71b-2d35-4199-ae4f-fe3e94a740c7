import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { SessionService } from 'src/app/authentication-services/session.service';
import { UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../services/application.service';

@Component({
  selector: 'app-application-detail',
  templateUrl: './application-detail.component.html',
  styleUrls: ['./application-detail.component.scss'],
})
export class ApplicationDetailComponent {
  loading!: boolean;
  outputData: any;
  documents: any[] = [];

  additionalDocuments: any[] = [];

  applicationDetail: any = {};
  currentUser: any = {};

  paramsId: any;

  projectId: any;
  applicationId: any;
  applicationStatusCode: any;
  associatedUpis: any[] = [];
  certificateDetail: any = {};


  constructor(
    private applicationService: ApplicationService,
    private utilService: UtilService,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private sessionService: SessionService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (
      jwtDecode(this.currentUser.data.token.access_token) as any
    ).UserId;

    route.params.subscribe((param: any) => {
      this.applicationService
        .findAllWithPath(
          environment.applicationUrl +
            'application/application/AllDetails/' +
            param.id
        )
        .subscribe((data) => {
          this.applicationDetail = data[0];
          this.projectId = this.applicationDetail.projects.id;
          console.log('this.projectId');
          console.log(this.projectId);
          console.log(this.applicationDetail);
          this.applicationId = this.applicationDetail.id;
          console.log('this.applicationId');
          console.log(this.applicationId);
          this.applicationStatusCode = this.applicationDetail.applicationStatus.code;
          console.log('this.applicationStatusCode');
          console.log(this.applicationStatusCode);
          if(this.applicationStatusCode==="CTFD"){
            // fetch certificate detail
            this.applicationService
              .findAllWithPath(
                environment.applicationUrl + 'certificate/certificate/application/' + this.applicationId
              )
              .subscribe((data) => {
                this.certificateDetail = data;
                console.log('certificateDetail');
                console.log(this.certificateDetail);
              }
              );

          }

                     // associated upi
                     const project = this.projectId;
                     this.applicationService
                     .findAllWithPath(
                       environment.applicationUrl +
                       'application/associatedUPI/ByProject/' +
                       project
                     )
                     .subscribe(
                       (data) => {
                         this.associatedUpis = data;
                       },
                       (error) => { }
                     );

        });

      // req doc upload

      this.applicationService
        .findAllWithPath(
          environment.documentUrl + 'DocMgt/documents/' + param.id
        )
        .subscribe((data) => {
          this.documents = data;
        });

      // additional upload
      this.applicationService
        .findAllWithPath(
          environment.documentUrl +
            'DocMgt/documents/uploadAdditional/' +
            param.id
        )
        .subscribe(
          (data) => {
            this.additionalDocuments = data;
          },
          (error) => {}
        );
    });
  }


  viewDocument(component: any, doc: any, sizeParams: any) {
    this.loading = true;
    this.applicationService
      .findAllWithPath(
        environment.documentUrl + 'DocMgt/' + doc.fileName + '/base64'
      )
      .subscribe(
        (data) => {
          this.loading = false;
          if (this.utilService.isExcelFile(data.base64Data)) {
            this.utilService.downloadExcel(data.base64Data);
          } else {
            this.outputData = data;
            console.log(data);
            this.outputData.currentId = doc.id;
            // this.outputData.fileName = doc.fileName;
            this.outputData.fileName = doc.fileName; // still the full filename with extension
            this.outputData.name = doc.name || this.extractNameFromFileName(doc.fileName);
            console.log(doc.fileName);
            this.modalService.open(component, {
              size: sizeParams,
              centered: true,
            });
          }
        },
        (error) => {
          this.loading = false;
        }
      );
  }

  clickToContinue() {
    // this.router.navigate(['/account/application/resume-application', this.applicationDetail.id]);
    if (this.applicationDetail.permitTypes.code === 'NCP') {
      this.router.navigate([
        'account/application/new-application-development-detail',
        this.applicationDetail.projects.id,
      ]);
    } else if (this.applicationDetail.permitTypes.code !== 'NCP') {
      this.router.navigate([
        '/account/application/other-application/' +
          this.applicationDetail.permitTypes.id +
          '/' +
          this.applicationDetail.projects.id,
      ]);
    }
  }
  extractNameFromFileName(fileName: string): string {
    return fileName.split('.').slice(0, -1).join('.') || 'Document';
  }

  loadAssociatedUpis(projectId: any ) {
    console.log('.....')
    console.log(this.projectId)
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
        'application/associatedUPI/ByProject/' +
        projectId
      )
      .subscribe(
        (data) => {
          this.associatedUpis = data;
        },
        (error) => { }
      );
  }
}
