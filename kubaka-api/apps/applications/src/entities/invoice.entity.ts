import { AbstractEntity } from '@app/common';
import {
  Entity,
  Column,
  OneToMany,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Application } from './application.entity';
import { PermitType } from './settings.entity';

@Entity()
export class InvoiceStatus extends AbstractEntity<InvoiceStatus> {
  @Column()
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => Invoice, (invoice) => invoice.invoiceStatus)
  invoices: Invoice[];
}

// @Entity()
// export class Price extends AbstractEntity<Price> {
//   @Column()
//   amount: number;

//   @Column({ type: 'varchar', nullable: true })
//   range: string;

//   @Column({ type: 'varchar', nullable: true })
//   userId: string;

//   @OneToMany(() => InvoiceItem, (invoiceItem) => invoiceItem.prices)
//   invoiceItems: InvoiceItem[];

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;

//   @UpdateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public updated_at!: Date;
// }
@Entity()
export class Price extends AbstractEntity<Price> {
  @Column()
  amount: number;

  @Column({ type: 'varchar', nullable: true })
  rangeInSqmMin: string;

  @Column({ type: 'varchar', nullable: true })
  rangeInSqmMax: string;

  // @Column({ type: 'varchar', nullable: true })
  // permitTypeId: string;
  @ManyToOne(() => PermitType, (permitType) => permitType.price)
  permitTypes: PermitType;

  @Column({ type: 'varchar', nullable: true })
  userId: string;

  @OneToMany(() => InvoiceItem, (invoiceItem) => invoiceItem.prices)
  invoiceItems: InvoiceItem[];

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}

@Entity()
export class InvoiceType extends AbstractEntity<InvoiceType> {
  @Column()
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @OneToMany(() => Invoice, (invoice) => invoice.invoiceTypes)
  invoices: Invoice[];
}

@Entity()
export class InvoiceItem extends AbstractEntity<InvoiceItem> {
  @Column()
  name: string;

  @ManyToOne(() => Price, (price) => price.invoiceItems)
  prices: Price[];

  @OneToMany(() => Invoice, (invoice) => invoice.invoiceItems)
  invoices: Invoice[];
}

@Entity()
export class Invoice extends AbstractEntity<Invoice> {
  @Column({ type: 'varchar' })
  invoiceNumber: string;

  @ManyToOne(() => Application, (application) => application.invoices)
  applications: Application;

  @Column({ type: 'varchar' })
  agencyCode: string;

  @Column({ type: 'varchar', nullable: true })
  paymentAccountIdentifier: string;

  @Column()
  amount: string;

  @Column({ type: 'varchar' })
  transactionNumber: string;

  @ManyToOne(() => InvoiceType, (invoiceType) => invoiceType.invoices)
  invoiceTypes: InvoiceType[];

  @ManyToOne(() => InvoiceItem, (invoiceItem) => invoiceItem.invoices)
  invoiceItems: InvoiceItem[];

  @ManyToOne(() => InvoiceStatus, (invoiceStatus) => invoiceStatus.invoices)
  invoiceStatus: InvoiceStatus;

  // @ManyToOne(
  //   () => ApplicationStatus,
  //   (applicationStatus) => applicationStatus.applications,
  // )
  // applicationStatus: ApplicationStatus;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;

  // Irembo request
  @Column({ nullable: true })
  applicantUserId: string;

  @Column({ nullable: true })
  userId: string;

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  dueDate: Date;

  @Column({ nullable: true })
  externalReferenceNumber: string;

  /// 0: pending , 1: paid , 2: refunded ==> IREMBO
  @Column({ default: '0', nullable: true })
  paymentStatus: string;
}

@Entity()
export class Receipt extends AbstractEntity<Receipt> {
  @Column({ type: 'varchar' })
  invoiceNumber: string;

  @Column({ type: 'varchar' })
  transactionId: string;

  @Column({ type: 'varchar' })
  paymentStatus: string;

  @Column({ type: 'varchar' })
  currency: string;

  @Column({ type: 'varchar' })
  paidAt: string;

  @Column({ type: 'varchar' })
  customer: string;

  @Column({ type: 'varchar' })
  amount: string;

  @Column({ type: 'varchar' })
  paymentMethod: string;
}
