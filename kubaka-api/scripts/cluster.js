const cluster = require('cluster');
const os = require('os');
const { spawn } = require('child_process');

// Get number of CPU cores dynamically
const numCPUs = os.cpus().length;
const appName = process.argv[2] || 'applications';

console.log(`System has ${numCPUs} CPU cores available`);

if (cluster.isMaster) {
  console.log(`Master process ${process.pid} is running`);
  console.log(`Starting ${appName} with ${numCPUs} worker processes`);

  // Fork workers for each CPU core
  for (let i = 0; i < numCPUs; i++) {
    const worker = cluster.fork({
      WORKER_ID: i + 1,
      CPU_CORES: numCPUs,
      APP_NAME: appName,
      WORKER_INDEX: i
    });
    console.log(`Worker ${worker.process.pid} started on CPU core ${i + 1}`);
  }

  // Handle worker exit and restart
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
    const newWorker = cluster.fork({
      WORKER_ID: worker.id,
      CPU_CORES: numCPUs,
      APP_NAME: appName,
      WORKER_INDEX: worker.id - 1
    });
    console.log(`New worker ${newWorker.process.pid} started`);
  });

  // Monitor cluster health
  cluster.on('online', (worker) => {
    console.log(`Worker ${worker.process.pid} is online`);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('Master received SIGTERM, shutting down workers gracefully...');
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    process.exit(0);
  });

  process.on('SIGINT', () => {
    console.log('Master received SIGINT, shutting down workers gracefully...');
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    process.exit(0);
  });

} else {
  // Worker process
  const workerId = process.env.WORKER_ID;
  const workerIndex = parseInt(process.env.WORKER_INDEX) || 0;
  const cpuCores = process.env.CPU_CORES;
  const appName = process.env.APP_NAME;
  
  console.log(`Worker ${process.pid} (ID: ${workerId}) started for ${appName}`);

  // Get base port from environment or use defaults
  const getBasePort = (appName) => {
    const portMap = {
      'applications': 3000,
      'auth': 3001,
      'payments': 3003,
      'notifications': 3004,
      'documents': 3005,
      'integrations': 3007
    };
    return portMap[appName] || 3000;
  };

  const basePort = getBasePort(appName);
  
  // For cluster mode, only the first worker uses the main port
  // Other workers will use different ports or run in background mode
  const isMainWorker = workerIndex === 0;
  
  if (isMainWorker) {
    // Main worker uses the original port
    console.log(`Main worker ${process.pid} using port ${basePort}`);
    
    const child = spawn('pnpm', ['run', 'start:dev', appName], {
      stdio: 'inherit',
      env: { 
        ...process.env, 
        NODE_ENV: 'development',
        WORKER_ID: workerId,
        CPU_CORES: cpuCores,
        PORT: basePort
      }
    });

    child.on('exit', (code) => {
      console.log(`Main worker ${process.pid} exited with code ${code}`);
      process.exit(code);
    });

    // Handle signals for graceful shutdown
    process.on('SIGTERM', () => {
      console.log(`Main worker ${process.pid} received SIGTERM`);
      child.kill('SIGTERM');
    });

    process.on('SIGINT', () => {
      console.log(`Main worker ${process.pid} received SIGINT`);
      child.kill('SIGINT');
    });
  } else {
    // Secondary workers run in background mode without port binding
    console.log(`Background worker ${process.pid} running without port binding`);
    
    // For secondary workers, we'll run them in a way that doesn't bind to ports
    // This could be for background processing, queue workers, etc.
    const child = spawn('pnpm', ['run', 'start:dev', appName], {
      stdio: 'inherit',
      env: { 
        ...process.env, 
        NODE_ENV: 'development',
        WORKER_ID: workerId,
        CPU_CORES: cpuCores,
        PORT: basePort + workerIndex, // Use different port for each worker
        BACKGROUND_WORKER: 'true'
      }
    });

    child.on('exit', (code) => {
      console.log(`Background worker ${process.pid} exited with code ${code}`);
      process.exit(code);
    });

    // Handle signals for graceful shutdown
    process.on('SIGTERM', () => {
      console.log(`Background worker ${process.pid} received SIGTERM`);
      child.kill('SIGTERM');
    });

    process.on('SIGINT', () => {
      console.log(`Background worker ${process.pid} received SIGINT`);
      child.kill('SIGINT');
    });
  }
} 