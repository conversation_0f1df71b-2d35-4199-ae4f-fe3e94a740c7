const net = require('net');
const { exec } = require('child_process');

// Ports used by your applications
const ports = {
  applications: 3000,
  auth: 3001,
  payments: 3003,
  notifications: 3004,
  documents: 3005,
  integrations: 3007
};

// Check if a port is in use
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve({ port, available: true });
      });
      server.close();
    });
    
    server.on('error', () => {
      resolve({ port, available: false });
    });
  });
}

// Get process using a port
function getProcessUsingPort(port) {
  return new Promise((resolve) => {
    exec(`lsof -i :${port}`, (error, stdout, stderr) => {
      if (error || !stdout) {
        resolve(null);
      } else {
        const lines = stdout.trim().split('\n');
        if (lines.length > 1) {
          const processInfo = lines[1].split(/\s+/);
          resolve({
            pid: processInfo[1],
            command: processInfo[0],
            user: processInfo[2]
          });
        } else {
          resolve(null);
        }
      }
    });
  });
}

// Kill process by PID
function killProcess(pid) {
  return new Promise((resolve) => {
    exec(`kill -9 ${pid}`, (error) => {
      resolve(!error);
    });
  });
}

async function main() {
  console.log('=== Port Conflict Checker ===\n');
  
  const results = [];
  
  // Check all ports
  for (const [app, port] of Object.entries(ports)) {
    const portCheck = await checkPort(port);
    const processInfo = portCheck.available ? null : await getProcessUsingPort(port);
    
    results.push({
      app,
      port,
      available: portCheck.available,
      process: processInfo
    });
    
    console.log(`${app.padEnd(15)} | Port ${port.toString().padStart(4)} | ${portCheck.available ? '✅ Available' : '❌ In Use'}`);
    
    if (processInfo) {
      console.log(`  └─ Process: ${processInfo.command} (PID: ${processInfo.pid}, User: ${processInfo.user})`);
    }
  }
  
  console.log('\n=== Summary ===');
  const conflicts = results.filter(r => !r.available);
  
  if (conflicts.length === 0) {
    console.log('✅ All ports are available!');
  } else {
    console.log(`❌ ${conflicts.length} port(s) have conflicts:`);
    
    for (const conflict of conflicts) {
      console.log(`\n${conflict.app} (Port ${conflict.port}):`);
      console.log(`  Process: ${conflict.process.command}`);
      console.log(`  PID: ${conflict.process.pid}`);
      console.log(`  User: ${conflict.process.user}`);
      console.log(`  Command to kill: kill -9 ${conflict.process.pid}`);
    }
    
    console.log('\n=== Solutions ===');
    console.log('1. Kill the conflicting processes:');
    for (const conflict of conflicts) {
      console.log(`   kill -9 ${conflict.process.pid}`);
    }
    
    console.log('\n2. Use optimized cluster mode (recommended):');
    console.log('   docker compose up --build');
    
    console.log('\n3. Use load-balanced mode:');
    for (const conflict of conflicts) {
      console.log(`   pnpm run start:load-balanced:${conflict.app}`);
    }
    
    console.log('\n4. Use single optimized worker:');
    for (const conflict of conflicts) {
      console.log(`   pnpm run start:optimized:${conflict.app}`);
    }
  }
  
  console.log('\n=== Current Node.js Processes ===');
  exec('ps aux | grep node | grep -v grep', (error, stdout, stderr) => {
    if (error || !stdout) {
      console.log('No Node.js processes found');
    } else {
      console.log(stdout);
    }
  });
}

// Auto-fix option
if (process.argv.includes('--fix')) {
  console.log('Attempting to auto-fix port conflicts...\n');
  
  main().then(async (results) => {
    const conflicts = results.filter(r => !r.available);
    
    if (conflicts.length > 0) {
      console.log('\nKilling conflicting processes...');
      
      for (const conflict of conflicts) {
        console.log(`Killing process ${conflict.process.pid} for ${conflict.app}...`);
        const killed = await killProcess(conflict.process.pid);
        console.log(killed ? '✅ Killed' : '❌ Failed to kill');
      }
      
      console.log('\nRe-checking ports...');
      setTimeout(() => {
        main();
      }, 2000);
    }
  });
} else {
  main();
} 