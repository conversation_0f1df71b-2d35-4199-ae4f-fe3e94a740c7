import { HttpClient } from "@angular/common/http";
import { Component } from "@angular/core";
// import { AgGridAngular } from "@ag-grid-community/angular";
// NOTE: Angular CLI does not support component CSS imports: angular-cli/issues/23273
// import "@ag-grid-community/styles/ag-grid.css";
// import "@ag-grid-community/styles/ag-theme-quartz.css";
import { AgGridAngular } from "@ag-grid-community/angular";
import { ClientSideRowModelModule } from "@ag-grid-community/client-side-row-model";
import {
  AgChartThemeOverrides,
  CellValueChangedEvent,
  ColDef,
  FirstDataRenderedEvent,
  GridReadyEvent,
  ModuleRegistry,
  SelectionChangedEvent,
  ValueFormatterParams
} from "@ag-grid-community/core";
import { GridChartsModule } from "@ag-grid-enterprise/charts-enterprise";
import { MenuModule } from "@ag-grid-enterprise/menu";
import { RowGroupingModule } from "@ag-grid-enterprise/row-grouping";
import { environment } from "src/environments/environment";
// import "./style.css";
ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  GridChartsModule,
  MenuModule,
  RowGroupingModule,
]);
@Component({
  selector: 'app-grid-charts-sample',
  standalone: true,
  imports: [AgGridAngular],
  templateUrl: './grid-charts-sample.component.html',
  styleUrls: ['./grid-charts-sample.component.scss']
})
export class GridChartsSampleComponent {
  public columnDefs: ColDef[] = [
    // different ways to define 'categories'
    { field: "athlete", width: 150, chartDataType: "category" },
    { field: "age", chartDataType: "category", sort: "asc" },
    { field: "sport" },
    // excludes year from charts
    { field: "year", chartDataType: "excluded" },
    // different ways to define 'series'
    { field: "gold", chartDataType: "series" },
    { field: "silver", chartDataType: "series" },
    { field: "bronze" }, // inferred as series by grid
  ];
  public defaultColDef: ColDef = {
    flex: 1,
  };
  public popupParent: HTMLElement | null = document.body;
  public chartThemeOverrides: AgChartThemeOverrides = {
    common: {
      title: {
        enabled: true,
        text: "Medals by Age",
      },
    },
    bar: {
      axes: {
        category: {
          label: {
            rotation: 0,
          },
        },
      },
    },
  };
  public rowData!: any[];
  public themeClass: string =
    "ag-theme-quartz";

  constructor(private http: HttpClient) {}

  onFirstDataRendered(params: FirstDataRenderedEvent) {
    params.api.createRangeChart({
      chartContainer: document.querySelector("#myChart") as HTMLElement,
      cellRange: {
        rowStartIndex: 0,
        rowEndIndex: 79,
        columns: ["age", "gold", "silver", "bronze"],
      },
      chartType: "groupedColumn",
      aggFunc: "sum",
    });
  }

  onGridReady(params: GridReadyEvent) {
    

    this.http
      .get<
        any[]
      >("https://www.ag-grid.com/example-assets/wide-spread-of-sports.json")
      .subscribe((data) => {
        this.rowData = data;
      });
  }
}