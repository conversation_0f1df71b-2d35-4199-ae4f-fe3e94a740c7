import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from '../component/dashboard/dashboard.component';
import { AnalyticDashboardComponent } from '../component/analytic-dashboard/analytic-dashboard.component';



const routes: Routes = [
    {
        path: '', component: DashboardComponent
    },
    { path: 'analytic-dashboard', component: AnalyticDashboardComponent}
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class DashboardRoutingModule { }
