import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-chat-message',
  templateUrl: './chat-message.component.html',
  styleUrls: ['./chat-message.component.scss']
})
export class ChatMessageComponent {
  currentUser: any;
  users: any[] = [];
  applicants: any[] = [];
  messages: any[] = [];
  messageForm!: FormGroup;
  userData: any = {};
  activeTab: string = 'Users';
  searchData: any = {};
  constructor(
    private fb: FormBuilder,
    public applicationService: ApplicationService,
    private sessionService: SessionService,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;

    

    // this.applicationService.findAllWithPath(environment.authUrl + 'user-management/users')
    //   .subscribe(data => {
    //     this.users = data.filter((x: any) => x.userType.code === 'STF' && x.role.code === 'DRCT');
    //   });

    // this.applicationService.findAllWithPath(environment.authUrl + 'user-management/users')
    //   .subscribe(data => {
    //     this.users = data.filter((x: any) => x.userType.code === 'STF' && x.role.code === 'DRCT');
    //   });


    if (this.currentUser.data && this.currentUser.data.user && this.currentUser.data.user.agency) {
      this.applicationService.findAllWithPath(environment.chatUrl + 'getAllUserWithUnreadMessage/' +
         this.currentUser.data.user.agency.id)
        .subscribe(data => {
          // this.users = data;
          this.users = data.data.filter((x: any) => x.userType.code === 'STF' && x.role.code === 'DRCT');
        });



      // this.applicationService.findAllWithPath(environment.authUrl + 'user-management/users')
      //   .subscribe(data => {
      //     this.users = data.filter((x: any) => x.userType.code === 'STF' && x.role.code === 'DRCT');
      //   });


      // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/landOwners/' + this.currentUser.data.user.agency.id)
      this.applicationService.findAllWithPath(environment.chatUrl + 'getAllApplicantWithUnreadMessage')
        .subscribe(data => {
          this.applicants = data.data;
        });
    }





  }

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }


  getUser(event: any) {
    this.userData = event;
    this.messageForm.controls['user2'].setValue(this.userData.id);
    this.messageForm.controls['receiverId'].setValue(this.userData.id);
    this.loadList();
  }


  ngOnInit(): void {
    this.messageForm = this.fb.group({
      senderId: [this.currentUser.userId, Validators.required],
      receiverId: ['', Validators.required],
      content: ['', Validators.required],
      user1: [this.currentUser.userId],
      user2: ['']
    });




  }


  loadList() {
    this.applicationService.findAllWithPath(environment.chatUrl + 'getConversation/' + this.messageForm.value.user1 + '/' + this.messageForm.value.user2
    ).subscribe(
      dataItem => {
        this.messages = dataItem;
      }
    )
  }


  onSubmit() {
    this.applicationService.saveAssetWithPath(this.messageForm.value, environment.chatUrl + 'sendMessage')
      .subscribe(
        data => {
          this.messageForm.controls['content'].setValue('');
          this.loadList();
        }, error => { })
  }
}
