import { Component, EventEmitter, Input, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-other-application-document',
  templateUrl: './other-application-document.component.html',
  styleUrls: ['./other-application-document.component.scss']
})
export class OtherApplicationDocumentComponent {
  @Input() inputData: any = {};
  @Output() backToParent = new EventEmitter();
  userForm!: UntypedFormGroup;
  submitted: boolean = false;
  currentUser: any = {};
  fileData: any = {};
  isChecked: boolean = false;
  uploadedDocuments: any[] = []
  isReadyToSubmit: boolean = false;
  reservedDocumentRequired: any[] = [];
  deleteData: any = {};
  documentRequired: any[] = [];
  isAssociated: boolean = false;
  outputData: any = {};
  allowToSubmit() {
    this.isChecked = !this.isChecked;
  }
  fileSubmit: boolean = false;
  closeAssociatedPopup() {
    this.modalService.dismissAll();
    this.isAssociated = !this.isAssociated;

  }
  checkAssociated(event: any) {
    this.outputData.applicationId = this.inputData.applicationSaved.id;
    this.isAssociated = !this.isAssociated;
    this.openModal(event, 'md');
    // this.modalService.

    // (click)="openModal(associatedUpiContent, 'md')"
  }
  constructor(
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private sessionService: SessionService,
    private modalService: NgbModal,
    private router: Router
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
  }


  onPrev() {
    this.backToParent.emit();
  }


  ngOnInit(): void {
    this.reservedDocumentRequired = this.inputData.document;
    if (this.inputData.isFromAssigned) {
      this.loadUploadedDocuments();
    } else {
      this.loadUploadedDocuments()
    }

    this.userForm = this.formBuilder.group({
      documentRequiredId: ["", [Validators.required]],
      applicationId: [""],
      document: [""],
      userId: [this.currentUser.userId],
      applicationStatusId: [""],
      usedPermitType: [""],
      projectId: [""],
      permitTypeId: [""],
      categoryTypeId: [""],
      buildTypeId: [""],
      agencyId: [""],
      permitTypeCode: [""],
      agencyCode: [""],
    });
  }

  loadUploadedDocuments() {
    if (this.inputData.applicationSaved) {
      this.applicationService.findAllWithPath(environment.documentUrl + 'DocMgt/documents/' + this.inputData.applicationSaved?.id)
        .subscribe(
          documents => {
            this.uploadedDocuments = documents;
            if (this.uploadedDocuments?.length === this.reservedDocumentRequired?.length ||
              this.uploadedDocuments?.length > this.reservedDocumentRequired?.length
            ) {
              this.isReadyToSubmit = true;
            }
            // this.documentRequired = [];
            // this.reservedDocumentRequired.forEach(document => {
            //   if (!this.uploadedDocuments.some(element => element.requiredDocumentId === document.id)) {
            //     this.documentRequired.push(document);
            //   }
            // });
            this.documentRequired = this.reservedDocumentRequired;
            this.documentRequired.forEach((document: any) => {
              let findElement = this.uploadedDocuments.find((x: any) => x.requiredDocumentId === document.id);
              if (findElement) {
                document.uploaded = findElement
              }
            });
          }
        )
    }

  }


  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  openDeleteModal(content: any, sizeParams: any, value: any) {
    this.deleteData = value;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

  toggleErrorMessage() {
    if (!this.fileData.file) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Please upload document ", "bottom", "center");
    }
  }

  getFileDetails(e: any, docInfo: any) {
    const fileInput: any = e.target as HTMLInputElement;
    const file = e.target?.files[0];
    const fileExtension = this.utilService.getCheckFileExtension(e).toLowerCase();

    // Validate BOQ file
    if (docInfo.code === '1' && fileExtension !== 'pdf') {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        'Please upload file in excel format',
        "bottom",
        "center"
      );
      fileInput.value = ''; // Reset input
      return; // Exit if validation fails
    }

    // Common logic for handling files
    this.fileData.fileName = file?.name;
    this.fileData.fileNameDisplay = file?.name;
    this.userForm.controls['documentRequiredId'].setValue(docInfo.id);
    this.handleFileSelected(e);
    this.fileData.myFiles = [];

    // Process files
    for (let i = 0; i < e.target.files.length; i++) {
      this.fileData.myFiles.push(e.target.files[i]);
    }

    // Check file type and size
    if (this.fileData.fileType !== 'excel' && fileExtension === 'pdf') {
      this.fileData.size = file.size / (1024 * 1024); // Convert to MB
      if (this.fileData.size > this.utilService.fileSize) {
        this.utilService.showNotification(
          NOTIFICATION_COLOR.error,
          "Error: File is too big, try uploading a file less than 100MB.",
          // "Error: File is too big, try uploading a file less than 20MB.",
          "bottom",
          "center"
        );
        fileInput.value = ''; // Reset input
        return;
      }

      this.fileData.file = e.target.files;
      this.onSubmit(); // Submit form if all checks pass
      fileInput.value = ''; // Reset input after successful handling
    } else {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.error,
        "Error: PDF files are allowed only.",
        "bottom",
        "center"
      );

      // Reset input after error
      setTimeout(() => {
        fileInput.value = '';
      }, 0);
    }
  }


  async handleFileSelected(event: any) {
    const size = event.srcElement.files[0].size;
    if (size < 1000 * 1000 * 1000) {
      this.fileData.size = size / 1000 / 1000;
      this.fileData.unit = "mb";
    }
  }


  onSubmit() {
    let formData = new FormData();
    const fileList: FileList = this.fileData.file;
    if (fileList && fileList[0]) {
      const file: File = fileList[0];
      this.fileData.fileNameDisplay = file.name
      formData.append('file', file, file.name);
    }
    formData.append('requiredDocumentId', this.userForm.value.documentRequiredId);
    formData.append('applicationId', this.inputData.applicationSaved.id);
    this.fileSubmit = true;
    this.applicationService.saveAssetWithPathFormData(formData, environment.documentUrl + 'DocMgt/upload')
      .subscribe(
        data => {
          this.loadUploadedDocuments();
          this.fileSubmit = false;
        }, error => {
          this.fileSubmit = false;
        }
      )

  }



  deleteFile() {
    this.applicationService.deleteWithPathNoId(environment.documentUrl + "DocMgt/" + this.deleteData.id)
      .subscribe(
        data => {
          this.modalService.dismissAll();
          this.loadUploadedDocuments();
        },
        error => { }
      )
  }


  cancel() { }

  onNext(): void {
    this.submitApplication();
  }

  submitApplication() {
    this.submitted = true;

    if (this.inputData.applicationSaved.applicationStatus.code === 'UNCRN') {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + VALUE.APPLICATION_STATUS_RE_SUBMIT_CODE)
        .subscribe(
          data => {
            this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
            this.confirmSubmit();
          }, error => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
          });
    } else {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + VALUE.APPLICATION_STATUS_SUBMIT_CODE)
        .subscribe(
          data => {
            this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
            this.confirmSubmit();
          }, error => {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
          });

    }


    // })
  }

  confirmSubmit() {
    let dataToSubmit = {
      "projectId": this.inputData.project.id,
      "userId": this.currentUser.userId,
      "permitTypeId": this.inputData.applicationSaved.permitTypes.id,
      "categoryTypeId": this.inputData.applicationSaved.categoryTypes.id,
      "buildTypeId": this.inputData.applicationSaved.buildTypes.id,
      "agencyId": this.inputData.applicationSaved.agencyId,
      "applicationStatusId": this.userForm.value.applicationStatusId,
      "permitTypeCode": this.inputData.applicationSaved.permitTypeCode,
      "agencyCode": this.inputData.applicationSaved.agencyCode
    }
    if (this.inputData.applicationSaved.applicationStatus.code === 'UNCRN') {
      this.applicationService.updateAssetWithoutParams(dataToSubmit, environment.applicationUrl + 'application/application/resubmit/' + this.inputData.applicationSaved.id)
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application submitted successfully", "bottom", "center");
            this.submitted = false;
            this.modalService.dismissAll();
            this.router.navigate(['/account/application/applications']);
          }, error => {
            this.submitted = false;
          }
        )
    } else {
      this.applicationService.saveAssetWithPathFormData(dataToSubmit, environment.applicationUrl + 'application/application/submit/' + this.inputData.applicationSaved.id)
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application submitted successfully", "bottom", "center");
            this.submitted = false;
            this.modalService.dismissAll();
            this.router.navigate(['/account/application/applications']);
          }, error => {
            this.submitted = false;
          }
        )
    }
  }

}
