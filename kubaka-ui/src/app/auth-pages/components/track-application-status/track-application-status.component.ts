import { Component, Input } from '@angular/core';
import { ApplicationService } from 'src/app/application/services/application.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-track-application-status',
  templateUrl: './track-application-status.component.html',
  styleUrls: ['./track-application-status.component.scss']
})
export class TrackApplicationStatusComponent {
  @Input() inputData: any;
  applicationDetail: any = {};

  constructor(
    private applicationService: ApplicationService
  ) { }



  ngOnInit(): void {
    
  }


}
