<div class="stepper-content" *ngIf="!outputData.isDocumenting">
    <!-- <div class="stepper-header">
        <div class="upper-counter"> Step <span>1</span> / <span>1</span> </div>
    </div> -->
    <div class="stepper-body">
        <div>
            <!-- Step content goes here -->
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"otherApplicationWithoutNCP.projectDetails" | translate }}</h2>
                </div>
                <div class="appl-info" style="display: none" *ngIf="!outputData.isLoading">
                    <section>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card">
                                <!-- <h3>{{"otherApplicationWithoutNCP.projectDetails" | translate }}</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.projectBrief" | translate }}</label>
                                        <span class="form-out_txtarea">{{projectDetails.projectDescription}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.estimatedMonthlyWaterM3" | translate }}</label>
                                        <span>{{projectDetails.waterConsumption ? projectDetails.waterConsumption :
                                            '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label> {{"otherApplicationWithoutNCP.distanceNearestLandLineFiberM" | translate }}</label>
                                        <span>{{projectDetails.DistanceToTheNearestLandIn ?
                                            projectDetails.DistanceToTheNearestLandIn : '0'}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="appl-info-card">
                                <!-- <h3>Project Estimates Details</h3> -->
                                <div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.estimatedProjectCostUsd" | translate }}</label>
                                        <span>${{projectDetails.ProjectCostInUSD ? projectDetails.ProjectCostInUSD :
                                            '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.estimatedProjectCostRwf" | translate }}</label>
                                        <span>{{projectDetails.ProjectCostInRwf ? projectDetails.ProjectCostInRwf :
                                            '0'}} Rwf</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.estimatedMonthlyElectricityKwh" | translate }}</label>
                                        <span>{{projectDetails.electricityConsumption ?
                                            projectDetails.electricityConsumption : '0'}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="kbk-x-s sp-2 appl-info-fx">
                            <div class="appl-info-card fx-2">
                                <h3>{{"otherApplicationWithoutNCP.developmentDetails" | translate }}</h3>
                                <div class="kbk-x kbk-wrap-4">
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.plotSizeSquareMeters" | translate }}</label>
                                        <span>{{projectDetails.plotSize ? projectDetails.plotSize : '0'}} m²</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.grossFloorArea" | translate }}</label>
                                        <span>{{projectDetails.grossFloorArea ? projectDetails.grossFloorArea :
                                            '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.proposedNumberOfFloorsGPlus" | translate }}</label>
                                        <span>G+{{projectDetails.numberOfFloor ? projectDetails.numberOfFloor :
                                            '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.numberOfParkingSpaces" | translate }}</label>
                                        <span>{{projectDetails.numberOfParkingSpace ?
                                            projectDetails.numberOfParkingSpace : '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.priceOfDwellingUnit" | translate }}</label>
                                        <span>{{projectDetails.priceOfDwellingUnitRwf ?
                                            projectDetails.priceOfDwellingUnitRwf : '0'}}</span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.builtUpArea" | translate }}</label>
                                        <span>{{projectDetails.buildUpArea ? projectDetails.buildUpArea : '0'}} </span>
                                    </div>
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.grossFloorArea" | translate }}</label>
                                        <span>{{projectDetails.grossFloorArea ? projectDetails.grossFloorArea :
                                            '0'}}</span>
                                    </div>
                                    <!-- <div class="form-out">
                                        <label>Estimate % of space</label>
                                        <span>60%</span>
                                    </div>
                                    <div class="form-out">
                                        <label>Price of Dwelling Unit</label>
                                        <span>$60</span>
                                    </div> -->
                                    <div class="form-out">
                                        <label>{{"otherApplicationWithoutNCP.capacityInfoPeopleSeats" | translate }}</label>
                                        <span>{{projectDetails.capacityInformation ? projectDetails.capacityInformation
                                            : '0'}}</span>
                                    </div>
                                    <!-- <div class="form-out">
                                        <label>For Industrial projects</label>
                                        <span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut excepturi hic, eaque
                                            velit a quod adipisci accusamus exercitationem? Qui, libero.</span>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
                <form [formGroup]="pageForm" autocomplete="off" *ngIf="currentUser.data.user.userType.code === 'LO'">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToDoItYouself"
                                        (change)="changeForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"otherApplicationWithoutNCP.doItYourself" | translate }}</span>
                                </label>
                            </div>
                            <div class="form-incheckbox">
                                <label class="checkbox">
                                    <input type="checkbox" id="check" [checked]="doYouWantToAssignToEngineer"
                                        (change)="changeAssignForm()" />
                                    <span class="checkbox_box"></span>
                                    <span class="checkbox_txt">{{"otherApplicationWithoutNCP.assignToEngineerArchitect" | translate }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
                <form (ngSubmit)="onSubmit()" *ngIf="!outputData.isLoading && !loading" [formGroup]="userForm"
                    autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.projectName" | translate }}</label>
                                <div>
                                    <textarea name="projectName" formControlName="projectName" id="projectName"
                                        cols="30" rows="5" readonly></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.projectDescription" | translate }}</label>
                                <div>
                                    <textarea name="projectDescription" formControlName="projectDescription"
                                        id="projectDescription" cols="30" rows="5" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.permitType" | translate }}</label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled && !this.otherParams.permitTypeId"
                                        name="permitTypeId" id="permitTypeId" formControlName="permitTypeId"
                                        (change)="getPermitTypeObject($event)" required>
                                        <option *ngFor="let op of permitTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.plotSize" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.buildingType" | translate }}</label>
                                <div>
                                    <select [class.disabled-select]="isSelectDisabled && !this.otherParams.permitTypeId"
                                        name="buildTypeId" id="buildTypeId" formControlName="buildTypeId"
                                        (change)="getBuildTypeObject($event)" required>
                                        <option *ngFor="let op of buildingTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.projectCategory" | translate }}</label>
                                <div>
                                    <select [class.disabled-select]="'true'" name="categoryTypeId" id="categoryTypeId"
                                        formControlName="categoryTypeId" required>
                                        <option *ngFor="let op of categoryTypes" [value]="op.id">{{op.name}}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.numberOfFloorGPlus" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfFloor" name="numberOfFloor" noNegative
                                        formControlName="numberOfFloor" (keyup)="calculateNumberOfFlow()" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.builtUpAreaSqm" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <!--  -->
                                    <input type="number" id="buildUpArea" name="buildUpArea"
                                        formControlName="buildUpArea" (focusout)="focusOutFunction()"
                                        (keyup)="calculateNumberOfFlow()" noNegative required
                                        [disabled]="userForm.value.buildTypeId?.length < 0 || userForm.value.buildTypeId?.length == 0">
                                    <div class="text-danger" *ngIf="userForm.hasError('greaterThanPlotSize')"> {{"otherApplicationWithoutNCP.buildUpAreaTooLarge" | translate }}. </div>
                                </div>
                            </div>

                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.grossFloorArea" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="grossFloorArea" name="grossFloorArea"
                                        formControlName="grossFloorArea" noNegative readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.numberOfParkingSpaces" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="numberOfParkingSpace" name="numberOfParkingSpace"
                                        formControlName="numberOfParkingSpace" noNegative required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.priceDwellingUnitRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="priceOfDwellingUnitRwf"
                                        name="priceOfDwellingUnitRwf" formControlName="priceOfDwellingUnitRwf" required
                                        autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.descriptionOperations" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="DescriptionOfOperation" name="DescriptionOfOperation"
                                        formControlName="DescriptionOfOperation" required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.percentageSpaceUse" | translate }}<span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="percentageSpaceUse" name="percentageSpaceUse"
                                        formControlName="percentageSpaceUse" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.numberOfDwellingUnits" | translate }}<span class="estrx"></span></label>
                                <div>
                                    <input type="text" id="numberOfDwellingUnits" name="numberOfDwellingUnits"
                                        formControlName="numberOfDwellingUnits" autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.estimatedMonthlyWaterM3Repeat" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="waterConsumption" name="waterConsumption"
                                        formControlName="waterConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.estimatedMonthlyElectricityWatts" | translate }}<span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="electricityConsumption" name="electricityConsumption"
                                        formControlName="electricityConsumption" noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.distanceNearestLandLineFiberM" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="DistanceToTheNearestLandIn"
                                        name="DistanceToTheNearestLandIn" formControlName="DistanceToTheNearestLandIn"
                                        noNegative required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.estimatedProjectCostUsd" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInUSD" name="ProjectCostInUSD"
                                        formControlName="ProjectCostInUSD" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.estimatedProjectCostRwf" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <input type="text" appCommaSeparator id="ProjectCostInRwf" name="ProjectCostInRwf"
                                        formControlName="ProjectCostInRwf" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.capacityInfoPeopleSeats" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="capacityInformation" name="capacityInformation"
                                        formControlName="capacityInformation" noNegative
                                        (keyup)="calculateNumberOfPeople()" required autocomplete="off">
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"otherApplicationWithoutNCP.technologySurveyOptional" | translate }} <span class="estrx">*</span></label>
                                <div>
                                    <select name="technologySurveyId" id="technologySurveyId"
                                        formControlName="technologySurveyId">
                                        <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="form-set">

                        </div> -->
                        <!-- This is the component that will differenciate additionally properties based on permit type selected -->
                        <app-other-application-permit-answer *ngIf="userForm.value.permitTypeId &&
                        (otherApplicationValue.isLoadingApplication)" [inputData]="
                            {userForm: userForm.value, permitTypes: this.permitTypes,
                                otherApplicationValue: otherApplicationValue
                            }" (backToParent)="updateOtherInfo($event)"></app-other-application-permit-answer>




                        <div class="alert alert-danger" role="alert" *ngIf="!formAccess.isIAEverified &&
                            formAccess.isRdbIAEchecked
                            && (userForm.value.categoryCode === 'CAT5' ||
                            userForm.value.categoryCode === 'CAT4')"> {{"otherApplicationWithoutNCP.dataNotFoundEIA" | translate }} </div>


                        <app-eia-form-checker *ngIf="
                                (userForm.value.categoryCode === 'CAT5' ||
                                userForm.value.categoryCode === 'CAT4') && this.projectDetails.upi"
                            [inputData]="{upi: this.projectDetails.upi}"
                            (backToParent)="comeAndAllowToAssign($event)"></app-eia-form-checker>


                        <h4 *ngIf="currentUser.data.user.role.code === 'APP' &&
                    (projectDetails?.projectStatus?.code === 'PAPRV'
                        || projectDetails?.projectStatus?.code === 'PASGD'
                    )" style="color: red;">{{"otherApplicationWithoutNCP.projectAlreadyAssigned" | translate }}.
                        </h4>

                        <app-assign-to-engineer-form *ngIf="
                            doYouWantToAssignToEngineer ||
                            userForm.value.categoryCode === 'CAT3' ||
                                ((userForm.value.categoryCode === 'CAT4' ||
                                userForm.value.categoryCode === 'CAT5')
                                && formAccess.isRdbIAEchecked && currentUser.data.user.role.code !== 'ENG')
                                && projectDetails?.projectStatus?.code !== 'PAPRV'
                        && projectDetails?.projectStatus?.code !== 'PASGD'
                                    " [inputData]="{
                                projectId: userForm.value.projectId,
                                allowToSubmit: formAccess.isIAEverified}"></app-assign-to-engineer-form>
                    </div>
                    <div class="step-panel_footer">
                        <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()">{{"otherApplicationWithoutNCP.cancel" | translate }}</button>
                        <button class="kbk-btn kbk-btn-main" type="submit" [disabled]="userForm.invalid">{{"otherApplicationWithoutNCP.next" | translate }} </button>
                    </div>
                </form>
                <!-- loading indicator -->
                <app-spinner *ngIf="outputData.isLoading" [inputData]="'Loading data .....'"></app-spinner>
                <!-- loading indicator -->
            </div>
        </div>
    </div>
</div>

<app-other-application-document *ngIf="outputData.isDocumenting" [inputData]="outputData"
    (backToParent)="backAndCloseDocument()"></app-other-application-document>












<ng-template #otherApplicationModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">{{"otherApplicationWithoutNCP.messageTitle" | translate }}</h2>
            <p>{{"otherApplicationWithoutNCP.message" | translate }} </p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="closeModal()"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="closeModal()"
                    id="deleteRecord-close">Choose another permit</button>
                <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="yesContinue()">Continue with checking document!</button>
            </div>
        </div>
    </div>
</ng-template>
