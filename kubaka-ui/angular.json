{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"KUBAKA-WEB-APP": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/kubaka-web-app", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": [], "localize": true, "i18nMissingTranslation": "error"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "2mb", "maximumError": "40mb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": {"scripts": true, "styles": true, "vendor": true}, "namedChunks": true, "aot": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"allowedHosts": ["testing.kubaka.gov.rw", "*************", "staging.kubaka.gov.rw", "api-gatway.kubaka.gov.rw", "kubaka.gov.rw"], "port": 4200, "browserTarget": "KUBAKA-WEB-APP:build:development", "liveReload": true, "hmr": false, "open": false}, "configurations": {"production": {"browserTarget": "KUBAKA-WEB-APP:build:production"}, "development": {"browserTarget": "KUBAKA-WEB-APP:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "KUBAKA-WEB-APP:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "f26809bc-b44a-42c3-9268-ebf341891dfe"}}