import { AbstractEntity } from '@app/common';
import {
  Column,
  Entity,
  // <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToMany,
  // ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { UserActivity } from './userActivity.entity';
import { User } from './user.entity';

@Entity()
export class Role extends AbstractEntity<Role> {
  /**
   * this decorator will help to auto generate id for the table.
   */

  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column()
  code: string;

  @ManyToMany(() => UserActivity, (userActivity) => userActivity.roles, {
    onDelete: 'NO ACTION',
    onUpdate: 'NO ACTION',
  })
  userActivities?: UserActivity[];

  // @ManyToOne(() => User, (event) => event.roles)
  // @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
  // public user!: User;

  @OneToMany(() => User, (user) => user.role)
  users: User[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}
