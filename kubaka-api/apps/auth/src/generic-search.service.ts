import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, Repository } from 'typeorm';

@Injectable()
export class GenericSearch<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.andWhere(whereSearch);
    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

@Injectable()
export class GenericSearch2<T> {
  async search(
    repository: Repository<T>,
    searchFields: Extract<keyof T, string>[],
    search: string,
    relations: string[] = [],
  ) {
    const queryBuilder = repository.createQueryBuilder('alias');
    const whereSearch: FindOptionsWhere<T> = {};
    searchFields.forEach(
      (field) => (whereSearch[`${field}` as string] = ILike(`%${search}%`)),
    );
    queryBuilder.andWhere(whereSearch);
    relations.forEach((relation) => {
      queryBuilder.leftJoinAndSelect(`alias.${relation}`, relation);
    });
    const [items, totalCount] = await queryBuilder.getManyAndCount();
    return { items, totalCount };
  }
}

export class GenericSearchWithMany<T> {
  async searchProjects(repository: Repository<T>, searchQuery: any) {
    let queryBuilder = repository.createQueryBuilder('user');

    if (searchQuery.licenceIdEng) {
      queryBuilder = queryBuilder.andWhere(
        'user.licenceIdEng = :licenceIdEng',
        {
          licenceIdEng: searchQuery.licenceIdEng,
        },
      );
    }
    if (searchQuery.licenceIdArch) {
      queryBuilder = queryBuilder.andWhere(
        'user.licenceIdArch = :licenceIdArch',
        { licenceIdArch: searchQuery.licenceIdArch },
      );
    }
    const items = await queryBuilder.getMany();
    const totalCount = items.length;

    return { items, totalCount };
  }
}
