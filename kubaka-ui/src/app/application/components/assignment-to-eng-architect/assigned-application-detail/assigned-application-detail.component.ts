import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-assigned-application-detail',
  templateUrl: './assigned-application-detail.component.html',
  styleUrls: ['./assigned-application-detail.component.scss']
})
export class AssignedApplicationDetailComponent {
  @Input() inputData: any;
  currentUser: any = {};
  inTokenData: any = {};
  projectDetail: any = {};
  @Output() backToParent = new EventEmitter();

  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    private applicationService: ApplicationService,
    private router: Router,
    private utilService: UtilService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.inTokenData = this.sessionService.getTokenData();
  }




  submit(event: any) {
    let code = event === 'approve' ? 'PAPRV' : 'PRJCT';
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/projectStatus/code/search?search=' + code)
      .subscribe(
        data => {
          let dataSubmit = {
            "userIdForAssignee": this.inputData.userIdForAssignee,
            "userTypeId": this.inputData.userTypeId,
            "licenseNumber": this.inputData.licenseNumber,
            "projectId": this.inputData.projects.id,
            "projectStatusId": data.items[0].id
          };
          if (code === 'PAPRV') {
            this.confirmSubmit(dataSubmit);
          } else {
            this.calledWhenEngineerOrArchictReject(dataSubmit.licenseNumber);
          }


          // if (event === 'approve') {
          //   let dataSubmit = {
          //     // "userIdForAssignee": "string",
          //     // "userTypeId": "string",
          //     // "licenseNumber": "string",
          //     "projectId": this.inputData.projects.id,
          //     "projectStatusId": "string"
          //   };
          //   this.confirmSubmit(dataSubmit);
          // }

          // if (event === 'reject') {
          //   let dataSubmit = {
          //     // "userIdForAssignee": "string",
          //     // "userTypeId": "string",
          //     // "licenseNumber": "string",
          //     "projectId": this.inputData.projects.id,
          //     "projectStatusId": "string"
          //   }
          // }
        })
  }


  ngOnInit(): void {
  }


  confirmSubmit(dataSubmit: any) {
    this.applicationService.patchWithPath(dataSubmit, environment.applicationUrl + 'application/assignee/' + this.inputData.id)
      .subscribe(
        data => {
          this.updateAssigneeStatus(dataSubmit);
          // this.backToParent.emit(true);
          // this.utilService.showNotification(NOTIFICATION_COLOR.success, "Project assigned successfully", "bottom", "center");
        }
      )
  }

  updateAssigneeStatus(dataSubmit: any) {
    this.applicationService.updateAssetWithoutParams(dataSubmit, environment.applicationUrl + 'application/assignee/' + this.inputData.id + '/status')
      .subscribe(
        data => {
          this.backToParent.emit(true);
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Project approved successfully", "bottom", "center");
        }
      )
  }


  calledWhenEngineerOrArchictReject(licenseNumber: any) {
    // this.applicationService.saveAssetWithPath(JSON.stringify(''), environment.applicationUrl + 'application/assignee/' + this.currentUser.userId + '/reject/' + licenseNumber)
    this.applicationService.saveAssetWithPath(JSON.stringify(
      {
        "projectId": this.inputData.projects.id,
        "licenseNumber": licenseNumber,
        "projectUserId": this.inputData.projects.userId
      }
    ), environment.applicationUrl + 'application/assignee/rejectProject')
      // projectUserId, licenseNumber, projectId
      //  http://localhost:3000/application/assignee/7231aab0-cd13-41e8-9f67-49b11aaf133f/reject/A.9090/project/08cf66eb-1a93-4234-9256-e13f49824845. ===> projectUserId; licenseNumber ; projectId
      .subscribe(
        data => {
          this.backToParent.emit(true);
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Project rejected successfully", "bottom", "center");
        }
      )
  }
}
