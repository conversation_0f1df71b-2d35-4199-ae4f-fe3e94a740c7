import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer, switchMap } from 'rxjs';
// ... existing code ...
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GlobalRefreshService {
  // private intervalSubject = new BehaviorSubject<number>(60000);
  private intervalSubject = new BehaviorSubject<number>(environment.globalRefreshInterval); // default 60s
  private refresh$: Observable<number>;

  constructor() {
    this.refresh$ = this.intervalSubject.pipe(
      switchMap(interval => timer(0, interval))
    );
  }
  

  get refreshInterval$(): Observable<number> {
    return this.refresh$;
  }

  setInterval(ms: number) {
    this.intervalSubject.next(ms);
  }

  getCurrentInterval(): number {
    return this.intervalSubject.value;
  }
}
