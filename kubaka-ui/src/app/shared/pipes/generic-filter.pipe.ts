import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'genericfilter'
})
export class FilterPipe implements PipeTransform {

    transform(items: any[], filter: string): any {
        if (!items || !filter) {
            return items;
        }
        return items.filter(item => JSON.stringify(item).toLowerCase().indexOf(filter.toLowerCase()) !== -1);
    }

}
@Pipe({
  name: 'splitByCapital'
})
export class SplitByCapitalPipe implements PipeTransform {
  transform(value: string): string {
    if (!value) return '';

    // Split by capital letters and capitalize each word
    return value
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}
@Pipe({ name: 'filePreview' })
export class FilePreviewPipe implements PipeTransform {
  transform(file: File | string): string {
    if (typeof file === 'string') {
      return file;
    }

    if ((typeof File !== 'undefined' && file instanceof File) || (typeof Blob !== 'undefined' && file instanceof Blob)) {
      // Create a preview URL from a file or blob
      return URL.createObjectURL(file);
    }
    return '';
  }
}
