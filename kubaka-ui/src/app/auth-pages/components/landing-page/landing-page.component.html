<header>
  <div class="gen-main">
    <div class="lng-nav">
      <div class="container">
        <nav class="kbk-x-e lang-links">
          <a (click)="switchLang('kin')" [class.active]="defaultSelectedLang === 'kin'">
            <img src="../../../../assets/flags/kiny.svg" alt="Kinyarwanda" /></a>
          <a (click)="switchLang('fr')" [class.active]="defaultSelectedLang === 'fr'"><img src="assets/flags/fr.svg" alt="Francais" /></a>
          <a (click)="switchLang('en')" [class.active]="defaultSelectedLang === 'en'"><img src="assets/flags/uk.svg" alt="English" /></a>
        </nav>
      </div>
    </div>
    <div>
      <div class="container">
        <nav class="prim-nav">
          <input type="checkbox" id="bgMenu" style="display: none;">
          <a class="logo">
            <img src="assets/ikons/logo/NavLogo.svg" alt="" />
          </a>
          <div class="main-nav">
            <div class="main-nav-list">
              <label class="kbk-btn-close" for="bgMenu">
                <img src="assets/ikons/colored/ikon-close.svg" alt="">
              </label>
              <!-- <ul class="kbk-x">
                <li class="kbk-nav-item" routerLinkActive="active">
                  <a class="kbk-nav-link">{{ "topBarMenu.home" | translate }}</a>
                </li>
                <li class="kbk-nav-item" routerLinkActive="active">
                  <a class="kbk-nav-link">{{ "topBarMenu.service" | translate }}</a>
                </li>
                <li class="kbk-nav-item" routerLinkActive="active">
                  <a class="kbk-nav-link">{{ "topBarMenu.faq" | translate }}</a>
                </li>
              </ul> -->
              <div class="kbk-x-e sp-sm">
                <button routerLink="/login" class="kbk-btn kbk-btn-outline"> {{ "topBarMenu.login" | translate }}
                </button>
                <button routerLink="/register" class="kbk-btn kbk-btn-main"> {{ "topBarMenu.signup" | translate }}
                </button>
              </div>
            </div>
          </div>
          <label class="mob-menu" for="bgMenu">
            <img src="assets/ikons/colored/ikon-menu.svg" alt="">
          </label>
        </nav>
      </div>
    </div>
  </div>
  <div class="gen-hero">
    <div class="container">
      <div class="content">
        <h1>{{ "heroPage.title" | translate }}</h1>
        <p>
          {{ "heroPage.subTitle" | translate }}
          <br>
          <a href="https://amakuru.kubaka.gov.rw/" target="_blank"> {{ "heroPage.subTitle2" | translate }} </a>
        </p>
        <form>
          <div class="kbk-x-c sp-xs">
            <input type="text" name="upiFinder" [(ngModel)]="upiFinder" class="flght flg"
              placeholder="Application № : COK-NCP-DYXH-46808-91691" />
            <button *ngIf="!submitted" type="button" class="kbk-btn kbk-btn-lg" data-bs-toggle="modal"
            id="create-btn"
              data-bs-target="#showModal" (click)="openModal(applicationStatusContent, 'ap-stas')"> {{
              "heroPage.searchButton" | translate }} </button>
            <button *ngIf="submitted" type="button" class="kbk-btn kbk-btn-lg" id="create-btn"> {{
              "heroPage.searchButton" | translate }}.. </button>
          </div>
        </form>
        <a data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
          (click)="opentModalEligible(upiFromLandContent, 'lg')">{{ "heroPage.link" | translate }}</a>
      </div>
    </div>
  </div>
</header>
<section class="section-serv">
  <div class="gen-serve">
    <div class="container">
      <div class="page-lead">
        <div class="content">
          <h1> {{ "permitCategory.title" | translate }}</h1>
          <p> {{ "permitCategory.subTitle" | translate }}</p>
          <div class="content-body">
            <ul class="wig-list">
              <li class="wid-item" *ngFor="let sr of services?.length ? services : []" (click)="openModalService(content, sr)">
                <a class="wid-card">
                  <div class="wid-icon">
                    <img src="{{ sr.icon }}" alt="" />
                  </div>
                  <div class="wid-dtails">
                    <span>{{ sr.applicationType | translate }} </span>
                  </div>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<section class="section-faq">
  <div class="gen-serve">
    <div class="container">
      <div class="content">
        <h1>{{ "FAQs.Title" | translate }}</h1>
        <p>{{ "FAQs.subTitle" | translate }}</p>
        <div class="content-body">
          <ul class="accordion-list">
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" />{{ "FAQs.questionOwner.1" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionOwner.1" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionOwner.2" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionOwner.2" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionOwner.3" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionOwner.3" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" />{{ "FAQs.questionOwner.4" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionOwner.4" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionEngineer.1" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionEngineer.1" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionEngineer.2" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionEngineer.2" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionEngineer.3" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionEngineer.3" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionArchitect.1" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionArchitect.1" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionArchitect.2" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionArchitect.2" | translate }} </p>
              </div>
            </li>
            <li class="accordion-item">
              <label class="accordion-btn">
                <input type="checkbox" style="display: none" /> {{ "FAQs.questionArchitect.3" | translate }} <span
                  class="more-btn"></span>
              </label>
              <div class="accordion-content">
                <p> {{ "FAQs.descriptionArchitect.3" | translate }} </p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>
<footer>
  <a class="footer-link">© {{year}} - Kubaka - {{ "footer" | translate }}</a>
  <br>
  <!-- <a class="footer-link2" href="https://techavenue137.rw/" target="_blank">{{ "footer2" | translate }}</a> -->
</footer>
<ng-template #content let-modal>
  <div class="modol-header">
    <h5 class="modol-title" id="exampleModalLabel">
      {{ "heroPage.constructionPermit" | translate }}</h5>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content">
    <app-service-info-page [inputData]="selectedService" (backAndClose)="close()"></app-service-info-page>
  </div>
</ng-template>
<ng-template #upiFromLandContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{ "zoningCheck.title" | translate }}</h2>
    <p> {{ "zoningCheck.subTitle" | translate }} </p>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content">
    <app-upi-info [info]="'0'"></app-upi-info>
  </div>
</ng-template>
<ng-template #applicationStatusContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">{{ "zoningCheck.title" | translate }}</h2>
    <p>{{ "zoningCheck.subTitle" | translate }}</p>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="outputData.isViewingTracking">
    <app-track-application-status [inputData]="outputData"></app-track-application-status>
  </div>
</ng-template>
