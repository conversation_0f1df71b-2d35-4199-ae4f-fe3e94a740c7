import { Module } from '@nestjs/common';
import { DocumentsManagementController } from './documents-management.controller';
import { DocumentsManagementService } from './documents-management.service';

import { DatabaseModule } from '@app/common';
import { ApplicationDocumentRepository } from './documents-management.repository';
import { MinioClientModule } from '../minio-client/minio-client.module';
import { ApplicationDocument } from '../../entities/application-documents.entity';

@Module({
  imports: [
    MinioClientModule,
    DatabaseModule,
    DatabaseModule.forFeature([ApplicationDocument]),
  ],
  controllers: [DocumentsManagementController],
  providers: [DocumentsManagementService, ApplicationDocumentRepository],
})
export class DocumentsManagementModule {}
