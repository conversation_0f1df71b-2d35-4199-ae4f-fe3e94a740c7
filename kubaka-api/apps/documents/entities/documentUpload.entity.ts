import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity()
export class DocumentUpload {
  /**
   * this decorator will help to auto generate id for the table.
   */
  @PrimaryGeneratedColumn('uuid')
  public id!: string;

  @Column({ nullable: true })
  requiredDocumentId: string;

  @Column()
  applicationId: string;

  @Column({ default: false })
  isValid: boolean;

  @Column({ default: '0', nullable: true })
  uploadType: string;

  @Column({ nullable: true })
  additionalDescription: string;

  @Column({ default: '0' })
  documentStatusId: string;

  @Column()
  fileUrl: string;

  @Column()
  fileName: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;
}
