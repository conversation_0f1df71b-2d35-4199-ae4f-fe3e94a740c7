<!-- Applicant Applications -->
<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-welnote">
                <div class="app-welnote_dtails">
                    <span class="prim-nt">{{"applicantApplication.welcomeMsg" | translate }}!</span>
                    <h3>
                        <span>{{ currentUser.data.user.lastName }}</span> {{ currentUser.data.user.firstName }}
                    </h3>
                </div>
            </div>
            <div class="app-lists">
                <div class="app-tblist">
                    <div class="app-tblist_title">
                        <span class="hder" aria-label="header tittle">{{"applicantApplication.myApplications" | translate }}</span>
                        <div class="tbleFilter">
                            <!-- <div class="form-input clear-m w-aut"> -->
                                <div class="form-input_search">
                                    <input type="text" name="searchTerm" [(ngModel)]="userService.searchTerm"
                                        (ngModelChange)="onSearchTermChange($event)"
                                        [placeholder]="'projectComponent.searchForSomething' | translate">
                                    <button type="button" class="btn">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                            <g data-name="Layer 2">
                                                <g data-name="search">
                                                    <rect width="24" height="24" opacity="0" />
                                                    <path
                                                        d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                                                </g>
                                            </g>
                                        </svg>
                                    </button>
                                </div>
                                <div class="form-input clear-m w-aut">
                                    <label><Strong>{{"applicantApplication.applicationStatus" | translate }}</Strong> </label>
                                    <div>
                                      <select name="applicationStatusId" id="applicationStatusId" [(ngModel)]="applicationStatusId" (change)="filterByStatus()">
                                        <!-- <option disabled value="">All</option> -->
                                        <option *ngFor="let r of applicationStatuses" [value]="r.id"> {{ r.name }} </option>
                                      </select>
                                    </div>
                                  </div>
                            <!-- </div> -->
                            <!-- <div class="form-input">
                                <div>
                                  <input type="date" name="filterByDate" [(ngModel)]="userService.filterByDate"
                                    (ngModelChange)="onSearchByDateChange($event)" required />
                                </div>
                              </div> -->
                            <a *ngIf="currentUser.data.user.userType.code === 'LO'"
                            class="kbk-btn kbk-btn-main brtn-edth" data-bs-toggle="modal" id="create-btn"
                                data-bs-target="#showModal" (click)="openModal(upiFromLandContent, 'lg')">{{"applicantApplication.newPermitApplication" | translate }}</a>
                        </div>
                    </div>
                    <ul class="tblist" *ngIf="lists.length > 0">
                        <li class="tblist-item" *ngFor="let li of lists">
                            <!-- <div class="tblist-item_ck">
                                  <label class="form-checkbox">
                                      <input type="checkbox">
                                  </label>
                              </div> -->
                            <div class="tblist-item_icon">
                                <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                            </div>
                            <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">UPI</span> {{li.projects.upi}}</span>
                                <span>
                                    <span class="ttl">{{"applicantApplication.permitType" | translate }}</span> {{li.permitTypes.name}}</span>
                            </div>
                            <!-- <div class="tblist-item_dt">
                                <span class="ttl">Project Name</span> {{li.projects.applicationName}}
                            </div> -->
                            <!-- <div class="tblist-item_dt">
                                <span class="ttl">Category</span> {{li.categoryTypes.name}}
                            </div> -->
                            <div class="tblist-item_dt txt-l">
                                <span>
                                    <span class="ttl">{{"applicantApplication.applicationNo" | translate }}</span> {{li.applicationName}} </span>
                                <span>
                                    <span class="ttl">{{"applicantApplication.projectName" | translate }}</span> {{li.projects.projectName |titlecase}}</span>
                            </div>
                            <div class="tblist-item_dt">
                                <span>
                                    <span class="ttl">{{"applicantApplication.buildType" | translate }}</span> {{li.buildTypes.name}}</span>
                                <span>
                                    <span class="ttl">{{"applicantApplication.siteLocation" | translate }}</span> {{li.projects.villageName}}</span>
                            </div>
                            <div class="tblist-item_dt">
                                <span>

                                      <span class="ttl">{{"applicantApplication.createdOn" | translate }}</span> {{li.created_at | date:'yyyy-MM-dd HH:mm:ss'}}</span>
                                  <span>
                                      <span class="ttl">{{"applicantApplication.submittedDate" | translate }}</span> {{ li.submittedDate | date:'yyyy-MM-dd HH:mm:ss' }}</span>
                            </div>
                            <div class="tblist-item_status">
                                <span class="bdg bdg-pend">{{li.applicationStatus?.name}}</span>
                            </div>
                            <div class="tblist-item_xcn" *ngIf="lists.length > 0">
                                <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="edit">
                    <img src="assets/ikons/colored/ikon-edit.svg" alt="" />
                  </a>
                  <a class="kbk-link-btn hs-tp" data-kbk-tooltip="delete">
                    <img src="assets/ikons/colored/ikon-trash.svg" alt="" />
                  </a> -->
                                <a *ngIf="li.applicationStatus.code !== 'UNCRN'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="view details"
                                    [routerLink]="['/account/application/application-detail', li?.id]">
                                    <!-- <img src="assets/ikons/colored/ikon-eye.svg" alt="" /> --> {{"applicantApplication.applicationDetails" | translate }}
                                </a>
                                <a *ngIf="li.applicationStatus.code === 'UNCRN'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="view details"
                                    [routerLink]="['/account/application/new-application-development-detail/', li?.projects?.id]">
                                    {{"applicantApplication.applicationDetails" | translate }}
                                </a>
                                <a *ngIf="li.applicationStatus.code ==='CTFD'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="Related Application" id="create-btn"
                                    (click)="otherApplication(li)">
                                    <!-- <img src="assets/ikons/colored/ikon-related.svg" alt="" /> -->{{"applicantApplication.relatedApplication" | translate }} </a>
                                <a *ngIf="li.invoices.length > 0 &&
                                    li.invoices[0].invoiceStatus.code !== 'PAD' && li.applicationStatus.code === 'CTFD'"
                                    [routerLink]="['/account/payment/invoice-detail/', li?.invoices[0]?.id,li?.id]"
                                    class="kbk-link-btn hs-tp" data-kbk-tooltip="Pay invoice" id="create-btn">
                                    {{"applicantApplication.pay" | translate }} </a>
                                <a *ngIf="li.invoices.length > 0 && (li.invoices[0]?.invoiceStatus?.code === 'PAD' ||
                                    li.invoices[0]?.invoiceStatus?.code === 'EXMTD'
                                ) &&
                                    li.applicationStatus.code === 'CTFD'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="View Permit" id="create-btn"
                                    [routerLink]="['/account/certificate/certificate-detail/', li?.certificates[0]?.id]">{{"applicantApplication.viewPermit" | translate }}</a>

                                <a *ngIf="li.applicationStatus.code === 'UNCRN'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="View comment" id="create-btn"
                                    data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                                                    (click)="viewComment(li,contentComment)"

                                    >{{"applicantApplication.viewComments" | translate }} </a>

                                    <a *ngIf="li.applicationStatus.code === 'PND'" class="kbk-link-btn hs-tp"
                                    data-kbk-tooltip="View comment" id="create-btn"
                                    data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                                                    (click)="openToDelete(li,deleteModel)"

                                    >{{"applicantApplication.delete" | translate }} </a>


                                <!-- <a class="kbk-link-btn">
                                      <img src="assets/ikons/colored/ikon-eye.svg" alt="">
                                  </a> -->
                            </div>
                        </li>
                    </ul>
                    <div class="pagnation" *ngIf="lists.length > 0">
                        <div class="pagnation-item">
                            <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                                <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} -
                                    {{endIndex}}</span>
                            </div>
                        </div>
                        <div class="pagnation-item">
                            <ngb-pagination [collectionSize]="totalRecords" [(page)]="page" [pageSize]="pageSize"
                                (pageChange)="getPremiumData();">
                            </ngb-pagination>
                        </div>
                    </div>
                    <!-- <div class="pagnation" *ngIf="lists.length > 0">
                        <div class="pagnation-item">
                            <div class="pagnation-dt" id="tickets-table_info" role="status" aria-live="polite">
                                <span class="ent">{{totalRecords}}</span> <span class="cur">{{startIndex}} -
                                    {{endIndex}}</span>
                            </div>
                        </div>
                        <div class="pagnation-item">
                            <ngb-pagination [collectionSize]="total" [(page)]="page" [pageSize]="pageSize"
                                (pageChange)="getPremiumData();">
                            </ngb-pagination>
                        </div>
                    </div> -->
                    <ul class="tblist" *ngIf="lists.length === 0">
                        <li class="tblist-item">
                            <h2 *ngIf="lists.length === 0" id="exampleModalLabel" class="no-rcrds-found">{{"applicantApplication.noRecordsFound" | translate }}</h2>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>




<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">{{"applicantApplication.confirmDeleteApplication" | translate }}
            </h2>
            <p>{{"applicantApplication.deleteWarning" | translate }}</p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <!-- <h4>You are about to delete a {{deleteData.fileName}} ?</h4> -->
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                    (click)="modal.close('Close click')" id="deleteRecord-close">{{"applicantApplication.close" | translate }}</button>
                <button *ngIf="!loading" type="button" class="kbk-btn kbk-btn-error"
                    id="delete-product" (click)="deleteFile()">{{"applicantApplication.yesDelete" | translate }}!</button>
                <button *ngIf="loading" type="button" class="kbk-btn kbk-btn-error"
                    id="delete-product">{{"applicantApplication.deletingApplication" | translate }}...</button>
            </div>
        </div>
    </div>
</ng-template>












<ng-template #upiFromLandContent role="document" let-modal>
    <div class="modol-header">
        <!-- <h2 id="exampleModalLabel">Check the eligibility</h2>
        <p> In accordance with the master plan, track what is permitted, prohibited, and conditional to be built on your
            plot  </p> -->
        <h2 id="exampleModalLabel">{{"applicantApplication.verification" | translate }}</h2>
        <p>{{"applicantApplication.verificationDetails" | translate }}.
        </p>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modol-content">
        <app-upi-info [info]="'1'" (backToParent)="getChoosenSelectedUse($event)"></app-upi-info>
    </div>
</ng-template>


<ng-template #contentComment role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel">{{"applicantApplication.applicationComment" | translate }}</h2>
        <p>{{"applicantApplication.applicationComment" | translate }} </p>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>

    <div class="app-dash">
        <div class="dash-flex">
            <div class="app-l">
                <div class="progress-steps">
                    <ul class="step-list">

                        <li class="step-list_item" *ngFor="let dt of comments">
                            <div class="step-list_titl step-ln">
                                <h5>{{"applicantApplication.approvalLevel" | translate }}: <span>{{dt.approvalLevels.name}}</span></h5>
                                <h5>{{"applicantApplication.status" | translate }}: <span>{{dt.approvalStatus?.name}}</span></h5>
                                <!-- <h5>Comment: <span>{{dt.comment}}</span></h5> -->
                                <h5>{{"applicantApplication.date" | translate }}: <span>{{dt.created_at | date}}</span></h5>
                            </div>
                            <div class="step-list_dscb">
                                <p>{{"applicantApplication.comment" | translate }}: {{dt.comment}}</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

</ng-template>
