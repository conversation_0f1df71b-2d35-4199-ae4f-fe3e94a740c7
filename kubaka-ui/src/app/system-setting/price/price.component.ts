import { Component, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ngxCsv } from 'ngx-csv';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NgbdAdvancedSortableHeader } from "src/app/shared/directives/sortable.directive";
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';
import { GlobalRefreshService } from 'src/app/shared/services/global-refresh.service';

@Component({
  selector: 'app-price',
  templateUrl: './price.component.html',
  styleUrls: ['./price.component.scss']
})
export class PriceComponent {
  userForm!: UntypedFormGroup;
  submitted = false;
  lists: any[] = [];
  total!: number;
  @ViewChildren(NgbdAdvancedSortableHeader)
  headers!: QueryList<NgbdAdvancedSortableHeader>;
  page = 1;
  pageSize = 20;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  econtent: any = {};
  masterSelected!: boolean;
  outPutData: any = {};
  currentUser: any = {};

  permitTypes: any[] = [];
  permitTypeFilter: any = '';

  fullList: any[] = [];
  filteredList: any[] = [];

  private refreshSub: any;

  constructor(
    private modalService: NgbModal,
    public userService: UserMgtService,
    private formBuilder: UntypedFormBuilder,
    private sessionService: SessionService,
    private globalRefresh: GlobalRefreshService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.initiliazeForm();
    this.loadList();
    this.lookups();
  }

  filterByPermitType() {
    this.fullList = this.content.filter((x: any) => x.permitTypes.id === this.permitTypeFilter);
    this.filterAndPaginate();
  }
  ngOnDestroy() {
    this.userService.searchTerm = '';
    if (this.refreshSub) {
      this.refreshSub.unsubscribe();
    }
  }

  loadList() {
    this.userService
      .findAllWithPath(environment.applicationUrl + APIURLPATH.INVOICEPRICES)
      .subscribe(
        (data) => {
          this.lists = data;
          this.content = data;
          this.fullList = data;
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();
          setTimeout(() => {
            document.getElementById("elmLoader")?.classList.add("d-none");
          }, 1200);
          this.collectionSize = this.lists.length;
          this.total = this.lists.length;
          this.totalRecords = this.lists.length;
          this.startIndex = (this.page - 1) * this.pageSize + 1;
          this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
          if (this.endIndex > this.totalRecords) {
            this.endIndex = this.totalRecords;
          }
          this.lists = this.lists.slice(
            this.startIndex - 1,
            this.endIndex
          );
        });
  }


  ngOnInit(): void {
    this.outPutData.name = 'Invoice Prices';
    this.refreshSub = this.globalRefresh.refreshInterval$.subscribe(() => {
      this.loadList();
    });
  }


  initiliazeForm() {
    this.userForm = this.formBuilder.group({
      id: [""],
      amount: [0, [Validators.required]],
      rangeInSqmMin: ['', [Validators.required]],
      rangeInSqmMax: ['', [Validators.required]],
      permitTypeId: ['', [Validators.required]],
      userId: [this.currentUser.userId, [Validators.required]],
    });
  }

  get form() {
    return this.userForm.controls;
  }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes(term)
    );
  }

  openModal(content: any) {
    this.outPutData.isOpen = true;
    this.outPutData.type = 'add';
    this.outPutData.name = 'Invoice Price';
    this.outPutData.url = environment.applicationUrl + APIURLPATH.INVOICEPRICES;
    this.outPutData.method = '0';
    this.initiliazeForm()
    this.outPutData.userForm = this.userForm;
    this.submitted = false;
    this.modalService.open(content, { size: "md", centered: true });
  }

  checkedValGet: any[] = [];
  onCheckboxChange(e: any) {
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }

  lookups() {
    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => { this.permitTypes = data }
      )

  }

  getPremiumData() {

    this.lists = this.content
      .slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);

  }


  editDataGet(id: any, content: any) {
    this.submitted = false;

    this.modalService.open(content, { size: "md", centered: true });
    // var modelTitle = document.querySelector(".modal-title") as HTMLAreaElement;
    // modelTitle.innerHTML = "Edit User";
    // var updateBtn = document.getElementById("add-btn") as HTMLAreaElement;
    // updateBtn.innerHTML = "Update";

    this.userService.findOneWithPath(id, environment.applicationUrl + APIURLPATH.INVOICEPRICES).subscribe({
      next: (data: any) => {
        const users = data;
        this.econtent = users;
        this.userForm.controls['amount'].setValue(this.econtent.amount);
        this.userForm.controls['rangeInSqmMin'].setValue(this.econtent.rangeInSqmMin);
        this.userForm.controls['rangeInSqmMax'].setValue(this.econtent.rangeInSqmMax);
        this.userForm.controls['permitTypeId'].setValue(this.econtent.permitTypeId);
        this.userForm.controls["id"].setValue(this.econtent.id);
        this.outPutData.type = 'edit';
        this.outPutData.userForm = this.userForm
        this.outPutData.name = 'Invoice Price';
        this.outPutData.url = environment.applicationUrl + APIURLPATH.INVOICEPRICES;
        this.outPutData.isOpen = true;

      },
    });
  }


  deleteData(id: any) {
    if (id) {
      this.userService.deleteWithPath(id, environment.applicationUrl + APIURLPATH.INVOICEPRICES).subscribe({
        next: data => { this.loadList(); },
        error: err => {
        }
      });

    } else {

    }
  }


  deleteMultiple(content: any) {
    var checkboxes: any = document.getElementsByName("checkAll");
    var result;
    var checkedVal: any[] = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        result = checkboxes[i].value;
        checkedVal.push(result);
      }
    }
    if (checkedVal.length > 0) {
      this.modalService.open(content, { centered: true });
    } else {
      Swal.fire({
        text: "Please select at least one checkbox",
        confirmButtonColor: "#239eba",
      });
    }
    this.checkedValGet = checkedVal;
  }

  checkUncheckAll(ev: any) {
    this.lists.forEach((x: { state: any }) => (x.state = ev.target.checked));
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }


  saveValue(event: any) {
    this.loadList();
    this.modalService.dismissAll();
  }



  saveUser() {
    if (this.userForm.invalid) {
    } else {
      if (this.userForm.get("id")?.value) {
        let dataToSave = this.userForm.value;
        dataToSave.updatedBy = this.currentUser.userId;
        this.submitted = true;
        this.userService.patchAssetWithPath(this.userForm.value.id, dataToSave, environment.applicationUrl + APIURLPATH.INVOICEPRICES)
          .subscribe(
            (data: any) => {
              this.submitted = false;
              this.modalService.dismissAll();
            },)
      } else {
        let dataToSave = this.userForm.value;
        dataToSave.createdBy = this.currentUser.userId,
          this.userService.saveAssetWithPath(this.userForm.value, environment.applicationUrl + APIURLPATH.INVOICEPRICES)
            .subscribe(
              (data: any) => {
                this.submitted = false;
                this.modalService.dismissAll();
              },
            )
      }
      // setTimeout(() => {
      //   // this.userForm.reset();
      // }, 2000);
      // this.submitted = true;
    }
  }


  // Csv File Export
  csvFileExport() {
    var users = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalseparator: ".",
      showLabels: true,
      showTitle: true,
      title: "Agency Data",
      useBom: true,
      noDownload: false,
      headers: [
        "ID",
        "amount",
        "rangeInSqmMin",
        "rangeInSqmMax",
      ],
    };
    new ngxCsv(this.content, "role", users);
  }
}
