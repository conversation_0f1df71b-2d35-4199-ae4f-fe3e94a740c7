// .kbtablek-btn {
//     background-color: #0099e8;
//     border-color: #0099e8;
//     border-radius: 0.25rem;
//     color: hsl(0, 0%, 100%);
//     height: 2.125rem;
//     width: 147px;
//     font-size: 0.875rem;
//     letter-spacing: 0;
//     padding: 0.5rem 1rem;
//     font-weight: 500;
//     text-transform: capitalize;
//     cursor: pointer;
// }


// .flrt-grp{
//     display: flex;
//     .form-input_search{
//         flex: 1;
//     }
//     .form-input{
//         flex: 1;
//     }
// }


// .form-input_search.fsm {
//     height: -12.1rem !important;
//     font-size: 0.875rem;
//     letter-spacing: 0;
//     padding: 0;
//     border-radius: 0.25rem;
// }


.kbk-btn.disabled {
    pointer-events: none;
    opacity: 0.6;
    cursor: not-allowed;
  }