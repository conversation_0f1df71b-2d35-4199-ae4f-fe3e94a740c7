<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"newApplication.step2.step" | translate }} <span>2</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{upiInfo?.isFromOldSystem ? ('newApplication.step2.projectFoundOldSystem' | translate) :
                        ('newApplication.step2.projectDetails' | translate)}}</h2>
                </div>
                <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"newApplication.step2.projectName" | translate }}</label>
                                <div>
                                    <textarea id="projectName" name="projectName" formControlName="projectName"
                                        cols="30" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step2.projectDescription" | translate }}</label>
                                <div>
                                    <textarea id="projectDescription" name="projectDescription"
                                        formControlName="projectDescription" cols="30" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"newApplication.step2.representativeFullName" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="ownerFullName" name="ownerFullName"
                                        formControlName="ownerFullName" readonly required>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"newApplication.step2.representativeNationalId" | translate }} <span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="text" id="ownerIdNo" name="ownerIdNo" formControlName="ownerIdNo"
                                        readonly required>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"newApplication.step2.plotSizeSquareMeters" | translate }}<span
                                        class="estrx">*</span></label>
                                <div>
                                    <input type="number" id="plotSize" name="plotSize" formControlName="plotSize"
                                        readonly required>
                                </div>
                            </div>
                        </div>
                        <div class="form-set">
                            <h4 *ngIf="currentUser.data.user.role.code === 'APP' &&
                              projectDetails?.projectStatus?.code === 'PAPRV'" style="color: red;">
                                {{"newApplication.step2.cannotContinueAssigned" | translate }}
                            </h4>
                        </div>
                    </div>

                    <div class="step-panel_footer">

                        <button (click)="onPrev()">{{"newApplication.step2.previous" | translate }}</button>

                        <button type="submit" [disabled]="userForm.invalid" *ngIf="(this.paramsId == '0' || projectDetails?.projectStatus?.code == 'PCRTD') &&
                      projectDetails?.projectStatus?.code !== 'PAPRV'"> {{ this.projectDetails.id ?
                            (('newApplication.step2.update' | translate) + ' ' + ('newApplication.step2.andNext' |
                            translate)) :
                            (('newApplication.step2.save' | translate) + ' ' + ('newApplication.step2.andNext' |
                            translate))
                            }}</button>


                        <!-- <button type="button"
                       (click)="next()" *ngIf="this.projectDetails.id &&
                       (projectDetails.projectStatus.code == 'PCRTD' ||
                       projectDetails.projectStatus.code == 'PRJCT'
                       )">Next</button> -->

                        <button type="button" *ngIf="this.paramsId !== '0'&&
                      projectDetails?.projectStatus?.code !== 'PAPRV'" (click)="next()">{{"newApplication.step2.next" |
                            translate }}</button>

                    </div>
                </form>
            </div>
        </div>
    </div>
</div>