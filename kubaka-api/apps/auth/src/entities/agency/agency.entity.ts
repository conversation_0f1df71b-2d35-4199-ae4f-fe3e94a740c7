import { AbstractEntity } from '@app/common';
import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { AgencyDistrict } from './agencyDistrict.entity';
import { User } from '../user-management/user.entity';
@Entity()
export class Agency extends AbstractEntity<Agency> {
  @Column()
  name: string;

  @Column()
  code: string;

  @Column({ nullable: true })
  paymentAccountIdentifier: string;

  @OneToMany(() => AgencyDistrict, (agencyDistrict) => agencyDistrict.agency)
  districts: AgencyDistrict[];

  @OneToMany(() => User, (user) => user.agency)
  users: User[];

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;
}
