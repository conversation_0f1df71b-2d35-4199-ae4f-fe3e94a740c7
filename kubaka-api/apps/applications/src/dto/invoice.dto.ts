import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class InvoiceStatusDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class PriceDto {
  @ApiProperty({ description: 'The field amount required' })
  @IsNotEmpty({ message: 'The field amount cannot be empty' })
  amount: number;

  @ApiProperty({ description: 'The field rangeInSqmMin required' })
  @IsNotEmpty({ message: 'The field rangeInSqmMin cannot be empty' })
  rangeInSqmMin: string;

  @ApiProperty({ description: 'The field rangeInSqmMax required' })
  @IsNotEmpty({ message: 'The field rangeInSqmMax cannot be empty' })
  rangeInSqmMax: string;

  @ApiProperty({ description: 'The field permitTypeId required' })
  @IsNotEmpty({ message: 'The field permitTypeId cannot be empty' })
  permitTypeId: string;

  @ApiProperty({ description: 'The field userId required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;
}
// export class PriceDto {
//   @ApiProperty({ description: 'The field amount required' })
//   @IsNotEmpty({ message: 'The field amount cannot be empty' })
//   amount: number;

//   @ApiProperty({ description: 'The field range required' })
//   @IsNotEmpty({ message: 'The field range cannot be empty' })
//   range: string;

//   @ApiProperty({ description: 'The field userId required' })
//   @IsNotEmpty({ message: 'The field userId cannot be empty' })
//   userId: string;
// }

export class InvoiceTypeDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code cannot be empty' })
  code: string;
}

export class InvoiceItemDto {
  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  name: string;

  @ApiProperty({ description: 'The field name required' })
  @IsNotEmpty({ message: 'The field name cannot be empty' })
  priceId: string;
}

export class InvoiceDto {
  @IsOptional()
  invoiceNumber: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field applicantUserId cannot be empty' })
  @ApiProperty({ description: 'The field applicantUserId is required' })
  applicantUserId: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  @ApiProperty({ description: 'The field agencyCode is required' })
  agencyCode: string;

  @IsNotEmpty({ message: 'The field paymentAccountIdentifier cannot be empty' })
  @ApiProperty({
    description: 'The field paymentAccountIdentifier is required',
  })
  paymentAccountIdentifier: string;

  @IsNotEmpty({ message: 'The field amount cannot be empty' })
  @ApiProperty({ description: 'The field amount is required' })
  amount: string;

  @IsOptional({ message: 'The field transactionNumber cannot be empty' })
  @ApiProperty({ description: 'The field transactionNumber is required' })
  transactionNumber: string;

  @IsNotEmpty({ message: 'The field invoiceTypeId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceTypeId is required' })
  invoiceTypeId: string;

  @IsNotEmpty({ message: 'The field invoiceItemId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceItemId is required' })
  invoiceItemId: string;

  @IsNotEmpty({ message: 'The field invoiceStatusId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceStatusId is required' })
  invoiceStatusId: string;

  @IsOptional()
  email: string;

  @IsOptional()
  phone: string;

  @IsOptional()
  description: string;

  @IsOptional()
  dueDate: Date;

  @IsOptional()
  externalReferenceNumber: string;
}
export class UpdateInvoiceDto {
  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @IsNotEmpty({ message: 'The field invoiceStatusId cannot be empty' })
  @ApiProperty({ description: 'The field invoiceStatusId is required' })
  invoiceStatusId: string;
}

export class ReceiptDto {
  @IsNotEmpty({ message: 'The field invoiceNumber cannot be empty' })
  @ApiProperty({ description: 'The field invoiceNumber is required' })
  invoiceNumber: string;

  @IsNotEmpty({ message: 'The field transactionId cannot be empty' })
  @ApiProperty({ description: 'The field transactionId is required' })
  transactionId: string;

  @IsNotEmpty({ message: 'The field paymentStatus cannot be empty' })
  @ApiProperty({ description: 'The field paymentStatus is required' })
  paymentStatus: string;

  @IsNotEmpty({ message: 'The field currency cannot be empty' })
  @ApiProperty({ description: 'The field currency is required' })
  currency: string;

  @IsNotEmpty({ message: 'The field transactionId cannot be empty' })
  @ApiProperty({ description: 'The field paidAt is required' })
  paidAt: string;

  @IsNotEmpty({ message: 'The field customer cannot be empty' })
  @ApiProperty({ description: 'The field customer is required' })
  customer: string;

  @IsNotEmpty({ message: 'The field amount cannot be empty' })
  @ApiProperty({ description: 'The field amount is required' })
  amount: string;

  @IsNotEmpty({ message: 'The field paymentMethod cannot be empty' })
  @ApiProperty({ description: 'The field paymentMethod is required' })
  paymentMethod: string;
}
