<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"applicationRoadMap.step" | translate }} <span>{{ currentStep }}</span> / <span>{{ steps.length }}</span> </div>
    </div>
    <div class="stepper-body">
        <ng-container [ngSwitch]="currentStep">
            <app-plot-info *ngSwitchCase="1"></app-plot-info>
            <app-development-details *ngSwitchCase="2" (backToParent)="saveTheResponse($event)"></app-development-details>
            <div *ngIf="outputData.isProjectDetails || currentStep + '' === '3'">
                <app-project-details *ngSwitchCase="3" [inputData]="outputData"></app-project-details>
            </div>
            <!-- <app-project-estimates *ngSwitchCase="4"></app-project-estimates> -->
            <app-project-attachment *ngSwitchCase="4"></app-project-attachment>
        </ng-container>
    </div>
</div>
<!-- <div class="step-panel">
    <div class="step-panel_header">
    </div>
    <div class="step-panel_body">

    </div>
    <div class="step-panel_footer">
      
    </div>
  </div> -->