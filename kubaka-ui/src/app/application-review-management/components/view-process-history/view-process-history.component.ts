import { Component, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ApplicationService } from 'src/app/application/services/application.service';
import { environment } from 'src/environments/environment';
import { ExportExcelService } from 'src/app/services/export-excel.service';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-view-process-history',
  templateUrl: './view-process-history.component.html',
  styleUrls: ['./view-process-history.component.scss'],
})
export class ViewProcessHistoryComponent {
  @Input() inputData: any = {};
  applicationDetails: any = {};
  processHistory: any = {};
  paramId: any;

  expandedRow: any = null;

  toggleExpanded(row: any) {
    this.expandedRow = this.expandedRow === row ? null : row;
  }

  isExpanded(row: any): boolean {
    return this.expandedRow === row;
  }

  getObjectEntries(obj: any): { key: string; value: any }[] {
    return Object.entries(obj).map(([key, value]) => ({ key, value }));
  }

  constructor(
    private applicationService: ApplicationService,
    private route: ActivatedRoute,
    private exportExcelService: ExportExcelService
  ) {}

  ngOnInit() {
    this.route.params.subscribe((params: any) => {
      if (this.inputData && this.inputData.isBackNotAllowed) {
        this.loadInfo(this.inputData.applicationId);
      } else {
        this.paramId = params.id;
        this.inputData.isBackNotAllowed = false;
        this.loadInfo(params.id);
      }
    });
    // }
  }

  loadInfo(id: any) {
    this.applicationService
      .findAllWithPath(
        environment.applicationUrl +
          'approval/applicationApproval/applicationId/' +
          id
      )
      .subscribe((data) => {
        this.applicationDetails = data;
        this.processHistory = this.formatProcessHistory();
      });
  }

  // revamping the process history format

  formatProcessHistory() {
    if (!this.applicationDetails) return [];

    const history: any[] = [];

    const {
      submissionLogs,
      reviewersOnApplications,
      approvalCheckList,
      approvals,
      reviewers,
    } = this.applicationDetails;

    // Submission Logs
    if (submissionLogs?.length) {
      submissionLogs.forEach((log: ProcessHistoryItem) => {
        history.push({
          date: log.createdAt || log.date,
          user: log?.name || log?.user?.name || '-',
          userType: log?.role || 'Applicant',
          ip: log.ipAddress || '-',
          os: log.operatingSystem || '-',
          browser: log.browser || '-',
          status: log.applicationStatusName || 'Draft',
          activity:
            log.applicationStatusName === 'Draft'
              ? 'Project Created'
              : log.applicationStatusName === 'Submitted'
              ? 'Project Submitted'
              : log.applicationStatusName,
        });
      });
    }

    // Reviewers on Applications
    if (reviewersOnApplications?.length) {
      reviewersOnApplications.forEach((rev: ProcessHistoryItem) => {
        history.push({
          date: rev.createdAt || rev.date,
          user: rev?.name || '-',
          userType: rev.role || 'Reviewer',
          ip: rev.ipAddress || '-',
          os: rev.operatingSystem || '-',
          browser: rev.browser || '-',
          status: rev.applicationStatusName || 'Assigned',
          activity: `Assigned to review ${
            this.applicationDetails?.user
              ? this.applicationDetails?.user
              : 'applicant'
          }`,
        });
      });
    }

    // Approval Checklist
    if (approvalCheckList?.length) {
      approvalCheckList.forEach((check: ProcessHistoryItem) => {
        history.push({
          date: check.createdAt,
          user: check?.name || check?.user?.name || '-',
          userType: check?.approvalLevel?.name || 'Checklist Reviewer',
          ip: check.ipAddress || '-',
          os: check.operatingSystem || '-',
          browser: check.browser || '-',
          status: check?.approvalStatus?.name || 'Accepted',
          activity: 'Reviewed the application',
          other: {
            checklist: {
              decision: check?.decision,
              conditionsOfApproval: check?.conditionsOfApproval,
              structuralComment: check?.structuralComment,
              civilEngineeringComment: check?.civilEngineeringComment,
              architecturalComment: check?.architecturalComment,
              urbanPlanningComment: check?.urbanPlanningComment,
              siteAnalysisComment: check?.siteAnalysisComment,
            },
          },
        });
      });
    }

    // Format Approvals
    if (approvals?.length) {
      approvals.forEach((app: ProcessHistoryItem) => {
        history.push({
          date: app.createdAt,
          user: app.user?.firstName + ' ' + app.user?.lastName || '-',
          userType: app.user?.userType?.name || 'Approver',
          ip: app.ipAddress || '-',
          os: app.operatingSystem || '-',
          browser: app.browser || '-',
          status:
            app.applicationStatusName || app.approvalStatus?.name || 'Approved',
          activity: 'Approval Action',
          other: {
            comment: app.comment,
          },
        });
      });
    }

    // Sort by date descending
    history.sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // console.table(history);
    const historyWithTimeDiff = this.calculateTimeDifferences(history);

    return historyWithTimeDiff;
  }

  private calculateTimeDifferences(history: any[]): any[] {
    return history.map((entry, index) => {
      let timeDifference = '';
      let timeDifferenceInMs = 0;

      if (index > 0) {
        const currentDate = new Date(entry.date);
        const previousDate = new Date(history[index - 1].date);
        timeDifferenceInMs = currentDate.getTime() - previousDate.getTime();
        timeDifference = this.formatTimeDifference(timeDifferenceInMs);
      }

      return {
        ...entry,
        timeDifference,
        timeDifferenceInMs,
      };
    });
  }

  private formatTimeDifference(milliseconds: number): string {
    if (milliseconds <= 0) return '';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      const remainingHours = hours % 24;
      return days === 1
        ? `1 day${remainingHours > 0 ? ` ${remainingHours}h` : ''}`
        : `${days} days${remainingHours > 0 ? ` ${remainingHours}h` : ''}`;
    } else if (hours > 0) {
      const remainingMinutes = minutes % 60;
      return hours === 1
        ? `1 hr${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`
        : `${hours} hr${
            remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''
          }`;
    } else if (minutes > 0) {
      return minutes === 1 ? '1 min' : `${minutes} min`;
    } else {
      return seconds === 1 ? '1 sec' : `${seconds} sec`;
    }
  }

  generateProcessHistoryExportData(): XLSX.WorkSheet {
    if (!this.processHistory || this.processHistory.length === 0) {
      return [];
    }

    const headerInfo = [
      [
        'Project Name',
        this.applicationDetails?.application?.projects.projectName || '-',
      ],
      [
        'Project UPI',
        this.applicationDetails?.application?.projects.upi || '-',
      ],
      [
        'Application Name',
        this.applicationDetails?.application?.applicationName || '-',
      ],
      ['Agency', this.applicationDetails?.application?.agencyCode || '-'],
      [
        'Permit Type',
        this.applicationDetails?.application?.permitTypes?.name || '-',
      ],
      [
        'Category Type',
        this.applicationDetails?.application?.categoryTypes?.name || '-',
      ],
      [
        'Build Type',
        this.applicationDetails?.application?.buildTypes?.name || '-',
      ],
      [
        'Submitted On',
        this.applicationDetails?.application?.submittedDate || '-',
      ],
      [],
    ];

    const tableHeader = [
      'Date',
      'User',
      'User Type',
      'Status',
      'Activity',
      'IP Address',
      'OS',
      'Browser',
      'Duration',
      // 'Checklist',
      'Comments',
    ];

    const dataRows = this.processHistory.map((row: ProcessHistoryItem) => [
      new Date(row.date).toLocaleString(),
      row.user,
      row.userType,
      row.status,
      row.activity,
      row.ip || '',
      row.os || '',
      row.browser || '',
      row.timeDifference || '',
      // row.other?.checklist
      //   ? this.getObjectEntries(row.other.checklist)
      //       .map((item) => {
      //         const keyWithSpaces = item.key.replace(
      //           /([a-z])([A-Z])/g,
      //           '$1 $2'
      //         );
      //         const formattedKey = keyWithSpaces.charAt(0).toUpperCase() + keyWithSpaces.slice(1);
      //         return `${formattedKey}: ${item.value || '-'}`;
      //       })
      //       .join('\n')
      //   : '',
      // row.comment || '',
    ]);

    const worksheetData = [...headerInfo, tableHeader, ...dataRows];
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // Set column widths
    worksheet['!cols'] = fitToColumn(dataRows);
    function fitToColumn(dataRows: any[]): XLSX.ColInfo[] {
      return dataRows[0].map((a: any, i: number) => ({
        wch: Math.max(
          ...dataRows.map((a2) => (a2[i] ? a2[i].toString().length + 2 : 10))
        ),
      }));
    }

    // Set row heights
    worksheet['!rows'] = Array(worksheetData.length).fill({ hpx: 20 });

    return worksheet;
  }

  downloadProcessHistoryCSV() {
    const worksheetData = this.generateProcessHistoryExportData();
    this.exportExcelService.exportProcessHistoryToCSV(
      worksheetData,
      `${
        this.applicationDetails?.application?.applicationName || 'Application'
      }_Process_History.xlsx`,
      'Process History'
    );
  }
}

export interface ProcessHistoryItem {
  decision: any;
  conditionsOfApproval: any;
  structuralComment: any;
  civilEngineeringComment: any;
  architecturalComment: any;
  urbanPlanningComment: any;
  siteAnalysisComment: any;
  approvalLevel: any;
  approvalStatus: any;
  applicationStatusName: string;
  ipAddress: string;
  operatingSystem: string;
  assignedAt: string | Date;
  reviewer: any;
  role: string;
  createdAt: string | Date;
  name: string;
  date: string | Date;
  user: any;
  userType: string;
  browser: string;
  status: string;
  activity: string;
  other: {
    approvalLevel: string;
    checklist: {};
  };
  comment: string;
  ip: string;
  os: string;
  timeDifference?: string;
  timeDifferenceInMs?: number;
}
