import {
  Body,
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Get,
  Param,
  Delete,
  HttpStatus,
} from '@nestjs/common';
import { DocumentsManagementService } from './documents-management.service';

import { FileInterceptor } from '@nestjs/platform-express';

import { BufferedFile } from '../minio-client/file.model';
import { CreateApplicationDocumentDto } from '../dto/application-documents.dto';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Documents')
@Controller('documents-management')
export class DocumentsManagementController {
  constructor(
    private readonly documentsManagementService: DocumentsManagementService,
  ) {}

  // file upload handling logic
  @Post('file-upload')
  @UseInterceptors(FileInterceptor('file'))
  async fileUpload(
    @UploadedFile() file: BufferedFile,
    @Body() createApplicationDocumentDto: CreateApplicationDocumentDto,
  ) {
    try {
      if (!file) {
        console.log('File is undefined');
        return 'File is undefined';
      }

      // Process or save the file here
      const { fileUrl, fileName } =
        await this.documentsManagementService.uploadFile(file);

      // Update createRequiredDocumentDto with the file information
      createApplicationDocumentDto.fileUrl = fileUrl;
      createApplicationDocumentDto.fileName = fileName;
      // console.log("this is file ",fileName)
      // console.log("and this is url",fileUrl)

      // Now you can use createRequiredDocumentDto for further processing
      await this.documentsManagementService.createApplicationDocument(
        createApplicationDocumentDto,
      );

      return {
        status: HttpStatus.CREATED,
        message: 'File uploaded successfully!',
        data: {
          url: fileUrl,
          filename: fileName,
        },
      };
    } catch (error) {
      console.error('Error uploading file in controller', error);
      // Handle the error appropriately, possibly by returning an error response.
      throw new Error('File upload failed in controller');
    }
  }

  @Get('get-file/:fileName')
  async getFile(@Param('fileName') fileName: string) {
    // const fileUrl = await this.fileUploadService.getFileUrl(fileName);

    // const file = createReadStream(join(process.cwd(), `uploads/${filename}`));
    return await this.documentsManagementService.getFileUrl(fileName);
    // return { file_url: fileUrl};
  }
  // deleting file logic

  @Delete('delete-file/:fileName')
  async deleteFile(@Param('fileName') fileName: string) {
    await this.documentsManagementService.deleteFile(fileName);
    return { message: 'File deleted successfully' };
  }
}
