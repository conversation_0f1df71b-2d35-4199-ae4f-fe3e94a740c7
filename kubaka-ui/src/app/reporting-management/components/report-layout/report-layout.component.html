<div class="app-dash">
    <div class="container">
        <div class="app-main">
            <div class="app-sysheader">
                <span class="hder" aria-label="header tittle">Reports</span>
                <ul class="kbk-tabs">
                    <li *ngIf="currentUser?.permissions?.isAllowToSeeUserReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/user-report']">User</a>
                    </li>
                    <li *ngIf="currentUser?.permissions?.isAllowToSeeGeneralApplicationReport
                    ||
                    currentUser.data.user.agency.code === 'RHA'" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/application-report']">Application</a>
                    </li>

                    <!-- <li *ngIf="currentUser?.permissions?.isAllowToSeeFilteredApplicationReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/application-report']">Application</a>
                    </li> -->
                    <!-- <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/grid-chart-sample']">Grid Chart</a>
                    </li> -->
                    <li *ngIf="currentUser?.permissions?.isAllowToSeeDashboardReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/dashboard/analytic-dashboard']">Dashboard</a>
                    </li>

                    <li *ngIf="currentUser?.permissions?.isAllowToSeeChatBoardReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/chat-board-report']">Chat board report</a>
                    </li>

                    <li *ngIf="currentUser?.permissions?.isAllowToSeeNonObjectionReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/non-objection-report']">Non objection report</a>
                    </li>

                    <li *ngIf="isRHAReviewer" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/non-objection-report']">Non objection report</a>
                    </li>

                    <li *ngIf="currentUser?.permissions?.isAllowToSeeWeeklyReport" class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/reviewer-weekly-report']">Weekly report</a>
                    </li>
                    <!-- <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/application-approval-report']">Application Approval report</a>
                    </li> -->




                    <!-- <li class="kbk-tabs-item">
                        <a class="kbk-tabs-link" routerLinkActive="active"
                            [routerLink]="['/account/report/all-data-by-reviewer-report']">All reviewer Weekly report</a>
                    </li> -->
                </ul>
            </div>
            <div class="app-cont">
                <!-- <div class="app-cont_title">

                </div> -->
                <div class="app-cont_list" style="padding: 0; border: none;">
                    <!-- body page -->
                    <router-outlet></router-outlet>
                    <!-- body page -->
                </div>
            </div>
        </div>
    </div>
</div>
