<div class="">
    <div class="modol-header" *ngIf="!submitted">
        <h2 *ngIf="inputData?.verifyType === 'NID'" id="exampleModalLabel">{{responseData.isNida ? 'National ID verified' : 'National ID is not verified'}}</h2>
        <h2 *ngIf="inputData?.verifyType === 'EIA'" id="exampleModalLabel">{{responseData.isEIA ? 'EIA Certificate verified' : 'No certificate found'}}</h2>
        <h2 *ngIf="inputData?.verifyType === 'RRA'" id="exampleModalLabel">{{responseData.isRRA ? 'No unpaid taxes found' : displayErrorMessage}}</h2>
        <h2 *ngIf="inputData?.verifyType === 'LIS'" id="exampleModalLabel">{{responseData.isLis ? 'UPI found in LIS' : 'UPI not found in LIS'}}</h2>

        <!-- <span *ngIf="outputData.verifyType === 'LIS'">Comming soon....</span> -->
        <!-- <p>Deleting your done will remove all of your information from our database.</p> -->
    </div>

    <div class="modol-header" *ngIf="submitted">
        <h2 id="exampleModalLabel">Verifying...</h2>
        <!-- <p>Deleting your done will remove all of your information from our database.</p> -->
    </div>
    <div class="modal-body" style="display: none;">
        <!-- <div class="mt-md">
            <h4>You are about to delete a test ?</h4>
        </div> -->
        <div class="kbk-x-c sp-sm mt-md">
            <!-- <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                id="deleteRecord-close">Close</button> -->
            <!-- <button type="button" class="kbk-btn kbk-btn-error" id="delete-product" (click)="deleteData('')"
          (click)="deleteFile()">Yes, Delete!</button> -->
        </div>


    </div>
    <!--  -->
    <div class="modol-content" *ngIf="!responseData.errorMessage && responseData.isNida">
        <form class="form" autocomplete="off">
            <div class="profile">
                <div class="profile-pic">
                    <!-- <img class="avt" src="assets/imgs/profile1.svg" alt="" />
                    <img src="assets/imgs/profile.jpg" alt="" /> -->
                    <img *ngIf="!inputData.photo" src="assets/imgs/profile1.svg" alt="" />
                    <img *ngIf="inputData.photo" [src]="'data:image/png;base64,' + inputData.photo" alt="Profile Image" />
                </div>
                <div class="profile-dtail">
                    <div class="dtail-out">
                        <span class="lebo">ID Number</span>
                        <span class="txt">{{ inputData.idNumber }}</span>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Names</span>
                            <span class="txt">{{ inputData.names }}</span>
                        </div>
                        <div class="dtail-out">
                            <span class="lebo">Date of Birth</span>
                            <span class="txt">{{ inputData.dateOfBirth }}</span>
                        </div>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Gender</span>
                            <span class="txt">{{ inputData.sex }}</span>
                        </div>
                        <div class="dtail-out">
                            <span class="lebo">Place of Issue</span>
                            <span class="txt">{{ inputData.district }}</span>
                        </div>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Marital Status</span>
                            <span class="txt">{{ inputData.maritalStatus }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modol-content">
        <form class="form" autocomplete="off">
            <div *ngIf="responseData.errorMessage">
                <div class="form-input">
                    <label>Message</label>
                    <div>
                        <textarea name="errorMessage" id="errorMessage" [(ngModel)]="responseData.errorMessage"
                            readonly></textarea>
                    </div>
                </div>
            </div>
        </form>
    </div>







    <div class="modol-content" *ngIf="responseData.isLis">
        <form class="form" autocomplete="off">
            <div class="profile">
                <div class="profile-pic">
                    <!-- <img class="avt" src="assets/imgs/profile1.svg" alt="" />
                    <img src="assets/imgs/profile.jpg" alt="" /> -->
                    <img *ngIf="!inputData.photo" src="assets/imgs/profile1.svg" alt="" />
                    <img *ngIf="inputData.photo" [src]="'data:image/png;base64,' + inputData.photo" alt="Profile Image" />
                </div>
                <div class="profile-dtail">
                    <span class="hder" aria-label="header tittle">Owner</span>
                    <div class="dtail-out">
                        <span class="lebo">ID Number</span>
                        <span class="txt">{{ responseData.owners[0]?.idNo }}</span>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Names</span>
                            <span class="txt">{{ responseData.owners[0]?.fullName }}</span>
                        </div>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Gender</span>
                            <span class="txt">{{responseData.owners[0]?.gender === 'M' ? 'Male' : 'Female'}}</span>
                        </div>
                        <div class="dtail-out">
                            <span class="lebo">Marital Status</span>
                            <span class="txt">{{responseData.owners[0]?.maritalStatus}}</span>
                        </div>
                    </div>
                </div>
                <div class="profile-dtail">
                    <span class="hder" aria-label="header tittle">Representative</span>
                    <div class="dtail-out">
                        <span class="lebo">ID Number</span>
                        <span class="txt">{{ responseData.representative?.idNo }}</span>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Names</span>
                            <span class="txt">{{ responseData.representative.surname }}
                                {{ responseData.representative.foreNames }}
                            </span>
                        </div>
                    </div>
                    <div class="kbk-x-s sp-2">
                        <div class="dtail-out">
                            <span class="lebo">Gender</span>
                            <span class="txt">{{responseData.representative.gender === 'M' ? 'Male' : 'Female'}}</span>
                        </div>
                        <div class="dtail-out">
                            <span class="lebo">Marital Status</span>
                            <span class="txt">{{responseData.representative.maritalStatus}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>


<div class="stepper-content" *ngIf="inputData?.verifyType === 'EIA'">
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <form class="form" autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>Certificate Number</label>
                                <div>
                                    <input class="flg" type="text" name="certificateNumber" id="certificateNumber"
                                        readonly [(ngModel)]="responseData.certificateNumber" required />
                                </div>
                            </div>
                            <div class="form-input">
                                <label>Name</label>
                                <div>
                                    <input class="flg" type="text" name="name" id="name" readonly
                                        [(ngModel)]="responseData.name" required />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="step-panel_footer">
                        <button type="button" *ngIf="submitted"> Wait... </button>
                        <button type="button" (click)="verifyCertificate()" *ngIf="!submitted"> Verify certificate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>







<div class="stepper-content" style="display: none;">
    <div class="stepper-body">
        <div>
            <div class="step-panel">


                <form class="form" autocomplete="off">
                    <div class="step-panel_body">

                        <section class="kbk-x-s kbk-aend sp-2">
                            <div>
                                <div class="track-info">
                                    <div class="track-profile">

                                    </div>
                                    <div class="track-dtail">
                                        <label>Applicant</label>
                                        <span class="track-user"></span>
                                        <span class="track-usersub"></span>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <!--
                        <div class="form-set">
                            <div class="form-input">
                                <label>Names</label>
                                <div>
                                    <input class="flg" type="text" name="names" id="names" [(ngModel)]="inputData.names"
                                    required />
                                </div>
                            </div>
                            <div class="form-input">
                                <label>Natiomal ID</label>
                                <div>
                                    <input class="flg" type="text" name="idNumber" id="idNumber" [(ngModel)]="inputData.idNumber"
                                        required />

                                </div>
                            </div>

                        </div> -->
                    </div>


                </form>
            </div>
        </div>
    </div>
</div>





<ng-template #upiFromLandContent role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel">Verification</h2>
        <!-- <p> We will check if you have unpaid taxes, look in the old BPMIS for your project, and pull your zoning plan.
        </p> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modol-content">
        <app-upi-info [info]="'2'" ></app-upi-info>
    </div>
  </ng-template>
