import { Injectable } from '@angular/core';
import {
  landingDataEnglish,
  landingDataFrench,
  landingDataKiny,
} from '../auth-pages/components/landing-page/data';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LanguageService {
  private defaultLangService = new BehaviorSubject(landingDataEnglish);
  public services$ = this.defaultLangService.asObservable();

  get services() {
    return this.defaultLangService.value;
  }

  constructor(private translateService: TranslateService) {
    this.translateService.addLangs(['en', 'kin', 'fr']);
    this.translateService.setDefaultLang('en');
    this.updateServicesData('en');
  }

    private updateServicesData(lang: string): void {
    let newServices = landingDataEnglish;
    if (lang === 'kin') newServices = landingDataKiny;
    else if (lang === 'fr') newServices = landingDataFrench;

    this.defaultLangService.next(newServices);
  }

  getCurrentLanguage(): string {
    return this.translateService.currentLang || this.translateService.defaultLang;
  }

  switchLang(lang: string): Promise<any> {
    return this.translateService.use(lang).toPromise().then(() => {
      this.updateServicesData(lang);
      return lang;
    }).catch(error => {
      // console.error('Language switch failed:', error);
      this.updateServicesData('en');
      throw error;
    });
  }
}
