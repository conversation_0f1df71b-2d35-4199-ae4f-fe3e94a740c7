import { Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-transfered-certificates',
  templateUrl: './transfered-certificates.component.html',
  styleUrls: ['./transfered-certificates.component.scss']
})
export class TransferedCertificatesComponent {
  lists: any[] = [];
  currentUser: any = {};
  saveForm!: UntypedFormGroup;
  submitted: boolean = false;

  page = 1;
  pageSize = 4;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];


  constructor(
    public applicationService: ApplicationService,
    private sessionService: SessionService,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private modalService: NgbModal,
    public utilService: UtilService,
    private userService: UserMgtService
  ) {
    this.currentUser = this.sessionService.getSession();
    // this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    this.applicationService.findAllWithPath(environment.applicationUrl + 'certificate/transfer')
      .subscribe(
        data => {
          // this.lists = data;
          this.fullList = data;
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate();
        }
      )
  }


  ngOnDestroy() {
    this.applicationService.searchTerm = '';
  }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    if (!this.userService.searchTerm) return true;
    const term = this.userService.searchTerm.toLowerCase();
    return Object.values(item).some(val =>
      String(val).toLowerCase().includes(term)
    );
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    this.filterAndPaginate();
  }



  openModal(content: any, sizeParams: any, event: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }

}
