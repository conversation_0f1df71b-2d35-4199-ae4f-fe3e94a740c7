const cluster = require('cluster');
const os = require('os');
const { spawn } = require('child_process');

// Get number of CPU cores dynamically
const numCPUs = os.cpus().length;
const appName = process.argv[2] || 'applications';

// Calculate optimal number of workers based on CPU cores
// Use 75% of available cores to leave some for system processes
const optimalWorkers = Math.max(1, Math.floor(numCPUs * 0.75));

console.log(`System has ${numCPUs} CPU cores available`);
console.log(`Starting ${optimalWorkers} worker processes for optimal performance`);

if (cluster.isMaster) {
  console.log(`Master process ${process.pid} is running`);
  console.log(`Starting ${appName} with ${optimalWorkers} worker processes`);

  // Store worker information
  const workers = new Map();

  // Fork workers
  for (let i = 0; i < optimalWorkers; i++) {
    const worker = cluster.fork({
      WORKER_ID: i + 1,
      CPU_CORES: numCPUs,
      APP_NAME: appName
    });
    
    workers.set(worker.id, {
      pid: worker.process.pid,
      startTime: Date.now(),
      requests: 0
    });
    
    console.log(`Worker ${worker.process.pid} started (ID: ${worker.id})`);
  }

  // Handle worker exit and restart
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
    workers.delete(worker.id);
    
    const newWorker = cluster.fork({
      WORKER_ID: worker.id,
      CPU_CORES: numCPUs,
      APP_NAME: appName
    });
    
    workers.set(newWorker.id, {
      pid: newWorker.process.pid,
      startTime: Date.now(),
      requests: 0
    });
    
    console.log(`New worker ${newWorker.process.pid} started (ID: ${newWorker.id})`);
  });

  // Monitor cluster health
  cluster.on('online', (worker) => {
    console.log(`Worker ${worker.process.pid} is online`);
  });

  // Handle messages from workers
  cluster.on('message', (worker, message) => {
    if (message.type === 'request') {
      const workerInfo = workers.get(worker.id);
      if (workerInfo) {
        workerInfo.requests++;
      }
    }
  });

  // Log cluster status periodically
  setInterval(() => {
    console.log(`\n=== Cluster Status ===`);
    console.log(`Active workers: ${Object.keys(cluster.workers).length}`);
    console.log(`Total CPU cores: ${numCPUs}`);
    console.log(`Workers per core: ${(optimalWorkers / numCPUs).toFixed(2)}`);
    
    for (const [id, info] of workers) {
      const uptime = Math.floor((Date.now() - info.startTime) / 1000);
      console.log(`Worker ${id} (PID: ${info.pid}): ${info.requests} requests, uptime: ${uptime}s`);
    }
    console.log(`=====================\n`);
  }, 30000); // Log every 30 seconds

  // Graceful shutdown
  const gracefulShutdown = (signal) => {
    console.log(`Master received ${signal}, shutting down workers gracefully...`);
    
    // Stop accepting new connections
    for (const id in cluster.workers) {
      cluster.workers[id].send({ type: 'shutdown' });
    }
    
    // Wait a bit then force kill
    setTimeout(() => {
      for (const id in cluster.workers) {
        cluster.workers[id].kill();
      }
      process.exit(0);
    }, 5000);
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

} else {
  // Worker process
  const workerId = process.env.WORKER_ID;
  const cpuCores = process.env.CPU_CORES;
  const appName = process.env.APP_NAME;
  
  console.log(`Worker ${process.pid} (ID: ${workerId}) started for ${appName}`);
  console.log(`Worker running on CPU core ${workerId} of ${cpuCores}`);

  // Start the NestJS application
  const child = spawn('pnpm', ['run', 'start:dev', appName], {
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      WORKER_ID: workerId,
      CPU_CORES: cpuCores,
      APP_NAME: appName
    }
  });

  // Send request count to master periodically
  let requestCount = 0;
  setInterval(() => {
    if (requestCount > 0) {
      process.send({ type: 'request', count: requestCount });
      requestCount = 0;
    }
  }, 10000);

  child.on('exit', (code) => {
    console.log(`Worker ${process.pid} exited with code ${code}`);
    process.exit(code);
  });

  // Handle shutdown signal from master
  process.on('message', (message) => {
    if (message.type === 'shutdown') {
      console.log(`Worker ${process.pid} received shutdown signal`);
      child.kill('SIGTERM');
    }
  });

  // Handle signals for graceful shutdown
  process.on('SIGTERM', () => {
    console.log(`Worker ${process.pid} received SIGTERM`);
    child.kill('SIGTERM');
  });

  process.on('SIGINT', () => {
    console.log(`Worker ${process.pid} received SIGINT`);
    child.kill('SIGINT');
  });
} 