import { AbstractEntity } from '@app/common';
import {
  Entity,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { PermitType } from './settings.entity';
import { Application } from './application.entity';

@Entity()
export class ApprovalLevel extends AbstractEntity<ApprovalLevel> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @Column({ type: 'varchar' })
  agencyId: string;

  @OneToMany(
    () => ApplicationApproval,
    (applicationApproval) => applicationApproval.approvalLevels,
  )
  applicationApprovals: ApplicationApproval[];

  @OneToMany(
    () => ApplicationApprovalCheckList,
    (applicationApprovalCheckList) =>
      applicationApprovalCheckList.approvalLevels,
  )
  applicationApprovalCheckLists: ApplicationApprovalCheckList[];

  @OneToMany(
    () => ApprovalStatus,
    (approvalStatus) => approvalStatus.approvalLevels,
  )
  approvalStatus: ApprovalStatus[];
}

@Entity()
export class ApprovalStatus extends AbstractEntity<ApprovalStatus> {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @Column({ type: 'varchar', default: 0, nullable: true })
  additionalCode: string;

  @ManyToOne(
    () => ApprovalLevel,
    (approvalLevel) => approvalLevel.approvalStatus,
  )
  approvalLevels: ApprovalLevel;

  @OneToMany(
    () => ApprovalDocument,
    (approvalDocument) => approvalDocument.approvalStatus,
  )
  approvalDocuments: ApprovalDocument[];

  @OneToMany(
    () => ApplicationApproval,
    (applicationApproval) => applicationApproval.approvalStatus,
  )
  applicationApprovals: ApplicationApproval[];

  @OneToMany(
    () => ApplicationApprovalCheckList,
    (applicationApprovalCheckList) =>
      applicationApprovalCheckList.approvalStatus,
  )
  applicationApprovalCheckLists: ApplicationApprovalCheckList[];
}

// with a file
// @Entity()
// export class ApplicationApproval extends AbstractEntity<ApplicationApproval> {
//   @Column({ type: 'varchar' })
//   comment: string;

//   @Column({ nullable: true })
//   applicationStatusId: string;

//   @ManyToOne(
//     () => Application,
//     (application) => application.applicationApprovals,
//   )
//   applications: Application;

//   @ManyToOne(
//     () => ApprovalStatus,
//     (approvalStatus) => approvalStatus.applicationApprovals,
//   )
//   approvalStatus: ApprovalStatus;

//   @ManyToOne(
//     () => ApprovalLevel,
//     (approvalLevel) => approvalLevel.applicationApprovals,
//   )
//   approvalLevels: ApprovalLevel;

//   @Column()
//   userId: string;

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;

//   @UpdateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public updated_at!: Date;

//   @Column({ type: 'text', nullable: true })
//   fileBase64: string;
// }

// with ip address and browser
@Entity()
export class ApplicationApproval extends AbstractEntity<ApplicationApproval> {
  @Column({ type: 'varchar' })
  comment: string;

  @Column({ nullable: true })
  applicationStatusId: string;

  @ManyToOne(
    () => Application,
    (application) => application.applicationApprovals,
  )
  applications: Application;

  @ManyToOne(
    () => ApprovalStatus,
    (approvalStatus) => approvalStatus.applicationApprovals,
  )
  approvalStatus: ApprovalStatus;

  @ManyToOne(
    () => ApprovalLevel,
    (approvalLevel) => approvalLevel.applicationApprovals,
  )
  approvalLevels: ApprovalLevel;

  @Column()
  userId: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  operatingSystem: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}
// // without ip address and browser
// @Entity()
// export class ApplicationApproval extends AbstractEntity<ApplicationApproval> {
//   @Column({ type: 'varchar' })
//   comment: string;

//   @Column({ nullable: true })
//   applicationStatusId: string;

//   @ManyToOne(
//     () => Application,
//     (application) => application.applicationApprovals,
//   )
//   applications: Application;

//   @ManyToOne(
//     () => ApprovalStatus,
//     (approvalStatus) => approvalStatus.applicationApprovals,
//   )
//   approvalStatus: ApprovalStatus;

//   @ManyToOne(
//     () => ApprovalLevel,
//     (approvalLevel) => approvalLevel.applicationApprovals,
//   )
//   approvalLevels: ApprovalLevel;

//   @Column()
//   userId: string;

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;

//   @UpdateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public updated_at!: Date;
// }

@Entity()
export class PermitCheckList extends AbstractEntity<PermitCheckList> {
  @Column()
  name: string;

  @Column({ type: 'varchar' })
  code: string;

  @ManyToOne(() => PermitType, (permitType) => permitType.permitCheckLists)
  permitTypes: PermitType;

  @OneToMany(
    () => ApplicationApprovalCheckList,
    (applicationApprovalCheckList) =>
      applicationApprovalCheckList.permitCheckLists,
  )
  applicationApprovalCheckLists: ApplicationApprovalCheckList[];
}

// with a file
@Entity()
export class ApplicationApprovalCheckList extends AbstractEntity<ApplicationApprovalCheckList> {
  @Column({ nullable: true })
  decision: string;

  @Column({ nullable: true })
  conditionsOfApproval: string;

  @Column({ nullable: true })
  structuralComment: string;

  @Column({ nullable: true })
  civilEngineeringComment: string;

  @Column({ nullable: true })
  architecturalComment: string;

  @Column({ nullable: true })
  urbanPlanningComment: string;

  @Column({ nullable: true })
  siteAnalysisComment: string;

  @Column({ type: 'varchar' })
  userId: string;

  @ManyToOne(
    () => PermitCheckList,
    (permitCheckList) => permitCheckList.applicationApprovalCheckLists,
  )
  permitCheckLists: PermitCheckList;

  @ManyToOne(
    () => Application,
    (application) => application.applicationApprovalCheckLists,
  )
  applications: Application;

  @ManyToOne(
    () => ApprovalStatus,
    (approvalStatus) => approvalStatus.applicationApprovalCheckLists,
  )
  approvalStatus: ApprovalStatus;

  @ManyToOne(
    () => ApprovalLevel,
    (approvalLevel) => approvalLevel.applicationApprovalCheckLists,
  )
  approvalLevels: ApprovalLevel;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @Column({ type: 'text', nullable: true })
  fileBase64: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column({ nullable: true })
  browser: string;

  @Column({ nullable: true })
  operatingSystem: string;
}
// // without a file
// @Entity()
// export class ApplicationApprovalCheckList extends AbstractEntity<ApplicationApprovalCheckList> {
//   @Column({ nullable: true })
//   decision: string;

//   @Column({ nullable: true })
//   conditionsOfApproval: string;

//   @Column({ nullable: true })
//   structuralComment: string;

//   @Column({ nullable: true })
//   civilEngineeringComment: string;

//   @Column({ nullable: true })
//   architecturalComment: string;

//   @Column({ nullable: true })
//   urbanPlanningComment: string;

//   @Column({ nullable: true })
//   siteAnalysisComment: string;

//   @Column({ type: 'varchar' })
//   userId: string;

//   // @Column({ type: 'varchar', nullable: true })
//   // applicationId: string;

//   @ManyToOne(
//     () => PermitCheckList,
//     (permitCheckList) => permitCheckList.applicationApprovalCheckLists,
//   )
//   permitCheckLists: PermitCheckList;

//   @ManyToOne(
//     () => Application,
//     (application) => application.applicationApprovalCheckLists,
//   )
//   applications: Application;

//   @ManyToOne(
//     () => ApprovalStatus,
//     (approvalStatus) => approvalStatus.applicationApprovalCheckLists,
//   )
//   approvalStatus: ApprovalStatus;

//   @ManyToOne(
//     () => ApprovalLevel,
//     (approvalLevel) => approvalLevel.applicationApprovalCheckLists,
//   )
//   approvalLevels: ApprovalLevel;

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;
// }

//before
// @Entity()
// export class ApplicationApprovalCheckList extends AbstractEntity<ApplicationApprovalCheckList> {
//   @Column({ nullable: true })
//   decision: string;

//   @Column({ nullable: true })
//   conditionsOfApproval: string;

//   @Column({ nullable: true })
//   structuralComment: string;

//   @Column({ nullable: true })
//   civilEngineeringComment: string;

//   @Column({ nullable: true })
//   architecturalComment: string;

//   @Column({ nullable: true })
//   urbanPlanningComment: string;

//   @Column({ nullable: true })
//   siteAnalysisComment: string;

//   @Column({ type: 'varchar' })
//   userId: string;

//   @ManyToOne(
//     () => PermitCheckList,
//     (permitCheckList) => permitCheckList.applicationApprovalCheckLists,
//   )
//   permitCheckLists: PermitCheckList;

//   @ManyToOne(
//     () => Application,
//     (application) => application.applicationApprovalCheckLists,
//   )
//   applications: Application;

//   @ManyToOne(
//     () => ApprovalStatus,
//     (approvalStatus) => approvalStatus.applicationApprovalCheckLists,
//   )
//   approvalStatus: ApprovalStatus;

//   @ManyToOne(
//     () => ApprovalLevel,
//     (approvalLevel) => approvalLevel.applicationApprovalCheckLists,
//   )
//   approvalLevels: ApprovalLevel;

//   @CreateDateColumn({
//     type: 'timestamptz',
//     default: () => 'CURRENT_TIMESTAMP',
//     select: true,
//   })
//   public created_at!: Date;
// }

@Entity()
export class ApprovalDocument extends AbstractEntity<ApprovalDocument> {
  // @Column()
  // url: string;
  @Column({ type: 'varchar' })
  documentId: string;

  @Column({ type: 'varchar' })
  userId: string;

  @ManyToOne(() => Application, (application) => application.approvalDocuments)
  applications: Application;

  @ManyToOne(
    () => ApprovalStatus,
    (approvalStatus) => approvalStatus.approvalDocuments,
  )
  approvalStatus: ApprovalStatus;

  @Column()
  requiredDocumentId: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public created_at!: Date;

  @UpdateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  public updated_at!: Date;
}
