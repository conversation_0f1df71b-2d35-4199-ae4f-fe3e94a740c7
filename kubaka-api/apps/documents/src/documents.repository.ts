import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { Documents } from '../entities/documents.entity';

@Injectable()
export class DocumentsRepository extends AbstractRepository<Documents> {
  constructor(
    @InjectRepository(Documents)
    documentsRepository: Repository<Documents>,
    entityManager: EntityManager,
  ) {
    super(entityManager, documentsRepository);
  }
}
