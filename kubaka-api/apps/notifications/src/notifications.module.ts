import { Module } from '@nestjs/common';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseModule } from '@app/common';
import {
  ChatBoard,
  IssueCategory,
  Message,
} from './entities/notification.entity';
import {
  ChatBoardRepository,
  IssueCategoryRepository,
  MessagesRepository,
} from './notifications.repository';
import { AUTH_SERVICE } from '@app/common/constants';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([Message, ChatBoard, IssueCategory]),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ClientsModule.registerAsync([
      {
        name: AUTH_SERVICE,
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('AUTH_HOST'),
            port: configService.get('AUTH_PORT'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    MessagesRepository,
    IssueCategoryRepository,
    ChatBoardRepository,
  ],
})
export class NotificationsModule {}
