import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import {
  Certificate,
  DevelopmentStatus,
  RandomInspection,
  Signatory,
  SiteStatus,
  TransferCertificate,
} from '../entities/certificate.entity';
import { AbstractRepository } from '@app/common';

@Injectable()
export class CertificateRepository extends AbstractRepository<Certificate> {
  constructor(
    @InjectRepository(Certificate)
    certificateRepository: Repository<Certificate>,
    entityManager: EntityManager,
  ) {
    super(entityManager, certificateRepository);
  }
}
@Injectable()
export class SignatoryRepository extends AbstractRepository<Signatory> {
  constructor(
    @InjectRepository(Signatory)
    signatoryRepository: Repository<Signatory>,
    entityManager: EntityManager,
  ) {
    super(entityManager, signatoryRepository);
  }
}
@Injectable()
export class TransferCertificateRepository extends AbstractRepository<TransferCertificate> {
  constructor(
    @InjectRepository(TransferCertificate)
    transferCertificateRepository: Repository<TransferCertificate>,
    entityManager: EntityManager,
  ) {
    super(entityManager, transferCertificateRepository);
  }
}
@Injectable()
export class DevelopmentStatusRepository extends AbstractRepository<DevelopmentStatus> {
  constructor(
    @InjectRepository(DevelopmentStatus)
    developmentStatusRepository: Repository<DevelopmentStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, developmentStatusRepository);
  }
}

@Injectable()
export class RandomInspectionRepository extends AbstractRepository<RandomInspection> {
  constructor(
    @InjectRepository(RandomInspection)
    randomInspectionRepository: Repository<RandomInspection>,
    entityManager: EntityManager,
  ) {
    super(entityManager, randomInspectionRepository);
  }
}
@Injectable()
export class SiteStatusRepository extends AbstractRepository<SiteStatus> {
  constructor(
    @InjectRepository(SiteStatus)
    siteStatusRepository: Repository<SiteStatus>,
    entityManager: EntityManager,
  ) {
    super(entityManager, siteStatusRepository);
  }
}
