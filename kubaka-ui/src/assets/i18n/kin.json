{"home": "Ahabanza", "topBarMenu": {"home": "Ahabanza", "service": "Serivise", "faq": "Ibyibazwa", "login": "Inji<PERSON>", "signup": "<PERSON><PERSON><PERSON>kis<PERSON>"}, "chatBotLabels": {"title": "Waba ufite ikibazo? baza Kubaka!", "subTitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Welcome": "<PERSON><PERSON><PERSON>", "Selectcategory": "Hitamo icyiciro", "Selectagency": "Hitamo ikigo(OSC)"}, "heroPage": {"title": "<PERSON><PERSON><PERSON> yo <PERSON>", "subTitle": "<PERSON><PERSON><PERSON><PERSON> imiterere y'<PERSON><PERSON><PERSON><PERSON> rwawe", "subTitle2": "Cyangwa Kanda hano kubisabwa BPMIS ishaje", "search": "Shakisha UPI hano", "searchButton": "<PERSON><PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON> i<PERSON>e kubutaka bwawe", "constructionPermit": "Uruhushya rwo kubaka", "constructionPermitApply": "Ohereza"}, "zoningCheck": {"title": "<PERSON><PERSON>", "subTitle": "<PERSON><PERSON><PERSON><PERSON>, cyangwa ibibujijwe cyangwa ibisabwa kubakwa mu kibanza cyawe", "search": "Uzuza UPI", "searchButton": "<PERSON><PERSON><PERSON><PERSON>", "checkRequirementProgress": "Kugen<PERSON>ra i<PERSON>abwa ...", "taxPaymentReminder": "Urashobora kwemeza ko imisoro yawe yishyuwe? <PERSON><PERSON> butaka bufite imisoro itaris<PERSON>wa", "unpaidTaxesDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON>", "declarationYear": "Umwaka w'itangazo", "taxTypeDescription": "Ubwoko bw'imisoro", "totalTaxToPay": "<PERSON><PERSON><PERSON> w'imis<PERSON> ugo<PERSON> kwishyurwa", "totalTaxPaid": "<PERSON><PERSON><PERSON> w'im<PERSON><PERSON> wamaze k<PERSON>shyurwa", "balance": "Ibisigaye", "isUnderRestriction": "<PERSON><PERSON> m<PERSON> y'imiterere yihariye", "isUnderMortgage": "<PERSON><PERSON> munsi y'ing<PERSON>e", "yes": "Yego", "no": "<PERSON><PERSON>", "location": "<PERSON><PERSON> but<PERSON> b<PERSON>", "district": "<PERSON><PERSON><PERSON>", "sector": "<PERSON><PERSON><PERSON>", "cell": "Akagari", "village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "landUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y'ubutaka", "registeredUse": "I<PERSON><PERSON><PERSON><PERSON><PERSON> yanditswe", "zoning": "Igenzura ry'im<PERSON>resh<PERSON><PERSON> y'ubutaka", "area": "Ubuso", "thereIsA": "<PERSON>", "road": "<PERSON><PERSON><PERSON>", "passesThroughPlot": "unyura mu butaka bwawe", "selectBuildMasterPlan": "Hitamo icyo ushaka kubaka nk'uko biteganyijwe mu gishushanyo mbonera", "permittedUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yemewe", "apply": "Gusaba", "prohibitedUse": "Imikoreshereze itemewe", "conditionalUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> igira i<PERSON>", "representative": "Uhagarari<PERSON>", "doesPlotUnderRestriction": "Ese ubutaka buri munsi y'imiterere yihariye", "doesPlotUnderMortgage": "<PERSON>se ubutaka buri munsi y'ing<PERSON>e", "owners": "<PERSON><PERSON><PERSON><PERSON> ubutaka", "names": "Amazina", "nationalId": "Indangamuntu", "maritalStatus": "Imiterere y'ubushake bwo gushyingirwa", "gender": "<PERSON><PERSON><PERSON><PERSON>", "male": "Umugabo", "female": "Umugore"}, "zoningCheckDetails": {"title": "<PERSON><PERSON> buteye naho bugeze", "subTitle": "<PERSON><PERSON><PERSON><PERSON> imiterere y'<PERSON><PERSON><PERSON><PERSON> rwawe hano", "permit": "<PERSON><PERSON><PERSON><PERSON>", "permitNumber": "<PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "location": "<PERSON>o <PERSON>", "stageTitle": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "stage0": "ICYICIRO KIBANZE", "stage0Title": "Ntabwo urohereza u<PERSON>abe", "stage0Description": "Iki ni iki<PERSON>ro cyambere igihe uba utarohereza u<PERSON>abe.", "stage1": "ICYICIRO CYAMBERE ", "stage1Title": "<PERSON><PERSON><PERSON> u<PERSON>", "stage1Description": "Iki ni iki<PERSON>ro cyambere igihe uba umaze kohereza ubusabe.", "stage2": "ICYICIRO CYA KABIRI", "stage2Title": "<PERSON><PERSON><PERSON>", "stage2Description": "Ni<PERSON><PERSON> wakiriye igisubizo cyubuyobozi bagusaba kugira ibyo uhindura kubusabe bwawe.", "stage3": "ICYICIRO CYA GATATU", "stage3Title": "Igenzura", "stage3Description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> bwawe buba buri kugenzurwa n'abako<PERSON> bashinzwe gutanga ibyangombwa.", "stage4": "ICYICIRO CYA KANE", "stage4Title": "<PERSON><PERSON><PERSON><PERSON>", "stage4Description": "<PERSON><PERSON> igihe uba wamaze gusubizwa ubuyobozi bwemeje ibijyanye n ubusabe bwawe.", "stage5": "ICYICIRO CYA GATANU", "stage5Title": "Kwangwa / Guh<PERSON>rika gusaba", "stage5Description": "<PERSON><PERSON><PERSON> wakiriye i<PERSON><PERSON><PERSON> bwanze ubusabe bwawe."}, "permitCategory": {"title": "<PERSON><PERSON><PERSON> wasaba", "subTitle": "Raba neza ibisabwa ngo ubone urushya rwawe hano", "Category1": "<PERSON><PERSON><PERSON><PERSON>ya rwo kubaka", "Category1Description1Tile1": "Ibyerekeye iyi serivisi", "Category1Description1": "<PERSON><PERSON> serivisi ye<PERSON>ra a<PERSON> gusaba uru<PERSON>hya rushya rwo kubaka. <PERSON><PERSON> ruhushya rutangwa nikigo gishinzwe imiturire mu Rwanda.", "Authorized1": "Rwanda Housing Authority", "Period1": "Byemewe kumasaha 12 gusa", "Price1": "Birahinduka", "authorized": "Yemerewe", "period": "<PERSON><PERSON><PERSON>", "price": "<PERSON>gic<PERSON>", "Cancelbtn1": "<PERSON><PERSON><PERSON>", "Applybtn1": "<PERSON><PERSON><PERSON>", "Category2": "Amatangazo y'Ubugenzuzi", "Category2Description2Title2": "Ibyerekeye iyi serivisi", "Category2Description2": "<PERSON>yi serivisi ye<PERSON>ra aba<PERSON>esha gusaba Amatangazo yo kugenzura inyubako zabo. <PERSON><PERSON> ruhushya rutangwa nikigo gishinzwe imiturire mu Rwanda.", "Authorized2": "Rwanda Housing Authority", "Period2": "Byemewe kumasaha 12 gusa", "Price2": "Birahinduka", "Cancelbtn2": "<PERSON><PERSON><PERSON>", "Applybtn2": "<PERSON><PERSON><PERSON>", "Category3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Category3Description3Title3": "Ibyerekeye iyi serivisi", "Category3Description3": "<PERSON>yi serivisi ye<PERSON>ra aba<PERSON>a gusaba kongererwa impushya zo kubaka. <PERSON><PERSON> gutung<PERSON>wa nikigo gishinzwe imiturire mu Rwanda.", "Authorized3": "Rwanda Housing Authority", "Period3": "Byemewe kumasaha 12 gusa", "Price3": "Birahinduka", "Cancelbtn3": "<PERSON><PERSON><PERSON>", "Applybtn3": "<PERSON><PERSON><PERSON>", "Category4": "<PERSON><PERSON><PERSON>", "Category4Description4Title4": "Ibyerekeye iyi serivisi", "Category4Description4": "Iyi serivisi ye<PERSON>ra a<PERSON>esh<PERSON> gusaba uruhushya rwo kubaka uruzitiro. <PERSON><PERSON> gut<PERSON> nikigo gishinzwe imiturire mu Rwanda.", "Authorized4": "Rwanda Housing Authority", "Period4": "Byemewe kumasaha 12 gusa", "Price4": "Birahinduka", "Cancelbtn4": "<PERSON><PERSON><PERSON>", "Applybtn4": "<PERSON><PERSON><PERSON>", "Category5": "gusenya-byuzuye/igice", "Category5Description5Title5": "Ibyerekeye iyi serivisi", "Category5Description5": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba uruhushya rwo gusenya, haba kubisenya byuzuye cyangwa igice. <PERSON>aba gutunganywa nikigo gishinzwe imiturire mu Rwanda.", "Authorized5": "Rwanda Housing Authority", "Period5": "Byemewe kumasaha 12 gusa", "Price5": "Birahinduka", "Cancelbtn5": "<PERSON><PERSON><PERSON>", "Applybtn5": "<PERSON><PERSON><PERSON>", "Category6": "<PERSON>ru<PERSON><PERSON> rwo gutura", "Category6Description6Title6": "Ibyerekeye iyi serivisi", "Category6Description6": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba uruhushya rwo gutura. <PERSON><PERSON> gut<PERSON>wa nikigo gishinzwe imiturire mu Rwanda.", "Authorized6": "Rwanda Housing Authority", "Period6": "Byemewe kumasaha 12 gusa", "Price6": "Birahinduka", "Cancelbtn6": "<PERSON><PERSON><PERSON>", "Applybtn6": "<PERSON><PERSON><PERSON>", "Category7": "<PERSON>ru<PERSON><PERSON> rwo gutura", "Category7Description7Title7": "Ibyerekeye iyi serivisi", "Category7Description7": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba uruhushya rwo gutura. <PERSON><PERSON> gut<PERSON>wa nikigo gishinzwe imiturire mu Rwanda.", "Authorized7": "Rwanda Housing Authority", "Period7": "Byemewe kumasaha 12 gusa", "Price7": "Birahinduka", "Cancelbtn7": "<PERSON><PERSON><PERSON>", "Applybtn7": "<PERSON><PERSON><PERSON>", "Category8": "Uruhushya rwo gusaba Imiterere yigihe gito", "Category8Description8Title8": "Ibyerekeye iyi serivisi", "Category8Description8": "<PERSON>yi serivisi ye<PERSON>ra aba<PERSON>esha gusaba uruhushya rwa porogaramu yigihe gito yemewe yo gusaba. <PERSON><PERSON> gutung<PERSON>wa nikigo gishinzwe imiturire mu Rwanda.", "Authorized8": "Rwanda Housing Authority", "Period8": "Byemewe kumasaha 12 gusa", "Price8": "Birahinduka", "Cancelbtn8": "<PERSON><PERSON><PERSON>", "Applybtn8": "<PERSON><PERSON><PERSON>", "Category9": "Kuvugurura inyubako iriho hamwe no guhindura Imiterere", "Category9Description9Title9": "Ibyerekeye iyi serivisi", "Category9Description9": "<PERSON>yi serivisi ye<PERSON>ra abakoresha gusaba kuvugurura inyubako ihari hamwe no guhindura imiterere. <PERSON><PERSON> gut<PERSON>wa n'ikigo gishinzwe imiturire mu Rwanda.", "Authorized9": "Rwanda Housing Authority", "Period9": "Byemewe kumasaha 12 gusa", "Price9": "Birahinduka", "Cancelbtn9": "<PERSON><PERSON><PERSON>", "Applybtn9": "<PERSON><PERSON><PERSON>", "Category10": "Kuvugurura inyubako iriho nta guhindura Imiterere", "Category10Description10Title10": "Ibyerekeye iyi serivisi", "Category10Description10": "<PERSON>yi serivisi ye<PERSON>ra aba<PERSON>a gusaba kuvugurura inyubako ihari nta guhindura imiterere. <PERSON><PERSON> gut<PERSON> n'ikigo gishinzwe imiturire mu Rwanda.", "Authorized10": "Rwanda Housing Authority", "Period10": "Byemewe kumasaha 12 gusa", "Price10": "Birahinduka", "Cancelbtn10": "<PERSON><PERSON><PERSON>", "Applybtn10": "<PERSON><PERSON><PERSON>", "Category11": "<PERSON><PERSON><PERSON><PERSON> inyuba<PERSON> ya<PERSON>", "Category11Description10Title11": "Ibyerekeye iyi serivisi", "Category11Description11": "<PERSON>yi serivisi ye<PERSON>ra aba<PERSON>a gusaba kuvugurura inyubako ihari nta guhindura imiterere. <PERSON><PERSON> gut<PERSON> n'ikigo gishinzwe imiturire mu Rwanda.", "Authorized11": "Rwanda Housing Authority", "Period11": "Byemewe kumasaha 12 gusa", "Price11": "Birahinduka", "Cancelbtn11": "<PERSON><PERSON><PERSON>", "Applybtn11": "<PERSON><PERSON><PERSON>"}, "FAQs": {"Title": "<PERSON><PERSON><PERSON><PERSON>", "subTitle": "Soma neza ibibazo abantu bakunze kwibaza hano nub<PERSON>o wabi<PERSON>", "questionOwner": {"1": "<PERSON><PERSON><PERSON> nak<PERSON>ha sisitemu nka n<PERSON>?", "2": "Ni ibihe byiciro bi<PERSON>wa in<PERSON>niyeri n'abubatsi?", "3": "<PERSON><PERSON><PERSON> i<PERSON>he kingana iki kugirango u<PERSON> rutangwe?", "4": "<PERSON><PERSON><PERSON><PERSON> rumara igihe kingana iki? Ese rushobora kongerwa?"}, "descriptionOwner": {"1": "Kugira ngo ukoreshe sisitemu n<PERSON>, ugomba gukora konti hanyuma ukinjira. Ugomba kuba ufite indangamuntu cyangwa pasiporo.", "2": "Ubusabe bwose murwego urwo arirwo rwose bushobora gutangwa naba injeniyeri n'abubatsi.", "3": "Ubusabe busubizwa mu iminsi 21 mu mugi kwa Kigali niminsi 30 mutundi turere", "4": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON>e rumara <PERSON>, kandi <PERSON><PERSON><PERSON> kong<PERSON>."}, "questionEngineer": {"1": "<PERSON><PERSON><PERSON> nak<PERSON>ha sisitemu nka in<PERSON>?", "2": "<PERSON><PERSON><PERSON> nshobora kubona uruhushya rwo gusaba nka injeniyeri?", "3": "Nka injeniyeri ngomba kuvugu<PERSON>ra u<PERSON><PERSON><PERSON> rwanjye buri mwaka?"}, "descriptionEngineer": {"1": "<PERSON><PERSON>, u<PERSON><PERSON> gukora konti hanyuma ukinjira ukoresheje nomero yawe ya lisanse.", "2": "<PERSON> nyirubwite bazaguha imishinga. Uzakira imenyesha. Us<PERSON>bora kwemera cyangwa kwanga umukoro.", "3": "Ba injeniyeri basaba asosiyasiyo ya ba injeniyeri kuvu<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>."}, "questionArchitect": {"1": "<PERSON><PERSON><PERSON> nak<PERSON> si<PERSON>?", "2": "<PERSON><PERSON><PERSON> nshobora kubona uruhushya rwo gusaba nkumwubat<PERSON>?", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ngomba kuvugu<PERSON>ra u<PERSON><PERSON><PERSON> rwanjye buri mwaka?"}, "descriptionArchitect": {"1": "<PERSON><PERSON><PERSON> bagomba gukora konti hanyuma baki<PERSON>ra bak<PERSON><PERSON><PERSON> nimero yabo y<PERSON>.", "2": "<PERSON> nyirubwite bazaguha imishinga. Uzakira imenyesha. Us<PERSON>bora kwemera cyangwa kwanga umukoro.", "3": "<PERSON><PERSON><PERSON> basaba as<PERSON><PERSON><PERSON><PERSON> yabu<PERSON> kuvu<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>"}}, "footer": "Uburenganzira b<PERSON>se burabitswe.", "footer2": "Yakozwe na Tech Avenue 137", "permitTypeInformation": {"applicationType1": "<PERSON><PERSON><PERSON><PERSON>ya rwo kubaka", "desc1": "<PERSON>yi serivisi ye<PERSON>ra <PERSON> gusaba uruhushya rushya rwo kubaka. <PERSON><PERSON> gut<PERSON> n'ikigo gishinzwe imiturire mu Rwanda.", "authorized1": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period1": "Byemewe kumyaka 2 gusa", "price1": "250,000 Rwf", "applicationType2": "Uruhushya rw'Ubugenzuzi", "desc2": "<PERSON>yi serivisi ye<PERSON><PERSON> a<PERSON> g<PERSON> an Uruhushya rw'Ubugenzuzi.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized2": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period2": "Byemewe kumyaka 2 gusa", "price2": "250,000 Rwf", "applicationType3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc3": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba rwo kuvugurura u<PERSON>.<PERSON><PERSON>abe butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized3": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period3": "Byemewe kumyaka 2 gusa", "price3": "250,000 Rwf", "applicationType4": "<PERSON><PERSON><PERSON>", "desc4": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> g<PERSON>ba <PERSON> uru<PERSON>.<PERSON>busabe butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized4": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period4": "Byemewe kumyaka 2 gusa", "price4": "250,000 Rwf", "applicationType5": "Gusenya - Byuzuye / Igice", "desc5": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> g<PERSON> Gusenya - Byuzuye / Igice.Ubusabe butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized5": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period5": "Byemewe kumyaka 2 gusa", "price5": "250,000 Rwf", "applicationType6": " <PERSON>ru<PERSON><PERSON> rwo gutura", "desc6": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba an occupancy permit.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized6": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period6": "Byemewe kumyaka 2 gusa", "price6": "250,000 Rwf", "applicationType7": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>a", "desc7": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> g<PERSON>ba Uruhushya rwo gutura.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized7": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period7": "Byemewe kumyaka 2 gusa", "price7": "250,000 Rwf", "applicationType8": "Imiterere yigihe gito <PERSON> rwo gusaba", "desc8": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba uruhusya rw igihe gito.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized8": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period8": "Byemewe kumyaka 2 gusa", "price8": "250,000 Rwf", "applicationType9": "Refurbshiment of existing building with structural alteration", "desc9": "<PERSON>yi serivisi ye<PERSON>ra a<PERSON> gusaba Imiterere yigihe gito <PERSON> rwo gusaba.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized9": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period9": "Byemewe kumyaka 2 gusa", "price9": "250,000 Rwf", "applicationType10": "<PERSON><PERSON><PERSON><PERSON><PERSON> inyubako ihari nta guhindura imiterere", "desc10": "<PERSON>yi serivisi ye<PERSON> a<PERSON> g<PERSON>vuguru<PERSON> inyubako ihari nta guhindura imiterere.<PERSON><PERSON><PERSON> butunganywa n'ikigo gishinzwe imiturire mu Rwanda.", "authorized10": "<PERSON><PERSON><PERSON> gishi<PERSON> imiturire mu Rwanda", "period10": "Byemewe kumyaka 2 gusa", "price10": "250,000 Rwf"}, "loginPage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "password": "Ijambo banga", "loginButton": "Inji<PERSON>", "forgotPassword": "Wibagiwe ijambo ry'ibanga?", "createAccount": "<PERSON><PERSON><PERSON> konti n<PERSON>ya", "backHome": "<PERSON><PERSON> aho utan<PERSON>ra", "waitmsg": "Tegerez<PERSON>"}, "registerPage": {"title": "<PERSON><PERSON><PERSON>kis<PERSON>", "accountType": "Ubwoko bwa konti", "accountTypeOptions": {"placeholder": "Hitamo ubwoko bwa konti", "owner": "<PERSON><PERSON><PERSON><PERSON>aka", "engineer": "<PERSON><PERSON><PERSON><PERSON><PERSON> [Engineer] / <PERSON><PERSON><PERSON>", "architect": "<PERSON><PERSON><PERSON><PERSON> [Architect] / <PERSON><PERSON><PERSON>"}, "proceedButton": "Komeza", "documentType": "Ubwoko bw'inyandiko", "documentTypeOptions": {"placeholder": "Hitamo ubwoko bw'inyandiko", "nationalId": "Indangamuntu", "passport": "Pasiporo"}, "nationalIdLabel": "Numero y'Indangamuntu", "passportLabel": "Numero ya Pasiporo", "licenseLabel": "Numero y'Icyangombwa", "savingtxt": "Kubika", "firstNameLabel": "<PERSON><PERSON><PERSON>", "lastNameLabel": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emailLabel": "<PERSON><PERSON><PERSON>", "phoneLabel": "Numero ya telefone", "emailPlaceholder": "<PERSON><PERSON> ad<PERSON> yawe ya imeli", "phonePlaceholder": "Andika numero ya telefone", "licensePlaceholder": "Andika numero ya license", "passwordLabel": "Ijambo banga", "confirmPasswordLabel": "Emeza ijambo ry'ibanga", "dobLabel": "<PERSON><PERSON><PERSON> y'am<PERSON><PERSON>o", "dobPlaceholder": "Hitamo itariki y'amavuko", "genderLabel": "Gender", "genderOptions": {"placeholder": "Hitamo", "female": "Umugore", "male": "Umugabo"}, "consent": "Ndemeza ko amakuru yatanzwe ari ukuri uko mbizi kandi mbifitiye icyizere.", "createAccountButton": "<PERSON><PERSON> konti", "haveAccount": "U<PERSON>e konti?", "loginHere": "<PERSON><PERSON><PERSON> hano", "orCancel": "<PERSON><PERSON><PERSON>", "backtxt": "<PERSON><PERSON>", "backHomeTxt": "<PERSON><PERSON> aho utan<PERSON>ra", "waitTxt": "Tegerez<PERSON>"}, "resetPasswordPage": {"title": "Gusubiramo ijambo banga", "emailLabel": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "<PERSON><PERSON> ad<PERSON> yawe ya imeli", "sendResetLinkButton": "Ohereza", "backToLogin": "<PERSON>ira ku kwinjira", "waitTxt": "Tegerez<PERSON>", "rememberPassword": "<PERSON><PERSON><PERSON><PERSON>, nibutse i<PERSON> ry'ibanga", "loginHere": "Kanda hano"}, "errorNotification": {"successMsg": "<PERSON><PERSON><PERSON> yabit<PERSON>we neza", "errorMsg": "<PERSON><PERSON><PERSON> ikosa mu gihe cyo gusaba"}, "guideLineHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> yo gutanga ubusabe", "guideLineTitle_1": "<PERSON><PERSON><PERSON><PERSON>", "guideLineContent_1": "Umushinga n'ibisob<PERSON><PERSON> by<PERSON><PERSON> bi<PERSON><PERSON>ho hashingiwe ku UPI yawe ndetse n'ibyo amategeko y'u Rwanda yemera kubakwa ku butaka bwawe.", "guideLineTitle_2": "<PERSON><PERSON> u<PERSON> bwawe", "guideLineContent_2": "Ubusabe bwawe bug<PERSON>a gut<PERSON>, kandi ugomba kwemeza ko nta misoro itarishyuwe cyangwa icyemezo cy'isuzuma ry'ingaruka ku <PERSON> (EIA) kiriho.", "guideLineTitle_3": "<PERSON><PERSON><PERSON> buri g<PERSON>wa", "guideLineContent_3": "<PERSON><PERSON><PERSON> yo kwakira ubusabe bwawe, umwe mu bagize itsinda rya OSC azawusuzuma, ake<PERSON>za ko byose by<PERSON><PERSON><PERSON> am<PERSON>.", "guideLineTitle_4": "Inyemezabwishyu igomba kwishyurwa", "guideLineContent_4": "Inyemezabwishyu zizakorwa nyuma y'uko ubusabe bwawe bwemejwe, kandi ugomba kwishyura kugira ngo ubone uruhus<PERSON>.", "guideLineTitle_5": "Uruhushya rwakozwe", "guideLineContent_5": "<PERSON><PERSON><PERSON><PERSON> rwawe ruza<PERSON>ka guku<PERSON><PERSON><PERSON> nyuma y'uko kwish<PERSON><PERSON>wa byarangiye.", "accountHeaderLinksContainer": {"engineer": "Engineer", "architect": "Architect"}, "accountHeaderMenu": {"dashboard": "Ahabanza", "systemAdministrator": "<PERSON><PERSON>oboz<PERSON> wa sisitemu", "myProjects": "<PERSON><PERSON><PERSON><PERSON> yan<PERSON>", "allApplications": "<PERSON><PERSON><PERSON> bwo<PERSON>", "transferredPermits": "<PERSON><PERSON><PERSON> zim<PERSON>we", "myApplications": "<PERSON><PERSON><PERSON> b<PERSON>", "myBox": "<PERSON><PERSON><PERSON><PERSON>", "projectRequests": "<PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON>", "invoices": "Inyemezabwishyu", "allInvoices": "Inyemezabwi<PERSON><PERSON> zose", "permits": "<PERSON>mpushya", "allPermits": "<PERSON><PERSON><PERSON> zose", "reports": "Raporo", "chat": "Ikiganiro"}, "accountHeaderUserDropDown": {"edit": "<PERSON><PERSON>", "change": "Hindura ijambo ry'ibanga", "logout": "<PERSON><PERSON><PERSON>"}, "dashboardComponent": {"welcomeMsg": "Murakaza neza", "loadingTxt": "Gutunganywa", "plotsTitle": "<PERSON><PERSON><PERSON><PERSON> yanjye muri <PERSON>", "viewMap": "<PERSON><PERSON>", "plotLocation": "<PERSON><PERSON><PERSON> <PERSON>'is<PERSON><PERSON>", "plotUPI": "UPI", "plotUse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y'ubutaka", "findApplication": "<PERSON><PERSON><PERSON><PERSON>", "applyPermitInstruction": "<PERSON><PERSON>, kanda hano", "allApplication": "<PERSON><PERSON><PERSON> bwo<PERSON>", "draftApplications": "<PERSON><PERSON><PERSON> bukiri mu nyan<PERSON>ko", "reviewedApplications": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "submittedApplications": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "permitted": "Impushya zatanz<PERSON>", "allPermitted": "<PERSON>mpus<PERSON> zose zata<PERSON>we", "rejectedApplications": "<PERSON><PERSON><PERSON> bwan<PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "underReview": "<PERSON>iri mu isuzuma", "underCorrection": "Biri mu gukoso<PERSON>wa", "resubmitted": "<PERSON><PERSON><PERSON>", "preApproval": "Icyemezo cy'ibanze", "nonObjectionReturned": "Icyemezo cyo kutagira imbogamizi cyasubijwe", "certified": "Byemejwe", "returnedApplication": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "nonObjectionReviewed": "Icyemezo cyo kutagira imbogamizi cyasuzumwe", "nonObjectionUnderReview": "Icyemezo cyo kutagira imbogamizi kiri mu isuzuma", "nonObjectionApproved": "Icyemezo cyo kutagira imbogamizi cyemejwe", "nonObjectionRejected": "Icyemezo cyo kutagira imbogamizi cyanzwe", "nonObjectionBackForCorrection": "Icyemezo cyo kutagira imbogamizi gisubijwe ngo gikosorwe", "submitted": "Byatanzwe", "tasksTitle": "<PERSON><PERSON><PERSON>", "taskTitle": "<PERSON><PERSON><PERSON>", "search": {"general": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "choose": "Ni iyihe wifuza guhitamo?"}, "rhaApplications": "Ubusabe bwa RHA", "agencyTxt": "Mu kigo cya"}, "countryLevelDashboard": {"agency": "Ikigo", "allAgency": "<PERSON><PERSON><PERSON> byose", "allApplications": "<PERSON><PERSON><PERSON> bwo<PERSON>", "preApproval": "Icyemezo cy'ibanze", "reviewedApplications": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "submittedApplications": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "permitted": "Impushya rwat<PERSON>zwe", "rejectedApplications": "<PERSON><PERSON><PERSON> bwan<PERSON>", "resubmitted": "<PERSON><PERSON><PERSON>", "underReview": "<PERSON>iri mu isuzuma", "underCorrection": "Biri mu gukoso<PERSON>wa", "nonObjectionReturned": "Icyemezo cyo kutagira imbogamizi cyasubijwe"}, "analyticsDashboard": {"title": "Ahabanza", "filterbyYear": "<PERSON><PERSON>", "filterbyAgency": "Reba i<PERSON>go", "filter": "Re<PERSON>"}, "chatMessageComponent": {"messageTxt": "<PERSON><PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applicants": "Abasaba", "received": "Ubutumwa bwakiriwe", "sent": "U<PERSON>um<PERSON> bwoherejwe", "sPlaceholder": "Shakisha...", "sApplicantPlaceholder": "<PERSON><PERSON><PERSON>a usaba..."}, "projectComponent": {"welcomeMsg": "Murakaza neza", "projects": "<PERSON><PERSON><PERSON><PERSON>", "searchForSomething": "shakisha...", "newPermitApplication": "Saba Icyagombwa", "plotSize": "Ingano y'ubutaka", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "selectedCategoryUse": "Icyiciro cy'imikoreshereze cyatoranijwe", "selectedUse": "Imi<PERSON><PERSON><PERSON><PERSON> ya<PERSON>anijwe", "province": "Intara", "district": "<PERSON><PERSON><PERSON>", "sector": "<PERSON><PERSON><PERSON>", "projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "removeEngineerArchitect": "<PERSON><PERSON><PERSON>/architect", "noRecordsFound": "<PERSON><PERSON> makuru a<PERSON>", "verification": "Ukwemeza", "verificationDetails": "<PERSON><PERSON>uzuma niba ufite imisoro <PERSON>, turebe muri BPMIS ya<PERSON><PERSON><PERSON> wawe, t<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> mbonera cy'ubutaka.", "confirmRemoveEngineerArchitect": "<PERSON><PERSON><PERSON>/architect ku m<PERSON>a", "removingEngineerArchitect": "<PERSON><PERSON>/architect ku m<PERSON>a", "close": "Funga", "yesDelete": "Yego, Siba", "removing": "Tegereza...", "status": "Status"}, "applicantApplication": {"welcomeMsg": "Murakaza neza", "myApplications": "<PERSON><PERSON><PERSON> b<PERSON>", "searchForSomething": "shakisha...", "applicationStatus": "<PERSON><PERSON><PERSON><PERSON> y<PERSON>u<PERSON>", "newPermitApplication": "Saba Icyagombwa", "permitType": "Ubwoko bw'Icyangombwa", "applicationNo": "<PERSON><PERSON><PERSON>", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "buildType": "Ubwo<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "siteLocation": "<PERSON><PERSON><PERSON> h'um<PERSON>", "createdOn": "Byakozwe ku", "submittedDate": "<PERSON><PERSON><PERSON>", "applicationDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "relatedApplication": "<PERSON><PERSON><PERSON> bufitanye isano", "pay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewPermit": "Reba <PERSON>agombwa", "viewComments": "<PERSON><PERSON>", "delete": "Siba", "noRecordsFound": "<PERSON><PERSON> makuru a<PERSON>", "confirmDeleteApplication": "Urimo gus<PERSON> u<PERSON>", "deleteWarning": "<PERSON><PERSON> u<PERSON> bwawe bizak<PERSON>ho amakuru yawe yose muri sisitemu.", "close": "Funga", "yesDelete": "Yego, Siba", "deletingApplication": "Tegerez<PERSON>", "verification": "Ukwemeza", "verificationDetails": "<PERSON><PERSON>uzuma niba ufite imisoro <PERSON>, turebe muri BPMIS ya<PERSON><PERSON><PERSON> wawe, t<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> mbonera cy'ubutaka.", "applicationComment": "Igitekerezo c<PERSON>'u<PERSON>", "approvalLevel": "Urwego", "status": "Status", "date": "<PERSON><PERSON><PERSON>", "comment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "applicantInvoices": {"welcomeMsg": "Murakaza neza", "myInvoices": "Inyemezabwishyu zanjye", "search": "shakisha...", "fromDate": "<PERSON><PERSON>", "toDate": "Kugeza", "pageSize": "Ingano", "invoiceItem": "Icyiciro cya inyemezabwishyu", "invoiceNo": "Nimero ya inyemezabwishyu", "amount": "<PERSON><PERSON><PERSON><PERSON>", "transactionNumber": "<PERSON><PERSON><PERSON><PERSON>u<PERSON><PERSON><PERSON><PERSON>", "createdOn": "Byakozwe ku", "dueDate": "<PERSON><PERSON><PERSON> <PERSON>'is<PERSON><PERSON><PERSON>", "applicationName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON>", "viewAndPay": "<PERSON><PERSON> kandi <PERSON>re", "view": "Re<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "refund": "<PERSON><PERSON><PERSON> amafaranga", "update": "<PERSON><PERSON>", "invoiceExtension": "<PERSON><PERSON><PERSON><PERSON> igihe cya inyemezabwishyu", "confirmRefund": "<PERSON><PERSON><PERSON>", "close": "Funga", "yesRefund": "Yego, Kugaruza", "confirmCancel": "<PERSON><PERSON><PERSON>", "yesDelete": "Yego, Siba", "updateInvoice": "<PERSON><PERSON><PERSON><PERSON>", "chooseInvoicePrices": "Hitamo ibiciro bya inyemeza<PERSON>wishyu", "getPrice": "<PERSON><PERSON>", "updating": "<PERSON><PERSON>", "askForExtension": "Saba kwongerwa igihe", "expirationDate": "<PERSON><PERSON><PERSON>", "submit": "Ohereza"}, "applicantPermits": {"welcomeMsg": "Murakaza neza", "permits": "Ibyangombwa", "searchForSomething": "shakisha...", "fromDate": "<PERSON><PERSON>", "toDate": "Kugeza", "pageSize": "Ingano", "invoiceNo": "Nimero ya inyemezabwishyu", "permitNo": "Nimero y'icyangombwa", "applicationNo": "<PERSON><PERSON><PERSON>", "permitType": "Ubwoko bw'Icyangombwa", "agencyCode": "Kode y'ikigo", "issuedOn": "Byatanzwe ku", "expiryDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Status", "waitingForPayment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generated": "Ibyakozwe", "permitStatus": "Imiterere y'Icyagombwa", "transfer": "Hinduranya", "cancel": "<PERSON><PERSON><PERSON>", "transferOwnership": "<PERSON><PERSON><PERSON><PERSON>", "documentType": "Ubwoko bw'inyandiko", "selectDocumentType": "Hitamo ubwoko bw'inyandiko", "documentNumber": "Nimero y'inyandiko (Indangamuntu / Pasiporo)", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Nimero ya telefone", "close": "Funga", "submit": "Ohereza", "confirmCancelPermit": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> u<PERSON> rufite nimero", "yesDelete": "Yego, Siba"}, "editProfile": {"header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "Email", "phoneNumber": "Nimero ya telefone", "cancel": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON>", "updating": "Tegerez<PERSON>"}, "changePassword": {"title": "Hindura ijambo ry'ibanga", "currentPwd": "Ijambo banga usaganywe", "newPwd": "Ijambo banga rishya", "pwdRequired": "Ijambo banga rirakenewe", "pwdMinLength": "Byibuze inyuguti 8", "pwdConditions": "Byibuze inyuguti 1 nto,  1 nkuru n'umubare 1", "pwdRules": "Ijambo banga rigomba guku<PERSON><PERSON> ibi bi<PERSON>", "minUpper": "Byibuze Inyuguti 1 Nkuru", "minLower": "Byibuze Inyuguti 1 Nto", "minSpecial": "Byibuze Ikimenyetso 1 cyihariye", "minNumber": "Byibuze umubare 1", "minChars": "Byibuze Inyuguti 8", "maxChars": "Nturenze Inyuguti 16", "confirmPwd": "Emeza ijambo ry'ibanga", "pwdMismatch": "Amagambo y'ibanga n<PERSON>e", "submit": "Ohereza"}, "newApplication": {"step1": {"step": "<PERSON><PERSON><PERSON>", "plotInformation": "Amak<PERSON> y'u<PERSON>aka", "plotNumberUpi": "Nimero y'ubutaka / UPI", "upiDetails": "Ibisobanuro bya UPI", "representative": "Uhagarari<PERSON>", "province": "Intara", "district": "<PERSON><PERSON><PERSON>", "sector": "<PERSON><PERSON><PERSON>", "cell": "Akagari", "village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parcelDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "applyFor": "Gusaba", "parcelLocation": "<PERSON><PERSON> <PERSON><PERSON><PERSON> b<PERSON>", "coordinates": "Ibirango", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "next": "komeza"}, "step2": {"step": "<PERSON><PERSON><PERSON>", "projectFoundOldSystem": "<PERSON><PERSON><PERSON> wawe uri muri sisitemu ya BPMIS ,u<PERSON><PERSON> ibisobanuro bibura ukomeza", "projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "representativeFullName": "<PERSON>zina ryuzu<PERSON> ry'uhagarariye", "representativeNationalId": "Indangamuntu y'uh<PERSON>rariye", "plotSizeSquareMeters": "Ingano y'ubutaka (<PERSON> <PERSON>o kare)", "cannotContinueAssigned": "<PERSON><PERSON><PERSON><PERSON><PERSON> gukomeza kuko um<PERSON>a wamaze guhabwa in<PERSON>/architect", "previous": "<PERSON><PERSON>", "update": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "andNext": "Ukomeze", "next": "komeza"}, "step3": {"step": "<PERSON><PERSON><PERSON>", "projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "checkAddAssociatedUpi": "Hitamo kongeramo UPI bifitanye isano", "doItYourself": "<PERSON><PERSON><PERSON> k<PERSON>a weny<PERSON>?", "assignToEngineerArchitect": "<PERSON><PERSON><PERSON> / architect", "permitType": "Ubwoko bw'Icyangombwa", "plotSize": "Ingano y'ubutaka", "combiningPlotSize": "<PERSON><PERSON><PERSON> ingano y'ubutaka", "buildingType": "Ubwoko bw'inyubako", "projectCategory": "Icyiciro c<PERSON>um<PERSON>", "numberOfFloorGPlus": "<PERSON><PERSON><PERSON> w'inzu / G+", "builtUpAreaSqMeters": "<PERSON><PERSON><PERSON><PERSON> (Mu <PERSON>o kare)", "buildUpAreaRestriction": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> ntibugomba kurenza ingano y'ubutaka.", "grossFloorArea": "Ubuso bw'inzu yose", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'aho imodoka ziparika", "estimatedPriceDwellingRwf": "Igiciro cy'agateganyo cy'inzu mu RWF", "descriptionOperations": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'uko ahantu ha<PERSON>", "numberOfDwellingUnit": "<PERSON><PERSON><PERSON> w'inzu", "estimatedMonthlyWaterM3": "<PERSON><PERSON> <PERSON>'a<PERSON><PERSON><PERSON> akoresh<PERSON> buri k<PERSON> (m3)", "estimatedMonthlyElectricityWatts": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'agateganyo akoresh<PERSON> buri <PERSON> (watts)", "distanceNearestLandLineFiberM": "Intera y'ubutaka bwihariye / umurongo wa fibre optique (m)", "estimatedProjectCostUsd": "Igiciro cy'agateganyo cy'um<PERSON>a muri <PERSON>", "estimatedProjectCostRwf": "Igiciro cy'agateganyo cy'umushinga muri RWF", "capacityInfoPeopleSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON> w'a<PERSON><PERSON> / imyanya", "technologySurveyOptional": "Isesengura ry'ikoranabuhanga (Hitamo)", "previous": "<PERSON><PERSON>", "next": "Komeza", "cannotContinueAssigned": "<PERSON><PERSON><PERSON><PERSON><PERSON> gukomeza kuko um<PERSON>a wamaze guhabwa in<PERSON> / architect", "associatedUpi": "UPI bifitanye isano"}, "step4": {"associatedUpi": "UPI bifitanye isano", "step": "<PERSON><PERSON><PERSON>", "projectAttachments": "<PERSON><PERSON><PERSON> wonger<PERSON>o ku mushinga", "requiredDocuments": "Ibikenewe", "downloadBoqFile": "Kanda hano ubone dosiye ya BOQ", "boqFileInstruction": "Dosiye ya BOQ iri mu buryo bwa Excel. Menya neza ko uyikuyeho hanyuma ukuzuza amakuru yawe. <PERSON><PERSON> <PERSON><PERSON><PERSON> dosiye, PDF gusa ni yo yemerewe.", "uploadedDocuments": "<PERSON><PERSON><PERSON>", "allowedFileTypes": "PDF gusa ni yo yemerewe. Uretse BOQ.", "upload": "<PERSON><PERSON><PERSON><PERSON>", "view": "Re<PERSON>", "wait": "Tegerez<PERSON>", "noFile": "<PERSON>ta dos<PERSON>ye", "remove": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON>", "resubmit": "<PERSON><PERSON><PERSON> woh<PERSON>", "submit": "Ohereza", "confirmDelete": "Urimo gusiba", "deletingYour": "Gusiba", "deleteWarning": "<PERSON><PERSON><PERSON><PERSON> amakuru yawe yose ari muri si<PERSON>mu.", "close": "Funga", "yesDelete": "Yego, Siba", "applicationDeletingFile": "Gusiba dosiye muri sisitemu", "viewDocument": "<PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "certifyTruth": "Ni inshingano zanjye kwemeza ukuri kw'amakuru yatanzwe kuri ubu busabe, kandi nemera amabwiriza agenga sisitemu.", "cancel": "<PERSON><PERSON><PERSON>"}, "associatedUpi": {"title": "<PERSON><PERSON><PERSON> ingano y'ubutaka", "search": "shakisha...", "new": "<PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerId": "ID ya nyir'ubutaka", "plotSize": "Ingano y'ubutaka", "remove": "<PERSON><PERSON><PERSON>", "noRecordsFound": "<PERSON><PERSON> makuru a<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "saving": "Tegerez<PERSON>", "save": "<PERSON><PERSON>"}, "assignToEngineer": {"header": "<PERSON><PERSON><PERSON>, u<PERSON> m<PERSON>a u<PERSON><PERSON> guh<PERSON><PERSON> inje<PERSON>/architect. <PERSON><PERSON> injeniyeri/architect", "userType": "Uzakurikirana umushiga", "engineerArchitectLicenseNumber": "<PERSON><PERSON><PERSON> ya lisansi y'inje<PERSON>i/architect", "names": "Amazina", "daysNeededForSubmission": "<PERSON><PERSON><PERSON> i<PERSON>e kugira ngo uruhushya rutangwe", "wait": "Tegerez<PERSON>", "assign": "<PERSON><PERSON><PERSON>"}}, "viewPlot": {"title": "<PERSON><PERSON><PERSON> u<PERSON><PERSON> b<PERSON>", "back": "<PERSON><PERSON>", "parcelDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "province": "Intara", "district": "<PERSON><PERSON><PERSON>", "sector": "<PERSON><PERSON><PERSON>", "cell": "Akagari", "village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinates": "Ibirango"}, "applicationDetails": {"title": "<PERSON><PERSON><PERSON>", "applicationsDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "clickToContinue": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "owners": "<PERSON><PERSON><PERSON><PERSON> ubutaka", "parcelDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "appliedFor": "Gusabye", "province": "Intara", "district": "<PERSON><PERSON><PERSON>", "sector": "<PERSON><PERSON><PERSON>", "cell": "Akagari", "village": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "projectCategory": "Icyiciro c<PERSON>um<PERSON>", "buildingType": "Ubwoko bw'inyubako", "permitType": "Ubwoko bw'Icyangombwa", "projectBrief": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "projectEstimatesDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON> y'um<PERSON><PERSON>", "estimatedMonthlyWaterM3": "<PERSON><PERSON> <PERSON>'a<PERSON><PERSON><PERSON> akoresh<PERSON> buri k<PERSON> (m3)", "distanceNearestLandLineFiberM": "Intera y'umurongo w'itumanaho / umurongo wa fibre optique (m)", "estimatedMonthlyElectricityKwh": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'agateganyo akoreshwa buri kwezi mu Kwh", "estimatedProjectCostUsd": "Igiciro cy'umushinga cy'agateganyo muri <PERSON>", "estimatedProjectCostRwf": "Igiciro cy'umushinga cy'agateganyo muri Rwf", "developmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON><PERSON>", "plotSizeSquareMeters": "Ingano y'ubutaka (<PERSON> <PERSON>o kare)", "combinedPlotSizeSquareMeters": "Ingano ihujwe y'ubutaka (Mu metero kare)", "grossFloorArea": "Ubuso bw'inzu yose", "proposedNumberOfFloorsGPlus": "Umubare w'inzu zitangajwe / G+", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'aho imodoka ziparika", "priceOfDwellingUnit": "Igiciro cy'inzu y'ubutuye", "builtUpArea": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "capacityInfoPeopleSeats": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> : umuba<PERSON> w'aban<PERSON> / imyanya", "submittedBy": "Yatanzwe na", "names": "Amazina", "phoneNumber": "Nimero ya telefone", "userType": "Ubwoko bw'umukoresha", "projectAttachmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON><PERSON>", "viewDocument": "<PERSON><PERSON>", "loading": "<PERSON><PERSON>", "projectAttachmentAdditionalFiles": "Izindi dosiye z'inyandiko z'umushinga", "associatedUpi": "UPI bifitanye isano"}, "otherApplicationInfo": {"title": "<PERSON><PERSON>", "occupancyQuestion": "Ese mufite uruhushya rwo gukoresha inyubako?", "firstAidBoxes": "Agasanduku k'ubutabazi bw'ibanze?", "disabilityGrabBars": "<PERSON><PERSON><PERSON><PERSON> ifasha aba<PERSON>e ubumuga mu bwi<PERSON>ro?", "paraLightningSystem": "<PERSON><PERSON><PERSON> yo kurinda inkuba", "equipmentCapacity": "Ubushobozi bw'ibikoresho", "constructionMethod": "Uburyo b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ibikoresho)", "disasterPrevention": "Kurinda ibiza", "fireAlarmSystem": "Sisitemu y'alarme y'umuriro ifite inzogera kuri buri gorofa", "noFireAlarmSystem": "<PERSON><PERSON> nta sisitemu y'alarme y'umuriro ifite inzogera kuri buri gorofa?", "fireExtinguishers": "Umutekano w'um<PERSON>ro buri metero 50 kuri buri gorofa", "noFireExtinguishers": "<PERSON><PERSON> nta mutekano w'um<PERSON><PERSON> buri metero 50 kuri buri gorofa?", "exitSigns": "Ibirango byerekana inzira y'ubutabazi kuri buri gorofa", "noExitSigns": "<PERSON>ki nta birango byerekana inzira y'ubut<PERSON>zi kuri buri gorofa?", "emergencyExit": "Inzira y'ubutabazi kuri buri gorofa", "floorPlan": "<PERSON><PERSON><PERSON><PERSON><PERSON> cy'inzu kuri buri gorofa", "noFloorPlan": "<PERSON>ki nta gishushanyo cy'inzu kuri buri gorofa?", "floorNumberSign": "Ikimenyetso cyerekana nimero y'i<PERSON><PERSON>a", "noFloorNumberSign": "<PERSON>ki nta kimenyetso cyerekana nimero y'i<PERSON><PERSON>a?", "elevatorFireSign": "Ikimenyetso kibuza gukoresha ascenseur mu gihe cy'umuriro", "noElevatorFireSign": "<PERSON><PERSON> nta kimenyetso kibuza gukoresha ascenseur mu gihe cy'umuriro?", "helicopterLanding": "Ahantu indege ya kajugujugu yagera hejuru y'inzu mu gihe habaye ikibazo hasi: YEGO / OYA (niba oya, impamvu)", "terrorAttacks": "Ibitero by'iterabwoba", "cctvCameras": "Camera z'umutekano (CCTV)", "noCctvCameras": "Camera z'umutekano (CCTV) (<PERSON><PERSON> atari byo, sobanura impamvu)", "metalDetectors": "Ibikoresho bipima i<PERSON>yurwa<PERSON> n'ibifatika", "noMetalDetectors": "Ibikoresho bipima i<PERSON>rwa<PERSON> n'ibifatika (<PERSON>ba atari byo, sobanura impamvu)", "underSearchMirror": "Indorerwamo yo kureba munsi y'imodoka", "noUnderSearchMirror": "Indorer<PERSON><PERSON> yo kureba munsi y'imodoka (<PERSON><PERSON> atari byo, sobanura impamvu)", "luggageScanners": "<PERSON><PERSON><PERSON> zisaka imizigo", "noLuggageScanners": "<PERSON><PERSON><PERSON> zisaka imizigo (<PERSON><PERSON> atari byo, sobanura impamvu)", "emergencyContacts": "<PERSON><PERSON>apa byerekana serivisi z'ubutabazi n'imibare ya telefoni", "noEmergencyContacts": "<PERSON><PERSON><PERSON> byerekana serivisi z'ubutabazi n'imibare ya telefoni (<PERSON>ba atari byo, sobanura impamvu)", "evacuationPlan": "Gahu<PERSON> yo guhungisha abantu mu gihe cy'ibyago", "noEvacuationPlan": "<PERSON><PERSON><PERSON> yo guhungisha abantu mu gihe cy'ibyago (<PERSON><PERSON> atari byo, sobanura impamvu)", "securityManager": "Umuyobozi w'umutekano n'abakozi bafite camera", "noSecurityManager": "Umuyobozi w'umutekano n'abakozi bafite camera (<PERSON><PERSON> atari byo, sobanura impamvu)", "internalComms": "<PERSON><PERSON><PERSON> y'it<PERSON><PERSON>o imbere mu nyubako", "noInternalComms": "<PERSON><PERSON><PERSON> <PERSON>'it<PERSON><PERSON>o imbere mu nyubako (<PERSON><PERSON> atari byo, sobanura impamvu)", "broadband": "Internet yihuta (Broadband)", "noBroadband": "Internet yihuta (Broadband) (<PERSON><PERSON>ari <PERSON>o, sobanura impamvu)", "accessCards": "Amakarita y'a<PERSON><PERSON><PERSON> n'abashyitsi", "noAccessCards": "Am<PERSON><PERSON> <PERSON>'a<PERSON><PERSON><PERSON> n'abas<PERSON> (<PERSON><PERSON> atari byo, sobanura impamvu)", "fixedLineApplication": "Gusaba umurongo wa telefone w'imiyoboro ihamye", "disabilityFacilities": "Ibikor<PERSON>o by<PERSON>ariye ku bafite ubumuga", "disabilityQuestion": "Ese hari i<PERSON>esho byihariye byateganyijwe ku bafite ubumuga muri iyi nyubako?", "noDisabilityFacilities": "Ese hari ibi<PERSON>esho byihariye byateganyijwe ku bafite ubumuga muri iyi nyubako? (<PERSON><PERSON> atari byo, sobanura impamvu)", "inspectionDate": "<PERSON><PERSON><PERSON> y'is<PERSON><PERSON> ya<PERSON>we", "userType": "Injeniyeri w'urubuga / Umunyabugeni w'inyubako (Hitamo ubwoko bw'umukoresha)", "licenseNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisingFirm": "Injeniyeri/umunyabugeni wo mu kigo gicunga ibikorwa", "remarks": "Ibyitonderwa", "constructionStage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "otherApplicationWithoutNCP": {"projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "projectBrief": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "estimatedMonthlyWaterM3": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "distanceNearestLandLineFiberM": "Intera iri hagati n'umuyoboro wa telefone cyangwa fibre optique hafi (m)", "estimatedProjectCostUsd": "Igiciro cy'umushinga cyabaruwe mu madolari ya Amerika (USD)", "estimatedProjectCostRwf": "Igiciro cy'umushinga cyabaruwe mu mafaranga y'u Rwanda (RWF)", "estimatedMonthlyElectricityKwh": "Amashanyarazi y'uk<PERSON> yabaruwe (Kwh)", "developmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON><PERSON>", "plotSizeSquareMeters": "Ingano y'ubutaka (m²)", "grossFloorArea": "Ubuso rusange bw'amagorofa", "proposedNumberOfFloorsGPlus": "Umubare w'amagorofa ateganyijwe / G+", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "priceOfDwellingUnit": "Igiciro cy'u<PERSON><PERSON> b<PERSON>", "builtUpArea": "Agace kabatswemo (Built-up area)", "capacityInfoPeopleSeats": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "doItYourself": "Ese urashaka kubikora wowe ubwawe?", "assignToEngineerArchitect": "Ohereza iyi dossier kuri in<PERSON>/umunyabugeni w'inyu<PERSON>ko", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "permitType": "Ubwoko bw'Icyangombwa", "plotSize": "Ingano y'ubutaka", "buildingType": "Ubwoko bw'inyubako", "projectCategory": "Icyiciro c<PERSON>um<PERSON>", "numberOfFloorGPlus": "Umubare w'amagorofa / G+", "builtUpAreaSqm": "Agace kabatswemo (m²)", "buildUpAreaTooLarge": "<PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON> nti<PERSON>za ingano y'ubutaka", "grossFloorAreaRepeat": "Ubuso rusange bw'amagorofa", "numberOfParkingSpacesRepeat": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "priceDwellingUnitRwf": "Igiciro cy'u<PERSON>uro cyabaruwe mu RWF", "descriptionOperations": "Ibisobanuro ku mikorere", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> w'inzu zo guturwamo", "estimatedMonthlyWaterM3Repeat": "Amazi akoreshwa ku kwezi (m³)", "estimatedMonthlyElectricityWatts": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'uk<PERSON><PERSON> yabaruwe (watts)", "distanceLandLineFiberRepeat": "Intera iri hagati n'umuyoboro wa fibre optique (m)", "estimatedCostUsdRepeat": "Igiciro cy'umushinga mu <PERSON>olari (USD)", "estimatedCostRwfRepeat": "Igiciro cy'umushinga mu mafaranga y'u Rwanda (RWF)", "capacityInfoRepeat": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "technologySurveyOptional": "Isuzuma ry'ikoranabuhanga (bitari ngombwa)", "dataNotFoundEIA": "Amakuru ntabonetse. Nyamuneka usabe icyemezo cy'ingaruka ku bidukikije kuri RDB!", "projectAlreadyAssigned": "Ntabwo ushobora gukomeza kuko um<PERSON>a wamaze guhabwa in<PERSON>yeri/umunyabugeni.", "cancel": "<PERSON><PERSON><PERSON>", "next": "Komeza", "messageTitle": "<PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON> indi dossier i<PERSON><PERSON>. S<PERSON>ira indi ifite ibisabwa cyangwa wemeze ko inyandiko zose zatanzwe. Ushobora no kongera izitaratanzwe.", "chooseAnotherPermit": "Hitamo indi perimi", "continueCheckingDocs": "Komeza kugenzura inyandiko!"}, "otherApplicationPermitAnswer": {"otherInformation": "<PERSON><PERSON>", "doYouHaveTheOccupancy": "Ese mufite uruhushya rwo gukoresha inyubako?", "firstAidBoxes": "Agasanduku k'ubutabazi bw'ibanze?", "disabilityToilets": "<PERSON><PERSON><PERSON><PERSON> ifasha aba<PERSON>e ubumuga mu bwi<PERSON>ro?", "paraLighteningSystem": "<PERSON><PERSON><PERSON> yo kurinda inkuba", "equipmentCapacity": "Ubushobozi bw'ibikoresho", "constructionMethod": "Uburyo b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ibikoresho)", "disasterPrevention": "Kurinda ibiza", "fireAlarmSystem": "Sisitemu y'alarme y'umuriro ifite inzogera kuri buri gorofa", "whyNoFireAlarmSystem": "<PERSON><PERSON> nta sisitemu y'alarme y'umuriro ifite inzogera kuri buri gorofa?", "fireExtinguishers": "Umutekano w'um<PERSON>ro buri metero 50 kuri buri gorofa", "whyNoFireExtinguishers": "<PERSON><PERSON> nta mutekano w'um<PERSON><PERSON> buri metero 50 kuri buri gorofa?", "exitSigns": "Ibirango byerekana inzira y'ubutabazi kuri buri gorofa", "whyNoExitSigns": "<PERSON>ki nta birango byerekana inzira y'ubut<PERSON>zi kuri buri gorofa?", "emergencyExit": "Inzira y'ubutabazi kuri buri gorofa", "whyNoEmergencyExit": "<PERSON><PERSON> nta n<PERSON>ra y'u<PERSON><PERSON><PERSON> kuri buri gorofa?", "floorPlan": "<PERSON><PERSON><PERSON><PERSON><PERSON> cy'inzu kuri buri gorofa", "whyNoFloorPlan": "<PERSON>ki nta gishushanyo cy'inzu kuri buri gorofa?", "numberSign": "Ikimenyetso cyerekana nimero y'i<PERSON><PERSON>a", "whyNoNumberSign": "<PERSON>ki nta kimenyetso cyerekana nimero y'i<PERSON><PERSON>a?", "elevatorSign": "Ikimenyetso kibuza gukoresha ascenseur mu gihe cy'umuriro", "whyNoElevatorSign": "<PERSON><PERSON> nta kimenyetso kibuza gukoresha ascenseur mu gihe cy'umuriro?", "helicopterLanding": "Ahantu indege ya kajugujugu yagera hejuru y'inzu mu gihe habaye ikibazo hasi: YEGO / OYA (niba oya, impamvu)", "whyNoHelicopterLanding": "<PERSON><PERSON><PERSON> indege ya kajugujugu yagera hejuru mu gihe habaye ikibazo hasi (niba oya, impamvu)", "terrorAttacks": "Ibitero by'iterabwoba", "cctvCameras": "Camera z'umutekano (CCTV)", "whyNoCctvCameras": "Camera z'umutekano (CCTV) (<PERSON><PERSON> atari byo, sobanura impamvu)", "metalDetector": "Ibikoresho bipima i<PERSON>yurwa<PERSON> n'ibifatika", "whyNoMetalDetector": "<PERSON><PERSON><PERSON><PERSON><PERSON> bi<PERSON> i<PERSON> (<PERSON><PERSON> atari byo, sobanura impamvu)", "underSearchMirror": "Indorerwamo yo kureba munsi y'imodoka", "whyNoUnderSearchMirror": "Indorer<PERSON><PERSON> yo kureba munsi y'imodoka (<PERSON><PERSON> atari byo, sobanura impamvu)", "luggageScanners": "<PERSON><PERSON><PERSON> zisaka imizigo", "whyNoLuggageScanners": "<PERSON><PERSON><PERSON> zisaka imizigo (<PERSON><PERSON> atari byo, sobanura impamvu)", "emergencyContactPlates": "<PERSON><PERSON>apa byerekana serivisi z'ubutabazi n'imibare ya telefoni", "whyNoEmergencyContactPlates": "<PERSON><PERSON><PERSON> byerekana serivisi z'ubutabazi n'imibare ya telefoni (<PERSON>ba atari byo, sobanura impamvu)", "evacuationPlan": "Gahu<PERSON> yo guhungisha abantu mu gihe cy'ibyago", "whyNoEvacuationPlan": "<PERSON><PERSON><PERSON> yo guhungisha abantu mu gihe cy'ibyago (<PERSON><PERSON> atari byo, sobanura impamvu)", "securityManagerCameras": "Umuyobozi w'umutekano n'abakozi bafite camera", "whyNoSecurityManagerCameras": "Umuyobozi w'umutekano n'abakozi bafite camera (<PERSON><PERSON> atari byo, sobanura impamvu)", "internalComms": "<PERSON><PERSON><PERSON> y'it<PERSON><PERSON>o imbere mu nyubako", "whyNoInternalComms": "<PERSON><PERSON><PERSON> <PERSON>'it<PERSON><PERSON>o imbere mu nyubako (<PERSON><PERSON> atari byo, sobanura impamvu)", "broadband": "Internet yihuta (Broadband)", "whyNoBroadband": "Internet yihuta (Broadband) (<PERSON><PERSON>ari <PERSON>o, sobanura impamvu)", "accessCards": "Amakarita y'a<PERSON><PERSON><PERSON> n'abashyitsi", "whyNoAccessCards": "Am<PERSON><PERSON> <PERSON>'a<PERSON><PERSON><PERSON> n'abas<PERSON> (<PERSON><PERSON> atari byo, sobanura impamvu)", "fixedTelephone": "Gusaba umurongo wa telefone uhamye", "facilitiesForPWD": "Ibikor<PERSON>o by<PERSON>ariye ku bafite ubumuga", "facilitiesForDisabled": "Ese hari i<PERSON>esho byihariye byateganyijwe ku bafite ubumuga muri iyi nyubako?", "whyNoFacilitiesForDisabled": "Ese hari ibi<PERSON>esho byihariye byateganyijwe ku bafite ubumuga muri iyi nyubako? (<PERSON><PERSON> atari byo, sobanura impamvu)", "inspectionDate": "<PERSON><PERSON><PERSON> y'is<PERSON><PERSON> ya<PERSON>we", "userType": "Injeniyeri w'urubuga / Umunyabugeni w'inyubako (Hitamo ubwoko bw'umukoresha)", "licenseNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supervisingFirm": "Injeniyeri/umunyabugeni wo mu kigo gicunga ibikorwa", "remarks": "Ibyitonderwa", "stageOfConstruction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "eiaFormChecker": {"checkingEIA": "Gusuzuma icyemezo cya EIA", "eiaApplyMessage": "Kanda hano usabe", "certificateNumber": "Nimero y'icyemezo", "upi": "UPI"}, "assignApplicationDetail": {"parcelDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "plotNumberUPI": "Numero ya Parcelle / UPI", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "plannedFor": "Biteganyirijwe", "location": "<PERSON><PERSON> g<PERSON>", "applicationDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "requestFrom": "<PERSON><PERSON><PERSON> buvuye kuri", "plotSize": "Ingano y'ubutaka", "buildUpArea": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "applicationDate": "<PERSON><PERSON><PERSON> yo gusaba", "rejectRequest": "<PERSON><PERSON>", "acceptRequest": "<PERSON><PERSON>"}, "assignedApplications": {"welcome": "Murakaza neza!", "projectRequests": "<PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>...", "filterByStatusSize": "<PERSON><PERSON><PERSON><PERSON>", "requestFrom": "Ibisabwa bi<PERSON>ye kuri", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "upi": "UPI", "plotSize": "Ingano y'ubutaka", "timelineForSubmission": "<PERSON><PERSON><PERSON> n<PERSON>wa cyo gutanga i<PERSON>wa", "appliedFor": "Byasabwaga ku", "applicationDate": "<PERSON><PERSON><PERSON> b<PERSON>", "status": "Imiterere", "viewOffer": "<PERSON><PERSON>", "applyForPermit": "Saba <PERSON>angombwa", "request": "Gusaba", "applicationRequest": "Gusaba uburenganzira"}, "projectDetailContinue": {"complete": "<PERSON><PERSON><PERSON>", "application": "<PERSON><PERSON><PERSON>", "projectInformation": "<PERSON>akuru ku mushinga", "checkToAddAssociatedUpi": "Reba ushake UPI ujyanye", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "permitType": "Ubwoko bw'Icyangombwa", "buildingType": "Ubwoko bw'inyubako", "projectCategory": "Icyiciro c<PERSON>um<PERSON>", "plotSize": "Ingano y'ubutaka (mu metero kare)", "combiningPlotSize": "<PERSON><PERSON><PERSON> u<PERSON>o bw'ubutaka", "numberOfFloor": "Umubare w'amagorofa / G+", "buildUpArea": "<PERSON><PERSON><PERSON> ka<PERSON> (mu metero kare)", "buildUpAreaError": "<PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON> nti<PERSON>za ingano y'ubutaka.", "grossFloorArea": "Ubuso rusange bw'amagorofa", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "estimatedPriceOfDwellingUnit": "Igiciro cy'inzu y'u<PERSON>uro cyabaruwe mu RWF", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> w'inzu zo guturwamo", "descriptionOfOperations": "Ibisobanuro ku mikorere", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "estimatedWaterConsumption": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "estimatedElectricityConsumption": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'uk<PERSON><PERSON> yabaruwe (watts)", "distanceToLandline": "Intera iri hagati n'umuyoboro wa telefone cyangwa fibre optique hafi (m)", "estimatedProjectCostUSD": "Igiciro cy'umushinga cyabaruwe mu madolari (USD)", "estimatedProjectCostRWF": "Igiciro cy'umushinga cyabaruwe mu mafaranga y'u Rwanda (RWF)", "capacityInformation": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "selectedCategoryUse": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ej<PERSON>", "selectedUse": "<PERSON><PERSON>", "isFromOldSystem": "<PERSON><PERSON> ubusabe buva mu buryo bwa kera", "isUnderMortgage": "<PERSON>iri mu ngwate", "isUnderRestriction": "Bifite imbogamizi", "cancel": "<PERSON><PERSON><PERSON>", "next": "Komeza", "associatedUpi": "UPI bifitanye isano"}, "continuePendingApplication": {"step": "<PERSON><PERSON><PERSON>", "developmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON><PERSON>", "oldSystemHeaderNote": "<PERSON><PERSON><PERSON> wawe wabonetse mu buryo bwa kera, u<PERSON><PERSON> ibisigaye ubone gukomeza", "checkToAddAssociatedUpi": "<PERSON><PERSON> wongereho UPI ijyanye", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "permitType": "Ubwoko bwa perimi", "buildingType": "Ubwoko bw'inyubako", "plotSize": "Ingano y'ubutaka (mu metero kare)", "buildUpArea": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (mu metero kare)", "buildUpAreaError": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ntibukwiye kurenza ingano y'ubutaka.", "numberOfFloor": "Umubare w'amagorofa / G+", "grossFloorArea": "Ubuso rusange bw'amagorofa", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "estimatedPriceOfDwellingUnitRWF": "Igiciro cy'inzu y'u<PERSON>uro cyabaruwe mu RWF", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> w'in<PERSON> z'ubuturo", "descriptionOfOperations": "Ibisobanuro ku mikorere", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "estimatedWaterConsumption": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "estimatedElectricityConsumption": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'uk<PERSON><PERSON> yabaruwe (watts)", "distanceToLandline": "Intera iri hagati na fibre optique cyangwa telefone hafi (m)", "estimatedProjectCostUSD": "Igiciro cy'umushinga cyabaruwe mu madolari (USD)", "estimatedProjectCostRWF": "Igiciro cy'umushinga cyabaruwe mu mafaranga y'u Rwanda (RWF)", "capacityInformation": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "selectedCategoryUse": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ej<PERSON>", "selectedUse": "<PERSON><PERSON>", "isFromOldSystem": "<PERSON><PERSON> ubusabe buva mu buryo bwa kera", "isUnderMortgage": "<PERSON>iri mu ngwate", "isUnderRestriction": "Bifite imbogamizi", "cancel": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON>", "next": "Komeza", "associatedUpi": "UPI bifitanye isano"}, "projectModification": {"title": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>a", "projectName": "<PERSON><PERSON><PERSON> <PERSON>'<PERSON>", "projectDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "plotSize": "Ingano y'ubutaka (mu metero kare)", "permitType": "Ubwoko bwa perimi", "buildingType": "Ubwoko bw'inyubako", "projectCategory": "Icyiciro c<PERSON>um<PERSON>", "builtUpArea": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (mu metero kare)", "builtUpAreaError": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> ntibukwiye kurenza ubuso bw'ubutaka.", "numberOfFloor": "Umubare w'amagorofa / G+", "grossFloorArea": "Ubuso rusange bw'amagorofa", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "estimatedPriceDwellingRwf": "Igiciro cy'inzu y'u<PERSON>uro cyabaruwe mu RWF", "descriptionOfOperations": "Ibisobanuro ku mikorere", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> w'inzu zo guturwamo", "estimatedWaterConsumption": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "estimatedElectricityConsumption": "<PERSON><PERSON><PERSON><PERSON><PERSON> y'uk<PERSON><PERSON> yabaruwe (watts)", "distanceToLandline": "Intera iri hagati n'umuyoboro wa fibre optique cyangwa telefone (m)", "estimatedProjectCostUsd": "Igiciro cy'umushinga cyabaruwe mu madolari (USD)", "estimatedProjectCostRwf": "Igiciro cy'umushinga cyabaruwe mu mafaranga y'u Rwanda (RWF)", "capacityInformation": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "technologySurvey": "Isuzuma ry'ikoranabuhanga (bitari ngombwa)", "cancel": "<PERSON><PERSON><PERSON>", "next": "Komeza"}, "relatedPermitType": {"step": "<PERSON><PERSON><PERSON>", "projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAssociatedUpi": "Ongeramo UPI bifitanye isano", "doItYourself": "Ese urashaka kubikora wowe ubwawe?", "assignToEngineer": "Ohereza iyi dossier kuri inje<PERSON>yeri cyangwa umunyabugeni <PERSON>ko", "permitType": "Ubwoko bwa perimi", "plotSize": "Ingano yubutaka", "buildingType": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "projectCategory": "<PERSON><PERSON><PERSON><PERSON>", "numberOfFloor": "<PERSON><PERSON><PERSON> wa<PERSON>a / G+", "builtUpArea": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> (mu metero kare)", "builtUpAreaError": "<PERSON><PERSON><PERSON> bwu<PERSON><PERSON><PERSON><PERSON> ntibukwiye kurenza ubuso bwubutaka.", "grossFloorArea": "<PERSON>bus<PERSON> rusa<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "parkingSpaces": "<PERSON><PERSON><PERSON> im<PERSON>", "dwellingPriceRwf": "Igiciro cyinzu yubuturo cyabaruwe mu RWF", "descriptionOfOperation": "Ibisobanuro ku mikorere", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> ryu<PERSON><PERSON> r<PERSON>", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> win<PERSON> zu<PERSON>", "waterConsumption": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "electricityConsumption": "<PERSON><PERSON><PERSON><PERSON><PERSON> yukwezi yabar<PERSON>we (watts)", "distanceToLandline": "Intera iri hagati numuyoboro wa fibre optique cyangwa telefone hafi (m)", "projectCostUsd": "Igiciro cyumus<PERSON> mu <PERSON>olari (USD)", "projectCostRwf": "Igiciro cyumushinga mu mafaranga yu Rwanda (RWF)", "capacityInformation": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "technologySurvey": "Isuzuma r<PERSON> (bitari ngombwa)", "assignConditionNote": "Hitamo iyi gahunda niba ushaka ko umushinga woherezwa ku injeniyeri/umunyabugeni cyangwa ugakomeza ubwawe.", "submitEIARequestNote": "Amakuru ntabonetse. Nyamuneka usabe icyemezo cyingaruka ku bidukikije kuri RDB!", "notAllowedToProceed": "Ntabwo ushobora gukomeza kuko um<PERSON>a wamaze guhabwa in<PERSON>yeri/umunyabugeni.", "previous": "<PERSON><PERSON>", "next": "Komeza"}, "applicationRoadMap": {"step": "<PERSON><PERSON><PERSON>"}, "otherApplicationDocument": {"associatedUpiTitle": "UPI bifitanye isano", "projectAttachmentTitle": "Icyegeranyo cy'umushinga", "requiredTitle": "Ibyangombwa bisabwa", "requiredBoqLink": "Kanda hano ushyireho dosiye ya BOQ", "requiredWarning": "Dosiye ya BOQ iri mu buryo bwa Excel. Nyamuneka banza uyimanure uyuzuze. Izindi dosiye zemerewe ni PDF gusa", "uploadedTitle": "Ibyangombwa byashyizweho", "upload": "Ohereza dosiye", "wait": "Tegereza...", "nofile": "nta dosiye", "delete": "Siba", "previous": "<PERSON><PERSON>", "saveDocument": "<PERSON><PERSON>", "submit": "Ohereza", "resubmit": "Ohereza bundi bushya", "confirm": "<PERSON><PERSON><PERSON>", "certificationText": "Ni inshingano yanjye y'umwi<PERSON><PERSON> kwemeza ko amakuru natanze muri ubu busabe ari ukuri kandi nemera amabwiriza agenga uru rubuga.", "cancel": "<PERSON><PERSON><PERSON>", "deleteFileTitle": "Ugiye gusiba ", "deleteFileBodyA": "Gusiba ", "deleteFileBodyB": "<PERSON><PERSON><PERSON>ho amakuru yawe yose muri system ya KUBAKA.", "confirmDelete": "Yego, <PERSON>ba!", "close": "Funga"}, "additionalUploadFile": {"title": "<PERSON><PERSON><PERSON>", "uploadedDocuments": "Dosiye zashyizweho", "additionalFile": "<PERSON><PERSON><PERSON> z<PERSON>i", "onlyPdfAllowed": "Dosiye zemerewe ni PDF gusa, uretse BOQ", "noFile": "nta dosiye", "upload": "Ohereza", "view": "Re<PERSON>", "remove": "<PERSON><PERSON><PERSON>", "viewDocumentTitle": "<PERSON><PERSON>", "youAreAboutToDelete": "Ugiye gusiba ", "descriptionA": "Gusiba ", "descriptionB": "<PERSON><PERSON><PERSON>ho amakuru yawe yose muri system ya KUBAKA.", "close": "Funga", "confirm": "Yego, <PERSON>ba!", "inProgress": "Porogaramu iri gusiba dosiye...", "wait": "Tegereza..."}, "otherApplication": {"projectDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by<PERSON><PERSON><PERSON><PERSON>", "doItYourself": "<PERSON>se urashaka kubi<PERSON>a wowe ubwawe", "assignToEngineerArchitect": "Ohereza iyi dossier ku injeniyeri cyangwa umunyabugeni w'inyubako", "projectBrief": "Incamake y'umushinga", "estimatedMonthlyWaterM3": "Amazi akoreshwa ku kwezi (m³) yabaruwe", "distanceToLandline": "Intera iri hagati n'umuyoboro wa fibre optique cyangwa telefone hafi (m)", "estimatedProjectCostUsd": "Igiciro cy'umushinga cyabaruwe mu madolari (USD)", "estimatedProjectCostRwf": "Igiciro cy'umushinga cyabaruwe mu mafaranga y'u Rwanda (RWF)", "estimatedMonthlyElectricityKwh": "Amashanyarazi y'uk<PERSON> yabaruwe (Kwh)", "developmentDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> by'<PERSON><PERSON><PERSON><PERSON>", "plotSizeSquareMeters": "Ingano y'ubutaka (mu metero kare)", "proposedNumberOfFloorsGPlus": "Umubare w'amagorofa ateganyijwe / G+", "numberOfParkingSpaces": "<PERSON><PERSON><PERSON> w'<PERSON><PERSON><PERSON><PERSON> imodoka", "priceOfDwellingUnit": "Igiciro cy'inzu y'ubuturo", "builtUpArea": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "grossFloorArea": "Ubuso rusange bw'amagorofa", "percentageSpaceUse": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfDwellingUnits": "<PERSON><PERSON><PERSON> w'in<PERSON> z'ubuturo", "descriptionOperations": "Ibisobanuro ku mikorere", "capacityInfoPeopleSeats": "Ubushobozi bwo kwakira aban<PERSON> / imyanya", "permitType": "Ubwoko bwa perimi", "buildingType": "Ubwoko bw'inyubako", "category": "Icyiciro c<PERSON>um<PERSON>", "upi": "UPI", "dataNotFoundEIA": "Amakuru ntabonetse. Nyamuneka usabe icyemezo cy'ingaruka ku bidukikije kuri RDB!", "submitRdb": "Ohereza ubusabe bwawe kuri RDB kugira ngo ubone icyemezo cya EIA", "projectAlreadyAssigned": "Ntabwo ushobora gukomeza kuko um<PERSON>a wamaze guhabwa in<PERSON>yeri/umunyabugeni", "prev": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "chooseAnotherPermit": "Hitamo indi perimi", "continueCheckingDocs": "Komeza kugenzura inyandiko!", "message": "<PERSON><PERSON><PERSON><PERSON>", "applicationExists": "<PERSON> ubusabe busanzweho. Shyiramo ubundi busabe cyangwa wemeze ko inyandiko zose zisabwa zatanzwe. Ushobora no kongera izitaratanzwe.", "loading": "Tegereza ....."}, "projectAttachment": {"title": "Dosiye z'umushinga", "requiredTitle": "Icyangombwa gisabwa", "requiredDocuments": "Ibyangombwa bisabwa", "choose": "Hitamo", "chooseUploadDocument": "Hitamo dosiye yo kohereza", "uploadDocumentPdf": "Shyiraho dosiye.pdf", "upload": "Ohereza", "close": "Funga", "delete": "Siba", "uploadedTitle": "Ibyangombwa byoherejwe", "deleteFileTitle": "Ugiye gusiba ", "deleteFileBodyA": "Gusiba ", "deleteFileBodyB": " <PERSON><PERSON><PERSON>ho amakuru yawe yose muri system ya KUBAKA.", "wait": "Tegereza...", "previous": "<PERSON><PERSON>", "submit": "Ohereza", "confirm": "<PERSON><PERSON><PERSON>", "certificationText": "Ni inshingano yanjye y'umwi<PERSON>ko kwemeza ko amakuru natanze muri ubu busabe ari ukuri kandi nemeye amabwiriza n'amategeko abigenga.", "cancel": "<PERSON><PERSON><PERSON>", "confirmDelete": "Yego, <PERSON>ba!", "nofile": "nta dosiye", "requiredDocumentsList": "Ibyangombwa bisabwa", "uploadedDocumentsList": "Ibyangombwa byashyizweho", "requiredWarning": "Dosiye ya BOQ iri mu buryo bwa Excel. Nyamuneka banza uyimanure uyuzuze. Izindi dosiye zemerewe ni PDF gusa.", "requiredBoqLink": "Kanda hano ushyireho dosiye ya BOQ"}, "factorLogin": {"verify": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "login": "Inji<PERSON>"}}