/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  Invoice,
  InvoiceItem,
  InvoiceStatus,
  InvoiceType,
  Price,
  Receipt,
} from '../entities/invoice.entity';
import { Certificate } from '../entities/certificate.entity';
import {
  InvoiceStatusRepository,
  PriceRepository,
  InvoiceTypeRepository,
  InvoiceItemRepository,
  InvoiceRepository,
  ReceiptRepository,
} from './invoice.repository';
import {
  InvoiceDto,
  InvoiceItemDto,
  InvoiceStatusDto,
  InvoiceTypeDto,
  PriceDto,
  ReceiptDto,
  UpdateInvoiceDto,
} from '../dto/invoice.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { AUTH_SERVICE } from '@app/common/constants';
import { ClientProxy } from '@nestjs/microservices';
import {
  IremboResponseGetDto,
  IremboPaymentRequestInputDto,
} from '../dto/invoiceIrembo.dto';

import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';

import axios from 'axios';
import { CertificateService } from '../certificate/certificate.service';

import config from '../../config';
import { ApplicationService } from '../application/application.service';

import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

export interface InvoiceWithUserDetails {
  userDetails: any;
}

interface IremboInvoiceResponse {
  success: boolean;
  data: {
    success: boolean;
    invoiceNumber: string;
    transactionId: string;
    amount: number;
    currency: string;
    createdAt: string;
    expiresAt: string;
    description: string;
    paymentStatusMessage: string;
    paymentStatus: string;
    paymentNarration: string;
  };
}

@Injectable()
export class InvoiceService {
  restClient: any;
  constructor(
    private readonly invoiceStatusRepository: InvoiceStatusRepository,
    private readonly priceRepository: PriceRepository,
    private readonly invoiceTypeRepository: InvoiceTypeRepository,
    private readonly invoiceItemRepository: InvoiceItemRepository,
    private readonly invoiceRepository: InvoiceRepository,
    private readonly receiptRepository: ReceiptRepository,

    @InjectRepository(Invoice)
    private invoiceEntityManagerRepository: Repository<Invoice>,

    @InjectRepository(InvoiceStatus)
    private invoiceStatusEntityManagerRepository: Repository<InvoiceStatus>,

    @InjectRepository(Price)
    private priceEntityManagerRepository: Repository<Price>,

    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,

    private readonly httpService: HttpService,

    private readonly certificateService: CertificateService,
    private readonly applicationService: ApplicationService,

    @InjectRepository(Certificate)
    private readonly certificateRepository: Repository<Certificate>,
  ) {}

  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }

  // invoice Status
  async createInvoiceStatus(invoiceStatusDto: InvoiceStatusDto) {
    const invoiceStatus = new InvoiceStatus({
      ...invoiceStatusDto,
    });
    return this.invoiceStatusRepository.create(invoiceStatus);
  }

  async findAllInvoiceStatus() {
    return this.invoiceStatusRepository.find({});
  }

  async findOneInvoiceStatus(id: string) {
    return this.invoiceStatusRepository.findOne({ id });
  }
  // invoice status by code
  async findOneInvoiceStatusByCode(code: string) {
    return this.invoiceStatusRepository.findOne({
      code,
    });
  }

  async updateInvoiceStatus(id: string, invoiceStatusDto: InvoiceStatusDto) {
    return this.invoiceStatusRepository.findOneAndUpdate(
      { id },
      invoiceStatusDto,
    );
  }

  async removeInvoiceStatus(id: string) {
    return this.invoiceStatusRepository.findOneAndDelete({ id });
  }

  // price
  async createPrice(priceDto: PriceDto) {
    const dataFromDb2 = await this.applicationService.findOnePermitType(
      priceDto.permitTypeId,
    );
    if (!dataFromDb2)
      throw new HttpException('Data Not Found', HttpStatus.NOT_FOUND);
    const price = new Price({
      ...priceDto,
      permitTypes: (priceDto.permitTypeId = {
        id: dataFromDb2.id,
      } as any),
    });
    return this.priceRepository.create(price);
  }

  // async findAllPrices() {
  //   return this.priceRepository.find({
  //     permitTypes: true,
  //   });
  // }

  async findAllPrices() {
    return this.priceRepository.findAll({
      relations: ['permitTypes'],
      order: {
        permitTypes: {
          code: 'ASC',
        },
      },
    });
  }

  async findOnePrice(id: string) {
    return this.priceRepository.findOne({ id, permitTypes: true });
  }

  // async updatePrice(id: string, priceDto: PriceDto) {
  //   return this.priceRepository.findOneAndUpdate({ id }, priceDto);
  // }
  async updatePrice(id: string, priceDto: PriceDto): Promise<Price> {
    const { amount, rangeInSqmMin, rangeInSqmMax, permitTypeId, userId } =
      priceDto;

    const permitTypeData =
      await this.applicationService.findOnePermitType(permitTypeId);
    if (!permitTypeData) {
      throw new HttpException('Permit Data Not Found', HttpStatus.NOT_FOUND);
    }

    await this.priceEntityManagerRepository
      .createQueryBuilder()
      .update(Price)
      .set({
        amount,
        rangeInSqmMin,
        rangeInSqmMax,
        permitTypes: permitTypeData,
        // permitTypeId,
        userId,
      })
      .where('id = :id', { id })
      .execute();

    return this.priceEntityManagerRepository.findOne({ where: { id } });
  }

  async removePrice(id: string) {
    return this.priceRepository.findOneAndDelete({ id });
  }

  // invoice Type
  async createInvoiceType(invoiceTypeDto: InvoiceTypeDto) {
    const invoiceType = new InvoiceType({
      ...invoiceTypeDto,
    });
    return this.invoiceTypeRepository.create(invoiceType);
  }

  async findAllInvoiceType() {
    return this.invoiceTypeRepository.find({});
  }
  // getting invoice type with ivoice code
  async findAllInvoiceTypeWithCode(invoiceCode: string) {
    return this.invoiceTypeRepository.findAll({
      where: { code: invoiceCode },
    });
  }

  async findOneInvoiceType(id: string) {
    return this.invoiceTypeRepository.findOne({ id });
  }

  async updateInvoiceType(id: string, invoiceTypeDto: InvoiceTypeDto) {
    return this.invoiceTypeRepository.findOneAndUpdate({ id }, invoiceTypeDto);
  }

  async removeInvoiceType(id: string) {
    return this.invoiceTypeRepository.findOneAndDelete({ id });
  }

  // invoice Item
  async createInvoiceItem(invoiceItemDto: InvoiceItemDto) {
    const dataFromDb = await this.priceRepository.findOne({
      id: invoiceItemDto.priceId,
    });
    if (!dataFromDb)
      throw new HttpException('Price Not Found', HttpStatus.BAD_REQUEST);

    const invoiceItem = new InvoiceItem({
      ...invoiceItemDto,
      prices: (invoiceItemDto.priceId = {
        id: dataFromDb.id,
      } as any),
    });
    return this.invoiceItemRepository.create(invoiceItem);
  }

  async findAllInvoiceItem() {
    return this.invoiceItemRepository.find({});
  }
  // getting invoice item by item name
  async findAllInvoiceItemByName(itemName: string) {
    return this.invoiceItemRepository.findAll({
      where: { name: itemName },
      relations: ['prices'],
    });
  }

  async findOneInvoiceItem(id: string) {
    return this.invoiceItemRepository.findOne({ id });
  }

  async updateInvoiceItem(id: string, invoiceItemDto: InvoiceItemDto) {
    return this.invoiceItemRepository.findOneAndUpdate({ id }, invoiceItemDto);
  }

  async removeInvoiceItem(id: string) {
    return this.invoiceItemRepository.findOneAndDelete({ id });
  }

  // invoice
  async createInvoice(invoiceDto: InvoiceDto) {
    // Check if an invoice with the same application number exists and is not canceled
    const existingInvoices = await this.invoiceEntityManagerRepository.find({
      where: {
        applications: { id: invoiceDto.applicationId },
      },
      relations: ['applications', 'invoiceStatus'],
    });

    const isInvoiceNotCancellable = existingInvoices.some(
      (invoice) => invoice.invoiceStatus.code !== 'CCD',
    );

    if (isInvoiceNotCancellable) {
      throw new HttpException(
        'Invoice is already created',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Search entities
    const dataFromDb = await this.invoiceTypeRepository.findOne({
      id: invoiceDto.invoiceTypeId,
    });
    if (!dataFromDb) {
      throw new HttpException('Type Not Found', HttpStatus.BAD_REQUEST);
    }

    const userDetails = await this.checkUser(invoiceDto.applicantUserId);

    const dataFromDb2 = await this.invoiceItemRepository.findOne({
      id: invoiceDto.invoiceItemId,
    });
    if (!dataFromDb2) {
      throw new HttpException('Item Not Found', HttpStatus.BAD_REQUEST);
    }

    // Determine invoice status based on amount
    let dataFromDb3;
    if (invoiceDto.amount === '0') {
      dataFromDb3 = await this.invoiceStatusRepository.findOne({
        code: 'EXMTD',
      });
    } else {
      dataFromDb3 = await this.invoiceStatusRepository.findOne({
        id: invoiceDto.invoiceStatusId,
      });
    }

    if (!dataFromDb3) {
      throw new HttpException('Status Not Found', HttpStatus.BAD_REQUEST);
    }

    // Generate invoice number
    const timestamp = Date.now().toString().slice(-5);
    const numericPart = Math.floor(Math.random() * 100000);
    const numericString = (numericPart + `-` + timestamp).padStart(10, '0');
    invoiceDto.invoiceNumber = `INV-${invoiceDto.agencyCode}-${numericString}`;

    // DueDate: Add 30 days from today
    const today: Date = new Date();
    const futureDate: Date = new Date();
    futureDate.setDate(today.getDate() + 30);

    if (invoiceDto.amount === '0') {
      // If amount is zero, save invoice directly without external API calls or notifications
      const invoice = new Invoice({
        ...invoiceDto,
        invoiceTypes: { id: dataFromDb.id } as any,
        invoiceItems: { id: dataFromDb2.id } as any,
        invoiceStatus: { id: dataFromDb3.id } as any,
        applications: { id: invoiceDto.applicationId } as any,
        phone: userDetails.phoneNumber,
        email: userDetails.email,
        description: dataFromDb.name,
        dueDate: futureDate,
        externalReferenceNumber: null,
      });

      // return this.invoiceRepository.create(invoice);
      const savedInvoice = await this.invoiceRepository.create(invoice);

      return {
        statusCode: 200,
        message: 'Invoice created successfully',
        data: savedInvoice,
      };
    } else {
      // Push invoice details to external API
      const externalApiData = {
        transactionId: invoiceDto.invoiceNumber,
        code: 'E-PYMNT',
        unitAmount: invoiceDto.amount,
        quantity: 1,
        paymentAccountIdentifier: invoiceDto.paymentAccountIdentifier,
        language: 'EN',
        expiryAt: futureDate,
        description: dataFromDb.name,
        notification: dataFromDb.name,
        phoneNumber: invoiceDto.phone,
        email: invoiceDto.email,
        name: userDetails.firstName,
      };

      let externalReferenceNumberFromIrembo: string;

      try {
        const iremboResponse = await axios.post(
          `${config.payment.irembopayAPI}/payment/create/`,
          externalApiData,
        );
        console.log('Irembo Response Data:', iremboResponse.data);
        const responseData = iremboResponse.data;
        if (
          responseData.status &&
          responseData.data &&
          responseData.data.invoiceNumber
        ) {
          externalReferenceNumberFromIrembo = responseData.data.invoiceNumber;
        } else {
          throw new Error('Invoice number not found in the response');
        }
      } catch (error) {
        console.error('Error pushing data to irembo API:', error);
        externalReferenceNumberFromIrembo = 'NOT_AVAILABLE';
      }

      // Create invoice object with external reference number
      const invoice = new Invoice({
        ...invoiceDto,
        invoiceTypes: { id: dataFromDb.id } as any,
        invoiceItems: { id: dataFromDb2.id } as any,
        invoiceStatus: { id: dataFromDb3.id } as any,
        applications: { id: invoiceDto.applicationId } as any,
        phone: userDetails.phoneNumber,
        email: userDetails.email,
        description: dataFromDb.name,
        dueDate: futureDate,
        externalReferenceNumber: externalReferenceNumberFromIrembo,
      });

      const savedData = await this.invoiceRepository.create(invoice);

      if (savedData) {
        // Send notifications
        const requestData = {
          sender_name: 'KUBAKA MIS',
          sender_email: `${config.notification.senderEmail}`,
          receiver_name: 'KUBAKA User',
          receiver_email: userDetails.email,
          subject: 'Invoice Notification',
          message: `
          Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
          Your application has been approved and an invoice has been generated. <br><br>
          Invoice Number: ${savedData.invoiceNumber}<br> You need to pay it,Bill ID: ${savedData.externalReferenceNumber}.<br>
          Best regards, <br>
          KUBAKA Team
        `,
        };

        try {
          const [emailResponse, smsResponse] = await Promise.all([
            axios.post(
              `${config.notification.emailAPI}/email/send/`,
              requestData,
            ),
            axios.post(`${config.notification.smsAPI}/sms/send/`, {
              msisdn: userDetails.phoneNumber,
              message: `Dear ${userDetails.firstName}, an invoice has been generated with number ${savedData.invoiceNumber} Dear ${userDetails.firstName}, an invoice has been generated with number ${savedData.invoiceNumber} You need to pay it.`,
            }),
          ]);

          console.log('Email sent:', emailResponse.data);
          console.log('SMS sent:', smsResponse.data);
        } catch (notificationError) {
          console.error('Error sending notifications:', notificationError);
        }
      }

      return {
        statusCode: 200,
        message: 'Invoice created successfully',
        data: savedData,
      };
    }
  }

  async findAllInvoices() {
    return this.invoiceRepository.findAll({
      relations: [
        'invoiceItems',
        'invoiceTypes',
        'invoiceStatus',
        'applications',
      ],
    });
  }

  async findOneInvoice(id: string) {
    return this.invoiceRepository.findOne({
      id,
      // invoiceTypes: true,
      invoiceStatus: true,
      applications: true,
      // invoiceItems: true,
    });
  }

  async findOneInvoiceAllData(id: string, applicationId: string) {
    console.log('Finding invoice with ID:', id);
    console.log('Application ID:', applicationId);
    // Check if id is provided and not empty
    if (id && id.trim() !== '' && id !== 'undefined' && id !== 'null') {
      let invoice = await this.invoiceEntityManagerRepository
        .createQueryBuilder('invoice')
        .where('invoice.id = :id', { id })
        .leftJoinAndSelect('invoice.applications', 'applications')
        .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
        .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
        .getOne();

      if (!invoice) {
        throw new NotFoundException('Invoice not found');
      }

      // if invoice is canceled
      if (invoice.invoiceStatus.code === 'CCD') {
        // check if there is any other invoice with the same application id which is not canceled
        const otherInvoices = await this.invoiceEntityManagerRepository
          .createQueryBuilder('invoice')
          .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
          .leftJoinAndSelect('invoice.applications', 'applications')
          .where('invoice.applications.id = :applicationId', {
            applicationId: invoice.applications.id,
          })
          .andWhere('invoiceStatus.code != :statusCode', {
            statusCode: 'CCD',
          })
          .getMany();

        if (!otherInvoices.length) {
          throw new HttpException(
            'This invoice is canceled, other invoice need to be created, please contact OCS director for support',
            HttpStatus.BAD_REQUEST,
          );
        }

        // if there is other invoice with the same application id which is not canceled
        if (otherInvoices.length > 0) {
          const nonCoceledInvoice = await this.invoiceEntityManagerRepository
            .createQueryBuilder('invoice')
            .where('invoice.id = :id', { id: otherInvoices[0].id })
            .leftJoinAndSelect('invoice.applications', 'applications')
            .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
            .getOne();

          // Interchange IDs between current invoice and non-canceled invoice
          if (nonCoceledInvoice) {
            // Store both IDs
            const currentInvoiceId = invoice.id;
            const nonCanceledInvoiceId = nonCoceledInvoice.id;

            // First update current invoice to a temporary ID to avoid unique constraint violation
            const tempId = uuidv4();
            await this.invoiceEntityManagerRepository
              .createQueryBuilder()
              .update(Invoice)
              .set({ id: tempId })
              .where('id = :id', { id: currentInvoiceId })
              .execute();

            // Then update non-canceled invoice to current invoice's ID
            await this.invoiceEntityManagerRepository
              .createQueryBuilder()
              .update(Invoice)
              .set({ id: currentInvoiceId })
              .where('id = :id', { id: nonCanceledInvoiceId })
              .execute();

            // Finally update current invoice to non-canceled invoice's ID
            await this.invoiceEntityManagerRepository
              .createQueryBuilder()
              .update(Invoice)
              .set({ id: nonCanceledInvoiceId })
              .where('id = :id', { id: tempId })
              .execute();

            // Update the invoice variable to use the new ID
            invoice.id = nonCanceledInvoiceId;
            invoice = await this.invoiceEntityManagerRepository
              .createQueryBuilder('invoice')
              .where('invoice.id = :id', { id: currentInvoiceId })
              .leftJoinAndSelect('invoice.applications', 'applications')
              .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
              .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
              .getOne();
          }
        }
      }
      // now i need to check is certificate exit or not
      // if certificate is not exit then throw error
      const certificate =
        await this.certificateService.findCertificateByApplicationId(
          invoice.applications.id,
        );

      if (!certificate) {
        // If no certificate is found, we need to create new one
        if (!invoice.applications) {
          throw new BadRequestException(
            'Application details not found for this invoice',
          );
        }
        if (!invoice.applications.permitTypes) {
          throw new BadRequestException(
            'Permit type not found for this application',
          );
        }

        await this.certificateService.createCertificate({
          applicationId: invoice.applications.id,
          userId: invoice.userId,
          applicantUserId: invoice.applications.userId,
          permitTypeId: invoice.applications.permitTypes.id,
          agencyCode: invoice.applications.agencyCode,
          title: 'string',
          lows: 'string',
          backgroundUrl: 'string',
          statusId: '0', // PENDING status
          certificateNumber: 'string',
          invoiceNumber: 'string',
          expiredDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        });
      }
      // console.log('Certificate found:', certificate);
      // if invoice status code is EXMTD and update certificate status to 1 and invoice number
      if (certificate) {
        if (
          invoice.invoiceStatus.code === 'EXMTD' ||
          invoice.invoiceStatus.code === 'PAID'
        ) {
          // Only update if certificate is a Certificate object (not an array)
          if (
            certificate &&
            typeof certificate === 'object' &&
            !Array.isArray(certificate) &&
            'id' in certificate
          ) {
            await this.certificateRepository
              .createQueryBuilder()
              .update(Certificate)
              .set({ statusId: '1', invoiceNumber: invoice.invoiceNumber })
              .where('id = :id', { id: certificate.id })
              .execute();
          }
        }

        if (
          invoice.invoiceStatus.code === 'PND' &&
          invoice.externalReferenceNumber &&
          invoice.externalReferenceNumber !== 'NOT_AVAILABLE'
        ) {
          // console.log("hdhdhdhdhd hdhhdhdh");
          try {
            const iremboResponse: AxiosResponse<IremboInvoiceResponse> =
              await firstValueFrom(
                this.httpService.get<IremboInvoiceResponse>(
                  `${config.payment.irembopayAPI}/payment/invoice/${invoice.externalReferenceNumber}`,
                ),
              );

            console.log('Irembo API response:', iremboResponse.data);
            const iremboData = iremboResponse.data;

            if (iremboData.success && iremboData.data) {
              // Update the invoice status based on Irembo response
              const iremboInvoStatus = iremboData.data.paymentStatus;
              const currentInvoStatus = invoice.invoiceStatus.code;
              if (iremboInvoStatus === 'PAID') {
                // fund status by currentInvoStatus code
                const newStatus = await this.invoiceStatusRepository.findOne({
                  code: 'PAD',
                });
                // update invoice status to PAID
                const updateInvoice = await this.invoiceEntityManagerRepository
                  .createQueryBuilder()
                  .update(Invoice)
                  .set({ invoiceStatus: newStatus, paymentStatus: '1' })
                  .where('id = :id', { id: invoice.id })
                  .execute();

                if (!updateInvoice) {
                  throw new HttpException(
                    'Failed to update invoice status',
                    HttpStatus.INTERNAL_SERVER_ERROR,
                  );
                }
                // also update certificate status to 1 (approved) and invoice number
                if (
                  certificate &&
                  typeof certificate === 'object' &&
                  !Array.isArray(certificate) &&
                  'id' in certificate
                ) {
                  const updatecert = await this.certificateRepository
                    .createQueryBuilder()
                    .update(Certificate)
                    .set({
                      statusId: '1',
                      invoiceNumber: invoice.invoiceNumber,
                    })
                    .where('id = :id', { id: certificate.id })
                    .execute();

                  if (!updatecert) {
                    throw new HttpException(
                      'Failed to update certificate status',
                      HttpStatus.INTERNAL_SERVER_ERROR,
                    );
                  }
                }
              }
            } else {
              console.error(
                'Invalid response from Irembo API: iremboData.success or iremboData.data is false',
              );
            }
          } catch (error) {
            console.error(
              'Error fetching invoice status from Irembo API:',
              error?.response?.data || error.message || error,
            );
            throw new HttpException(
              'Failed to fetch invoice status from Irembo API',
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        }
        // if invice  has externalReferenceNumber eqal to NOT_AVAILABLE and is not EXMTD we new to
        // generate new invoice number from irembo and update our externalReferenceNumber
        if (
          (invoice.externalReferenceNumber === 'NOT_AVAILABLE' ||
            invoice.externalReferenceNumber === null ||
            invoice.externalReferenceNumber === '') &&
          invoice.invoiceStatus.code !== 'EXMTD'
        ) {
          const today: Date = new Date();
          const futureDate: Date = new Date();
          futureDate.setDate(today.getDate() + 30);
          const userDetails = await this.checkUser(invoice.applications.userId);
          // let create new invoice number
          const timestamp = Date.now().toString().slice(-5);
          const numericPart = Math.floor(Math.random() * 100000);
          const numericString = (numericPart + `-` + timestamp).padStart(
            10,
            '0',
          );
          const newInvoiceNumber = `INV-${invoice.applications.agencyCode}-${numericString}`;

          const externalApiData = {
            transactionId: newInvoiceNumber,
            code: 'E-PYMNT',
            unitAmount: invoice.amount,
            quantity: 1,
            paymentAccountIdentifier: invoice.paymentAccountIdentifier,
            language: 'EN',
            expiryAt: futureDate,
            description: userDetails.firstName,
            notification: userDetails.firstName,
            phoneNumber: invoice.phone,
            email: invoice.email,
            name: userDetails.firstName,
          };
          let externalReferenceNumberFromIrembo: string;

          try {
            const iremboResponse = await axios.post(
              `${config.payment.irembopayAPI}/payment/create/`,
              externalApiData,
            );
            console.log('Irembo Response Data:', iremboResponse.data);
            const responseData = iremboResponse.data;
            if (
              responseData.status &&
              responseData.data &&
              responseData.data.invoiceNumber
            ) {
              externalReferenceNumberFromIrembo =
                responseData.data.invoiceNumber;
              // Update the invoice with the new external reference number and status
              const newStatus = await this.invoiceStatusRepository.findOne({
                code: 'PND',
              });
              // update invoice status to PAID
              const updateInvoice = await this.invoiceEntityManagerRepository
                .createQueryBuilder()
                .update(Invoice)
                .set({
                  invoiceStatus: newStatus,
                  paymentStatus: '0',
                  externalReferenceNumber: externalReferenceNumberFromIrembo,
                  invoiceNumber: newInvoiceNumber,
                })
                .where('id = :id', { id: invoice.id })
                .execute();
              if (!updateInvoice) {
                throw new HttpException(
                  'Failed to update invoice status',
                  HttpStatus.INTERNAL_SERVER_ERROR,
                );
              }
              invoice = await this.invoiceEntityManagerRepository
                .createQueryBuilder('invoice')
                .where('invoice.id = :id', { id: invoice.id })
                .leftJoinAndSelect('invoice.applications', 'applications')
                .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
                .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
                .getOne();
            } else {
              throw new Error('Invoice number not found in the response');
            }
          } catch (error) {
            console.error('Error pushing data to irembo API:', error);
            externalReferenceNumberFromIrembo = 'NOT_AVAILABLE';
          }
        }
      }

      const userId = invoice.applications.userId;
      const userDetails = await this.checkUser(userId);
      const { password, ...userDetailsWithoutPassword } = userDetails;
      const invoicesWithUserDetails = {
        ...invoice,
        applications: {
          ...invoice.applications,
          userDetails: userDetailsWithoutPassword,
        },
      };

      return invoicesWithUserDetails;
    } else if (
      (!id || id.trim() === '' || id === 'undefined' || id === 'null') &&
      applicationId &&
      applicationId.trim() !== '' &&
      applicationId !== 'undefined' &&
      applicationId !== 'null'
    ) {
      // getting application by applicationId
      const application =
        await this.applicationService.findOneApplication(applicationId);

      if (!application) {
        throw new NotFoundException('Application not found');
      }
      // getting angecy details by agency code
      const agencyId = application.agencyId;
      // getting agency detail by agency id using agency code usermanagement using findOneAgency
      const agency = await firstValueFrom(
        this.authService.send<any>({ cmd: 'findOneAgency' }, agencyId),
      );

      if (!agency) {
        throw new NotFoundException('Agency not found');
      }
      // getting all users by agency id
      const users = await firstValueFrom(
        this.authService.send<any>({ cmd: 'findUserByAgency' }, agency.id),
      );
      if (!users || users.length === 0) {
        throw new NotFoundException('No users found for this agency');
      }
      let directorUser = users.find(
        (user) =>
          user.role?.code === 'DRCT' &&
          user.isActive === true &&
          user.isActing === false,
      );
      if (
        application.agencyCode === 'COK' &&
        application.permitTypes.code === 'FINS'
      ) {
        directorUser = users.find(
          (user) =>
            user.role?.code === 'SINSP' &&
            user.isActive === true &&
            user.isActing === false,
        );
      }
      if (!directorUser) {
        throw new NotFoundException(
          'Director user not found for this agency or permit type',
        );
      }
      // getting price depending on permit type
      const builduparea = application.buildUpArea;
      // console.log('builduparea:', builduparea);
      const amount = await this.getPriceByPermitTypeAndRange(
        application.permitTypes.id,
        builduparea,
      );
      let invoiceStatuscode = 'PND';
      if (amount > 0) {
        invoiceStatuscode = 'EXMTD';
      }
      // getting ivoice type by code
      const invoiceType = await this.invoiceTypeRepository.findOne({
        code: 'INVPER',
      });
      // getting invoiceItem by name

      const invoiceItem = await this.invoiceItemRepository.findOne({
        name: 'Construction Permit',
      });
      // invoice status by code
      const invoiceStatus = await this.invoiceStatusRepository.findOne({
        code: invoiceStatuscode,
      });
      console.log('invoiceTypegggdggddsgss:', invoiceStatus);

      // now i need to generate new invoice by colling createInvoice
      const invoiceDto: InvoiceDto = {
        applicationId: application.id,
        applicantUserId: application.userId,
        userId: directorUser.id,
        agencyCode: application.agencyCode,
        paymentAccountIdentifier: agency.paymentAccountIdentifier,
        amount: amount.toString(),
        transactionNumber: 'string',
        invoiceTypeId: invoiceType.id,
        invoiceItemId: invoiceItem.id,
        invoiceStatusId: invoiceStatus.id,
        invoiceNumber: '', // or generate if needed
        email: '', // or another relevant email
        phone: '', // or another relevant phone
        description: '', // or a relevant description
        // add any other required fields with default or real values
        externalReferenceNumber: 'NOT_AVAILABLE',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      };
      // create new invoice
      const newInvoice = await this.createInvoice(invoiceDto);
      if (!newInvoice || !newInvoice.data) {
        throw new NotFoundException('Failed to create invoice');
      }
      const invoiceID = newInvoice.data.id; // Return the newly created invoice data
      let invoice = await this.invoiceEntityManagerRepository
        .createQueryBuilder('invoice')
        .where('invoice.id = :id', { id: invoiceID })
        .leftJoinAndSelect('invoice.applications', 'applications')
        .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
        .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
        .getOne();
      // application is certified
      // if (application.applicationStatus.code !== 'CTFD') {
      //   //throuwing error saying application is not certified
      //   throw new BadRequestException('Application is not certified');
      //   // update application status to CERTIFIED
      // }
      const certificate1 =
        await this.certificateService.findCertificateByApplicationId(
          invoice.applications.id,
        );
      if (!certificate1) {
        // If no certificate is found, we need to create new one
        if (!invoice.applications) {
          throw new BadRequestException(
            'Application details not found for this invoice',
          );
        }
        if (!invoice.applications.permitTypes) {
          throw new BadRequestException(
            'Permit type not found for this application',
          );
        }

        await this.certificateService.createCertificate({
          applicationId: invoice.applications.id,
          userId: invoice.userId,
          applicantUserId: invoice.applications.userId,
          permitTypeId: invoice.applications.permitTypes.id,
          agencyCode: invoice.applications.agencyCode,
          title: 'string',
          lows: 'string',
          backgroundUrl: 'string',
          statusId: '0', // PENDING status
          certificateNumber: 'string',
          invoiceNumber: 'string',
          expiredDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        });
      }
      const certificate =
        await this.certificateService.findCertificateByApplicationId(
          invoice.applications.id,
        );
      if (!certificate) {
        throw new BadRequestException('Certificate not found for this invoice');
      }
      if (certificate) {
        if (
          invoice.invoiceStatus.code === 'EXMTD' ||
          invoice.invoiceStatus.code === 'PAID'
        ) {
          // Only update if certificate is a Certificate object (not an array)
          if (
            certificate &&
            typeof certificate === 'object' &&
            !Array.isArray(certificate) &&
            'id' in certificate
          ) {
            await this.certificateRepository
              .createQueryBuilder()
              .update(Certificate)
              .set({ statusId: '1', invoiceNumber: invoice.invoiceNumber })
              .where('id = :id', { id: certificate.id })
              .execute();
          }
        }

        if (
          invoice.invoiceStatus.code === 'PND' &&
          invoice.externalReferenceNumber &&
          invoice.externalReferenceNumber !== 'NOT_AVAILABLE'
        ) {
          // console.log("hdhdhdhdhd hdhhdhdh");
          try {
            const iremboResponse: AxiosResponse<IremboInvoiceResponse> =
              await firstValueFrom(
                this.httpService.get<IremboInvoiceResponse>(
                  `${config.payment.irembopayAPI}/payment/invoice/${invoice.externalReferenceNumber}`,
                ),
              );

            console.log('Irembo API response:', iremboResponse.data);
            const iremboData = iremboResponse.data;

            if (iremboData.success && iremboData.data) {
              // Update the invoice status based on Irembo response
              const iremboInvoStatus = iremboData.data.paymentStatus;
              const currentInvoStatus = invoice.invoiceStatus.code;
              if (iremboInvoStatus === 'PAID') {
                // fund status by currentInvoStatus code
                const newStatus = await this.invoiceStatusRepository.findOne({
                  code: 'PAD',
                });
                // update invoice status to PAID
                const updateInvoice = await this.invoiceEntityManagerRepository
                  .createQueryBuilder()
                  .update(Invoice)
                  .set({ invoiceStatus: newStatus, paymentStatus: '1' })
                  .where('id = :id', { id: invoice.id })
                  .execute();

                if (!updateInvoice) {
                  throw new HttpException(
                    'Failed to update invoice status',
                    HttpStatus.INTERNAL_SERVER_ERROR,
                  );
                }
                // also update certificate status to 1 (approved) and invoice number
                if (
                  certificate &&
                  typeof certificate === 'object' &&
                  !Array.isArray(certificate) &&
                  'id' in certificate
                ) {
                  const updatecert = await this.certificateRepository
                    .createQueryBuilder()
                    .update(Certificate)
                    .set({
                      statusId: '1',
                      invoiceNumber: invoice.invoiceNumber,
                    })
                    .where('id = :id', { id: certificate.id })
                    .execute();

                  if (!updatecert) {
                    throw new HttpException(
                      'Failed to update certificate status',
                      HttpStatus.INTERNAL_SERVER_ERROR,
                    );
                  }
                }
              }
            } else {
              console.error(
                'Invalid response from Irembo API: iremboData.success or iremboData.data is false',
              );
            }
          } catch (error) {
            console.error(
              'Error fetching invoice status from Irembo API:',
              error?.response?.data || error.message || error,
            );
            throw new HttpException(
              'Failed to fetch invoice status from Irembo API',
              HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        }
        // if invice  has externalReferenceNumber eqal to NOT_AVAILABLE and is not EXMTD we new to
        // generate new invoice number from irembo and update our externalReferenceNumber
        if (
          (invoice.externalReferenceNumber === 'NOT_AVAILABLE' ||
            invoice.externalReferenceNumber === null ||
            invoice.externalReferenceNumber === '') &&
          invoice.invoiceStatus.code !== 'EXMTD'
        ) {
          const today: Date = new Date();
          const futureDate: Date = new Date();
          futureDate.setDate(today.getDate() + 30);
          const userDetails = await this.checkUser(invoice.applications.userId);
          // let create new invoice number
          const timestamp = Date.now().toString().slice(-5);
          const numericPart = Math.floor(Math.random() * 100000);
          const numericString = (numericPart + `-` + timestamp).padStart(
            10,
            '0',
          );
          const newInvoiceNumber = `INV-${invoice.applications.agencyCode}-${numericString}`;

          const externalApiData = {
            transactionId: newInvoiceNumber,
            code: 'E-PYMNT',
            unitAmount: invoice.amount,
            quantity: 1,
            paymentAccountIdentifier: invoice.paymentAccountIdentifier,
            language: 'EN',
            expiryAt: futureDate,
            description: userDetails.firstName,
            notification: userDetails.firstName,
            phoneNumber: invoice.phone,
            email: invoice.email,
            name: userDetails.firstName,
          };
          let externalReferenceNumberFromIrembo: string;

          try {
            const iremboResponse = await axios.post(
              `${config.payment.irembopayAPI}/payment/create/`,
              externalApiData,
            );
            console.log('Irembo Response Data:', iremboResponse.data);
            const responseData = iremboResponse.data;
            if (
              responseData.status &&
              responseData.data &&
              responseData.data.invoiceNumber
            ) {
              externalReferenceNumberFromIrembo =
                responseData.data.invoiceNumber;
              // Update the invoice with the new external reference number and status
              const newStatus = await this.invoiceStatusRepository.findOne({
                code: 'PND',
              });
              // update invoice status to PAID
              const updateInvoice = await this.invoiceEntityManagerRepository
                .createQueryBuilder()
                .update(Invoice)
                .set({
                  invoiceStatus: newStatus,
                  paymentStatus: '0',
                  externalReferenceNumber: externalReferenceNumberFromIrembo,
                  invoiceNumber: newInvoiceNumber,
                })
                .where('id = :id', { id: invoice.id })
                .execute();
              if (!updateInvoice) {
                throw new HttpException(
                  'Failed to update invoice status',
                  HttpStatus.INTERNAL_SERVER_ERROR,
                );
              }
              invoice = await this.invoiceEntityManagerRepository
                .createQueryBuilder('invoice')
                .where('invoice.id = :id', { id: invoice.id })
                .leftJoinAndSelect('invoice.applications', 'applications')
                .leftJoinAndSelect('applications.permitTypes', 'permitTypes')
                .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
                .getOne();
            } else {
              throw new Error('Invoice number not found in the response');
            }
          } catch (error) {
            console.error('Error pushing data to irembo API:', error);
            externalReferenceNumberFromIrembo = 'NOT_AVAILABLE';
          }
        }
      }
      const userId = invoice.applications.userId;
      const userDetails = await this.checkUser(userId);
      const { password, ...userDetailsWithoutPassword } = userDetails;

      const invoicesWithUserDetails = {
        ...invoice,
        applications: {
          ...invoice.applications,
          userDetails: userDetailsWithoutPassword,
        },
      };

      return invoicesWithUserDetails;
    } else {
      throw new BadRequestException('Invalid invoice ID or application ID');
    }
  }

  async findAllInvoiceOfApplication(applicationId: string) {
    const invoice = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.applications', 'applications')
      .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
      .where('applications.id = :applicationId', { applicationId })
      .getMany();

    return invoice;
  }

  async findInvoiceWithZeroAmount(applicationId: string): Promise<boolean> {
    const invoices = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.applications = :applicationId', { applicationId })
      .andWhere('invoice.amount = :amount', { amount: '0' })
      .leftJoinAndSelect('invoice.applications', 'applications')
      .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
      .getMany();

    return invoices.length > 0;
  }

  async findAllInvoiceWithZeroAmount(applicationId: string) {
    const invoices = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.applications = :applicationId', { applicationId })
      .andWhere('invoice.amount = :amount', { amount: '0' })
      .leftJoinAndSelect('invoice.applications', 'applications')
      .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
      .getMany();

    return invoices;
  }

  async findAllInvoiceWithInvoiceStatusOfAnUser(
    applicantUserId: string,
    invoiceStatusId: string,
  ): Promise<{ number: number }> {
    const count = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.applicantUserId = :applicantUserId', { applicantUserId })
      .andWhere('invoice.invoiceStatusId = :invoiceStatusId', {
        invoiceStatusId,
      })
      .getCount();

    return { number: count };
  }

  // invoice by agency
  // async findAllInvoiceByAgency(agencyCode: string) {
  //   const invoice = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder('invoice')
  //     .where('invoice.agencyCode = :agencyCode', { agencyCode })
  //     .leftJoinAndSelect('invoice.applications', 'applications')
  //     .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
  //     .leftJoinAndSelect('invoice.invoiceItems', 'invoiceItems')
  //     .getMany();
  //   const userId = invoice[0].applications.userId;
  //   const userDetails = await this.checkUser(userId);
  //   // eslint-disable-next-line @typescript-eslint/no-unused-vars
  //   const { password, ...userDetailsWithoutPassword } = userDetails;
  //   const invoicesWithUserDetails = invoice.map((inv) => ({
  //     ...inv,
  //     applications: {
  //       ...inv.applications,
  //       userDetails: userDetailsWithoutPassword,
  //     },
  //   }));

  //   return invoicesWithUserDetails;
  // }
  async findAllInvoiceByAgency(agencyCode: string) {
    const invoices = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.agencyCode = :agencyCode', { agencyCode })
      .leftJoinAndSelect('invoice.applications', 'applications')
      .leftJoinAndSelect('invoice.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('invoice.invoiceItems', 'invoiceItems')
      .getMany();

    if (!invoices.length) {
      throw new NotFoundException(
        'No invoices found for the given agency code',
      );
    }

    const userId = invoices[0].applications.userId;
    const userDetails = await this.checkUser(userId);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...userDetailsWithoutPassword } = userDetails;
    const invoicesWithUserDetails = invoices.map((inv) => ({
      ...inv,
      applications: {
        ...inv.applications,
        userDetails: userDetailsWithoutPassword,
      },
    }));

    return invoicesWithUserDetails;
  }

  // async updateInvoice(id: string, invoiceDto: InvoiceDto) {
  //   return this.invoiceRepository.findOneAndUpdate({ id }, invoiceDto);
  // }

  async updateInvoice(id: string, invoiceDto: InvoiceDto) {
    // 1. Get invoice status
    const invoiceStatusData = await this.invoiceStatusRepository.findOne({
      id: invoiceDto.invoiceStatusId,
    });

    if (!invoiceStatusData) {
      throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
    }

    // 2. Update invoice in DB
    const invoiceUpdate = await this.invoiceEntityManagerRepository
      .createQueryBuilder()
      .update(Invoice)
      .set({
        invoiceStatus: invoiceStatusData,
        userId: invoiceDto.userId,
        paymentAccountIdentifier: invoiceDto.paymentAccountIdentifier,
        amount: invoiceDto.amount,
        transactionNumber: invoiceDto.transactionNumber,
        externalReferenceNumber: invoiceDto.externalReferenceNumber,
      })
      .where('id = :id', { id })
      .returning('*')
      .execute();

    const updatedInvoice = invoiceUpdate.raw?.[0];
    if (!updatedInvoice) {
      throw new HttpException(
        'Failed to update invoice',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // 3. Compute expiration time: now + 30 days
    const expirationTimestamp = Date.now() + 30 * 24 * 60 * 60 * 1000; // 30 days in ms
    const expirationTime = new Date(expirationTimestamp).toISOString();

    // 4. Prepare payload
    const iremboPayload = {
      invoiceId: invoiceDto.invoiceNumber,
      amount: invoiceDto.amount,
      expirationTime,
    };

    // 5. Send to Irembo API
    try {
      const iremboResponse = await axios.post(
        `${config.payment.irembopayAPI}/payment/update/invoice/`,
        iremboPayload,
      );

      console.log('Irembo API response:', iremboResponse.data);
    } catch (error) {
      console.error(
        'Failed to notify Irembo:',
        error?.response?.data || error.message,
      );
      // Optional: throw error or continue silently
    }

    return updatedInvoice;
  }

  async updateInvoiceStatusOnInvoice(
    id: string,
    updateInvoiceDto: UpdateInvoiceDto,
  ) {
    // get invoice status
    const invoiceStatusData = await this.invoiceStatusRepository.findOne({
      id: updateInvoiceDto.invoiceStatusId,
    });
    if (!invoiceStatusData)
      throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
    console.log(invoiceStatusData);

    // Update invoice status
    const invoiceUpdate = await this.invoiceEntityManagerRepository
      .createQueryBuilder()
      .update(Invoice)
      .set({ invoiceStatus: invoiceStatusData })
      .where('id = :id', { id: id })
      .returning('*')
      .execute();

    // console.log(invoiceUpdate);
    return invoiceUpdate;
  }

  async removeInvoice(id: string) {
    return this.invoiceRepository.findOneAndDelete({ id });
  }

  async updatePaymentStatusOfInvoice(invoiceId: string) {
    const invoice = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.id = :invoiceId', { invoiceId })
      .getOne();

    // If the invoice is not found, throw an error
    if (!invoice) {
      throw new HttpException(
        'Invoice not found for the given ID',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update the status of the invoice
    const result = await this.invoiceEntityManagerRepository
      .createQueryBuilder()
      .update(invoice)
      .set({ paymentStatus: '1' })
      .where('id = :invoiceId', { invoiceId })
      .execute();

    if (result.affected === 0) {
      throw new HttpException(
        'Failed to update the invoice status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      status: HttpStatus.OK,
      message: 'invoice status updated successfully',
    };
  }

  async cancelInvoice(invoiceId: string) {
    // get invoice status
    const invoiceCode = 'CCD';
    const invoiceStatusData = await this.invoiceStatusRepository.findOne({
      code: invoiceCode,
    });
    if (!invoiceStatusData)
      throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
    console.log(invoiceStatusData);
    // Fetch the invoice using the invoice ID
    const invoice = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.id = :invoiceId', { invoiceId })
      .getOne();

    // If the invoice is not found, throw an error
    if (!invoice) {
      throw new HttpException(
        'Invoice not found for the given ID',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update the status of the invoice
    const result = await this.invoiceEntityManagerRepository
      .createQueryBuilder()
      .update(invoice)
      .set({ invoiceStatus: invoiceStatusData })
      .where('id = :invoiceId', { invoiceId })
      .execute();

    if (result.affected === 0) {
      throw new HttpException(
        'Failed to update the invoice status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      status: HttpStatus.OK,
      message: 'invoice status updated successfully',
    };
  }

  async refundedInvoice(invoiceId: string) {
    // get invoice status
    const invoiceCode = 'RFND';
    const invoiceStatusData = await this.invoiceStatusRepository.findOne({
      code: invoiceCode,
    });
    if (!invoiceStatusData)
      throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
    console.log(invoiceStatusData);
    // Fetch the invoice using the invoice ID
    const invoice = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.id = :invoiceId', { invoiceId })
      .getOne();

    // If the invoice is not found, throw an error
    if (!invoice) {
      throw new HttpException(
        'Invoice not found for the given ID',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update the status of the invoice
    const result = await this.invoiceEntityManagerRepository
      .createQueryBuilder()
      .update(invoice)
      .set({ invoiceStatus: invoiceStatusData, paymentStatus: '2' })
      .where('id = :invoiceId', { invoiceId })
      .execute();

    if (result.affected === 0) {
      throw new HttpException(
        'Failed to update the invoice status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      status: HttpStatus.OK,
      message: 'invoice status updated successfully',
    };
  }
  // async getInvoicesByApplicantUserId(
  //   applicantUserId: string,
  // ): Promise<Invoice[]> {
  //   return this.invoiceEntityManagerRepository
  //     .createQueryBuilder('invoice')
  //     .where('invoice.applicantUserId = :applicantUserId', { applicantUserId })
  //     .getMany();
  // }

  async getInvoicesByApplicantUserId(applicantUserId: string) {
    console.log(applicantUserId);
    try {
      const invoices = await this.invoiceEntityManagerRepository
        .createQueryBuilder('invoice')
        .where(
          'invoice.applicantUserId = :applicantUserId OR invoice.applicantUserId IS NULL',
          { applicantUserId },
        )
        .getMany();

      if (invoices.length === 0) {
        throw new Error('No invoices found for the specified applicantUserId.');
      }

      return invoices;
    } catch (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }
  }

  // Receipt
  // async createReceipt(receiptDto: ReceiptDto) {
  //   const receipt = new Receipt({
  //     ...receiptDto,
  //   });
  //   return this.receiptRepository.create(receipt);
  // }

  // async createReceipt(receiptDto: ReceiptDto) {
  //   const receipt = new Receipt({
  //     ...receiptDto,
  //   });

  //   const transactionId = receiptDto.transactionId;

  //   const invoiceData = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder('invoice')
  //     .where('invoice.invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: transactionId,
  //     })
  //     .leftJoinAndSelect('invoice.applications', 'applications')
  //     .getOne();

  //   if (!invoiceData) {
  //     throw new Error(`Invoice with transaction ID ${transactionId} not found`);
  //   }

  //   const invoiceStatusData = await this.invoiceStatusEntityManagerRepository
  //     .createQueryBuilder('invoiceStatus')
  //     .where('code = :code', {
  //       code: 'PAD',
  //     })
  //     .getOne();

  //   if (!invoiceStatusData) {
  //     throw new Error('Invoice status with code PAD not found');
  //   }

  //   console.log(transactionId);
  //   console.log(invoiceData.applicantUserId);
  //   console.log(invoiceData);
  //   console.log(invoiceStatusData);

  //   // Update invoice its status entity
  //   const invoiceUpdate = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Invoice)
  //     .set({ invoiceStatus: invoiceStatusData })
  //     .where('invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: transactionId,
  //     })
  //     .execute();
  //   console.log(invoiceUpdate);

  //   // Update certificate status
  //   await this.certificateService.UpdateCertificateStatus(
  //     invoiceData.applicantUserId,
  //   );

  //   // Create receipt after updating certificate status
  //   return this.receiptRepository.create(receipt);
  // }

  // before payment review
  // async createReceipt(receiptDto: ReceiptDto) {
  //   const receipt = new Receipt({
  //     ...receiptDto,
  //   });

  //   const transactionId = receiptDto.transactionId;

  //   const invoiceData = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder('invoice')
  //     .where('invoice.invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: transactionId,
  //     })
  //     .leftJoinAndSelect('invoice.applications', 'applications')
  //     .getOne();

  //   if (!invoiceData) {
  //     throw new Error(`Invoice with transaction ID ${transactionId} not found`);
  //   }

  //   const invoiceStatusData = await this.invoiceStatusEntityManagerRepository
  //     .createQueryBuilder('invoiceStatus')
  //     .where('code = :code', {
  //       code: 'PAD',
  //     })
  //     .getOne();

  //   if (!invoiceStatusData) {
  //     throw new Error('Invoice status with code PAD not found');
  //   }

  //   // Update certificate status
  //   await this.certificateService.UpdateCertificateStatus(
  //     invoiceData.applicantUserId,
  //   );

  //   console.log(transactionId);
  //   console.log(invoiceData.applicantUserId);
  //   console.log(invoiceData);
  //   console.log(invoiceStatusData);

  //   // Update invoice status
  //   const invoiceUpdate = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Invoice)
  //     .set({ invoiceStatus: invoiceStatusData })
  //     .where('invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: transactionId,
  //     })
  //     .execute();
  //   console.log(invoiceUpdate);

  //   console.log(invoiceData.applicantUserId);

  //   return this.receiptRepository.create(receipt);
  // }

  async createReceipt(receiptDto: ReceiptDto) {
    const receipt = new Receipt({
      ...receiptDto,
    });

    const transactionId = receiptDto.transactionId;

    // Fetch the invoice data
    const invoiceData = await this.invoiceEntityManagerRepository
      .createQueryBuilder('invoice')
      .where('invoice.invoiceNumber = :invoiceNumber', {
        invoiceNumber: transactionId,
      })
      .leftJoinAndSelect('invoice.applications', 'applications')
      .getOne();

    if (!invoiceData) {
      throw new Error(`Invoice with transaction ID ${transactionId} not found`);
    }

    //

    // Fetch the invoice status with code 'PAD'
    const invoiceStatusData = await this.invoiceStatusEntityManagerRepository
      .createQueryBuilder('invoiceStatus')
      .where('code = :code', {
        code: 'PAD',
      })
      .getOne();

    if (!invoiceStatusData) {
      throw new Error('Invoice status with code PAD not found');
    }

    // Update the certificate status
    await this.certificateService.UpdateCertificateStatus(
      invoiceData.applicantUserId,
      invoiceData.invoiceNumber,
    );

    console.log('Transaction ID:', transactionId);
    console.log('Applicant User ID:', invoiceData.applicantUserId);
    console.log('Invoice Data:', invoiceData);
    console.log('Invoice Status Data:', invoiceStatusData);

    // Set the new status in the invoiceData
    invoiceData.invoiceStatus = invoiceStatusData;

    // Save the updated invoice entity
    const invoiceUpdate =
      await this.invoiceEntityManagerRepository.save(invoiceData);

    console.log('Updated Invoice:', invoiceUpdate);

    // update the paymentStatus
    this.updatePaymentStatusOfInvoice(invoiceData.id);

    // Return the created receipt after invoice update
    return this.receiptRepository.create(receipt);
  }

  // async createReceipt(receiptDto: ReceiptDto) {
  //   const receipt = new Receipt({
  //     ...receiptDto,
  //   });

  //   const transactionId = receiptDto.transactionId;

  //   const invoiceData = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder('invoice')
  //     .where('invoice.invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: receiptDto.transactionId,
  //     })
  //     .leftJoinAndSelect('invoice.applications', 'applications')
  //     .getOne();

  //   const invoiceStatusData = await this.invoiceStatusEntityManagerRepository
  //     .createQueryBuilder('invoiceStatus')
  //     .where('code = :code', {
  //       code: 'PAD',
  //     })
  //     .getOne();

  //   console.log(transactionId);
  //   console.log(invoiceData.applicantUserId);
  //   console.log(invoiceData);
  //   console.log(invoiceStatusData);

  //   // Update invoice its status entity
  //   const invoiceUpdate = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Invoice)
  //     .set({ invoiceStatus: invoiceStatusData })
  //     .where('invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: receiptDto.transactionId,
  //     })
  //     .execute();
  //   console.log(invoiceUpdate);

  //   // update certificate status
  //   this.certificateService.UpdateCertificateStatus(
  //     invoiceData.applicantUserId,
  //   );

  //   return this.receiptRepository.create(receipt);
  // }

  // async createReceipt(receiptDto: ReceiptDto) {
  //   const receipt = new Receipt({
  //     ...receiptDto,
  //   });

  //   const invoiceStatusData = await this.invoiceStatusEntityManagerRepository
  //     .createQueryBuilder('invoiceStatus')
  //     .where('code = :code', {
  //       code: 'PAD',
  //     })
  //     .getOne();

  //   console.log(invoiceStatusData);

  //   // Update invoice its status entity
  //   const invoiceUpdate = await this.invoiceEntityManagerRepository
  //     .createQueryBuilder()
  //     .update(Invoice)
  //     .set({ invoiceStatus: invoiceStatusData })
  //     .where('invoiceNumber = :invoiceNumber', {
  //       invoiceNumber: receiptDto.transactionId,
  //     })
  //     .execute();
  //   console.log(invoiceUpdate);
  //   return this.receiptRepository.create(receipt);
  // }

  async findAllReceipt() {
    return this.receiptRepository.find({});
  }

  async findOneReceipt(id: string) {
    return this.receiptRepository.findOne({ id });
  }

  async updateReceipt(id: string, receiptDto: ReceiptDto) {
    return this.receiptRepository.findOneAndUpdate({ id }, receiptDto);
  }

  async removeReceipt(id: string) {
    return this.receiptRepository.findOneAndDelete({ id });
  }

  // // updating invoice on Irembo
  // async updateInvoiceToIrembo(
  //   invoiceId: string,
  //   amount: number,
  //   expirationTime: string,
  // ): Promise<any> {
  //   const externalApiData = {
  //     invoiceId,
  //     amount,
  //     expirationTime,
  //   };

  //   try {
  //     const iremboResponse = await axios.post(
  //       `${config.payment.iremboDev}/payment/update/invoice/`,
  //       externalApiData,
  //     );

  //     console.log('Irembo Response Data:', iremboResponse.data);

  //     const responseData = iremboResponse.data;

  //     // Check if the update was successful
  //     if (
  //       responseData.success &&
  //       responseData.data &&
  //       responseData.data.invoiceNumber
  //     ) {
  //       return {
  //         message: responseData.message,
  //         data: responseData.data,
  //       };
  //     } else {
  //       throw new Error('Invoice update failed');
  //     }
  //   } catch (error) {
  //     console.error('Error pushing data to Irembo API:', error);
  //     throw new BadRequestException('Failed to update the invoice.');
  //     // throw new BadRequestException('Failed to update the invoice.');
  //   }
  // }
  async updateInvoiceToIrembo(
    invoiceId: string,
    amount: number,
    expirationTime: string,
  ): Promise<any> {
    const externalApiData = {
      invoiceId,
      amount,
      expirationTime,
    };

    try {
      const iremboResponse = await axios.post(
        // `${config.payment.iremboProd}/payment/update/invoice/`,
        `${config.payment.irembopayAPI}/payment/update/invoice/`,
        externalApiData,
      );

      console.log('Irembo Response Data:', iremboResponse.data);

      const responseData = iremboResponse.data;

      // Check if the update was successful
      if (
        responseData.success &&
        responseData.data &&
        responseData.data.invoiceNumber
      ) {
        return {
          message: responseData.message,
          data: responseData.data,
        };
      } else {
        // Throw an error with the message from the response if success is false
        throw new Error(responseData.message || 'Invoice update failed');
      }
    } catch (error) {
      console.error('Error pushing data to Irembo API:', error.message);

      // Handle the case where the external Irembo provided a specific error message
      if (error.message) {
        throw new BadRequestException(error.message);
      }

      // Default error message for other exceptions
      throw new BadRequestException('Failed to update the invoice.');
    }
  }

  // get price of the invoice base om the builduparea and permit type
  async getPriceByPermitTypeAndRange(
    permitTypeId: string,
    rangeNumber: number,
  ): Promise<number> {
    const price = await this.priceEntityManagerRepository
      .createQueryBuilder('price')
      .innerJoinAndSelect('price.permitTypes', 'permitType')
      .where('permitType.id = :permitTypeId', { permitTypeId })
      .andWhere(
        'CAST(price.rangeInSqmMin AS FLOAT) <= :rangeNumber AND CAST(price.rangeInSqmMax AS FLOAT) >= :rangeNumber',
        { rangeNumber },
      )
      .select('price.amount', 'amount')
      .getRawOne();

    if (!price) {
      throw new NotFoundException('Price not found for the given parameters');
    }

    return price ? price.amount : 0;
  }
}
