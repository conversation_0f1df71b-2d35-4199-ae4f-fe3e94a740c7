import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
// import { Expose, Type } from 'class-transformer';
import { IsOptional, IsNotEmpty, IsDefined } from 'class-validator';
// import { IsOptional, IsNotEmpty, ValidateNested } from 'class-validator';
// import {
//   BuildTypeDto,
//   CategoryTypeDto,
//   PermitTypeDto,
//   TechnologySurveyDto,
// } from './settings.dto';

export class ProjectDto {
  @IsOptional()
  id: string;

  @IsOptional()
  @ApiProperty()
  isAssociatedUpi: boolean;

  @ApiProperty({ description: 'The field upi is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  upi: string;

  @ApiProperty({ description: 'The field ownerFullName is required' })
  @IsOptional({ message: 'The field ownerFullName cannot be empty' })
  ownerFullName: string;

  @ApiProperty({ description: 'The field ownerIdNo is required' })
  @IsOptional({ message: 'The field ownerIdNo cannot be empty' })
  ownerIdNo: string;

  @ApiProperty()
  @IsOptional()
  isFromOldSystem: boolean;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemDevelopersName: string;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemPermitNumber: string;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemInvoiceNumber: string;

  @ApiProperty()
  @IsOptional()
  isRRAVerified: boolean;

  @ApiProperty()
  @IsOptional()
  isUnderMortgage: boolean;

  @ApiProperty()
  @IsOptional()
  isUnderRestriction: boolean;

  @ApiProperty()
  @IsOptional()
  centralCoordinateX: string;

  @ApiProperty()
  @IsOptional()
  centralCoordinateY: string;

  @ApiProperty()
  @IsOptional()
  villageCode: string;

  @ApiProperty()
  @IsOptional()
  villageName: string;

  @ApiProperty()
  @IsOptional()
  cellCode: string;

  @ApiProperty()
  @IsOptional()
  cellName: string;

  @ApiProperty()
  @IsOptional()
  sectorCode: string;

  @ApiProperty()
  @IsOptional()
  sectorName: string;

  @ApiProperty()
  @IsOptional()
  districtCode: string;

  @ApiProperty()
  @IsOptional()
  districtName: string;

  @ApiProperty()
  @IsOptional()
  provinceCode: string;

  @ApiProperty()
  @IsOptional()
  provinceName: string;

  @IsNotEmpty({ message: 'The field selectedUse cannot be empty' })
  @ApiProperty({ description: 'The field selectedUse id is required' })
  selectedUse: string;

  @IsNotEmpty({ message: 'The field selectedCategoryUse cannot be empty' })
  @ApiProperty({ description: 'The field selectedCategoryUse id is required' })
  selectedCategoryUse: string;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsNotEmpty({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsNotEmpty({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field plotSize is required' })
  @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
  plotSize: number;

  @ApiProperty({ description: 'The field originalPlotSize is required' })
  @IsNotEmpty({ message: 'The field originalPlotSize cannot be empty' })
  originalPlotSize: number;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field user id is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field agency id is required' })
  agencyId: string;

  @IsOptional()
  @ApiProperty({ description: 'The field technologySurvey id is required' })
  projectStatusId: string;
}

export class ProjectDtoGet {
  @ApiProperty({ description: 'The field upi is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  upi: string;

  @IsOptional()
  @ApiProperty()
  isAssociatedUpi: boolean;

  @ApiProperty({ description: 'The field ownerFullName is required' })
  @IsOptional({ message: 'The field ownerFullName cannot be empty' })
  ownerFullName: string;

  @ApiProperty()
  @IsOptional()
  isFromOldSystem: boolean;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemDevelopersName: string;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemInvoiceNumber: string;

  @ApiProperty()
  @IsOptional()
  isRRAVerified: boolean;

  @ApiProperty()
  @IsOptional()
  isFromOldSystemPermitNumber: string;

  @ApiProperty()
  @IsOptional()
  isUnderMortgage: boolean;

  @ApiProperty({ description: 'The field ownerIdNo is required' })
  @IsOptional({ message: 'The field ownerIdNo cannot be empty' })
  ownerIdNo: string;

  @ApiProperty()
  @IsOptional()
  isUnderRestriction: boolean;

  @ApiProperty()
  @IsOptional()
  centralCoordinateX: string;

  @ApiProperty()
  @IsOptional()
  centralCoordinateY: string;

  @ApiProperty()
  @IsOptional()
  villageCode: string;

  @ApiProperty()
  @IsOptional()
  villageName: string;

  @ApiProperty()
  @IsOptional()
  cellCode: string;

  @ApiProperty()
  @IsOptional()
  cellName: string;

  @ApiProperty()
  @IsOptional()
  sectorCode: string;

  @ApiProperty()
  @IsOptional()
  sectorName: string;

  @ApiProperty()
  @IsOptional()
  districtCode: string;

  @ApiProperty()
  @IsOptional()
  districtName: string;

  @ApiProperty()
  @IsOptional()
  provinceCode: string;

  @ApiProperty()
  @IsOptional()
  provinceName: string;

  @ApiProperty({ description: 'The field projectName is required' })
  @IsNotEmpty({ message: 'The field projectName cannot be empty' })
  projectName: string;

  @ApiProperty({ description: 'The field projectDescription is required' })
  @IsNotEmpty({ message: 'The field projectDescription cannot be empty' })
  projectDescription: string;

  @ApiProperty({ description: 'The field plotSize is required' })
  @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
  plotSize: number;

  @ApiProperty({ description: 'The field originalPlotSize is required' })
  @IsNotEmpty({ message: 'The field originalPlotSize cannot be empty' })
  originalPlotSize: number;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field user id is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field selectedUse cannot be empty' })
  @ApiProperty({ description: 'The field selectedUse id is required' })
  selectedUse: string;

  @IsNotEmpty({ message: 'The field selectedCategoryUse cannot be empty' })
  @ApiProperty({ description: 'The field selectedCategoryUse id is required' })
  selectedCategoryUse: string;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field agency id is required' })
  agencyId: string;
}

// export class ApplicationDto {
//   //project estimate

//   @IsOptional()
//   @ApiProperty()
//   waterConsumption: number;

//   @IsOptional()
//   @ApiProperty()
//   electricityConsumption: number;

//   @IsOptional()
//   @ApiProperty()
//   DistanceToTheNearestLandIn: number;

//   @IsOptional()
//   @ApiProperty()
//   ProjectCostInUSD: number;

//   @IsOptional()
//   @ApiProperty()
//   ProjectCostInRwf: number;

//   @IsOptional()
//   @ApiProperty()
//   buildUpArea: number;

//   @IsOptional()
//   @ApiProperty()
//   numberOfFloor: number;

//   @IsOptional()
//   @ApiProperty()
//   grossFloorArea: number;

//   @IsOptional()
//   @ApiProperty()
//   numberOfParkingSpace: number;

//   @IsOptional()
//   @ApiProperty()
//   priceOfDwellingUnitRwf: number;

//   @IsOptional()
//   @ApiProperty()
//   capacityInformation: number;

//   @IsOptional()
//   @ApiProperty()
//   numberOfDwellingUnits: string;

//   @IsOptional()
//   @ApiProperty()
//   DescriptionOfOperation: string;

//   @IsOptional()
//   @ApiProperty()
//   percentageSpaceUse: string;

//   @IsOptional()
//   id: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field user id is required' })
//   userId: string;

//   @IsOptional()
//   @ApiProperty()
//   certificateNumberEIA: string;

//   @IsNotEmpty({ message: 'The field project cannot be empty' })
//   @ApiProperty({ description: 'The field project id is required' })
//   projectId: string;

//   @IsNotEmpty({ message: 'The field permitType cannot be empty' })
//   @ApiProperty({ description: 'The field permitType id is required' })
//   permitTypeId: string;

//   @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
//   @ApiProperty({ description: 'The field categoryTypeId id is required' })
//   categoryTypeId: string;

//   @IsNotEmpty({ message: 'The field buildType cannot be empty' })
//   @ApiProperty({ description: 'The field buildType id is required' })
//   buildTypeId: string;

//   @IsNotEmpty({ message: 'The field technologySurvey cannot be empty' })
//   @ApiProperty({ description: 'The field technologySurvey id is required' })
//   technologySurveyId: string;

//   @IsNotEmpty({ message: 'The field agency cannot be empty' })
//   @ApiProperty({ description: 'The field agency id is required' })
//   agencyId: string;

//   @IsNotEmpty({ message: 'The field applicationStatus cannot be empty' })
//   @ApiProperty({ description: 'The field applicationStatus id is required' })
//   applicationStatusId: string;

//   @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
//   @ApiProperty({ description: 'The field categoryType id is required' })
//   permitTypeCode: string;

//   @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
//   @ApiProperty({ description: 'The field agencyCode id is required' })
//   agencyCode: string;

//   // @IsNotEmpty({ message: 'The field user cannot be empty' })
//   // @ApiProperty({ description: 'The field user id is required'})
//   // userId: string;

//   @IsOptional()
//   applicationName: string;
// }

export class OtherInfoApplicationDto {
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;

  @IsNotEmpty({ message: 'The field licenseNumber cannot be empty' })
  @ApiProperty({ description: 'The field licenseNumber is required' })
  licenseNumber: string;

  @IsNotEmpty({ message: 'The field permitType cannot be empty' })
  @ApiProperty({ description: 'The field permitType id is required' })
  permitTypeId: string;

  @IsOptional()
  @ApiProperty()
  applicationId: string;

  @IsOptional()
  @ApiProperty()
  doYouHaveTheOccupancy: string;

  @IsOptional()
  @ApiProperty()
  isFastAidBox: string;

  @IsOptional()
  @ApiProperty()
  disabilityToiletsFlipUpGrabBars: string;

  @IsOptional()
  @ApiProperty()
  paraLighteningSystem: string;

  @IsOptional()
  @ApiProperty()
  equipmentCapacity: string;

  @IsOptional()
  @ApiProperty()
  constructionMethod: string;

  @IsOptional()
  @ApiProperty()
  fireAlarmSystemWithAnAlarmBellOnEach: string;

  @IsOptional()
  @ApiProperty()
  whyNotFireAlarmSystemWithAnAlarmBellOnEach: string;

  @IsOptional()
  @ApiProperty()
  fireExtinguishersEvery50mOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  whyNotFireExtinguishersEvery50mOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  functioningExitSignsOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  whyNotfunctioningExitSignsOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  anEmergencyExitOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  whyNotanEmergencyExitOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  floorPlanOnEachLevel: string;

  @IsOptional()
  @ApiProperty()
  whyNotfloorPlanOnEachLevel: string;

  @IsOptional()
  @ApiProperty()
  numberSignOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  whyNotnumberSignOnEachFloor: string;

  @IsOptional()
  @ApiProperty()
  signForbiddingTheUseOfElevatorsInCaseOfFire: string;

  @IsOptional()
  @ApiProperty()
  whyNotsignForbiddingTheUseOfElevatorsInCaseOfFire: string;

  @IsOptional()
  @ApiProperty()
  landingSpaceOnTopOfTheBuildingForHelicopters: string;

  @IsOptional()
  @ApiProperty()
  whyNotlandingSpaceOnTopOfTheBuildingForHelicopters: string;

  @IsOptional()
  @ApiProperty()
  CCTVCameras: string;

  @IsOptional()
  @ApiProperty()
  whyNotCCTVCameras: string;

  @IsOptional()
  @ApiProperty()
  WalkThroughAndHeldMetalDetect: string;

  @IsOptional()
  @ApiProperty()
  whyNotWalkThroughAndHeldMetalDetect: string;

  @IsOptional()
  @ApiProperty()
  UnderSearchMirror: string;

  @IsOptional()
  @ApiProperty()
  whyNotUnderSearchMirror: string;

  @IsOptional()
  @ApiProperty()
  LuggageScanners: string;

  @IsOptional()
  @ApiProperty()
  whyNotLuggageScanners: string;

  @IsOptional()
  @ApiProperty()
  PlatesIndicatingEmergencyResponseUnitsPhoneNumbers: string;

  @IsOptional()
  @ApiProperty()
  whyNotPlatesIndicatingEmergencyResponseUnitsPhoneNumbers: string;

  @IsOptional()
  @ApiProperty()
  EmergencyEvacuationPlan: string;

  @IsOptional()
  @ApiProperty()
  whyNotEmergencyEvacuationPlan: string;

  @IsOptional()
  @ApiProperty()
  SecurityManagerAndStaffCameras: string;

  @IsOptional()
  @ApiProperty()
  whyNotSecurityManagerAndStaffCameras: string;

  @IsOptional()
  @ApiProperty()
  AnInternalCommunicationSystem: string;

  @IsOptional()
  @ApiProperty()
  whyNotAnInternalCommunicationSystem: string;

  @IsOptional()
  @ApiProperty()
  BroadBandInternetServices: string;

  @IsOptional()
  @ApiProperty()
  whyNotBroadBandInternetServices: string;

  @IsOptional()
  @ApiProperty()
  StaffAndVisitorAccessCards: string;

  @IsOptional()
  @ApiProperty()
  whyNotStaffAndVisitorAccessCards: string;

  @IsOptional()
  @ApiProperty()
  applicationForFixedTelephoneLineConnection: string;

  @IsOptional()
  @ApiProperty()
  areThereAnyFacilitiesForTheDisabledProvidedBuilding: string;

  @IsOptional()
  @ApiProperty()
  whyNotAreThereAnyFacilitiesForTheDisabledProvidedBuilding: string;

  @IsOptional()
  @ApiProperty()
  stageOfConstruction: string;

  @IsOptional()
  @ApiProperty()
  supervisingFirmSiteEngineer: string;

  @IsOptional()
  @ApiProperty()
  remarks: string;

  @IsOptional()
  @ApiProperty()
  dateForRequestedInspection: string;
}
export class ApplicationDto {
  //project estimate

  @IsOptional()
  @ApiProperty()
  upi: string;

  @IsOptional()
  @ApiProperty()
  applicationNumberForIremboHub: string;

  @IsOptional()
  @ApiProperty()
  isAssociatedUpi: boolean;

  @IsOptional()
  @ApiProperty()
  waterConsumption: number;

  @IsOptional()
  @ApiProperty()
  electricityConsumption: number;

  @IsOptional()
  @ApiProperty()
  DistanceToTheNearestLandIn: number;

  @IsOptional()
  @ApiProperty()
  ProjectCostInUSD: number;

  @IsOptional()
  @ApiProperty()
  ProjectCostInRwf: number;

  @IsOptional()
  @ApiProperty()
  buildUpArea: number;

  @IsOptional()
  @ApiProperty()
  numberOfFloor: number;

  @IsOptional()
  @ApiProperty()
  grossFloorArea: number;

  @IsOptional()
  @ApiProperty()
  numberOfParkingSpace: number;

  @IsOptional()
  @ApiProperty()
  priceOfDwellingUnitRwf: number;

  @IsOptional()
  @ApiProperty()
  capacityInformation: number;

  @IsOptional()
  @ApiProperty()
  numberOfDwellingUnits: string;

  @IsOptional()
  @ApiProperty()
  DescriptionOfOperation: string;

  @IsOptional()
  @ApiProperty()
  projectName: string;

  @IsOptional()
  @ApiProperty()
  projectDescription: string;

  @IsOptional()
  @ApiProperty()
  percentageSpaceUse: string;

  @IsOptional()
  id: string;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field user id is required' })
  userId: string;

  // @IsNotEmpty({ message: 'The field submittedByUserId cannot be empty' })
  @IsOptional()
  @ApiProperty({ description: 'The field submittedByUserId id is required' })
  submittedByUserId: string;

  @IsOptional()
  @ApiProperty()
  certificateNumberEIA: string;

  @IsOptional()
  @ApiProperty()
  combiningPlotSize: number;

  @ApiProperty()
  @IsOptional()
  isEIAVerified: boolean;

  @IsNotEmpty({ message: 'The field project cannot be empty' })
  @ApiProperty({ description: 'The field project id is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field permitType cannot be empty' })
  @ApiProperty({ description: 'The field permitType id is required' })
  permitTypeId: string;

  @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
  @ApiProperty({ description: 'The field categoryTypeId id is required' })
  categoryTypeId: string;

  @IsNotEmpty({ message: 'The field buildType cannot be empty' })
  @ApiProperty({ description: 'The field buildType id is required' })
  buildTypeId: string;

  @IsNotEmpty({ message: 'The field technologySurvey cannot be empty' })
  @ApiProperty({ description: 'The field technologySurvey id is required' })
  technologySurveyId: string;

  @IsNotEmpty({ message: 'The field agency cannot be empty' })
  @ApiProperty({ description: 'The field agency id is required' })
  agencyId: string;

  @IsNotEmpty({ message: 'The field applicationStatus cannot be empty' })
  @ApiProperty({ description: 'The field applicationStatus id is required' })
  applicationStatusId: string;

  @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
  @ApiProperty({ description: 'The field categoryType id is required' })
  permitTypeCode: string;

  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  @ApiProperty({ description: 'The field agencyCode id is required' })
  agencyCode: string;

  // @IsNotEmpty({ message: 'The field user cannot be empty' })
  // @ApiProperty({ description: 'The field user id is required'})
  // userId: string;

  @IsOptional()
  applicationName: string;

  @IsOptional()
  isLocked: boolean;

  @IsOptional()
  @Type(() => OtherInfoApplicationDto)
  @ApiProperty({
    description: 'Other related details',
  })
  other?: OtherInfoApplicationDto;

  // @IsOptional()
  // @ValidateNested()
  // @Type(() => OtherInfoApplicationDto)
  // @ApiProperty({
  //   description: 'Other related details',
  // })
  // other: OtherInfoApplicationDto;
}

// update DTO for ProjectDto
export class ApplicationResubmitIremboDto {
  //project estimate

  @IsOptional()
  @ApiProperty()
  upi: string;

  @IsOptional()
  @ApiProperty()
  applicationNumberForIremboHub: string;
  @IsOptional()
  @ApiProperty()
  isAssociatedUpi: boolean;

  @IsOptional()
  @ApiProperty()
  waterConsumption: number;

  @IsOptional()
  @ApiProperty()
  electricityConsumption: number;

  @IsOptional()
  @ApiProperty()
  DistanceToTheNearestLandIn: number;

  @IsOptional()
  @ApiProperty()
  ProjectCostInUSD: number;

  @IsOptional()
  @ApiProperty()
  ProjectCostInRwf: number;

  @IsOptional()
  @ApiProperty()
  buildUpArea: number;

  @IsOptional()
  @ApiProperty()
  numberOfFloor: number;

  @IsOptional()
  @ApiProperty()
  grossFloorArea: number;

  @IsOptional()
  @ApiProperty()
  numberOfParkingSpace: number;

  @IsOptional()
  @ApiProperty()
  priceOfDwellingUnitRwf: number;

  @IsOptional()
  @ApiProperty()
  capacityInformation: number;

  @IsOptional()
  @ApiProperty()
  numberOfDwellingUnits: string;

  @IsOptional()
  @ApiProperty()
  DescriptionOfOperation: string;

  @IsOptional()
  @ApiProperty()
  projectName: string;

  @IsOptional()
  @ApiProperty()
  projectDescription: string;

  @IsOptional()
  @ApiProperty()
  percentageSpaceUse: string;

  @IsOptional()
  id: string;

  @IsNotEmpty({ message: 'The field user cannot be empty' })
  @ApiProperty({ description: 'The field user id is required' })
  userId: string;

  // @IsNotEmpty({ message: 'The field submittedByUserId cannot be empty' })
  @IsOptional()
  @ApiProperty({ description: 'The field submittedByUserId id is required' })
  submittedByUserId: string;

  @IsOptional()
  @ApiProperty()
  certificateNumberEIA: string;

  @IsOptional()
  @ApiProperty()
  combiningPlotSize: number;

  @ApiProperty()
  @IsOptional()
  isEIAVerified: boolean;

  @IsNotEmpty({ message: 'The field project cannot be empty' })
  @ApiProperty({ description: 'The field project id is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field permitType cannot be empty' })
  @ApiProperty({ description: 'The field permitType id is required' })
  permitTypeId: string;

  @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
  @ApiProperty({ description: 'The field categoryTypeId id is required' })
  categoryTypeId: string;

  @IsNotEmpty({ message: 'The field buildType cannot be empty' })
  @ApiProperty({ description: 'The field buildType id is required' })
  buildTypeId: string;

  @IsNotEmpty({ message: 'The field technologySurvey cannot be empty' })
  @ApiProperty({ description: 'The field technologySurvey id is required' })
  technologySurveyId: string;

  @IsNotEmpty({ message: 'The field agency cannot be empty' })
  @ApiProperty({ description: 'The field agency id is required' })
  agencyId: string;

  @IsNotEmpty({ message: 'The field applicationStatus cannot be empty' })
  @ApiProperty({ description: 'The field applicationStatus id is required' })
  applicationStatusId: string;

  @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
  @ApiProperty({ description: 'The field categoryType id is required' })
  permitTypeCode: string;

  @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
  @ApiProperty({ description: 'The field agencyCode id is required' })
  agencyCode: string;

  // @IsNotEmpty({ message: 'The field user cannot be empty' })
  // @ApiProperty({ description: 'The field user id is required'})
  // userId: string;

  @IsOptional()
  applicationName: string;

  @IsOptional()
  isLocked: boolean;

  @IsOptional()
  @Type(() => OtherInfoApplicationDto)
  @ApiProperty({
    description: 'Other related details',
  })
  other?: OtherInfoApplicationDto;

  // @IsOptional()
  // @ValidateNested()
  // @Type(() => OtherInfoApplicationDto)
  // @ApiProperty({
  //   description: 'Other related details',
  // })
  // other: OtherInfoApplicationDto;
}

// export class ProjectDto {
//   @IsOptional()
//   id: string;

//   @ApiProperty({ description: 'The field upi is required' })
//   @IsNotEmpty({ message: 'The field upi cannot be empty' })
//   upi: string;

//   // @ApiProperty({ description: 'The field villageId is required' })
//   // @IsNotEmpty({ message: 'The field villageId cannot be empty' })
//   // villageId: string;

//   @ApiProperty()
//   @IsOptional()
//   isFromOldSystem: boolean;

//   @ApiProperty()
//   @IsOptional()
//   isUnderMortgage: boolean;

//   @ApiProperty()
//   @IsOptional()
//   isUnderRestriction: boolean;

//   @ApiProperty()
//   @IsOptional()
//   centralCoordinateX: string;

//   @ApiProperty()
//   @IsOptional()
//   centralCoordinateY: string;

//   @ApiProperty()
//   @IsOptional()
//   villageCode: string;

//   @ApiProperty()
//   @IsOptional()
//   villageName: string;

//   @ApiProperty()
//   @IsOptional()
//   cellCode: string;

//   @ApiProperty()
//   @IsOptional()
//   cellName: string;

//   @ApiProperty()
//   @IsOptional()
//   sectorCode: string;

//   @ApiProperty()
//   @IsOptional()
//   sectorName: string;

//   @ApiProperty()
//   @IsOptional()
//   districtCode: string;

//   @ApiProperty()
//   @IsOptional()
//   districtName: string;

//   @ApiProperty()
//   @IsOptional()
//   provinceCode: string;

//   @ApiProperty()
//   @IsOptional()
//   provinceName: string;

//   @IsNotEmpty({ message: 'The field selectedUse cannot be empty' })
//   @ApiProperty({ description: 'The field selectedUse id is required' })
//   selectedUse: string;

//   @IsNotEmpty({ message: 'The field selectedCategoryUse cannot be empty' })
//   @ApiProperty({ description: 'The field selectedCategoryUse id is required' })
//   selectedCategoryUse: string;

//   @ApiProperty({ description: 'The field projectName is required' })
//   @IsNotEmpty({ message: 'The field projectName cannot be empty' })
//   projectName: string;

//   @ApiProperty({ description: 'The field projectDescription is required' })
//   @IsNotEmpty({ message: 'The field projectDescription cannot be empty' })
//   projectDescription: string;

//   @ApiProperty({ description: 'The field plotSize is required' })
//   @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
//   plotSize: number;

//   @ApiProperty({ description: 'The field buildUpArea is required' })
//   @IsNotEmpty({ message: 'The field buildUpArea cannot be empty' })
//   buildUpArea: number;

//   @ApiProperty({ description: 'The field numberOfFloor is required' })
//   @IsNotEmpty({ message: 'The field numberOfFloor cannot be empty' })
//   numberOfFloor: number;

//   @ApiProperty({ description: 'The field grossFloorArea is required' })
//   @IsNotEmpty({ message: 'The field grossFloorArea cannot be empty' })
//   grossFloorArea: number;

//   @ApiProperty({ description: 'The field numberOfParkingSpace is required' })
//   @IsNotEmpty({ message: 'The field numberOfParkingSpace cannot be empty' })
//   numberOfParkingSpace: number;

//   @ApiProperty({ description: 'The field priceOfDwellingUnitRwf is required' })
//   @IsNotEmpty({ message: 'The field priceOfDwellingUnitRwf cannot be empty' })
//   priceOfDwellingUnitRwf: number;

//   @ApiProperty({ description: 'The field capacityInformation is required' })
//   @IsNotEmpty({ message: 'The field capacityInformation cannot be empty' })
//   capacityInformation: number;

//   @ApiProperty({ description: 'The field numberOfDwellingUnits is required' })
//   @IsNotEmpty({ message: 'The field numberOfDwellingUnits cannot be empty' })
//   numberOfDwellingUnits: string;

//   @ApiProperty({ description: 'The field DescriptionOfOperation is required' })
//   @IsNotEmpty({ message: 'The field DescriptionOfOperation cannot be empty' })
//   DescriptionOfOperation: string;

//   @ApiProperty({ description: 'The field percentageSpaceUse is required' })
//   @IsNotEmpty({ message: 'The field percentageSpaceUse cannot be empty' })
//   percentageSpaceUse: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field user id is required' })
//   userId: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field agency id is required' })
//   agencyId: string;

//   //project estimate

//   @ApiProperty({ description: 'The field waterConsumption is required' })
//   @IsNotEmpty({ message: 'The field waterConsumption cannot be empty' })
//   waterConsumption: number;

//   @ApiProperty({ description: 'The field electricityConsumption is required' })
//   @IsNotEmpty({ message: 'The field electricityConsumption cannot be empty' })
//   electricityConsumption: number;

//   @ApiProperty({
//     description: 'The field DistanceToTheNearestLandIn is required',
//   })
//   @IsNotEmpty({
//     message: 'The field DistanceToTheNearestLandIn cannot be empty',
//   })
//   DistanceToTheNearestLandIn: number;

//   @ApiProperty({ description: 'The field ProjectCostInUSD is required' })
//   @IsNotEmpty({ message: 'The field ProjectCostInUSD cannot be empty' })
//   ProjectCostInUSD: number;

//   @ApiProperty({ description: 'The field ProjectCostInRwf is required' })
//   @IsNotEmpty({ message: 'The field ProjectCostInRwf cannot be empty' })
//   ProjectCostInRwf: number;

//   @IsNotEmpty({ message: 'The field technologySurvey cannot be empty' })
//   @ApiProperty({ description: 'The field technologySurvey id is required' })
//   technologySurveyId: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field technologySurvey id is required' })
//   projectStatusId: string;
// }

// export class ProjectDtoGet {
//   @ApiProperty({ description: 'The field upi is required' })
//   @IsNotEmpty({ message: 'The field upi cannot be empty' })
//   upi: string;

//   @ApiProperty()
//   @IsOptional()
//   isUnderMortgage: boolean;

//   @ApiProperty()
//   @IsOptional()
//   isUnderRestriction: boolean;

//   @ApiProperty()
//   @IsOptional()
//   centralCoordinateX: string;

//   @ApiProperty()
//   @IsOptional()
//   centralCoordinateY: string;

//   @ApiProperty()
//   @IsOptional()
//   villageCode: string;

//   @ApiProperty()
//   @IsOptional()
//   villageName: string;

//   @ApiProperty()
//   @IsOptional()
//   cellCode: string;

//   @ApiProperty()
//   @IsOptional()
//   cellName: string;

//   @ApiProperty()
//   @IsOptional()
//   sectorCode: string;

//   @ApiProperty()
//   @IsOptional()
//   sectorName: string;

//   @ApiProperty()
//   @IsOptional()
//   districtCode: string;

//   @ApiProperty()
//   @IsOptional()
//   districtName: string;

//   @ApiProperty()
//   @IsOptional()
//   provinceCode: string;

//   @ApiProperty()
//   @IsOptional()
//   provinceName: string;

//   @ApiProperty({ description: 'The field projectName is required' })
//   @IsNotEmpty({ message: 'The field projectName cannot be empty' })
//   projectName: string;

//   @ApiProperty({ description: 'The field projectDescription is required' })
//   @IsNotEmpty({ message: 'The field projectDescription cannot be empty' })
//   projectDescription: string;

//   @ApiProperty({ description: 'The field plotSize is required' })
//   @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
//   plotSize: number;

//   @ApiProperty({ description: 'The field buildUpArea is required' })
//   @IsNotEmpty({ message: 'The field buildUpArea cannot be empty' })
//   buildUpArea: number;

//   @ApiProperty({ description: 'The field numberOfFloor is required' })
//   @IsNotEmpty({ message: 'The field numberOfFloor cannot be empty' })
//   numberOfFloor: number;

//   @ApiProperty({ description: 'The field grossFloorArea is required' })
//   @IsNotEmpty({ message: 'The field grossFloorArea cannot be empty' })
//   grossFloorArea: number;

//   @ApiProperty({ description: 'The field numberOfParkingSpace is required' })
//   @IsNotEmpty({ message: 'The field numberOfParkingSpace cannot be empty' })
//   numberOfParkingSpace: number;

//   @ApiProperty({ description: 'The field priceOfDwellingUnitRwf is required' })
//   @IsNotEmpty({ message: 'The field priceOfDwellingUnitRwf cannot be empty' })
//   priceOfDwellingUnitRwf: number;

//   @ApiProperty({ description: 'The field capacityInformation is required' })
//   @IsNotEmpty({ message: 'The field capacityInformation cannot be empty' })
//   capacityInformation: number;

//   @ApiProperty({ description: 'The field numberOfDwellingUnits is required' })
//   @IsNotEmpty({ message: 'The field numberOfDwellingUnits cannot be empty' })
//   numberOfDwellingUnits: string;

//   @ApiProperty({ description: 'The field DescriptionOfOperation is required' })
//   @IsNotEmpty({ message: 'The field DescriptionOfOperation cannot be empty' })
//   DescriptionOfOperation: string;

//   @ApiProperty({ description: 'The field percentageSpaceUse is required' })
//   @IsNotEmpty({ message: 'The field percentageSpaceUse cannot be empty' })
//   percentageSpaceUse: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field user id is required' })
//   userId: string;

//   @IsNotEmpty({ message: 'The field selectedUse cannot be empty' })
//   @ApiProperty({ description: 'The field selectedUse id is required' })
//   selectedUse: string;

//   @IsNotEmpty({ message: 'The field selectedCategoryUse cannot be empty' })
//   @ApiProperty({ description: 'The field selectedCategoryUse id is required' })
//   selectedCategoryUse: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field agency id is required' })
//   agencyId: string;

//   //project estimate

//   @ApiProperty({ description: 'The field waterConsumption is required' })
//   @IsNotEmpty({ message: 'The field waterConsumption cannot be empty' })
//   waterConsumption: number;

//   @ApiProperty({ description: 'The field electricityConsumption is required' })
//   @IsNotEmpty({ message: 'The field electricityConsumption cannot be empty' })
//   electricityConsumption: number;

//   @ApiProperty({
//     description: 'The field DistanceToTheNearestLandIn is required',
//   })
//   @IsNotEmpty({
//     message: 'The field DistanceToTheNearestLandIn cannot be empty',
//   })
//   DistanceToTheNearestLandIn: number;

//   @ApiProperty({ description: 'The field ProjectCostInUSD is required' })
//   @IsNotEmpty({ message: 'The field ProjectCostInUSD cannot be empty' })
//   ProjectCostInUSD: number;

//   @ApiProperty({ description: 'The field ProjectCostInRwf is required' })
//   @IsNotEmpty({ message: 'The field ProjectCostInRwf cannot be empty' })
//   ProjectCostInRwf: number;
// }

// export class ApplicationDto {
//   @IsOptional()
//   id: string;

//   @IsNotEmpty({ message: 'The field user cannot be empty' })
//   @ApiProperty({ description: 'The field user id is required' })
//   userId: string;

//   @IsOptional()
//   @ApiProperty()
//   certificateNumberEIA: string;

//   @IsNotEmpty({ message: 'The field project cannot be empty' })
//   @ApiProperty({ description: 'The field project id is required' })
//   projectId: string;

//   @IsNotEmpty({ message: 'The field permitType cannot be empty' })
//   @ApiProperty({ description: 'The field permitType id is required' })
//   permitTypeId: string;

//   @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
//   @ApiProperty({ description: 'The field categoryTypeId id is required' })
//   categoryTypeId: string;

//   @IsNotEmpty({ message: 'The field buildType cannot be empty' })
//   @ApiProperty({ description: 'The field buildType id is required' })
//   buildTypeId: string;

//   @IsNotEmpty({ message: 'The field agency cannot be empty' })
//   @ApiProperty({ description: 'The field agency id is required' })
//   agencyId: string;

//   @IsNotEmpty({ message: 'The field applicationStatus cannot be empty' })
//   @ApiProperty({ description: 'The field applicationStatus id is required' })
//   applicationStatusId: string;

//   @IsNotEmpty({ message: 'The field categoryType cannot be empty' })
//   @ApiProperty({ description: 'The field categoryType id is required' })
//   permitTypeCode: string;

//   @IsNotEmpty({ message: 'The field agencyCode cannot be empty' })
//   @ApiProperty({ description: 'The field agencyCode id is required' })
//   agencyCode: string;

//   // @IsNotEmpty({ message: 'The field user cannot be empty' })
//   // @ApiProperty({ description: 'The field user id is required'})
//   // userId: string;

//   @IsOptional()
//   applicationName: string;
// }

// export class FindUpdateApplicationDto {
//   @ApiProperty({ description: 'The field project id is required' })
//   projectId: string;

//   @ApiProperty({ description: 'The field user id is required' })
//   userId: string;

//   @ApiProperty({ description: 'The field permitType id is required' })
//   permitTypeId: string;

//   @ApiProperty({ description: 'The field categoryTypeId id is required' })
//   categoryTypeId: string;

//   @ApiProperty({ description: 'The field buildType id is required' })
//   buildTypeId: string;

//   @ApiProperty({ description: 'The field agency id is required' })
//   agencyId: string;

//   @ApiProperty({ description: 'The field applicationStatus id is required' })
//   applicationStatusId: string;

//   @ApiProperty({ description: 'The field categoryType id is required' })
//   permitTypeCode: string;

//   @ApiProperty({ description: 'The field agencyCode id is required' })
//   agencyCode: string;

//   @IsOptional()
//   id: string;

//   @IsOptional()
//   applicationName: string;
// }

// export class AssociatedUPIDto {
//   @IsOptional()
//   id: string;

//   @ApiProperty({ description: 'The field upi is required' })
//   @IsNotEmpty({ message: 'The field upi cannot be empty' })
//   upi: string;

//   @ApiProperty({ description: 'The field plotSize is required' })
//   @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
//   plotSize: string;

//   @ApiProperty({ description: 'The field ownerFullName is required' })
//   @IsNotEmpty({ message: 'The field ownerFullName cannot be empty' })
//   ownerFullName: string;

//   @ApiProperty({ description: 'The field ownerIdNo is required' })
//   @IsNotEmpty({ message: 'The field ownerIdNo cannot be empty' })
//   ownerIdNo: string;

//   @ApiProperty({ description: 'The field isRRAVerified is required' })
//   @IsNotEmpty({ message: 'The field isRRAVerified cannot be empty' })
//   isRRAVerified: boolean;

//   @ApiProperty({ description: 'The field userId is required' })
//   @IsNotEmpty({ message: 'The field userId cannot be empty' })
//   userId: string;

//   @ApiProperty({ description: 'The field projectId is required' })
//   @IsNotEmpty({ message: 'The field projectId cannot be empty' })
//   projectId: string;
// }

export class AssociatedUPIDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field upi is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  upi: string;

  @ApiProperty({ description: 'The field plotSize is required' })
  @IsNotEmpty({ message: 'The field plotSize cannot be empty' })
  plotSize: string;

  @ApiProperty({ description: 'The field ownerFullName is required' })
  @IsNotEmpty({ message: 'The field ownerFullName cannot be empty' })
  ownerFullName: string;

  @ApiProperty({ description: 'The field ownerIdNo is required' })
  @IsNotEmpty({ message: 'The field ownerIdNo cannot be empty' })
  ownerIdNo: string;

  @ApiProperty({ description: 'The field isRRAVerified is required' })
  @IsDefined({ message: 'The field isRRAVerified is required' })
  isRRAVerified: boolean;

  @ApiProperty({ description: 'The field userId is required' })
  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  userId: string;

  @ApiProperty({ description: 'The field projectId is required' })
  @IsNotEmpty({ message: 'The field projectId cannot be empty' })
  projectId: string;
}

export class AssigneeDto {
  @IsOptional()
  id: string;

  @IsOptional()
  EIACertificateNumber: string;

  @ApiProperty({ description: 'The field timeLineDays is required' })
  @IsNotEmpty({ message: 'The field timeLineDays be empty' })
  timeLineDays: string;

  @ApiProperty({ description: 'The field Assignee is required' })
  @IsNotEmpty({ message: 'The field Assignee be empty' })
  userIdForAssignee: string;

  @ApiProperty({ description: 'The field userType is required' })
  @IsNotEmpty({ message: 'The field userType be empty' })
  userTypeId: string;

  @ApiProperty({ description: 'The field licenseNumber is required' })
  @IsNotEmpty({ message: 'The field licenseNumber be empty' })
  licenseNumber: string;

  @IsNotEmpty({ message: 'The field projectId cannot be empty' })
  @ApiProperty({ description: 'The field projectId is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field projectStatusId cannot be empty' })
  @ApiProperty({ description: 'The field projectStatusId is required' })
  projectStatusId: string;
}

export class UpdateAssigneeDto {
  @IsOptional()
  id: string;

  @ApiProperty()
  @IsOptional()
  userIdForAssignee: string;

  @ApiProperty()
  @IsOptional()
  userTypeId: string;

  @ApiProperty()
  @IsOptional()
  licenseNumber: string;

  @IsNotEmpty({ message: 'The field projectId cannot be empty' })
  @ApiProperty({ description: 'The field projectId is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field projectStatusId cannot be empty' })
  @ApiProperty({ description: 'The field projectStatusId is required' })
  projectStatusId: string;
}

export class RejectProjectDto {
  @IsNotEmpty({ message: 'The field projectId cannot be empty' })
  @ApiProperty({ description: 'The field projectId is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field licenseNumber cannot be empty' })
  @ApiProperty({ description: 'The field licenseNumber is required' })
  licenseNumber: string;

  @IsNotEmpty({ message: 'The field projectUserId cannot be empty' })
  @ApiProperty({ description: 'The field projectUserId is required' })
  projectUserId: string;
}

export class DeleteEngineerOnProjectDto {
  @IsNotEmpty({ message: 'The field projectId cannot be empty' })
  @ApiProperty({ description: 'The field projectId is required' })
  projectId: string;

  @IsNotEmpty({ message: 'The field licenseNumber cannot be empty' })
  @ApiProperty({ description: 'The field licenseNumber is required' })
  licenseNumber: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId is required' })
  userId: string;
}

export class UpdateApplicationDto extends PartialType(ApplicationDto) {}

export class ApplicationDtoUpdate {
  @ApiProperty({ description: 'The field project id is required' })
  @IsNotEmpty({ message: 'The field cannot be empty' })
  projectId: string;

  @ApiProperty({ description: 'The field user id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  userId: string;

  @ApiProperty({ description: 'The field permitType id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  permitTypeId: string;

  @ApiProperty({ description: 'The field categoryTypeId id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  categoryTypeId: string;

  @ApiProperty({ description: 'The field buildType id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  buildTypeId: string;

  @ApiProperty({ description: 'The field agency id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  agencyId: string;

  @ApiProperty({ description: 'The field applicationStatus id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  applicationStatusId: string;

  @ApiProperty({ description: 'The field categoryType id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  permitTypeCode: string;

  @ApiProperty({ description: 'The field agencyCode id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  agencyCode: string;

  @IsOptional()
  applicationName: string;

  // @IsNotEmpty({ message: 'The field technologySurvey cannot be empty' })
  // @ApiProperty({ description: 'The field technologySurvey id is required' })
  // technologySurveyId: string;
}
export class LockApplicationDtoUpdate {
  @ApiProperty({ description: 'The field user id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  userId: string;

  @ApiProperty({ description: 'The field applicationStatus id is required' })
  @IsNotEmpty({ message: 'The field upi cannot be empty' })
  applicationStatusId: string;
}

export class PermitQuestionDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field user is required' })
  @IsNotEmpty({ message: 'The field user be empty' })
  userId: string;

  @ApiProperty({ description: 'The field description is required' })
  @IsNotEmpty({ message: 'The field description be empty' })
  description: string;

  @ApiProperty({ description: 'The field code is required' })
  @IsNotEmpty({ message: 'The field code be empty' })
  code: string;

  @ApiProperty()
  @IsOptional()
  comment: string;

  @IsNotEmpty({ message: 'The field permitTypeId cannot be empty' })
  @ApiProperty({ description: 'The field permitTypeId is required' })
  permitTypeId: string;

  @ApiProperty()
  @IsOptional()
  questionCategoryId: string;
}

export class AnswerDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field permitQuestion is required' })
  @IsNotEmpty({ message: 'The field permitQuestion be empty' })
  permitQuestionId: string;

  @ApiProperty({ description: 'The field user is required' })
  @IsNotEmpty({ message: 'The field user be empty' })
  userId: string;

  @ApiProperty()
  @IsOptional()
  comment: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;
}

export class ReviewersOnApplicationDto {
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'The field user is required' })
  @IsNotEmpty({ message: 'The field user be empty' })
  userId: string;

  @ApiProperty()
  @IsOptional()
  status: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId is required' })
  applicationId: string;

  @ApiProperty()
  @IsOptional()
  ipAddress: string;

  @ApiProperty()
  @IsOptional()
  browser: string;

  @ApiProperty()
  @IsOptional()
  operatingSystem: string;
}
export class CreateChartDto {
  @ApiProperty({ description: 'The field year' })
  year: number;

  @ApiProperty({ description: 'The field agency' })
  agencyId: string;
}

// export class FoundationInspectionDto {
//   @IsOptional()
//   @ApiProperty({ description: 'The field geotechnicalReportRespected' })
//   geotechnicalReportRespected: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field foundationType' })
//   foundationType: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field proceduresComply' })
//   proceduresComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field soilTreatmentComply' })
//   soilTreatmentComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field erosionControlMeasures' })
//   erosionControlMeasures: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field footingSizeComply' })
//   footingSizeComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field locationComply' })
//   locationComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field reinforcementSizeComply' })
//   reinforcementSizeComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field spacingReinforcementsComply' })
//   spacingReinforcementsComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field concretePlacementComply' })
//   concretePlacementComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field plumbingComply' })
//   plumbingComply: string;

//   @IsOptional()
//   @ApiProperty({ description: 'The field comments' })
//   comments: string;

//   @IsNotEmpty({ message: 'The field userId cannot be empty' })
//   @ApiProperty({ description: 'The field userId' })
//   userId: string;

//   @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
//   @ApiProperty({ description: 'The field applicationId' })
//   applicationId: string;
// }

export class FoundationInspectionDto {
  @IsOptional()
  @ApiProperty({ description: 'The field geotechnicalReportRespected' })
  geotechnicalReportRespected: string;

  @IsOptional()
  @ApiProperty({ description: 'The field foundationType' })
  foundationType: string;

  @IsOptional()
  @ApiProperty({ description: 'The field proceduresComply' })
  proceduresComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field soilTreatmentComply' })
  soilTreatmentComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field erosionControlMeasures' })
  erosionControlMeasures: string;

  @IsOptional()
  @ApiProperty({ description: 'The field footingSizeComply' })
  footingSizeComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field locationComply' })
  locationComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field reinforcementSizeComply' })
  reinforcementSizeComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field spacingReinforcementsComply' })
  spacingReinforcementsComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field concretePlacementComply' })
  concretePlacementComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field plumbingComply' })
  plumbingComply: string;

  @IsOptional()
  @ApiProperty({ description: 'The field comments' })
  comments: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId' })
  applicationId: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'The uploaded file',
  })
  @IsOptional()
  file?: any;
}

export class OccupancyInspectionDto {
  @IsOptional()
  @ApiProperty({ description: 'The field fireExtinguishersInResidence' })
  fireExtinguishersInResidence: string;

  @IsOptional()
  @ApiProperty({ description: 'The field fireBlanketInKitchen' })
  fireBlanketInKitchen: string;

  @IsOptional()
  @ApiProperty({ description: 'The field twoExitsPerResidence' })
  twoExitsPerResidence: string;

  @IsOptional()
  @ApiProperty({ description: 'The field emergencyExit' })
  emergencyExit: string;

  @IsOptional()
  @ApiProperty({ description: 'The field fireAlarmOnEachFloor' })
  fireAlarmOnEachFloor: string;

  @IsOptional()
  @ApiProperty({ description: 'The field smokeDetectorsSprinklers' })
  smokeDetectorsSprinklers: string;

  @IsOptional()
  @ApiProperty({ description: 'The field hoseReelsEachFloor' })
  hoseReelsEachFloor: string;

  @IsOptional()
  @ApiProperty({ description: 'The field lightningArrestor' })
  lightningArrestor: string;

  @IsOptional()
  @ApiProperty({ description: 'The field fireExtinguishersEvery30m' })
  fireExtinguishersEvery30m: string;

  @IsOptional()
  @ApiProperty({ description: 'The field exitSignsEachFloor' })
  exitSignsEachFloor: string;

  @IsOptional()
  @ApiProperty({ description: 'The field emergencyExitEachFloor' })
  emergencyExitEachFloor: string;

  @IsOptional()
  @ApiProperty({ description: 'The field floorPlanEachLevel' })
  floorPlanEachLevel: string;

  @IsOptional()
  @ApiProperty({ description: 'The field signForElevators' })
  signForElevators: string;

  @IsOptional()
  @ApiProperty({ description: 'The field landingSpaceHelicopter' })
  landingSpaceHelicopter: string;

  @IsOptional()
  @ApiProperty({ description: 'The field evacuationPlan' })
  evacuationPlan: string;

  @IsOptional()
  @ApiProperty({ description: 'The field cctvCameras' })
  cctvCameras: string;

  @IsOptional()
  @ApiProperty({ description: 'The field metalDetectors' })
  metalDetectors: string;

  @IsOptional()
  @ApiProperty({ description: 'The field luggageScanners' })
  luggageScanners: string;

  @IsOptional()
  @ApiProperty({ description: 'The field emergencyPhoneNumbers' })
  emergencyPhoneNumbers: string;

  @IsOptional()
  @ApiProperty({ description: 'The field fireHydrantNearby' })
  fireHydrantNearby: string;

  @IsOptional()
  @ApiProperty({ description: 'The field evacuationPlanSecurity' })
  evacuationPlanSecurity: string;

  @IsOptional()
  @ApiProperty({ description: 'The field securityManagerStaff' })
  securityManagerStaff: string;

  @IsOptional()
  @ApiProperty({ description: 'The field restrictedAccess' })
  restrictedAccess: string;

  @IsOptional()
  @ApiProperty({ description: 'The field insuranceBuilding' })
  insuranceBuilding: string;

  @IsOptional()
  @ApiProperty({ description: 'The field comments' })
  comments: string;

  @IsNotEmpty({ message: 'The field userId cannot be empty' })
  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  @ApiProperty({ description: 'The field applicationId' })
  applicationId: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'The uploaded file',
  })
  @IsOptional()
  file?: any;
}

export class SubmissionLogDto {
  @ApiProperty({ description: 'The field applicationId' })
  applicationId: string;

  @ApiProperty({ description: 'The field userId' })
  userId: string;

  @ApiProperty({ description: 'The field applicationStatusId' })
  applicationStatusId: string;
}

export class DataForChartDto {
  @ApiProperty({ description: 'The field agency' })
  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  agencyId: string;

  @ApiProperty({ description: 'The field year' })
  @IsNotEmpty({ message: 'The field applicationId cannot be empty' })
  year: number;
}
