const cluster = require('cluster');
const os = require('os');
const { spawn } = require('child_process');
const http = require('http');

// Get number of CPU cores dynamically
const numCPUs = os.cpus().length;
const appName = process.argv[2] || 'applications';

// Calculate optimal number of workers (use 75% of cores)
const optimalWorkers = Math.max(1, Math.floor(numCPUs * 0.75));

console.log(`System has ${numCPUs} CPU cores available`);
console.log(`Starting ${optimalWorkers} worker processes for optimal performance`);

if (cluster.isMaster) {
  console.log(`Master process ${process.pid} is running`);
  console.log(`Starting ${appName} with ${optimalWorkers} worker processes`);

  // Get base port for the application
  const getBasePort = (appName) => {
    const portMap = {
      'applications': 3000,
      'auth': 3001,
      'payments': 3003,
      'notifications': 3004,
      'documents': 3005,
      'integrations': 3007
    };
    return portMap[appName] || 3000;
  };

  const basePort = getBasePort(appName);
  const workers = new Map();
  let currentWorkerIndex = 0;

  // Fork workers
  for (let i = 0; i < optimalWorkers; i++) {
    const worker = cluster.fork({
      WORKER_ID: i + 1,
      CPU_CORES: numCPUs,
      APP_NAME: appName,
      WORKER_PORT: basePort + i + 1, // Workers use ports basePort+1, basePort+2, etc.
      IS_WORKER: 'true'
    });
    
    workers.set(worker.id, {
      pid: worker.process.pid,
      port: basePort + i + 1,
      startTime: Date.now(),
      requests: 0,
      status: 'starting'
    });
    
    console.log(`Worker ${worker.process.pid} started on port ${basePort + i + 1}`);
  }

  // Create load balancer server
  const server = http.createServer((req, res) => {
    // Simple round-robin load balancing
    const workerIds = Array.from(workers.keys());
    if (workerIds.length === 0) {
      res.writeHead(503, { 'Content-Type': 'text/plain' });
      res.end('No workers available');
      return;
    }

    const workerId = workerIds[currentWorkerIndex % workerIds.length];
    const worker = cluster.workers[workerId];
    const workerInfo = workers.get(workerId);
    
    if (worker && workerInfo.status === 'ready') {
      // Forward request to worker
      workerInfo.requests++;
      currentWorkerIndex++;
      
      // Simple proxy to worker
      const options = {
        hostname: 'localhost',
        port: workerInfo.port,
        path: req.url,
        method: req.method,
        headers: req.headers
      };

      const proxyReq = http.request(options, (proxyRes) => {
        res.writeHead(proxyRes.statusCode, proxyRes.headers);
        proxyRes.pipe(res);
      });

      req.pipe(proxyReq);
    } else {
      res.writeHead(503, { 'Content-Type': 'text/plain' });
      res.end('Worker not ready');
    }
  });

  // Start load balancer on main port
  server.listen(basePort, () => {
    console.log(`Load balancer started on port ${basePort}`);
    console.log(`Workers will be available on ports ${basePort + 1} to ${basePort + optimalWorkers}`);
  });

  // Handle worker messages
  cluster.on('message', (worker, message) => {
    if (message.type === 'ready') {
      const workerInfo = workers.get(worker.id);
      if (workerInfo) {
        workerInfo.status = 'ready';
        console.log(`Worker ${worker.process.pid} is ready on port ${workerInfo.port}`);
      }
    } else if (message.type === 'request') {
      const workerInfo = workers.get(worker.id);
      if (workerInfo) {
        workerInfo.requests++;
      }
    }
  });

  // Handle worker exit and restart
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
    workers.delete(worker.id);
    
    const newWorker = cluster.fork({
      WORKER_ID: worker.id,
      CPU_CORES: numCPUs,
      APP_NAME: appName,
      WORKER_PORT: basePort + worker.id,
      IS_WORKER: 'true'
    });
    
    workers.set(newWorker.id, {
      pid: newWorker.process.pid,
      port: basePort + worker.id,
      startTime: Date.now(),
      requests: 0,
      status: 'starting'
    });
    
    console.log(`New worker ${newWorker.process.pid} started on port ${basePort + worker.id}`);
  });

  // Monitor cluster health
  cluster.on('online', (worker) => {
    console.log(`Worker ${worker.process.pid} is online`);
  });

  // Log cluster status periodically
  setInterval(() => {
    console.log(`\n=== Load Balancer Status ===`);
    console.log(`Main port: ${basePort}`);
    console.log(`Active workers: ${Array.from(workers.values()).filter(w => w.status === 'ready').length}/${workers.size}`);
    
    for (const [id, info] of workers) {
      const uptime = Math.floor((Date.now() - info.startTime) / 1000);
      console.log(`Worker ${id} (PID: ${info.pid}, Port: ${info.port}): ${info.requests} requests, status: ${info.status}, uptime: ${uptime}s`);
    }
    console.log(`===========================\n`);
  }, 30000);

  // Graceful shutdown
  const gracefulShutdown = (signal) => {
    console.log(`Master received ${signal}, shutting down gracefully...`);
    server.close(() => {
      for (const id in cluster.workers) {
        cluster.workers[id].send({ type: 'shutdown' });
      }
      setTimeout(() => {
        for (const id in cluster.workers) {
          cluster.workers[id].kill();
        }
        process.exit(0);
      }, 5000);
    });
  };

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

} else {
  // Worker process
  const workerId = process.env.WORKER_ID;
  const workerPort = process.env.WORKER_PORT;
  const cpuCores = process.env.CPU_CORES;
  const appName = process.env.APP_NAME;
  
  console.log(`Worker ${process.pid} (ID: ${workerId}) started for ${appName} on port ${workerPort}`);

  // Start the NestJS application with specific port
  const child = spawn('pnpm', ['run', 'start:dev', appName], {
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      WORKER_ID: workerId,
      CPU_CORES: cpuCores,
      PORT: workerPort,
      IS_WORKER: 'true'
    }
  });

  // Notify master when ready
  setTimeout(() => {
    process.send({ type: 'ready' });
  }, 5000);

  child.on('exit', (code) => {
    console.log(`Worker ${process.pid} exited with code ${code}`);
    process.exit(code);
  });

  // Handle shutdown signal from master
  process.on('message', (message) => {
    if (message.type === 'shutdown') {
      console.log(`Worker ${process.pid} received shutdown signal`);
      child.kill('SIGTERM');
    }
  });

  // Handle signals for graceful shutdown
  process.on('SIGTERM', () => {
    console.log(`Worker ${process.pid} received SIGTERM`);
    child.kill('SIGTERM');
  });

  process.on('SIGINT', () => {
    console.log(`Worker ${process.pid} received SIGINT`);
    child.kill('SIGINT');
  });
} 