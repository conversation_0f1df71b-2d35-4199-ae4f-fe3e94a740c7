import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  passresetForm!: FormGroup;
  submitted = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private utilService: UtilService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.passresetForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")]]
    });
  }

  get f() { return this.passresetForm.controls; }


  onSubmit() {
    this.submitted = true;
    if (this.passresetForm.invalid) {
      return;
    } else {
      this.authService.findAllWithPath(environment.authUrl + 'auth/forgot-password/' + this.passresetForm.value.email)
        .subscribe(
          data => {
            this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "bottom", "center");
            this.router.navigate(['/']);
          }
        )
    }
  }
}
