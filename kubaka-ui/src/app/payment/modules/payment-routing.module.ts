import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InvoicesComponent } from '../components/invoices/invoices.component';
import { InvoiceDetailComponent } from '../components/invoice-detail/invoice-detail.component';
import { InvoiceDetailWaterMarkComponent } from '../components/invoice-detail-water-mark/invoice-detail-water-mark.component';


const routes: Routes = [
    {
        path: "", component: InvoicesComponent,
    },
    {
        path: "invoice-detail/:id/:applicationId", component: InvoiceDetailComponent,
        // path: "invoice-detail/:id/:applicationId", component: InvoiceDetailComponent,
    },

    {
      // path: "invoice-detail-water-mark/:id", component: InvoiceDetailWaterMarkComponent
      path: "invoice-detail-water-mark/:id/:applicationId", component: InvoiceDetailWaterMarkComponent

    }


];

@NgModule({
    imports: [
        RouterModule.forChild(routes)],

    exports: [RouterModule]
})
export class InvoiceRoutingModule { }
