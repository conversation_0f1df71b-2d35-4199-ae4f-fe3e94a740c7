.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 1036px!important;
    color: var(--bs-modal-color);
    pointer-events: auto;
    background-color: var(--bs-modal-bg);
    background-clip: padding-box;
    border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
    border-radius: var(--bs-modal-border-radius);
    outline: 0;
}


.chlst-st{
    display: flex;
    max-width: 100%;
    padding: 0px;
    h3{
        flex: 1;
    }
    .btns {
        flex: 1;
        width: -moz-fit-content;
        width: fit-content !important;
        margin-right: -720px !important;
    }
}