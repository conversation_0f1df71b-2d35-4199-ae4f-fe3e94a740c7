{"home": "Home", "topBarMenu": {"home": "Home", "service": "Service", "faq": "FAQs", "login": "<PERSON><PERSON>", "signup": "Sign Up"}, "chatBotLabels": {"title": "Any Questions? Chat with <PERSON><PERSON><PERSON>!", "subTitle": "Get reply instantly", "Welcome": "Welcome", "Selectcategory": "Select category", "Selectagency": "Select agency(OSC)"}, "heroPage": {"title": "Building Permit System", "subTitle": "Track the status of your permit application", "subTitle2": "Or CLick here for the old BPMIS application", "search": "Search UPI Here", "searchButton": "Search", "link": "Check what's allowed on your land", "constructionPermit": "Construction Permit", "constructionPermitApply": "Apply"}, "zoningCheck": {"title": "Check the eligibility", "subTitle": "In accordance with the master plan, track what is permitted, prohibited, and conditional to be built on your plot ", "search": "Fill the UPI", "searchButton": "Search", "checkRequirementProgress": "Check requirement in progress", "taxPaymentReminder": "Could you kindly ensure that your taxes are paid? This plot has some unpaid taxes", "unpaidTaxesDetails": "Unpaid taxes details", "declarationYear": "Declaration Year", "taxTypeDescription": "Tax Type Description", "totalTaxToPay": "Total Tax To Pay", "totalTaxPaid": "Total Tax Paid", "balance": "Balance", "isUnderRestriction": "Is Under Restriction", "isUnderMortgage": "Is Under Mortgage", "yes": "Yes", "no": "No", "location": "Location", "district": "District", "sector": "Sector", "cell": "Cell", "village": "Village", "landUse": "Land use", "registeredUse": "Registered use", "zoning": "Zoning", "area": "Area", "thereIsA": "There is a", "road": "Road", "passesThroughPlot": "that passes through your plot", "selectBuildMasterPlan": "Please select what you want to build according to the master plan", "permittedUse": "Permitted use", "apply": "Apply", "prohibitedUse": "Prohibited use", "conditionalUse": "Conditional use", "representative": "Representative", "doesPlotUnderRestriction": "Does plot under Restriction", "doesPlotUnderMortgage": "Does plot under Mortgage", "owners": "Owners", "names": "Names", "nationalId": "National ID", "maritalStatus": "Marital status", "gender": "Gender", "male": "Male", "female": "Female"}, "zoningCheckDetails": {"title": "How the application for the certificate is made", "subTitle": "Track your license status here", "permit": "Permit", "permitNumber": "Application Number", "date": "<PERSON><PERSON><PERSON> ", "location": "Location", "stageTitle": "The stages the application goes through", "stage0": "STAGE 0", "stage0Title": "Pending Application", "stage0Description": "This is the first stage if you have not yet submitted an application.", "stage1": "STAGE 1", "stage1Title": "Submitted Application", "stage1Description": "This is the first stage once you have submitted your application.", "stage2": "STAGE 2", "stage2Title": "Back for correction application", "stage2Description": "This is when you have received a response from the authority that you need to change something in your application.", "stage3": "STAGE 3", "stage3Title": "Review Application", "stage3Description": "Here, your application will be reviewed by the application staff.", "stage4": "STAGE 4", "stage4Title": "Approved Application", "stage4Description": "This is when you have received a response from the authority that has approved/rejected your application.", "stage5": "STAGE 5", "stage5Title": "Rejected/Cancelled Application", "stage5Description": "This is when you have received a response from the authority that has approved/rejected your application."}, "permitCategory": {"title": "Permits to ask for", "subTitle": "Check out the requirements to get your permit here", "Category1": "New Construction Permit", "Category1Description1Tile1": "About this Service", "Category1Description1": "This service allows Users to apply for a New Construction Permit. The application is processed by Rwanda Housing Authority.", "Authorized1": "Rwanda Housing Authority", "Period1": "Valid for only 12hours", "Price1": "Vary", "authorized": "Authorized", "period": "Period", "price": "Price", "Cancelbtn1": "Cancel", "Applybtn1": "Apply", "Category2": "Inspection Notice", "Category2Description2Title2": "About this Service", "Category2Description2": "This service allows users to apply for an Inspection Notice of their buildings. The application is processed by Rwanda Housing Authority.", "Authorized2": "Rwanda Housing Authority", "Period2": "Valid for only 12hours", "Price2": "Vary", "Cancelbtn2": "Cancel", "Applybtn2": "Apply", "Category3": "Renewal of Permit", "Category3Description3Title3": "About this Service", "Category3Description3": "This service allows users to apply for the renewal of their building permits. The application is processed by Rwanda Housing Authority.", "Authorized3": "Rwanda Housing Authority", "Period3": "Valid for only 12hours", "Price3": "Vary", "Cancelbtn3": "Cancel", "Applybtn3": "Apply", "Category4": "Construction of Fence", "Category4Description4Title4": "About this Service", "Category4Description4": "This service allows users to apply for a construction of fence permit. The application is processed by Rwanda Housing Authority.", "Authorized4": "Rwanda Housing Authority", "Period4": "Valid for only 12hours", "Price4": "Vary", "Cancelbtn4": "Cancel", "Applybtn4": "Apply", "Category5": "demolation-full/partial", "Category5Description5Title5": "About this Service", "Category5Description5": "This service allows users to apply for a demolition permit, whether for full or partial demolition. The application is processed by Rwanda Housing Authority.", "Authorized5": "Rwanda Housing Authority", "Period5": "Valid for only 12hours", "Price5": "Vary", "Cancelbtn5": "Cancel", "Applybtn5": "Apply", "Category6": "Occupancy permit", "Category6Description6Title6": "About this Service", "Category6Description6": "This service allows users to apply for an occupancy permit. The application is processed by Rwanda Housing Authority.", "Authorized6": "Rwanda Housing Authority", "Period6": "Valid for only 12hours", "Price6": "Vary", "Cancelbtn6": "Cancel", "Applybtn6": "Apply", "Category7": "Project Modification", "Category7Description7Title7": "About this Service", "Category7Description7": "This service allows users to apply for project modification. The application is processed by Rwanda Housing Authority.", "Authorized7": "Rwanda Housing Authority", "Period7": "Valid for only 12hours", "Price7": "Vary", "Cancelbtn7": "Cancel", "Applybtn7": "Apply", "Category8": "Temporary Structure Authorization", "Category8Description8Title8": "About this Service", "Category8Description8": "This service allows users to apply for Temporary Structure Authorization Applications. The application is processed by Rwanda Housing Authority.", "Authorized8": "Rwanda Housing Authority", "Period8": "Valid for only 12hours", "Price8": "Vary", "Cancelbtn8": "Cancel", "Applybtn8": "Apply", "Category9": "Refurbishment of Existing Building with Structural alteration", "Category9Description9Title9": "About this Service", "Category9Description9": "This service allows users to apply for the refurbishment of existing building with structural alterations. The application is processed by Rwanda Housing Authority.", "Authorized9": "Rwanda Housing Authority", "Period9": "Valid for only 12hours", "Price9": "Vary", "Cancelbtn9": "Cancel", "Applybtn9": "Apply", "Category10": "Refurbishment of Existing Building without Structural alteration", "Category10Description10Title10": "About this Service", "Category10Description10": "This service allows users to apply for the refurbishment of existing building without structural alterations. The application is processed by Rwanda Housing Authority.", "Authorized10": "Rwanda Housing Authority", "Period10": "Valid for only 12hours", "Price10": "Vary", "Cancelbtn10": "Cancel", "Applybtn10": "Apply"}, "FAQs": {"Title": "Frequent Asked Questions", "subTitle": "Read the most frequently asked questions here and how to solve them", "questionOwner": {"1": "How do I use the system as a land owner?", "2": "What categories are assigned to engineers and architects?", "3": "How long does it take for a permit application to be processed?", "4": "What is the duration of a permit, and can it be renewed?"}, "descriptionOwner": {"1": "To use the system as an owner, you need to create an account and log in. You must have your national ID or passport.", "2": "All applications in any category can be submitted by engineers and architects.", "3": "The permit application duration is 21 days in COK OSC and 30 days in other OSC.", "4": "The initial new construction permit duration is two year, and it can be renewed."}, "questionEngineer": {"1": "How do I use the system as an engineer?", "2": "How can I access a permit application as an engineer?", "3": "As an engineer do I have to renew my license every year?"}, "descriptionEngineer": {"1": "As an engineer, you need to create an account and log in using your license number.", "2": "Owners will assign you to projects. You will receive notifications and can either accept or reject the assignment.", "3": "Engineers need to check with the Engineer Association for each renewal."}, "questionArchitect": {"1": "How do I use the system as an architect?", "2": "How can I access a permit application as an architect?", "3": "As an architect do you I have to renewal my license every year ?"}, "descriptionArchitect": {"1": "Architects should create an account and log in using their license number.", "2": "Owners will assign you to projects. You will receive notifications and can either accept or reject the assignment.", "3": "Architects need to check with the Architect Association for each renewal."}}, "footer": "All rights reserved.", "footer2": "Powered by Tech Avenue 137", "permitTypeInformation": {"applicationType1": "New Construction Permit", "desc1": "This service allows Users to apply for a New Construction Permit. The application is processed by Rwanda Housing Authority.", "authorized1": "Rwanda Housing Authority", "period1": "Valid for only 2 year", "price1": "Vary", "applicationType2": "Inspection Notice", "desc2": "This service allows users to apply for an Inspection Notice of their buildings. The application is processed by Rwanda Housing Authority.", "authorized2": "Rwanda Housing Authority", "period2": "Valid for only 12 hours", "price2": "Vary", "applicationType3": "Renewal of Permit", "desc3": "This service allows users to apply for the renewal of their building permits. The application is processed by Rwanda Housing Authority.", "authorized3": "Rwanda Housing Authority", "period3": "Valid for only 12 hours", "price3": "Vary", "applicationType4": "Construction of Fence", "desc4": "This service allows users to apply for a construction of fence permit. The application is processed by Rwanda Housing Authority.", "authorized4": "Rwanda Housing Authority", "period4": "Valid for only 12 hours", "price4": "Vary", "applicationType5": "Demolition - Full / Partial", "desc5": "This service allows users to apply for a demolition permit, whether for full or partial demolition. The application is processed by Rwanda Housing Authority.", "authorize5": "Rwanda Housing Authority", "period5": "Valid for only 12 hours", "price5": "Vary", "applicationType6": "Occupancy Permit", "desc6": "This service allows users to apply for an occupancy permit. The application is processed by Rwanda Housing Authority.", "authorized6": "Rwanda Housing Authority", "period6": "Valid for only 12 hours", "price6": "Vary", "applicationType7": "Project Modification", "desc7": "This service allows users to apply for project modification. The application is processed by Rwanda Housing Authority.", "authorized7": "Rwanda Housing Authority", "period7": "Valid for only 12 hours", "price7": "Vary", "applicationType8": "Temporary Structure Authorization", "desc8": "This service allows users to apply for Temporary Structure Authorization Applications. The application is processed by Rwanda Housing Authority.", "authorized8": "Rwanda Housing Authority", "period8": "Valid for only 12 hours", "price8": "Vary", "applicationType9": "Refurbshiment of existing building with structural alteration", "desc9": "This service allows users to apply for the refurbishment of existing building with structural alterations. The application is processed by Rwanda Housing Authority.", "authorized9": "Rwanda Housing Authority", "period9": "Valid for only 12 hours", "price9": "Vary", "applicationType10": "Refurbshiment of existing building without structural alteration", "desc10": "This service allows users to apply for the refurbishment of existing building without structural alterations. The application is processed by Rwanda Housing Authority.", "authorized10": "Rwanda Housing Authority", "period10": "Valid for only 12 hours", "price10": "Vary"}, "loginPage": {"title": "<PERSON><PERSON>", "email": "Email", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "forgotPassword": "Forgot Password?", "createAccount": "Create Account", "backHome": "Back Home", "waitmsg": "Wait"}, "registerPage": {"title": "Applicant Registration", "accountType": "Account Type", "accountTypeOptions": {"placeholder": "Choose account type", "owner": "Land Owner", "engineer": "Engineer / Firm", "architect": "Architect / Firm"}, "proceedButton": "Proceed", "documentType": "Document Type", "documentTypeOptions": {"placeholder": "Choose document type", "nationalId": "National ID", "passport": "Passport"}, "nationalIdLabel": "National Identification Number", "passportLabel": "Passport Number", "licenseLabel": "License Number", "savingTxt": "Saving", "firstNameLabel": "First Name", "lastNameLabel": "Last Name", "emailLabel": "Email", "phoneLabel": "Phone Number", "emailPlaceholder": "Please enter your email address", "phonePlaceholder": "Enter phone number", "licensePlaceholder": "Enter your licence number", "passwordLabel": "Password", "confirmPasswordLabel": "Confirm Password", "dobLabel": "Date of Birth", "dobPlaceholder": "Select Date of Birth", "genderLabel": "Gender", "genderOptions": {"placeholder": "<PERSON><PERSON>", "female": "Female", "male": "Male"}, "consent": "I consent that the information provided are accurate to the best of my knowledge and belief.", "createAccountButton": "Create Account", "haveAccount": "Have an account?", "loginHere": "Login here", "orCancel": "Or Cancel", "backTxt": "Back", "backHomeTxt": "Back Home", "waitTxt": "Wait", "password": "Password"}, "registerFormMsg": {"emailRequired": "Email is required", "emailInvalid": "Please provide a valid email address", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordGeneral": "Password must contain at least one lowercase letter, one uppercase letter, and one digit", "passwordGeneral2": "Password Must contain below conditions", "passwordMaxLength": "Password must not exceed 20 characters"}, "resetPasswordPage": {"title": "Reset Password", "emailLabel": "Email", "emailPlaceholder": "Please enter your email address", "sendResetLinkButton": "Send Reset Link", "backToLogin": "Back to Login", "waitTxt": "Wait", "rememberPassword": "Wait, I remembered my password", "loginHere": "Click here"}, "errorNotification": {"successMsg": "Data saved successfully", "errorMsg": "Something went wrong during request"}, "guideLineHeader": "Application Process Guideline", "guideLineTitle_1": "Create a project", "guideLineContent_1": "A project and its details will be created based on your UPI and what is allowed to be built on your plot under Rwandan law.", "guideLineTitle_2": "Submit your application", "guideLineContent_2": "Your application needs to be submitted, and you need to make sure you don't have any unpaid taxes or EIA certificates.", "guideLineTitle_3": "Application under Review", "guideLineContent_3": "Upon receiving your application, a member of the OSC team will review it, and the reviewer will ensure everything complies with the law.", "guideLineTitle_4": "Invoice to be paid", "guideLineContent_4": "Invoices will be generated after the application is approved, and you must pay in order to receive the permit.", "guideLineTitle_5": "Permit generated", "guideLineContent_5": "Your permit will be available for download after payment has been made.", "accountHeaderLinksContainer": {"engineer": "Professional Engineers", "architect": "Licensed Architects"}, "accountHeaderMenu": {"dashboard": "Dashboard", "systemAdministrator": "System Administrator", "myProjects": "My Projects", "allApplications": "All Applications", "transferredPermits": "Transferred Permits", "myApplications": "My Applications", "myBox": "My Box", "projectRequests": "Project Requests", "invoices": "Invoices", "allInvoices": "All Invoices", "permits": "Permits", "allPermits": "App Permits", "reports": "Reports", "chat": "Cha<PERSON>"}, "accountHeaderUserDropDown": {"edit": "Edit Profile", "change": "Change Password", "logout": "Logout"}, "dashboardComponent": {"welcomeMsg": "Welcome", "loadingTxt": "Loading", "plotsTitle": "My plots under <PERSON><PERSON><PERSON>", "viewMap": "View on Map", "plotLocation": "Plot Location", "plotUPI": "UPI", "plotUse": "Plot Use", "findApplication": "Find application", "applyPermitInstruction": "To apply for a permit, click here", "allApplication": "All Applications", "draftApplications": "Draft Applications", "reviewedApplications": "Reviewed Applications", "submittedApplications": "Submitted Applications", "permitted": "Permitted", "allPermitted": "All Permitted", "rejectedApplications": "Rejected Applications", "rejected": "Rejected", "underReview": "Under Review", "underCorrection": "Under Correction", "resubmitted": "Resubmitted", "preApproval": "Pre approval", "nonObjectionReturned": "Non objection returned", "certified": "Certified", "returnedApplication": "Returned Application", "nonObjectionReviewed": "Non objection Reviewed", "nonObjectionUnderReview": "Non objection under review", "nonObjectionApproved": "Non objection Approved", "nonObjectionRejected": "Non objection Rejected", "nonObjectionBackForCorrection": "Non objection back for correction", "submitted": "Submitted", "tasksTitle": "My tasks", "taskTitle": "My Task", "search": {"general": "General Search", "choose": "Which one do you want to choose"}, "rhaApplications": "RHA Applications", "agencyTxt": "in Agency of"}, "countryLevelDashboard": {"agency": "Agency", "allAgency": "All agency", "allApplications": "All Applications", "preApproval": "Pre approval", "reviewedApplications": "Reviewed Applications", "submittedApplications": "Submitted Applications", "permitted": "Permitted", "rejectedApplications": "Rejected Applications", "resubmitted": "Resubmitted", "underReview": "Under Review", "underCorrection": "Under Correction", "nonObjectionReturned": "Non objection returned"}, "analyticsDashboard": {"title": "Dashboard", "filterbyYear": "Filter by year", "filterbyAgency": "Filter by agency", "filter": "Filter"}, "chatMessageComponent": {"messageTxt": "Message", "users": "Users", "applicants": "Applicants", "received": "Received", "sent": "<PERSON><PERSON>", "sPlaceholder": "Search for something...", "sApplicantPlaceholder": "Search for applicant..."}, "projectComponent": {"welcomeMsg": "Welcome", "projects": "Projects", "searchForSomething": "Search for something...", "newPermitApplication": "New permit application", "plotSize": "<PERSON><PERSON>", "projectName": "Project name", "projectDescription": "Project description", "selectedCategoryUse": "Selected Category Use", "selectedUse": "Selected Use", "province": "Province", "district": "District", "sector": "Sector", "projectDetails": "Project Details", "removeEngineerArchitect": "Remove Engineer/Architect", "noRecordsFound": "No records found", "verification": "Verification", "verificationDetails": "We will check if you have unpaid taxes, look in the old BPMIS for your project, and pull your zoning plan.", "confirmRemoveEngineerArchitect": "You are about to remove engineer/architect from project", "removingEngineerArchitect": "Removing engineer/architect from your project", "close": "Close", "yesDelete": "Yes, Delete", "removing": "Removing...", "status": "Status"}, "applicantApplication": {"welcomeMsg": "Welcome", "myApplications": "My applications", "searchForSomething": "Search for something...", "applicationStatus": "Application Status", "newPermitApplication": "New permit application", "permitType": "Permit type", "applicationNo": "Application No", "projectName": "Project Name", "buildType": "Build Type", "siteLocation": "Site Location", "createdOn": "Created on", "submittedDate": "Submitted date", "applicationDetails": "Application Details", "relatedApplication": "Related Application", "pay": "Pay", "viewPermit": "View Permit", "viewComments": "View Comments", "delete": "Delete", "noRecordsFound": "No records found", "confirmDeleteApplication": "You are about to delete an application", "deleteWarning": "Deleting your application will remove all of your information from our database.", "close": "Close", "yesDelete": "Yes, Delete", "deletingApplication": "Deleting application", "verification": "Verification", "verificationDetails": "We will check if you have unpaid taxes, look in the old BPMIS for your project, and pull your zoning plan.", "applicationComment": "Application comment", "approvalLevel": "Approval Level", "status": "Status", "date": "Date", "comment": "Comment"}, "applicantInvoices": {"welcomeMsg": "Welcome", "myInvoices": "My invoices", "search": "Search for something...", "fromDate": "From date", "toDate": "To date", "pageSize": "Page size", "invoiceItem": "Invoice Item", "invoiceNo": "Invoice No", "amount": "Amount", "transactionNumber": "Transaction Number", "createdOn": "Created on", "dueDate": "Due date", "applicationName": "Application Name", "more": "More", "viewAndPay": "View and pay", "view": "View", "cancel": "Cancel", "refund": "Refund", "update": "Update", "invoiceExtension": "Invoice Extension", "confirmRefund": "You are about to refund", "close": "Close", "yesRefund": "Yes, Refund", "confirmCancel": "You are about to cancel", "yesDelete": "Yes, Delete", "updateInvoice": "Update invoice", "chooseInvoicePrices": "Choose Invoice Prices", "getPrice": "Get price", "updating": "Updating", "askForExtension": "Ask for extension", "expirationDate": "Expiration Date", "submit": "Submit"}, "applicantPermits": {"welcomeMsg": "Welcome", "permits": "Permits", "searchForSomething": "Search for something…", "fromDate": "From date", "toDate": "To date", "pageSize": "<PERSON>", "invoiceNo": "Invoice No", "permitNo": "Permit No", "applicationNo": "Application No", "permitType": "Permit Type", "agencyCode": "Agency Code", "issuedOn": "Issued On", "expiryDate": "Expiry Date", "status": "Status", "waitingForPayment": "Waiting for payment", "generated": "Generated", "permitStatus": "Permit Status", "transfer": "Transfer", "cancel": "Cancel", "transferOwnership": "Transfer Ownership", "documentType": "Document Type", "selectDocumentType": "Select document type", "documentNumber": "Document Number (National ID/ Passport)", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "close": "Close", "submit": "Submit", "confirmCancelPermit": "You are about to cancel permit with this number", "yesDelete": "Yes, Delete"}, "editProfile": {"header": "Personal Info", "title": "Edit profile", "firstName": "First name", "lastName": "Last name", "email": "Email", "phoneNumber": "Phone number", "cancel": "Cancel", "update": "Update", "updating": "Updating"}, "changePassword": {"title": "Change Password", "currentPwd": "Current Password", "newPwd": "New Password", "pwdRequired": "Password is required", "pwdMinLength": "Pwd must be at least 8 chars", "pwdConditions": "Password must contain at least one lowercase letter, one uppercase letter, and one digit", "pwdRules": "Pwd must follow below conditions", "minUpper": "Min 1 Uppercase Letter", "minLower": "Min 1 Lowercase Letter", "minSpecial": "Min 1 Special Character", "minNumber": "Min 1 Number", "minChars": "Min 8 Chars", "maxChars": "Max 16 Chars", "confirmPwd": "Confirm Password", "pwdMismatch": "Passwords do not match", "submit": "Submit"}, "newApplication": {"step1": {"step": "Step", "plotInformation": "Plot information", "plotNumberUpi": "Plot Number / UPI", "upiDetails": "UPI Details", "representative": "Representative", "province": "Province", "district": "District", "sector": "Sector", "cell": "Cell", "village": "Village", "parcelDetails": "<PERSON><PERSON><PERSON>", "applyFor": "Apply for", "parcelLocation": "Parcel location", "coordinates": "Coordinates", "cancel": "Cancel", "next": "Next"}, "step2": {"step": "Step", "projectFoundOldSystem": "Your project already found in old system (BPMIS), complete the missing details and proceed", "projectDetails": "Project Details", "projectName": "Project Name", "projectDescription": "Project Description", "representativeFullName": "Representative Full Name", "representativeNationalId": "Representative National ID", "plotSizeSquareMeters": "<PERSON><PERSON> (In Square Meters)", "cannotContinueAssigned": "You cannot continue because project is already assigned to engineer/architect", "previous": "Previous", "update": "Update", "save": "Save", "andNext": "and Next", "next": "Next"}, "step3": {"step": "Step", "projectDetails": "Project Details", "checkAddAssociatedUpi": "Check to add associated UPI", "doItYourself": "Do you want to do it by yourself", "assignToEngineerArchitect": "Assign this application to the Engineer / Architect", "permitType": "Permit Type", "plotSize": "<PERSON><PERSON>", "combiningPlotSize": "Combining Plot Size", "buildingType": "Building Type", "projectCategory": "Project Category", "numberOfFloorGPlus": "Number of Floor / G+", "builtUpAreaSqMeters": "Built-up Area (In Square Meters)", "buildUpAreaRestriction": "Build Up Area cannot be greater than Plot Size.", "grossFloorArea": "Gross Floor Area", "numberOfParkingSpaces": "Number of parking spaces", "estimatedPriceDwellingRwf": "Estimated price of dwelling unit in RWF", "descriptionOperations": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "numberOfDwellingUnit": "Number of Dwelling Unit", "estimatedMonthlyWaterM3": "Estimated monthly water consumption (m3)", "estimatedMonthlyElectricityWatts": "Estimated Monthly Electricity Consumption (watts)", "distanceNearestLandLineFiberM": "Distance to the nearest Land Line/ optic fiber cable (m)", "estimatedProjectCostUsd": "Estimated project cost in USD", "estimatedProjectCostRwf": "Estimated project cost in RWF", "capacityInfoPeopleSeats": "Capacity Information: Number of people / seats", "technologySurveyOptional": "Technology Survey (Optional)", "previous": "Previous", "next": "Next", "cannotContinueAssigned": "You cannot continue because project is already assigned to engineer/architect", "associatedUpi": "Associated UPI"}, "step4": {"associatedUpi": "Associated UPI", "step": "Step", "projectAttachments": "Project attachments", "requiredDocuments": "Required Documents", "downloadBoqFile": "Click here to download the BOQ file", "boqFileInstruction": "The BOQ file is in Excel format. Make sure you download it and fill in your details. For other files only PDF are allowed.", "uploadedDocuments": "Uploaded Documents", "allowedFileTypes": "Only PDF files are allowed. Except for BoQ.", "upload": "Upload", "view": "View", "wait": "Wait", "noFile": "no file", "remove": "Remove", "previous": "Previous", "resubmit": "Resubmit", "submit": "Submit", "confirmDelete": "You are about to delete a", "deletingYour": "Deleting your", "deleteWarning": "will remove all of your information from our database.", "close": "Close", "yesDelete": "Yes, Delete", "applicationDeletingFile": "Application is deleting file", "viewDocument": "View Document", "confirm": "Confirm", "certifyTruth": "It is my solemn duty to certify the truth of the information submitted on this application and I agree to the terms and conditions.", "cancel": "Cancel"}, "associatedUpi": {"title": "Combining Plot size", "search": "Search for something...", "new": "New", "owner": "Owner", "ownerId": "Owner ID", "plotSize": "Plot size", "remove": "Remove", "noRecordsFound": "No records found", "cancel": "Cancel", "saving": "Saving", "save": "Save"}, "assignToEngineer": {"header": "En raison de sa catégorie, ce projet doit être attribué à un ingénieur/architecte. Veuillez sélectionner et rechercher", "userType": "Type d'utilisateur", "engineerArchitectLicenseNumber": "Numéro de licence de l'ingénieur/architecte", "names": "Noms", "daysNeededForSubmission": "Jours nécessaires pour la soumission", "wait": "<PERSON><PERSON><PERSON>", "assign": "Attribuer"}}, "viewPlot": {"title": "Parcel location", "back": "Back", "parcelDetails": "<PERSON><PERSON><PERSON>", "province": "Province", "district": "District", "sector": "Sector", "cell": "Cell", "village": "Village", "coordinates": "Coordinates"}, "applicationDetails": {"title": "Applications", "applicationsDetails": "Applications details", "clickToContinue": "Click to continue", "back": "Back", "owners": "Owners", "parcelDetails": "<PERSON><PERSON><PERSON>", "appliedFor": "Applied for", "province": "Province", "district": "District", "sector": "Sector", "cell": "Cell", "village": "Village", "projectDetails": "Project Details", "projectCategory": "Project Category", "buildingType": "Building Type", "permitType": "Permit Type", "projectBrief": "Project Brief", "projectEstimatesDetails": "Project Estimates Details", "estimatedMonthlyWaterM3": "Estimated monthly water consumption (m3)", "distanceNearestLandLineFiberM": "Distance to the nearest Land Line/ optic fiber cable (m)", "estimatedMonthlyElectricityKwh": "Estimated monthly electricity consumption in Kwh", "estimatedProjectCostUsd": "Estimated project cost in USD", "estimatedProjectCostRwf": "Estimated project cost in Rwf", "developmentDetails": "Development Details", "plotSizeSquareMeters": "Plot size (In Square Meters)", "combinedPlotSizeSquareMeters": "Combined Plot size (In Square Meters)", "grossFloorArea": "Gross floor area", "proposedNumberOfFloorsGPlus": "Proposed Number of floors / G+", "numberOfParkingSpaces": "Number of parking spaces", "priceOfDwellingUnit": "Price of dwelling unit", "builtUpArea": "Built-up area", "capacityInfoPeopleSeats": "Capacity information: number of people / seats", "submittedBy": "Submitted By", "names": "Names", "phoneNumber": "Phone number", "userType": "User Type", "projectAttachmentDetails": "Project Attachment Details", "viewDocument": "View Document", "loading": "Loading", "projectAttachmentAdditionalFiles": "Project Attachment Additional files", "associatedUpi": "Associated UPI"}, "otherApplicationInfo": {"title": "Other Information", "occupancyQuestion": "Do You Have The Occupancy?", "firstAidBoxes": "First Aid boxes?", "disabilityGrabBars": "Disability toilets flip-up grab bars?", "paraLightningSystem": "Para-lightening system", "equipmentCapacity": "Equipment Capacity", "constructionMethod": "Construction method (materials)", "disasterPrevention": "Disaster prevention", "fireAlarmSystem": "A fire alarm system with an alarm bell on each", "noFireAlarmSystem": "Why not fire alarm system with an alarm bell on each", "fireExtinguishers": "Fire extinguishers every 50m on each floor", "noFireExtinguishers": "Why not fire extinguishers every 50m on each floor", "exitSigns": "Functioning exit signs on each floor", "noExitSigns": "Why not Functioning exit signs on each floor", "emergencyExit": "An emergency exit on each floor", "floorPlan": "Floor plan on each level", "noFloorPlan": "Why not Floor plan on each level", "floorNumberSign": "Number sign on each floor", "noFloorNumberSign": "Why not Number sign on each floor", "elevatorFireSign": "Sign forbidding the use of elevators in case of fire", "noElevatorFireSign": "Why not Sign forbidding the use of elevators in case of fire", "helicopterLanding": "Landing space on top of the building, for helicopters to evacuate people in case there's a problem down: YES / NO (if not why)", "terrorAttacks": "Terror attacks", "cctvCameras": "CCTV cameras", "noCctvCameras": "CCTV cameras (if not why)", "metalDetectors": "Walk through and hand-held metal detect", "noMetalDetectors": "Walk through and hand-held metal detect (if not why)", "underSearchMirror": "Under search mirror", "noUnderSearchMirror": "Under search mirror (if not why)", "luggageScanners": "Luggage scanners", "noLuggageScanners": "Luggage scanners (if not why)", "emergencyContacts": "Plates indicating emergency response units, phone numbers", "noEmergencyContacts": "Plates indicating emergency response units, phone numbers (if not why)", "evacuationPlan": "Emergency evacuation plan", "noEvacuationPlan": "Emergency evacuation plan (if not why)", "securityManager": "Security manager and staff cameras", "noSecurityManager": "Security manager and staff cameras (if not why)", "internalComms": "An internal communication system", "noInternalComms": "An internal communication system (if not why)", "broadband": "Broadband (internet services)", "noBroadband": "Broadband (internet services) (if not why)", "accessCards": "Staff and visitor's access cards", "noAccessCards": "Staff and visitor's access cards (if not why)", "fixedLineApplication": "Application for fixed telephone line connection", "disabilityFacilities": "Facilities for persons with disabilities", "disabilityQuestion": "Are there any facilities for the disabled provided in this building (PWD sensitive)", "noDisabilityFacilities": "Are there any facilities for the disabled provided in this building (PWD sensitive) (if not why)", "inspectionDate": "Date for requested inspection", "userType": "Site Engineer/Architect (Choose User Type)", "licenseNumber": "License Number", "supervisingFirm": "Supervising firm site engineer/architect", "remarks": "Remarks", "constructionStage": "Stage of construction"}, "otherApplicationWithoutNCP": {"projectDetails": "Project Details", "projectBrief": "Project Brief", "estimatedMonthlyWaterM3": "Estimated monthly water consumption (m3)", "distanceNearestLandLineFiberM": "Distance to the nearest Land Line/ optic fiber cable (m)", "estimatedProjectCostUsd": "Estimated project cost in USD", "estimatedProjectCostRwf": "Estimated project cost in RWF", "estimatedMonthlyElectricityKwh": "Estimated monthly electricity consumption in Kwh", "developmentDetails": "Development Details", "plotSizeSquareMeters": "Plot size (In Square Meters)", "grossFloorArea": "Gross Floor Area", "proposedNumberOfFloorsGPlus": "Proposed Number of floors / G+", "numberOfParkingSpaces": "Number of parking spaces", "priceOfDwellingUnit": "Price of dwelling unit", "builtUpArea": "Built-up area", "capacityInfoPeopleSeats": "Capacity Information: Number of people / seats", "doItYourself": "Do you want to do it by yourself", "assignToEngineerArchitect": "Assign this application to the Engineer / Architect", "projectName": "Project name", "projectDescription": "Project Description", "permitType": "Permit Type", "plotSize": "<PERSON><PERSON>", "buildingType": "Building Type", "projectCategory": "Project Category", "numberOfFloorGPlus": "Number of Floor / G+", "builtUpAreaSqm": "Built-up Area (In Square Meters)", "buildUpAreaTooLarge": "Build Up Area cannot be greater than Plot Size", "priceDwellingUnitRwf": "Estimated price of dwelling unit in RWF", "descriptionOperations": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "numberOfDwellingUnits": "Number of Dwelling Unit", "estimatedMonthlyWaterM3Repeat": "Estimated monthly water consumption (m3)", "estimatedMonthlyElectricityWatts": "Estimated Monthly Electricity Consumption(watts)", "technologySurveyOptional": "Technology Survey (Optional)", "dataNotFoundEIA": "Data not found. Please submit your request to RDB for an Environmental Impact Access certificate!", "projectAlreadyAssigned": "You can not continue because the project is already assigned to the engineer/architect", "cancel": "Cancel", "next": "Next", "messageTitle": "Message", "message": "An application already exists. Please consider applying for a different one, or ensure that all required documents are uploaded. You can also upload any missing documents", "chooseAnotherPermit": "Choose another permit", "continueCheckingDocs": "Continue with checking document!"}, "otherApplicationPermitAnswer ": {"otherInformation": "Other Information", "doYouHaveTheOccupancy": "Do You Have The Occupancy?", "firstAidBoxes": "First Aid boxes?", "disabilityToilets": "Disability toilets flip-up grab bars?", "paraLighteningSystem": "Para-lightening system", "equipmentCapacity": "Equipment Capacity", "constructionMethod": "Construction method(materials)", "disasterPrevention": "Disaster prevention", "fireAlarmSystem": "A fire alarm system with an alarm bell on each", "whyNoFireAlarmSystem": "Why not fire alarm system with an alarm bell on each", "fireExtinguishers": "Fire extinguishers every 50m on each floor", "whyNoFireExtinguishers": "Why not fire extinguishers every 50m on each floor", "exitSigns": "Functioning exit signs on each floor", "whyNoExitSigns": "Why not Functioning exit signs on each floor", "emergencyExit": "An emergency exit on each floor", "whyNoEmergencyExit": "Why not An emergency exit on each floor", "floorPlan": "Floor plan on each level", "whyNoFloorPlan": "Why not Floor plan on each level", "numberSign": "Number sign on each floor", "whyNoNumberSign": "Why not Number sign on each floor", "elevatorSign": "Sign forbidding the use of elevators in case of fire", "whyNoElevatorSign": "Why not Sign forbidding the use of elevators in case of fire", "helicopterLanding": "Landing space on top of the building, for helicopters to evacuate people in case there's a problem down: YES / NO ( if not why)", "whyNoHelicopterLanding": "Landing space on top of the building, for helicopters to evacuate people in case there's a problem down ( if not why)", "terrorAttacks": "Terror attacks", "cctvCameras": "CCTV cameras", "whyNoCctvCameras": "CCTV cameras ( if not why)", "metalDetector": "Walk through and held metal detect", "whyNoMetalDetector": "Walk through and held metal detect ( if not why)", "underSearchMirror": "Under search mirror", "whyNoUnderSearchMirror": "Under search mirror ( if not why)", "luggageScanners": "Luggage scanners", "whyNoLuggageScanners": "Luggage scanners ( if not why)", "emergencyContactPlates": "Plates indicating emergency response units, phone numbers", "whyNoEmergencyContactPlates": "Plates indicating emergency response units, phone numbers ( if not why)", "evacuationPlan": "Emergency evacuation plan", "whyNoEvacuationPlan": "Emergency evacuation plan ( if not why)", "securityManagerCameras": "Security manager and staff cameras", "whyNoSecurityManagerCameras": "Security manager and staff cameras ( if not why)", "internalComms": "An internal communication system", "whyNoInternalComms": "An internal communication system ( if not why)", "broadband": "Broad band( internet services)", "whyNoBroadband": "Broad band( internet services) ( if not why)", "accessCards": "Staff and visitor's access cards", "whyNoAccessCards": "Staff and visitor's access cards ( if not why)", "fixedTelephone": "Application for fixed telephone line connection", "facilitiesForPWD": "Facilities for persons with disabilities", "facilitiesForDisabled": "Are there any facilities for the disabled provided in this building (PWD sensitive)", "whyNoFacilitiesForDisabled": "Are there any facilities for the disabled provided in this building (PWD sensitive) ( if not why)", "inspectionDate": "Date for requested inspection", "userType": "Site Engineer/Architect (Choose User Type)", "licenseNumber": "License Number", "supervisingFirm": "Supervising firm site engineer/architect", "remarks": "Remarks", "stageOfConstruction": "Stage of construction"}, "eiaFormChecker": {"checkingEIA": "Checking EIA certificate", "eiaApplyMessage": "Please click here to apply", "certificateNumber": "Certificate Number", "upi": "UPI"}, "assignApplicationDetail": {"parcelDetails": "<PERSON><PERSON><PERSON>", "plotNumberUPI": "PLOT Number / UPI", "projectName": "Project name", "plannedFor": "Planned for", "location": "Location", "applicationDetails": "Application Details", "requestFrom": "Request from", "plotSize": "<PERSON><PERSON>", "buildUpArea": "Build Up Area", "applicationDate": "Application Date", "rejectRequest": "Reject Request", "acceptRequest": "Accept Request"}, "assignedApplications": {"welcome": "Welcome!", "projectRequests": "Project Requests", "searchPlaceholder": "Search for something...", "filterByStatusSize": "Filter by Status size", "requestFrom": "Request from", "projectName": "Project Name", "upi": "UPI", "plotSize": "<PERSON><PERSON>", "timelineForSubmission": "Timeline for submission", "appliedFor": "Applied for", "applicationDate": "Application Date", "status": "Status", "viewOffer": "View Offer", "applyForPermit": "Apply for permit", "request": "Request", "applicationRequest": "Application request"}, "projectDetailContinue": {"complete": "Complete", "application": "Application", "projectInformation": "Project Information", "checkToAddAssociatedUpi": "Check to add associated UPI", "projectName": "Project Name", "projectDescription": "Project Description", "permitType": "Permit Type", "buildingType": "Building Type", "projectCategory": "Project Category", "plotSize": "<PERSON><PERSON> (In Square Meters)", "combiningPlotSize": "Combining Plot Size", "numberOfFloor": "Number of Floor / G+", "buildUpArea": "Built-up Area (In Square Meters)", "buildUpAreaError": "Build Up Area cannot be greater than Plot Size.", "grossFloorArea": "Gross Floor Area", "numberOfParkingSpaces": "Number of parking spaces", "estimatedPriceOfDwellingUnit": "Estimated price of dwelling unit in RWF", "numberOfDwellingUnits": "Number of Dwelling Unit", "descriptionOfOperations": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "estimatedWaterConsumption": "Estimated monthly water consumption (m3)", "estimatedElectricityConsumption": "Estimated Monthly Electricity Consumption(watts)", "distanceToLandline": "Distance to the nearest Land Line/ optic fiber cable(m)", "estimatedProjectCostUSD": "Estimated project cost in USD", "estimatedProjectCostRWF": "Estimated project cost in RWF", "capacityInformation": "Capacity Information: Number of people / seats", "selectedCategoryUse": "Selected Category Use", "selectedUse": "Selected Use", "isFromOldSystem": "Is application come from old system", "isUnderMortgage": "Is Under Mortgage", "isUnderRestriction": "Is Under Restriction", "cancel": "Cancel", "next": "Next", "associatedUpi": "Associated UPI"}, "continuePendingApplication": {"step": "Step", "developmentDetails": "Development Details", "oldSystemHeaderNote": "Your project already found in old system complete the missing details and proceed", "checkToAddAssociatedUpi": "Check to add associated UPI", "projectName": "Project Name", "projectDescription": "Project Description", "permitType": "Permit Type", "buildingType": "Building Type", "plotSize": "<PERSON><PERSON> (In Square Meters)", "buildUpArea": "Built-up Area (In Square Meters)", "buildUpAreaError": "Build Up Area cannot be greater than Plot Size.", "numberOfFloor": "Number of Floor / G+", "grossFloorArea": "Gross Floor Area", "numberOfParkingSpaces": "Number of parking spaces", "estimatedPriceOfDwellingUnitRWF": "Estimated price of dwelling unit in RWF", "numberOfDwellingUnits": "Number of Dwelling Unit", "descriptionOfOperations": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "estimatedWaterConsumption": "Estimated monthly water consumption (m3)", "estimatedElectricityConsumption": "Estimated Monthly Electricity Consumption(watts)", "distanceToLandline": "Distance to the nearest Land Line/ optic fiber cable(m)", "estimatedProjectCostUSD": "Estimated project cost in USD", "estimatedProjectCostRWF": "Estimated project cost in RWF", "capacityInformation": "Capacity Information: Number of people / seats", "selectedCategoryUse": "Selected Category Use", "selectedUse": "Selected Use", "isFromOldSystem": "Is application come from old system", "isUnderMortgage": "Is Under Mortgage", "isUnderRestriction": "Is Under Restriction", "cancel": "Cancel", "update": "Update", "next": "Next", "associatedUpi": "Associated UPI"}, "projectModification": {"title": "Project Modification", "projectName": "Project Name", "projectDescription": "Project Description", "plotSize": "<PERSON><PERSON> (In Square Meters)", "permitType": "Permit Type", "buildingType": "Building Type", "projectCategory": "Project Category", "builtUpArea": "Built-up Area (In Square Meters)", "builtUpAreaError": "Build Up Area cannot be greater than Plot Size.", "numberOfFloor": "Number of Floor / G+", "grossFloorArea": "Gross Floor Area", "numberOfParkingSpaces": "Number of parking spaces", "estimatedPriceDwellingRwf": "Estimated price of dwelling unit in RWF", "descriptionOfOperations": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "numberOfDwellingUnits": "Number of Dwelling Unit", "estimatedWaterConsumption": "Estimated monthly water consumption (m3)", "estimatedElectricityConsumption": "Estimated Monthly Electricity Consumption(watts)", "distanceToLandline": "Distance to the nearest Land Line/ optic fiber cable(m)", "estimatedProjectCostUsd": "Estimated project cost in USD", "estimatedProjectCostRwf": "Estimated project cost in RWF", "capacityInformation": "Capacity Information: Number of people / seats", "technologySurvey": "Technology Survey (Optional)", "cancel": "Cancel", "next": "Next"}, "relatedPermitType": {"step": "Step", "projectDetails": "Project Details", "addAssociatedUpi": "Check to add associated UPI", "doItYourself": "Do you want to do it by yourself", "assignToEngineer": "Assign this application to the Engineer / Architect", "permitType": "Permit Type", "plotSize": "<PERSON><PERSON>", "buildingType": "Building Type", "projectCategory": "Project Category", "numberOfFloor": "Number of Floor / G+", "builtUpArea": "Built-up Area (In Square Meters)", "builtUpAreaError": "Build Up Area cannot be greater than Plot Size.", "grossFloorArea": "Gross Floor Area", "parkingSpaces": "Number of parking spaces", "dwellingPriceRwf": "Estimated price of dwelling unit in RWF", "descriptionOfOperation": "Description of operations", "percentageSpaceUse": "Percentage Space Use", "numberOfDwellingUnits": "Number of Dwelling Unit", "waterConsumption": "Estimated monthly water consumption (m3)", "electricityConsumption": "Estimated monthly electricity consumption(watts)", "distanceToLandline": "Distance to the nearest Land Line/ optic fiber cable(m)", "projectCostUsd": "Estimated project cost in USD", "projectCostRwf": "Estimated project cost in RWF", "capacityInformation": "Capacity Information: Number of people / seats", "technologySurvey": "Technology Survey (Optional)", "assignConditionNote": "Select this option if you want to assign this project to an engineer/architect or continue on your own.", "submitEIARequestNote": "Data not found. Please submit your request to RDB for an Environmental Impact Access certificate!", "notAllowedToProceed": "You can not continue because project is already assigned to engineer/architect.", "previous": "Previous", "next": "Next", "associatedUpi": "Associated UPI"}, "applicationRoadMap": {"step": "Step"}, "otherApplicationDocument": {"associatedUpiTitle": "Associated UPI", "projectAttachmentTitle": "Project Attachments", "requiredTitle": "Required Documents", "requiredBoqLink": "Click here to download the BOQ file", "requiredWarning": "The BOQ file is in Excel format. Make sure you download it and fill in your details. For other files only PDF are allowed", "uploadedTitle": "Uploaded Documents", "upload": "Upload", "wait": "Wait...", "nofile": "no file", "delete": "Delete", "previous": "Previous", "saveDocument": "Save the document", "submit": "Submit", "resubmit": "Resubmit", "confirm": "Confirm", "certificationText": "It is my solemn duty to certify the truth of the information submitted on this application and I agree to the terms and conditions.", "cancel": "Cancel", "deleteFileTitle": "You are about to delete a", "deleteFileBodyA": "Deleting your ", "deleteFileBodyB": "will remove all of your information from our database.", "confirmDelete": "Yes, Delete!", "close": "Close"}, "additionalUploadFile": {"title": "Additional Documents", "uploadedDocuments": "Uploaded Documents", "additionalFile": "Additional File", "onlyPdfAllowed": "Only PDF files are allowed. Except for BoQ", "noFile": "no file", "upload": "Upload", "view": "View", "remove": "Remove", "viewDocumentTitle": "View Document", "youAreAboutToDelete": "You are about to delete a ", "descriptionA": "Deleting your ", "descriptionB": " will remove all of your information from our database.", "close": "Close", "confirm": "Yes, Delete!", "inProgress": "Application is deleting file...", "wait": "Wait..."}, "otherApplication": {"projectDetails": "Project Details", "doItYourself": "Do you want to do it by yourself", "assignToEngineerArchitect": "Assign this application to the Engineer / Architect", "projectBrief": "Project Brief", "estimatedMonthlyWaterM3": "Estimated monthly water consumption (m³)", "distanceToLandline": "Distance to the nearest Land Line/optic fiber cable (m)", "estimatedProjectCostUsd": "Estimated project cost in USD", "estimatedProjectCostRwf": "Estimated project cost in RWF", "estimatedMonthlyElectricityKwh": "Estimated monthly electricity consumption in Kwh", "developmentDetails": "Development Details", "plotSizeSquareMeters": "<PERSON><PERSON> (In Square Meters)", "proposedNumberOfFloorsGPlus": "Proposed Number of Floors / G+", "numberOfParkingSpaces": "Number of Parking Spaces", "priceOfDwellingUnit": "Price of Dwelling Unit", "builtUpArea": "Built-up Area", "grossFloorArea": "Gross Floor Area", "percentageSpaceUse": "Percentage Space Use", "numberOfDwellingUnits": "Number of Dwelling Unit", "descriptionOperations": "Description of Operations", "capacityInfoPeopleSeats": "Capacity Information: Number of People / Seats", "permitType": "Permit Type", "buildingType": "Building Type", "category": "Project Category", "upi": "UPI", "dataNotFoundEIA": "Data not found. Please submit your request to RDB for an Environmental Impact Access certificate!", "submitRdb": "Submit your request to RDB for EIA", "projectAlreadyAssigned": "You can not continue because the project is already assigned to the engineer/architect", "prev": "Previous", "next": "Next", "cancel": "Cancel", "chooseAnotherPermit": "Choose another permit", "continueCheckingDocs": "Continue with checking document!", "message": "Message", "applicationExists": "An application already exists. Please consider applying for a different one, or ensure that all required documents are uploaded. You can also upload any missing documents.", "loading": "Loading data ....."}, "projectAttachment": {"title": "Project Attachment", "requiredTitle": "Required Document", "requiredDocuments": "Required Documents", "choose": "<PERSON><PERSON>", "chooseUploadDocument": "Choose Upload Document", "uploadDocumentPdf": "Upload document.pdf", "upload": "Upload", "close": "Close", "delete": "Delete", "uploadedTitle": "Uploaded Documents", "deleteFileTitle": "You are about to delete a ", "deleteFileBodyA": "Deleting your ", "deleteFileBodyB": " will remove all of your information from our database.", "wait": "Wait...", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "certificationText": "It is my solemn duty to certify the truth of the information submitted on this application and I agree to the terms and conditions.", "cancel": "Cancel", "confirmDelete": "Yes, Delete!", "nofile": "no file", "requiredDocumentsList": "Required Documents", "uploadedDocumentsList": "Uploaded Documents", "requiredWarning": "The BOQ file is in Excel format. Make sure you download it and fill in your details. For other files only PDF are allowed", "requiredBoqLink": "Click here to download the BOQ file"}, "factorLogin": {"verify": "Verify", "confirm": "Confirm", "login": "<PERSON><PERSON>"}}