<div>
    <div class="step-panel">
      <div class="step-panel_header">
        <h2>Old {{upiInfo?.isFromOldSystem ? 'Your project already found in old system complete the missing details and proceed' : 'Development Details'}}</h2>
      </div>
      <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
        <div class="step-panel_body">
          <div class="form-set">
            <div class="form-input">
              <label>Project Name <span class="estrx">*</span></label>
              <div>
                <input type="text" id="projectName" name="projectName" formControlName="projectName" required>
              </div>
            </div>
            <div class="form-input">
              <label>Project Description</label>
              <div>
                <input type="text" id="projectDescription" name="projectDescription" formControlName="projectDescription"
                  required>
              </div>

            </div>
            <div class="form-input">
              <label>Plot Size (In Square Meters) <span class="estrx">*</span></label>
              <div>
                <input type="number" id="plotSize" name="plotSize"
                formControlName="plotSize" readonly required>
              </div>
            </div>
            
            <div class="form-input">
              <label>Technology Survey</label>
              <div>
                <select name="technologySurveyId" id="technologySurveyId" formControlName="technologySurveyId" required>
                  <option *ngFor="let op of technologySurveys" [value]="op.id">{{op.name}}</option>
                </select>
              </div>
            </div>
       
          </div>
        </div>
        <div class="step-panel_footer">
          <button (click)="onPrev()">Previous</button>
          <button type="submit" [disabled]="userForm.invalid">Next</button>
        </div>
      </form>
    </div>
  </div>
