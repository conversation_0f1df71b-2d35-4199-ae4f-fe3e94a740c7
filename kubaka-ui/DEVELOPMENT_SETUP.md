---
<<<<<<< HEAD
noteId: "ffe7bbb06cb611f0a2dfd351e4d1ec0f"
=======
noteId: "12f64a206cb011f0a2dfd351e4d1ec0f"
>>>>>>> bertin_dev
tags: []
---
# Angular Development Setup Guide

## Source Map Debugging Configuration

Your Angular application is now configured for optimal source map debugging. This allows you to click on components in the browser and navigate directly to the source code.

### Current Configuration

✅ **Source Maps Enabled**:

- TypeScript source maps are enabled in `tsconfig.json`
- Angular build source maps are configured for scripts, styles, and vendor chunks
- Development configuration is optimized for debugging

### How to Start Development Server

```bash
# Option 1: Use the new dev script (recommended)
npm run dev

# Option 2: Use ng serve directly
ng serve --configuration=development --source-map

# Option 3: Use the standard start script
npm start
```

### Browser Developer Tools Setup

#### Chrome DevTools (Recommended)

1. **Open Developer Tools**: Press `F12` or `Cmd+Option+I` (Mac) / `Ctrl+Shift+I` (Windows/Linux)
2. **Enable Source Maps**:

   - Go to Settings (gear icon) in DevTools
   - Under "Sources" section, ensure these are checked:
     - ✅ "Enable JavaScript source maps"
     - ✅ "Enable CSS source maps"
     - ✅ "Auto-prettify minified files"
3. **Workspace Setup** (Optional but recommended):

   - In the Sources panel, right-click on your project folder
   - Select "Add folder to workspace"
   - Choose your project root directory
   - This enables direct editing from DevTools

#### Firefox Developer Tools

1. **Open Developer Tools**: Press `F12` or `Cmd+Option+I` (Mac) / `Ctrl+Shift+I` (Windows/Linux)
2. **Enable Source Maps**:

   - Go to Settings (gear icon)
   - Under "Advanced settings", ensure:
     - ✅ "Show original sources" is enabled
     - ✅ "Source maps" is enabled

### Debugging Workflow

#### 1. Component Debugging

- Open DevTools → Sources panel
- Navigate to `webpack://` → `src/app/`
- Find your component files
- Set breakpoints directly in TypeScript files

#### 2. Template Debugging

- Use Angular DevTools extension (recommended)
- Install from Chrome Web Store: "Angular DevTools"
- Inspect component state and template bindings

#### 3. Service Debugging

- Set breakpoints in service methods
- Use `console.log()` for quick debugging
- Monitor network requests in Network tab

### Hot Reload Features

✅ **Live Reload**: Enabled - browser refreshes automatically on file changes
✅ **Source Maps**: Enabled - click components to go to source code
✅ **Development Mode**: Optimized for debugging (no minification)

### Troubleshooting

#### If source maps don't work:

1. **Check browser settings**:

   - Ensure source maps are enabled in DevTools settings
   - Clear browser cache and reload
2. **Verify Angular configuration**:

   ```bash
   # Check if development configuration is used
   ng serve --configuration=development
   ```
3. **Check TypeScript configuration**:

   - Ensure `"sourceMap": true` in `tsconfig.json`
   - Verify `tsconfig.app.json` extends the base config

#### If components don't update:

1. **Check file watchers**:

   ```bash
   # Restart the development server
   npm run dev
   ```
2. **Clear Angular cache**:

   ```bash
   ng cache clean
   ```

### Best Practices

1. **Use TypeScript strict mode** (already enabled)
2. **Set breakpoints in TypeScript files**, not generated JavaScript
3. **Use Angular DevTools extension** for component inspection
4. **Monitor console for errors** during development
5. **Use source maps for all debugging** - avoid debugging minified code

### File Structure for Debugging

```
src/
├── app/
│   ├── components/          # Your components
│   ├── services/           # Your services
│   ├── models/             # TypeScript interfaces/models
│   └── shared/             # Shared utilities
├── assets/                 # Static assets
└── styles.scss            # Global styles
```

### Development Commands

```bash
# Start development server with source maps
npm run dev

# Build for development
ng build --configuration=development

# Run tests
npm test

# Lint code
ng lint

# Generate component
ng generate component my-component

# Generate service
ng generate service my-service
```

### Browser Extensions (Recommended)

1. **Angular DevTools** - Official Angular debugging extension
2. **Redux DevTools** - If using state management
3. **React Developer Tools** - If using React components
4. **JSON Viewer** - For API response inspection

### Performance Tips

1. **Use production build for testing performance**:

   ```bash
   ng build --configuration=production
   ```
2. **Monitor bundle size**:

   ```bash
   ng build --stats-json
   ```
3. **Use Angular CLI analytics** (optional):

   ```bash
   ng analytics on
   ```

Your Angular application is now fully configured for optimal development with source map debugging!
noteId: "5c7e04a0688f11f085893ffef2371ddd"
tags: []


---
