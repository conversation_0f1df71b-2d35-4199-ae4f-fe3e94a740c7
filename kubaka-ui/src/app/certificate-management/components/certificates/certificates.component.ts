import { Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-certificates',
  templateUrl: './certificates.component.html',
  styleUrls: ['./certificates.component.scss']
})
export class CertificatesComponent {
  lists: any[] = [];
  currentUser: any = {};
  saveForm!: UntypedFormGroup;
  submitted: boolean = false;
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  cancelData: any = {};
  searchData: any = {};
  fullList: any[] = [];
  filteredList: any[] = [];


  constructor(
    public applicationService: ApplicationService,
    private sessionService: SessionService,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private modalService: NgbModal,
    public utilService: UtilService,
    private userService: UserMgtService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'certificate/certificate/applicantUserId/search?userId=' + this.currentUser.userId)
    //   .subscribe(
    //     data => {
    //       this.lists = data.items;
    //     }
    //   )
    this.loadList();
  }


  ngOnDestroy() {
    this.applicationService.searchTerm = '';
    this.userService.searchTerm = '';
  }


  onSearchByDateChange(date: string): void {
    // this.userService.filterByDate = date;
    // this.filterAndPaginate();
    if (this.searchData.dateFrom && this.searchData.dateTo) {
      const from = this.searchData.dateFrom ? new Date(this.searchData.dateFrom) : null;
      const to = this.searchData.dateTo ? new Date(this.searchData.dateTo) : null;

      if (from) {
        // Convert 'from' to UTC start of the day
        from.setHours(0, 0, 0, 0);
      }

      if (to) {
        // Convert 'to' to UTC end of the day
        to.setHours(23, 59, 59, 999);
      }
      if (to) {
        // Add one day to the 'to' date to include the entire day
        to.setDate(to.getDate() + 1);
      }

      const filtered = this.fullList.filter(item => {
        const createdAt = new Date(item.created_at);
        return (!from || createdAt >= from) && (!to || createdAt < to);
      });




      this.filteredList = filtered;
      this.totalRecords = filtered.length;
      this.startIndex = (this.page - 1) * this.pageSize + 1;
      this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
      if (this.endIndex > this.totalRecords) {
        this.endIndex = this.totalRecords;
      }
      this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    }
  }


  loadList() {
    if (this.currentUser.data.user.userType.code !== "STF") {
      this.applicationService
        // .findAllWithPath(environment.applicationUrl + 'certificate/certificate/applicantUserId/search?userId=' + this.currentUser.userId)
        // .findAllWithPath(environment.applicationUrl + 'certificate/certificate/user/' + this.currentUser.userId)
        .findAllWithPath(environment.applicationUrl + 'certificate/certificate/cert/' + this.currentUser.userId)
        .subscribe(
          (data) => {
            this.fullList = data;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
            // this.lists = data;
            // this.content = data;
            // setTimeout(() => {
            //   document.getElementById("elmLoader")?.classList.add("d-none");
            // }, 1200);
            // this.collectionSize = this.lists.length;
            // this.total = this.lists.length;
            // this.totalRecords = this.lists.length;
            // this.startIndex = (this.page - 1) * this.pageSize + 1;
            // this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
            // if (this.endIndex > this.totalRecords) {
            //   this.endIndex = this.totalRecords;
            // }
            // this.lists = this.lists.slice(
            //   this.startIndex - 1,
            //   this.endIndex
            // );
          });
    }
    else if (this.currentUser.data.user.userType.code === "STF" && this.currentUser?.data?.user?.role?.code == "ADM") {
      this.applicationService
        .findAllWithPath(environment.applicationUrl + 'certificate/certificate/')
        .subscribe(
          (data) => {
            this.fullList = data;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
          });
        }
            // this.lists = data;
            // this.content = data;
            // setTimeout(() => {
            //   document.getElementById("elmLoade

    else {
      this.applicationService
        .findAllWithPath(environment.applicationUrl + 'certificate/certificate/agency/' + this.currentUser.data.user.agency.code)
        .subscribe(
          (data) => {
            this.fullList = data;
            this.totalRecords = this.fullList.length;
            this.filterAndPaginate();
            // this.lists = data;
            // this.content = data;
            // setTimeout(() => {
            //   document.getElementById("elmLoader")?.classList.add("d-none");
            // }, 1200);
            // this.collectionSize = this.lists.length;
            // this.total = this.lists.length;
            // this.totalRecords = this.lists.length;
            // this.startIndex = (this.page - 1) * this.pageSize + 1;
            // this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
            // if (this.endIndex > this.totalRecords) {
            //   this.endIndex = this.totalRecords;
            // }
            // this.lists = this.lists.slice(
            //   this.startIndex - 1,
            //   this.endIndex
            // );
          });
    }
  }


  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    this.lists.forEach(element => {
      if (element.statusId === '1') {
        element.statusName = 'Valid'
      }
      if (element.statusId === '2') {
        element.statusName = 'Transfered'
      }
      if (element.statusId === '3') {
        element.statusName = 'Cancelled'
      }
    });
  }

  applyFilter(item: any): boolean {
    // if (!this.userService.searchTerm) return true;
    // const term = this.userService.searchTerm.toLowerCase();
    // return Object.values(item).some(val =>
    //   String(val).toLowerCase().includes(term)
    // );
    const term = this.applicationService.searchTerm ? this.applicationService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';

    const matchesTerm = term ? Object.values(item).some(val => String(val).toLowerCase().includes(term)) : true;
    const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

    return matchesTerm && matchesDate;
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  getPremiumData() {
    // this.filterAndPaginate();
    if (this.searchData.dateFrom && this.searchData.dateTo) {
      this.onSearchByDateChange('');
    } else {
      this.filterAndPaginate();
    }
  }
  changePageSize() {
    this.pageSize = (+this.pageSize);
    this.filterAndPaginate();
  }





  ngOnInit(): void {
    this.saveForm = this.formBuilder.group({
      documentNumber: ["", [Validators.required]],
      phoneNumber: ["", [Validators.required]],
      firstName: [""],
      documentTypeId: [""],
      certificateNumber: [""],
      lastName: [""],
      userId: [this.currentUser.userId],
    });
  }



  changeAndGetNamesByNationalId() {
    if (this.saveForm.value.documentTypeId === '1' &&
      this.saveForm.value.documentNumber && this.saveForm.value.documentNumber.length === 16) {
      this.applicationService.findAllWithPath(environment.nidaUrl + this.saveForm.value.documentNumber)
        .subscribe(
          data => {
            this.saveForm.controls['firstName'].setValue(data.surnames);
            this.saveForm.controls['lastName'].setValue(data.foreName);
          }
        )
    }
  }

  openModal(content: any, sizeParams: any, event: any) {
    this.cancelData = event;
    this.modalService.open(content, { size: sizeParams, centered: true });
  }


  // this.applicationService.findAllWithPath(environment.landAPI + this.upiFinder)
  //     .subscribe(
  //       data => {


  onSubmit() {
    // this.applicationService.saveAssetWithPath(this.saveForm.value, environment.applicationUrl + 'certificate/transfer')
    // .subscribe(
    //   data => {
    //     this.utilService.showNotification(NOTIFICATION_COLOR.success, "Certificate transfered successfully", "bottom", "center");
    //     this.cancel();
    //   },
    //   error => {

    //   }
    // )
    this.checkUpiOwner();

  }


  checkUpiOwner() {
    this.submitted = true;
    this.applicationService.findAllWithPath(environment.landAPI + this.cancelData.projects.upi)
      .subscribe(
        data => {
          if (this.saveForm.value.documentNumber === data.data.owners.idNo) {
            this.applicationService.saveAssetWithPath(this.saveForm.value, environment.applicationUrl + 'certificate/transfer')
              .subscribe(
                data => {
                  this.submitted = false;
                  this.utilService.showNotification(NOTIFICATION_COLOR.success, "Certificate transfered successfully", "bottom", "center");
                  this.cancel();
                },
                error => {
                  this.submitted = false;
                }
              )
          } else {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "Your national ID has not been assigned to this UPI, please contact the land center", " top", "right");
          }
        }, error => { this.submitted = false; }
      )
  }


  cancel() {
    this.modalService.dismissAll();
  }


  confirmCancel() {
    this.applicationService.patchWithPath(JSON.stringify({}), environment.applicationUrl + 'certificate/certificate/cancel/' + this.cancelData.id)
      .subscribe(
        data => {
          this.modalService.dismissAll();
          this.loadList();
          this.utilService.showNotification(NOTIFICATION_COLOR.success, data.message, "top", "right");
        }
      )
  }
}
