import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateApplicationDocumentDto {
  @ApiProperty({
    description: 'The field Url is required but will be set in code',
  })
  fileUrl?: string;

  @ApiProperty({ description: 'The field application id is required' })
  @IsNotEmpty({ message: 'application ID is required' })
  applicationid: string;

  @ApiProperty({ description: 'The field RequiredDocument id is required' })
  @IsNotEmpty({ message: 'required document ID is required' })
  RequiredDocumentid: string;

  @ApiProperty({
    description: 'The field fileName is required but will be set in code',
  })
  fileName?: string;

  @ApiProperty({ description: 'The field categoryType id is required' })
  @IsNotEmpty({ message: 'created at is required' })
  createdAt: Date;
}
