{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/applications/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/applications/tsconfig.app.json"}, "projects": {"common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "applications": {"type": "application", "root": "apps/applications", "entryFile": "main", "sourceRoot": "apps/applications/src", "compilerOptions": {"tsConfigPath": "apps/applications/tsconfig.app.json"}}, "documents": {"type": "application", "root": "apps/documents", "entryFile": "main", "sourceRoot": "apps/documents/src", "compilerOptions": {"tsConfigPath": "apps/documents/tsconfig.app.json"}}, "notifications": {"type": "application", "root": "apps/notifications", "entryFile": "main", "sourceRoot": "apps/notifications/src", "compilerOptions": {"tsConfigPath": "apps/notifications/tsconfig.app.json"}}, "payments": {"type": "application", "root": "apps/payments", "entryFile": "main", "sourceRoot": "apps/payments/src", "compilerOptions": {"tsConfigPath": "apps/payments/tsconfig.app.json"}}, "auth": {"type": "application", "root": "apps/auth", "entryFile": "main", "sourceRoot": "apps/auth/src", "compilerOptions": {"tsConfigPath": "apps/auth/tsconfig.app.json"}}, "integrations": {"type": "application", "root": "apps/integrations", "entryFile": "main", "sourceRoot": "apps/integrations/src", "compilerOptions": {"tsConfigPath": "apps/integrations/tsconfig.app.json"}}}, "monorepo": true, "root": "apps/applications"}