<div class="stepper-content">
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <div class="form-header">
                        <h2 class="display-md">{{"changePassword.title" | translate }}</h2>
                    </div>
                </div>
                <form class="form" [formGroup]="saveForm" (ngSubmit)="onSubmit()" autocomplete="off">
                    <div class="step-panel_body">
                        <div class="form-set">
                            <div class="form-input">
                                <label>{{"changePassword.currentPwd" | translate }}</label>
                                <div>
                                    <input class="flg" type="password" id="currentPassword" name="currentPassword"
                                        formControlName="currentPassword" placeholder="**********" required />
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"changePassword.newPwd" | translate }}</label>
                                <div>
                                    <input class="flg" type="password" name="newPassword" formControlName="newPassword"
                                        placeholder="**********" required />
                                    <div *ngIf="
              saveForm.get('newPassword')?.invalid &&
              saveForm.get('newPassword')?.touched
            ">
                                        <small *ngIf="
                saveForm.get('newPassword')?.hasError('required')
              " class="text-danger">{{"changePassword.pwdRequired" | translate }}</small>
                                        <small *ngIf="
                saveForm.get('newPassword')?.hasError('minlength')
              " class="text-danger">{{"changePassword.pwdMinLength" | translate }}</small>
                                        <small *ngIf="
                saveForm.get('newPassword')?.hasError('pattern')
              " class="text-danger"> {{"changePassword.pwdConditions" | translate }} </small>
                                    </div>
                                    <div *ngIf="
              saveForm.controls['newPassword'].hasError('pattern')
            ">
                                        <span class="text-danger">{{"changePassword.pwdRules" | translate }}.</span>
                                        <ul>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'uppercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'uppercase'
                  )
                }"> {{"changePassword.minUpper" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'lowercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'lowercase'
                  )
                }"> {{"changePassword.minLower" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'special-character'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'special-character'
                  )
                }"> {{"changePassword.minSpecial" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'number'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'number'
                  )
                }"> {{"changePassword.minNumber" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'length'
                  )
                }"> {{"changePassword.minChars" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['newPassword'],
                    'length'
                  )
                }"> {{"changePassword.maxChars" | translate }}. </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="form-input">
                                <label>{{"changePassword.confirmPwd" | translate }}</label>
                                <div>
                                    <input class="flg" type="password" name="confirmPassword"
                                        formControlName="confirmPassword" placeholder="**********" required />
                                    <div *ngIf="
              saveForm.get('confirmPassword')?.invalid &&
              saveForm.get('confirmPassword')?.touched
            ">
                                        <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('required')
              " class="text-danger">{{"changePassword.pwdRequired" | translate }}</small>
                                        <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('minlength')
              " class="text-danger">{{"changePassword.pwdMinLength" | translate }}</small>
                                        <small *ngIf="
                saveForm.get('confirmPassword')?.hasError('pattern')
              " class="text-danger"> {{"changePassword.pwdConditions" | translate }} </small>
                                    </div>
                                    <div *ngIf="
              saveForm.controls['confirmPassword'].hasError('pattern')
            ">
                                        <span class="text-danger">{{"changePassword.pwdRules" | translate }}.</span>
                                        <ul>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'uppercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'uppercase'
                  )
                }"> {{"changePassword.minUpper" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'lowercase'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'lowercase'
                  )
                }"> {{"changePassword.minLower" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'special-character'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'special-character'
                  )
                }"> {{"changePassword.minSpecial" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'number'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'number'
                  )
                }"> {{"changePassword.minNumber" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  )
                }"> {{"changePassword.minChars" | translate }}. </li>
                                            <li [ngClass]="{
                  'text-success': utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  ),
                  'text-danger': !utilService.checkValidations(
                    saveForm.controls['confirmPassword'],
                    'length'
                  )
                }"> {{"changePassword.maxChars" | translate }}. </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-1" *ngIf="saveForm.hasError('notMatched')">
                        <mat-error *ngIf="saveForm.hasError('notMatched')">
                            <span class="danger">{{"changePassword.pwdMismatch" | translate }}.</span>
                        </mat-error>
                    </div>
                    <div class="step-panel_footer">
                        <button class="kbk-btn kbk-btn-main" type="submit" [disabled]="saveForm.invalid">{{"changePassword.submit" | translate }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>