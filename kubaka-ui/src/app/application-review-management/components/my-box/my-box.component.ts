import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../../application/services/application.service';
import { APIURLPATH } from 'src/app/shared/services/url-path';

import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-my-box',
  templateUrl: './my-box.component.html',
  styleUrls: ['./my-box.component.scss']
})
export class MyBoxComponent {
  @ViewChild("yourSelfContent") modalContent!: TemplateRef<any>;
  @ViewChild("isNewPermit") modalNewPermitContent!: TemplateRef<any>
  currentUser: any = {};
  applicationDetail: any = {
    filterByApplicationStatus: 'all', // set all by default
    filterByPermitType: 'all'
  };
  lists: any[] = [];
  allApplications: any[] = [];
  outputData: any = {};
  page = 1;
  pageSize = 50;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  total!: number;
  fullList: any[] = [];
  filteredList: any[] = [];
  paramsId: any;
  applicationStatuses: any[] = [];
  searchData: any = {};
  permitTypes: any[] = [];


  constructor(
    private sessionService: SessionService,
    private modalService: NgbModal,
    public userService: UserMgtService,
    private applicationService: ApplicationService,
    private router: Router,
    private route: ActivatedRoute,
    public utilService: UtilService,
    private appConfig: AppConfig
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;

    route.params.subscribe((params: any) => {
      this.paramsId = params.status;
    })
    this.loadList();

    this.applicationService.findAllWithPath(environment.applicationUrl +
      'application/applicationStatus')
      .subscribe(
        (response: any) => {
          this.applicationStatuses = response;
        }
      )


    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => { this.permitTypes = data; }
      )

  }

  // loadList() {
  //   // "DRCT"
  //   if (this.currentUser.data.user.role.code === "DRCT") {
  //     this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=PAPRV')
  //       .subscribe(
  //         codeData => {
  //           this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + codeData.items[0].id + '/agency/' + this.currentUser.data.user.agency.id)
  //             .subscribe(
  //               data => {
  //                 if (this.paramsId) {
  //                   this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId))
  //                 } else {
  //                   this.fullList = data;
  //                 }
  //                 this.fullList.forEach((element: any) => {
  //                   element.count = element.assignUsersFoReview?.length;
  //                   element.categoryTypeName = element.categoryTypes.name;
  //                   element.buildTypeName = element.buildTypes.name;
  //                   element.permitTypeName = element.permitTypes.name;
  //                   element.upi = element.projects.upi;
  //                 });
  //                 this.totalRecords = this.fullList.length;
  //                 this.filterAndPaginate();
  //               },
  //             )
  //         }
  //       )
  //   } else if (this.currentUser.data.user.role.code === 'TMLD') {
  //     this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=RVW')
  //       .subscribe(
  //         codeData => {
  //           this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + codeData.items[0].id + '/agency/' + this.currentUser.data.user.agency.id)
  //             .subscribe(
  //               data => {
  //                 if (this.paramsId) {
  //                   this.fullList = this.calculateAndAddDaysColumn(data.filter((item: any) => item.applicationStatus.code === this.paramsId))
  //                 } else {
  //                   this.fullList = data;
  //                 }
  //                 this.fullList.forEach((element: any) => {
  //                   element.count = element.assignUsersFoReview?.length;
  //                   element.categoryTypeName = element.categoryTypes.name;
  //                   element.buildTypeName = element.buildTypes.name;
  //                   element.permitTypeName = element.permitTypes.name;
  //                   element.upi = element.projects.upi;
  //                 });
  //                 this.totalRecords = this.fullList.length;
  //                 this.filterAndPaginate();
  //               },
  //             )
  //         }
  //       )
  //   } else if (this.currentUser.data.user.role.code === 'OFCMNG') {

  //     const agencyId = this.currentUser.data.user.agency.id;

  //     // Fetch both statuses in parallel
  //     forkJoin([
  //       this.applicationService.findAllWithPath(
  //         environment.applicationUrl + 'application/applicationStatus/code/search?search=SUB'
  //       ),
  //       this.applicationService.findAllWithPath(
  //         environment.applicationUrl + 'application/applicationStatus/code/search?search=RSMB'
  //       )
  //     ]).subscribe(([subStatus, rvwStatus]) => {
  //       const subStatusId = subStatus.items[0]?.id;
  //       const rvwStatusId = rvwStatus.items[0]?.id;

  //       // Fetch applications for both statuses
  //       forkJoin([
  //         this.applicationService.findAllWithPath(
  //           `${environment.applicationUrl}application/application/${subStatusId}/agency/${agencyId}`
  //         ),
  //         this.applicationService.findAllWithPath(
  //           `${environment.applicationUrl}application/application/${rvwStatusId}/agency/${agencyId}`
  //         )
  //       ]).subscribe(([subApplications, rvwApplications]) => {
  //         let combined = [...subApplications, ...rvwApplications];

  //         if (this.paramsId) {
  //           combined = combined.filter((item: any) => item.applicationStatus.code === this.paramsId);
  //         }

  //         this.fullList = this.calculateAndAddDaysColumn(combined);

  //         this.fullList.forEach((element: any) => {
  //           element.count = element.assignUsersFoReview?.length;
  //           element.categoryTypeName = element.categoryTypes.name;
  //           element.buildTypeName = element.buildTypes.name;
  //           element.permitTypeName = element.permitTypes.name;
  //           element.upi = element.projects.upi;
  //         });

  //         this.totalRecords = this.fullList.length;
  //         this.filterAndPaginate();
  //       });
  //     });

  //   } else if (this.currentUser.data.user.role.code === 'STF' ||
  //     this.currentUser.data.user.role.code === 'INSP') {
  //     this.applicationService.findAllWithPath(environment.applicationUrl +
  //       'application/reviewersOnApplication/myBox/allApplications/all/' + this.currentUser.userId)
  //       .subscribe(data => {
  //         this.allApplications = data;
  //         this.fullList = data;
  //         this.content = data;

  //         this.paginateLists();
  //       })
  //   } else
  //     if (this.currentUser.data.user.role.code === 'DINSP' || this.currentUser.data.user.role.code === 'INSP'
  //     ) {
  //       this.applicationService.findAllWithPath(environment.applicationUrl +
  //         // 'application/application/allData/inspection/' + this.currentUser.data.user.agency.id)
  //         'application/application/allData/inspection/reviewer/' + this.currentUser.userId)
  //         .subscribe(
  //           data => {
  //             this.fullList = data;
  //             this.fullList.forEach((element: any) => {
  //               element.count = element.assignUsersFoReview?.length;
  //               element.categoryTypeName = element.categoryTypes.name;
  //               element.buildTypeName = element.buildTypes.name;
  //               element.permitTypeName = element.permitTypes.name;
  //               element.upi = element.projects.upi;
  //             });
  //             this.totalRecords = this.fullList.length;
  //             this.filterAndPaginate();
  //           },
  //         )
  //     } else
  //       if (
  //         this.currentUser.data.user.role.code === 'SINSP'
  //       ) {
  //         this.applicationService.findAllWithPath(environment.applicationUrl +
  //           'application/application/allData/inspection/' + this.currentUser.data.user.agency.id)
  //           // 'application/application/allData/inspection/' + this.currentUser.userId)
  //           .subscribe(
  //             data => {
  //               this.fullList = data;
  //               this.fullList.forEach((element: any) => {
  //                 element.count = element.assignUsersFoReview?.length;
  //                 element.categoryTypeName = element.categoryTypes.name;
  //                 element.buildTypeName = element.buildTypes.name;
  //                 element.permitTypeName = element.permitTypes.name;
  //                 element.upi = element.projects.upi;
  //               });
  //               this.totalRecords = this.fullList.length;
  //               this.filterAndPaginate();
  //             },
  //           )
  //       }

  //   // DINSP || SINSP
  // }

  loadList() {
    const roleCode = this.currentUser.data.user.role.code;
    const agencyId = this.currentUser.data.user.agency?.id;
    const userId = this.currentUser.userId;
    const pageParam = `page=${this.page}`;
    const limitParam = `limit=${this.pageSize}`;
    const paginationParams = `?${pageParam}&${limitParam}`;

    if (roleCode === 'DRCT') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/applicationStatus/code/search?search=PAPRV`)
        .subscribe(codeData => {
          const statusId = codeData.items[0].id;
          this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/${statusId}/agency/${agencyId}${paginationParams}`)
            .subscribe(data => {
              const filtered = this.paramsId ? data.filter((item: any) => item.applicationStatus.code === this.paramsId) : data;
              this.processPaginatedData(data, data.total || data.length);
            });
        });
    }

    else if (roleCode === 'TMLD') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/applicationStatus/code/search?search=RVW`)
        .subscribe(codeData => {
          const statusId = codeData.items[0].id;
          this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/${statusId}/agency/${agencyId}${paginationParams}`)
            .subscribe(data => {
              const filtered = this.paramsId ? data.filter((item: any) => item.applicationStatus.code === this.paramsId) : data;
              this.processPaginatedData(data, data.total || data.length);
            });
        });
    }

    else if (roleCode === 'OFCMNG') {
      forkJoin([
        this.applicationService.findAllWithPath(`${environment.applicationUrl}application/applicationStatus/code/search?search=SUB`),
        this.applicationService.findAllWithPath(`${environment.applicationUrl}application/applicationStatus/code/search?search=RSMB`)
      ]).subscribe(([subStatus, rvwStatus]) => {
        console.log(subStatus);
        console.log(rvwStatus);
        const subStatusId = subStatus.items[0]?.id;
        const rvwStatusId = rvwStatus.items[0]?.id;

        forkJoin([
          this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/${subStatusId}/agency/${agencyId}${paginationParams}`),
          this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/${rvwStatusId}/agency/${agencyId}${paginationParams}`)
        ]).subscribe(([subApplications, rvwApplications]) => {
          console.log(subApplications);
          console.log(rvwApplications);
          let combined: any = [...subApplications.data, ...rvwApplications.data];
          if (this.paramsId) {
            combined = combined.filter((item:any) => item.applicationStatus.code === this.paramsId);
          }
          this.processPaginatedData(combined, combined.total || combined.length);
        });
      });
    }

    else if (roleCode === 'STF' || roleCode === 'INSP') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/reviewersOnApplication/myBox/allApplications/all/${userId}${paginationParams}`)
        .subscribe(data => {
          this.content = data;
          this.processPaginatedData(data, data.total || data.length);
        });
    }

    else if (roleCode === 'DINSP' || roleCode === 'INSP') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/allData/inspection/reviewer/${userId}${paginationParams}`)
        .subscribe(data => {
          this.processPaginatedData(data, data.total || data.length);
        });
    }

    else if (roleCode === 'SINSP') {
      this.applicationService.findAllWithPath(`${environment.applicationUrl}application/application/allData/inspection/${agencyId}${paginationParams}`)
        .subscribe(data => {
          this.processPaginatedData(data, data.total || data.length);
        });
    }
  }



  // processPaginatedData(data: any[], total: number) {
  //   this.content = this.calculateAndAddDaysColumn(data);
  //   this.fullList = this.calculateAndAddDaysColumn(data);

  //   this.fullList.forEach((element: any) => {
  //     element.count = element.assignUsersFoReview?.length;
  //     element.categoryTypeName = element.categoryTypes?.name;
  //     element.buildTypeName = element.buildTypes?.name;
  //     element.permitTypeName = element.permitTypes?.name;
  //     element.upi = element.projects?.upi;
  //   });

  //   // this.totalRecords = this.fullList.length;
  //   // this.filterAndPaginate();
  //   this.lists = this.fullList;
  //   this.totalRecords = total;
  //   this.startIndex = (this.page - 1) * this.pageSize + 1;
  //   this.endIndex = this.startIndex + this.lists.length - 1;
  // }
  processPaginatedData(data: any, total: number) {
  const items = Array.isArray(data) ? data : data?.data ?? [];

  this.content = this.calculateAndAddDaysColumn(items);
  this.fullList = this.calculateAndAddDaysColumn(items);

  this.fullList.forEach((element: any) => {
    element.count = element.assignUsersFoReview?.length;
    element.categoryTypeName = element.categoryTypes?.name;
    element.buildTypeName = element.buildTypes?.name;
    element.permitTypeName = element.permitTypes?.name;
    element.upi = element.projects?.upi;
  });

  this.lists = this.fullList;
  this.totalRecords = total;
  this.startIndex = (this.page - 1) * this.pageSize + 1;
  this.endIndex = this.startIndex + this.lists.length - 1;
}


  paginateLists() {
    if (this.applicationDetail.filterByApplicationStatus === 'all') {
      this.fullList = this.allApplications;
      this.fullList.forEach((element: any) => {
        element.count = element.assignUsersFoReview?.length;
        element.categoryTypeName = element.categoryTypes.name;
        element.buildTypeName = element.buildTypes.name;
        element.permitTypeName = element.permitTypes.name;
        element.upi = element.projects.upi;
      });
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    } else {
      this.fullList = this.content;
      this.changeByApplicationStatus();
    }

  }



  calculateAndAddDaysColumn(dates: any[]): any[] {
    const currentDate = new Date();
    return dates.map(dateObj => {
      if (dateObj.isCountingActive) {
        if (
          ((new Date(dateObj.reSubmittedDate).getTime())
            > (new Date(dateObj.submittedDate).getTime()))
        ) {
          const createdAt = new Date(dateObj.reSubmittedDate);
          const differenceInTime = currentDate.getTime() - createdAt.getTime();
          const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
          return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
        }

        else {
          const createdAt = new Date(dateObj.submittedDate);
          const differenceInTime = currentDate.getTime() - createdAt.getTime();
          const differenceInDays = Math.floor(differenceInTime / (1000 * 3600 * 24)); // Convert milliseconds to days
          return { ...dateObj, daysBetween: differenceInDays }; // Add daysBetween property
        }

      } else {
        return { ...dateObj, daysBetween: dateObj.numberOfDayItTakes };
      }
    });
  }

  changeByApplicationStatus() {
    if (this.applicationDetail.filterByApplicationStatus !== 'all') {
      this.fullList = this.content.filter((x: any) => x.projectStatusId === this.outputData.applicationStatusId);
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    } else {
      this.paginateLists();
    }

  }

  changeByPermitType() {
    if (this.applicationDetail.filterByPermitType !== 'all') {
      this.fullList = this.content.filter((x: any) => x.permitTypes.id === this.applicationDetail.filterByPermitType);
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    } else {
      this.paginateLists();
    }

  }

  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;
    this.startIndex = (this.page - 1) * this.pageSize + 1;
    this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  }

  applyFilter(item: any): boolean {
    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';

    const matchesTerm = term ? Object.values(item).some(val => String(val).toLowerCase().includes(term)) : true;
    const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

    return matchesTerm && matchesDate;
  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  // getPremiumData() {

  //   if (this.searchData.dateFrom && this.searchData.dateTo) {
  //     this.onSearchByDateChange('');
  //   } else if (this.userService.searchTerm) {
  //     this.onSearchTermChange(this.userService.searchTerm);
  //   } else {
  //     this.filterAndPaginate();
  //   }
  // }

  getPremiumData() {
    this.page = 1; // Always reset to first page
    this.loadList();
  }

  // onSearchByDateChange(date: string): void {

  //   if (this.searchData.dateFrom && this.searchData.dateTo) {
  //     const from = this.searchData.dateFrom ? new Date(this.searchData.dateFrom) : null;
  //     const to = this.searchData.dateTo ? new Date(this.searchData.dateTo) : null;

  //     if (from) {
  //       // Convert 'from' to UTC start of the day
  //       from.setHours(0, 0, 0, 0);
  //     }

  //     if (to) {
  //       // Convert 'to' to UTC end of the day
  //       to.setHours(23, 59, 59, 999);
  //     }
  //     if (to) {
  //       // Add one day to the 'to' date to include the entire day
  //       to.setDate(to.getDate() + 1);
  //     }

  //     const filtered = this.fullList.filter(item => {
  //       const createdAt = new Date(item.submittedDate);
  //       return (!from || createdAt >= from) && (!to || createdAt < to);
  //     });




  //     this.filteredList = filtered;
  //     this.totalRecords = filtered.length;
  //     this.startIndex = (this.page - 1) * this.pageSize + 1;
  //     this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
  //     if (this.endIndex > this.totalRecords) {
  //       this.endIndex = this.totalRecords;
  //     }
  //     this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
  //   }
  // }

  onSearchByDateChange(event: any): void {
    if (this.searchData.dateFrom && this.searchData.dateTo) {
      const from = new Date(this.searchData.dateFrom);
      const to = new Date(this.searchData.dateTo);

      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);

      // Optional: include the whole 'to' day
      const adjustedTo = new Date(to);
      adjustedTo.setDate(adjustedTo.getDate() + 1);

      const filtered = this.content.filter((item: any) => {
        const createdAt = new Date(item.submittedDate);
        return createdAt >= from && createdAt < adjustedTo;
      });

      this.fullList = this.calculateAndAddDaysColumn(filtered);
      this.totalRecords = this.fullList.length;
      this.filterAndPaginate();
    }
  }




  changePageSize() {
    this.pageSize = (+this.pageSize);
    this.filterAndPaginate();
  }


  getChoosenSelectedUse(event: any) {
    let existingUpiInApplication = this.lists.find((l: any) => l.projects.upi === event.upi);
    this.applicationDetail = event;
    if (existingUpiInApplication) {
      this.applicationDetail.isUpiExists = true;
    } else {
      this.applicationDetail.isUpiExists = false;
      this.goToApplication();
    }
  }


  isNewPermitM(event: any) {
    if (event === 'yes') {
      this.applicationDetail.isNewPermit = false;
      this.cancel();
      this.goToApplication();
    } else {
      this.applicationDetail.isNewPermit = true;
    }
  }

  goToApplication() {
    localStorage.setItem(this.appConfig.UPI_NEW_INFO, JSON.stringify(this.applicationDetail));
    this.router.navigate(['/account/application/new-application/0/0']);
  }





  rraIntegration() {

  }




  searchEngineerByLicenseNumber() {
    // search existing certificate number through this.applicationDetail.certificateNumber properties
    // then
    this.checkIsNewPermit();


    // this.cancel();
    // this.router.navigate(['/account/application/new-application/1/0']);
  }


  checkIsNewPermit() {
    this.cancel();
    this.modalService.open(this.modalNewPermitContent, { size: 'md', centered: true });
  }




  cancel() {
    this.modalService.dismissAll();
  }


  ngOnDestroy() {
    this.userService.searchTerm = '';
  }


  submitCertificateNumber() {
    this.goToApplication();
    // applicationDetail.isNewPermit
    // applicationDetail.certificateNumber
  }

  assignToUser(content: any, event: any, type: any) {
    this.outputData = {};
    this.outputData = event;
    this.outputData.isAssigningData = true;
    this.openModal(content, type);
  }

  openModal(content: any, sizeParams: any) {
    this.modalService.open(content, { size: sizeParams, centered: true });
  }



  closePopup(event: any) {
    if (event) {
      this.outputData = {};
      this.modalService.dismissAll();
      this.loadList();
    } else {
      this.modalService.dismissAll()
    }

  }


  viewCertificate(event: any) {
    if (event.statusId === '3') {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, `The certificate has been cancelled`, "top", "right");
    } else {
      this.router.navigate(['/account/certificate/certificate-detail/', event.id]);
    }

  }
}
