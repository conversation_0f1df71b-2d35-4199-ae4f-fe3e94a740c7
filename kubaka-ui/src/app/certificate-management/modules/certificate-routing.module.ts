import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CertificateDetailComponent } from '../components/certificate-detail/certificate-detail.component';
import { CertificatesComponent } from '../components/certificates/certificates.component';
import { TransferedCertificatesComponent } from '../components/transfered-certificates/transfered-certificates.component';



const routes: Routes = [
    { path: '', component: CertificatesComponent },
    { path: 'certificate-detail/:id', component: CertificateDetailComponent },
    { path: 'transfered-certificates', component: TransferedCertificatesComponent }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class CertificateRoutingModule { }
