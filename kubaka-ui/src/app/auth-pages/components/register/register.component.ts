import { Component } from '@angular/core';
import {
  FormBuilder, FormControl, FormGroup, FormGroupDirective,
  NgForm, Validators
} from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';

export class MyErrorStateMatcher implements ErrorStateMatcher {
  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    return !!(control && control.valid && (control.dirty || control.touched) && form?.hasError('notMatched'));
  }
}

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  accountTypeForm!: FormGroup;
  submitted = false;
  engineers: any[] = [];
  isChoosingAccountType: boolean = true;
  isRegisteringCredentials: boolean = false;
  // formProcess: any = {};
  accountTypes: any[] = [
    { id: '1', name: 'Land Owner' },
    { id: '2', name: 'Engineer / Firm' },
    { id: '3', name: 'Architect / Firm' }
  ];

  documentTypes = [
    { id: '1', name: 'National ID' },
    { id: '2', name: 'Passport' },

  ];

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private utilService: UtilService
  ) {

    this.authService.findAllWithPath(environment.integrationUrl + 'integration/engineer')
      .subscribe(
        data => {
          this.engineers = data;
        }
      )


    // this.isChoosingAccountType = true;

  }

  ngOnInit(): void {
    this.accountTypeForm = this.formBuilder.group({
      accountTypeId: ['', [Validators.required]],
      nid: [''], //spaceFormatValidator
      licenseNumber: ['', [Validators.required]],
      licenceIdArch: [''],
      licenceIdEng: [''],
      verified: [''],
      documentTypeId: [''],
      passport: [''],
      nidData: [{}],
      engineerData: [{}],
      archictData: [{}],
      phoneNumber: ['']
    });
  }


  cleanData() {
    if (this.accountTypeForm.value.documentTypeId === '2') {
      this.accountTypeForm.controls['nidData'].setValue({});
    }

  }

  // keepData(event: any) {
  //   // if (event) {
  //   this.accountTypeForm.controls['accountTypeId'].setValue(event && event.accountTypeId ? event.accountTypeId : "");
  //   this.accountTypeForm.controls['nid'].setValue(event && event.nid ? event.nid : "");
  //   this.accountTypeForm.controls['licenseNumber'].setValue(event && event.licenseNumber ? event.licenseNumber : "");

  //   //   this.submitted = false;
  //   this.registerDetails();
  //   // }
  // }

  keepData(event: any) {
    this.accountTypeForm.controls['phoneNumber'].setValue('');
    if (event) {
      this.accountTypeForm.patchValue({
        accountTypeId: event.accountTypeId,
        nid: event.nid,
        licenseNumber: event.licenseNumber || '',
      });

      this.submitted = false;
      this.isRegisteringCredentials = false;
      this.isChoosingAccountType = true;
    }

    // this.resetForm();
  }

  get f() { return this.accountTypeForm.controls; }


  // checkAccountType() {
  //   if (this.accountTypeForm.value.accountTypeId === '1') {
  //     this.accountTypeForm.controls['licenseNumber'].setValue(' ');
  //   } else {
  //     this.accountTypeForm.controls['nid'].setValue(' ');
  //   }

  //   if (this.accountTypeForm.value.accountTypeId === '3') {
  //     this.authService.findAllWithPath(environment.integrationUrl + 'integration/engineer')
  //       .subscribe(
  //         data => {
  //           this.engineers = data;
  //         }
  //       )
  //   }
  // }

  checkAccountType() {
    this.accountTypeForm.controls['phoneNumber'].setValue('');
    this.accountTypeForm.controls['documentTypeId'].setValue('');
    const accountType = this.accountTypeForm.get('accountTypeId')?.value;

    if (accountType === '1') {
      this.accountTypeForm.get('licenseNumber')?.disable();
      this.accountTypeForm.get('nid')?.enable();
    } else if (accountType === '2' || accountType === '3') {
      this.accountTypeForm.get('nid')?.disable();
      this.accountTypeForm.get('licenseNumber')?.enable();
    }
  }



  formatPhoneNumber(phoneNumber: string): string {
    // Check if the phone number starts with "0"
    if (!phoneNumber.startsWith('0')) {
      // Prepend "0" if it doesn't start with "0"
      return '0' + phoneNumber;
    }
    // Return the phone number as it is if it already starts with "0"
    return phoneNumber;
  }




  onSubmit() {
    if (this.accountTypeForm.invalid) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Fill all fields to proceed", "bottom", "center");
    } else if (this.accountTypeForm.value.documentTypeId === '1' && !this.accountTypeForm.value.nid) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "National ID is required", "bottom", "center");
    } else if (this.accountTypeForm.value.documentTypeId === '2' && !this.accountTypeForm.value.passport) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Passport is required", "bottom", "center");
    } else if (this.accountTypeForm.value.accountTypeId === '1' && !this.accountTypeForm.value.documentTypeId) {
      this.utilService.showNotification(NOTIFICATION_COLOR.error, "Document number is required ", "bottom", "center");
    } else {
      this.submitted = true;
      if (this.accountTypeForm.value.accountTypeId === '1') {
        if (this.accountTypeForm.value.documentTypeId === '1') {
          this.authService.saveAssetWithPath(null, environment.nidaUrl + this.accountTypeForm.value.nid)
            .subscribe(
              data => {
                this.isVerified();
                if (data) {
                  this.accountTypeForm.controls['nidData'].setValue(data.response);
                }
                this.registerDetails();
                this.submitted = true;
              }, error => {
                this.submitted = false;
                this.registerDetails();
              })

        } else {
          this.submitted = false;
          this.registerDetails();
        }
      } else if (this.accountTypeForm.value.accountTypeId === '2') {
        this.accountTypeForm.controls['licenceIdEng'].setValue(this.accountTypeForm.value.licenseNumber);
        let replacedString: string = '';
        if (this.accountTypeForm.value.licenseNumber.includes("/")) {
          // let replacedString: string = licenseNumberValue.replace(/\//g, "%2F");
          replacedString = this.accountTypeForm.value.licenseNumber.replace(/\//g, "%2F");
        } else {
          replacedString = this.accountTypeForm.value.licenseNumber;
        }
        // this.authService.findAllWithPath(environment.integrationUrl + 'integration/engineer/' + replacedString)

        this.authService.findAllWithPath(environment.integrationUrl + 'integration/engineer/{registrationNumber}?registrationNumber='
          + replacedString)
          .subscribe(
            data => {
              if (data) {
                if (data && !data.identificationNumber) {
                  this.utilService.showNotification(NOTIFICATION_COLOR.error, "National ID is not found, please ask for help from your professional institute", "bottom", "center");
                  this.submitted = false;
                } else if (data.identificationNumber && data.contactTelephoneNumber) {
                  this.isVerified();
                  this.accountTypeForm.controls['engineerData'].setValue(data);
                  this.submitted = false;
                  this.verifyPhoneAndNationalId({ phoneNumber: data.contactTelephoneNumber, nationalId: data.identificationNumber });
                  this.submitted = false;
                }
              } else {
                this.submitted = false;
              }
           
            }, error => {
              this.submitted = false;
            }
          )

      } else if (this.accountTypeForm.value.accountTypeId === '3') {
        this.accountTypeForm.controls['licenceIdArch'].setValue(this.accountTypeForm.value.licenseNumber);
        this.authService.findAllWithPath(environment.integrationUrl + 'integration/engineer')
          .subscribe(
            data => {
              this.submitted = false;
              if (data) {
                let findArchtc = this.engineers.find((x: any) =>
                  this.utilService.removeSpaceInString(x.value.registrationindex) ===
                  this.utilService.removeSpaceInString(this.accountTypeForm.value.licenseNumber)
                );


                if (findArchtc) {
                  this.isVerified();
                  this.accountTypeForm.controls['archictData'].setValue(findArchtc.value);
                  // check if value has national id
                  if (!findArchtc.value?.number) {
                    this.utilService.showNotification(NOTIFICATION_COLOR.error, "National ID is not found, please ask for help from your professional institute", "bottom", "center");
                    this.submitted = false;
                    this.registerDetails();
                    this.submitted = false;
                  } else {

                    this.verifyPhoneAndNationalId({ phoneNumber: findArchtc.value?.contacts, nationalId: findArchtc.value?.number });
                    this.submitted = false;
                  }
                  // this.registerDetails();
                } else {
                  // this.registerDetails();
                  this.utilService.showNotification(NOTIFICATION_COLOR.error, "License number not found", "bottom", "center");
                }
              } else {
                // this.registerDetails();
                this.submitted = false;
              }
            }, error => {
              // this.registerDetails();
              this.submitted = false;
            },
          )
      }
    }
  }

  verifyPhoneAndNationalId(retreiviedData: any) {
    // this.authService.findAllWithPath(environment.integrationUrl + 'integration/nida/' + this.formatPhoneNumber(retreiviedData.phoneNumber))
    //   .subscribe(data => {
    this.accountTypeForm.controls['phoneNumber'].setValue(this.formatPhoneNumber(retreiviedData.phoneNumber));
    // if (data.result.replace(/\s+/g, '') === retreiviedData.nationalId.replace(/\s+/g, '')) {
    this.registerDetails();
    // this.submitted = false;
    // } else {
    //   this.submitted = false;
    //   this.utilService.showNotification(NOTIFICATION_COLOR.error, "Phone number " + retreiviedData.phoneNumber + " is not registered to your national Id " + retreiviedData.nationalId, "top", "right");
    // }
    // })
  }


  registerDetails() {

    if (this.isRegisteringCredentials) {
      this.resetForm();
    }

    this.isRegisteringCredentials = !this.isRegisteringCredentials;
    this.isChoosingAccountType = !this.isChoosingAccountType;

  }
  // registerDetails() {
  //   // this.formProcess.isRegisteringCredentials = true;
  //   // this.formProcess.isChoosingAccountType = false;
  //   this.formProcess.isRegisteringCredentials = !this.formProcess.isRegisteringCredentials;
  //   this.formProcess.isChoosingAccountType = !this.formProcess.isChoosingAccountType;
  // }

  isVerified() {
    this.accountTypeForm.controls['verified'].setValue(true);
  }


  resetForm() {
    this.accountTypeForm.reset({
      accountTypeId: '',
      nid: '',
      licenseNumber: '',
      licenceIdArch: '',
      licenceIdEng: '',
      verified: '',
      documentTypeId: '',
      passport: '',
      nidData: {},
      engineerData: {},
      archictData: {}
    });
    this.submitted = false;
  }


}
