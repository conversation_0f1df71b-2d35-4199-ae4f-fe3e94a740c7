import { ChangeDetectorRef, Component, ElementRef, Input, ViewChild } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { jwtDecode } from 'jwt-decode';
import { AppConfig } from 'src/app/app.config';
import { SessionService } from 'src/app/authentication-services/session.service';
import { VALUE } from 'src/app/core/value-enum/value-enum';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { ApplicationService } from '../../../services/application.service';
import { StepperService } from '../../../services/stepper.service';

@Component({
  selector: 'app-project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.scss']
})
export class ProjectDetailsComponent {
  @ViewChild('mySelect') mySelect!: ElementRef<HTMLSelectElement>;
  isSelectDisabled = true;
  @Input() inputData: any = {};
  userForm!: UntypedFormGroup;
  categoryTypes: any[] = [

  ];
  buildingTypes: any[] = [];
  permitTypes: any[] = [];

  applicationDetails: any = {};
  existingProject: any = {};
  doYouWantToAssignToEngineer: boolean = false;
  submitted: boolean = false;
  upiInfo: any = {};
  loading: boolean = false;
  currentUser: any;
  formAccess: any = {};
  technologySurveys: any[] = [];

  constructor(
    private stepperService: StepperService,
    private applicationService: ApplicationService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private cdr: ChangeDetectorRef,
    private appConfig: AppConfig,
    private sessionService: SessionService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;
    // from development details
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_old_INFO) as any);
    // from development details
    this.callLookups();
  }

  ngOnInit(): void {
    if (this.upiInfo && this.upiInfo?.isFromOldSystem) {

    } else {
      this.userForm = this.formBuilder.group({
        permitTypeId: ["", [Validators.required]],
        categoryTypeId: ["", [Validators.required]],
        buildTypeId: ["", [Validators.required]],
        buildTypeCode: [""],
        permitTypeCode: [""],
        applicationStatusId: [""], // hard coded
        userId: [''],
        submittedByUserId: [this.currentUser.userId],
        agencyId: [""],
        projectId: ["", [Validators.required]],
        agencyCode: ["", [Validators.required]],
        categoryCode: [''],
        usedPermitType: [''],
        certificateNumberEIA: [''],
        //
        waterConsumption: ["", [Validators.required]],
        electricityConsumption: ["", [Validators.required]],
        DistanceToTheNearestLandIn: ["", [Validators.required]],
        ProjectCostInUSD: ["", [Validators.required]],
        ProjectCostInRwf: ["", [Validators.required]],
        buildUpArea: ["", [Validators.required]],
        numberOfFloor: ["", [Validators.required]],    // help to calculate amount
        grossFloorArea: ["", [Validators.required]],
        numberOfParkingSpace: [""],
        priceOfDwellingUnitRwf: ["", [Validators.required]],
        capacityInformation: ["", [Validators.required]],
        numberOfDwellingUnits: [""],
        DescriptionOfOperation: [""],
        technologySurveyId: [""],
        // optional 
        plotSize: [],
        // optional
        percentageSpaceUse: ["", [Validators.required]],
      }, { validator: this.buildUpAreaValidator });
      this.userForm.controls['plotSize'].setValue(this.upiInfo.size);
    }
    this.cdr.detectChanges();
    this.focusOutFunction();
    this.checkIfUPIhasAlreadyProject();
  }




  initializeDataFromOldSystem() {
    this.userForm = this.formBuilder.group({
      upi: [this.upiInfo.Plot_No],
      buildUpArea: [this.upiInfo.Built_up_area, [Validators.required]],
      numberOfFloor: [this.upiInfo.Number_of_Floors, [Validators.required]],    // help to calculate amount
      grossFloorArea: ["", [Validators.required]],
      numberOfParkingSpace: [""],
      priceOfDwellingUnitRwf: ["", [Validators.required]],
      capacityInformation: ["", [Validators.required]],
      numberOfDwellingUnits: [""],
      percentageSpaceUse: ["", [Validators.required]],

    }, { validator: this.buildUpAreaValidator })
  }




  checkIfUPIhasAlreadyProject() {
    // Call project by upi
    this.upiInfo = JSON.parse(localStorage.getItem(this.appConfig.UPI_NEW_INFO) as any);
    this.loading = true;
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/project/upi/search?upi=' + this.upiInfo.upi)
      .subscribe(
        data => {
          this.applicationDetails = data.items[0];
          this.userForm.controls['projectId'].setValue(this.applicationDetails.id);
          this.userForm.controls['agencyId'].setValue(this.applicationDetails.agencyId);
          this.userForm.controls['userId'].setValue(this.applicationDetails.userId);
          // Calling agency by district code from what you have selected to apply
          this.applicationService.findAllWithPath(environment.authUrl + 'user-management/agencyDistricts/ByDistrictCode/' + this.applicationDetails.districtCode)
            .subscribe(
              agencyData => {
                this.userForm.controls['agencyCode'].setValue(agencyData.code);
                this.loading = false;
              },
              error => { this.loading = false; }
            )
          // Calling agency by district code from what you have selected to apply

          // Call exiting application by project id and permit type
          this.getExistingApplicationAndPermitType();
          // Call exiting application by project id and permit type
        },
        error => { this.loading = false; }
      )
  }


  getExistingApplicationAndPermitType() {
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=' + VALUE.NEW_CONSTRUCTION_PERMIT_CODE)
      .subscribe(
        data => {
          this.userForm.controls['usedPermitType'].setValue(data.items[0].id);
          this.applicationService.findAllWithPath(environment.applicationUrl + 'application/application/' + this.userForm.value.projectId + '/permit-type/' + this.userForm.value.usedPermitType)
            .subscribe(
              data => {
                if (data.length > 0) {
                  this.fillTheDataTotheForm(data[0]);
                  // if data present means that application already existis
                  this.formAccess.isApplicationExists = true;
                }
              }
            )
        },
        error => { }
      )
  }

  fillTheDataTotheForm(data: any) {
    this.userForm.controls['permitTypeId'].setValue(data.permitTypes.id);
    this.userForm.controls['categoryTypeId'].setValue(data.categoryTypes.id);
    this.userForm.controls['buildTypeId'].setValue(data.buildTypes.id);
    this.userForm.controls['permitTypeCode'].setValue(data.permitTypeCode);
    this.userForm.controls['applicationStatusId'].setValue(data.applicationStatus.id);
    // this.userForm.controls['userId'].setValue(data.userId);
    this.userForm.controls['agencyId'].setValue(data.agencyId);
    this.userForm.controls['projectId'].setValue(data.projects.id);
    this.userForm.controls['agencyCode'].setValue(data.agencyCode);
    this.userForm.controls['categoryCode'].setValue(data.categoryTypes.code);
    // this.userForm.controls['usedPermitType'].setValue(data.permitType.id);
    this.userForm.controls['certificateNumberEIA'].setValue(data.certificateNumberEIA);
    this.userForm.controls['waterConsumption'].setValue(data.waterConsumption);
    this.userForm.controls['electricityConsumption'].setValue(data.electricityConsumption);
    this.userForm.controls['DistanceToTheNearestLandIn'].setValue(data.DistanceToTheNearestLandIn);
    this.userForm.controls['ProjectCostInUSD'].setValue(data.ProjectCostInUSD);
    this.userForm.controls['ProjectCostInRwf'].setValue(data.ProjectCostInRwf);
    this.userForm.controls['buildUpArea'].setValue(data.buildUpArea);
    this.userForm.controls['numberOfFloor'].setValue(data.numberOfFloor);
    this.userForm.controls['grossFloorArea'].setValue(data.grossFloorArea);
    this.userForm.controls['numberOfParkingSpace'].setValue(data.numberOfParkingSpace);
    this.userForm.controls['priceOfDwellingUnitRwf'].setValue(data.priceOfDwellingUnitRwf);
    this.userForm.controls['capacityInformation'].setValue(data.capacityInformation);
    this.userForm.controls['numberOfDwellingUnits'].setValue(data.numberOfDwellingUnits);
    this.userForm.controls['DescriptionOfOperation'].setValue(data.DescriptionOfOperation);
    // optional 
    this.userForm.controls['plotSize'].setValue(data.projects.plotSize);
    // optional
    this.userForm.controls['percentageSpaceUse'].setValue(data.percentageSpaceUse);
  }



  getBuildTypeObject(event: any) {
    let buildType = this.buildingTypes.find((x: any) => x.id === this.userForm.value.buildTypeId);
    this.userForm.controls['buildTypeCode'].setValue(buildType.code);
    this.calculateToGetCategory()
  }

  calculateToGetCategory() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }


  calculateNumberOfPeople() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }

  calculateNumberOfFlow() {
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }



  focusOutFunction() {
    this.userForm.controls['percentageSpaceUse'].setValue(((this.userForm.value.buildUpArea / this.upiInfo.size) * 100).toFixed(2));
    this.findCategoryByCode(this.utilService.getCategory(this.userForm.value));
    this.userForm.controls['categoryCode'].setValue(this.utilService.getCategory(this.userForm.value));
  }


  allowToAssign() {
    this.formAccess.doYouWantToShareToEngineer = !this.formAccess.doYouWantToShareToEngineer;
  }



  findCategoryByCode(code: any) {
    // this.applicationService.findAllWithPath(environment.applicationUrl + 'application/categoryType/code/search?search=' + code)
    //   .subscribe(data => {
    //     this.userForm.controls['categoryTypeId'].setValue(data.items[0].id);
    //   })
    if (code) {
      this.userForm.controls['categoryTypeId'].setValue(this.categoryTypes.find((x: any) => x.code === code)?.id);
    }
  }


  chooseCategory(event: any) {
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => {
          this.categoryTypes = data;
        }
      )
  }

  onPrev(): void {
    this.stepperService.prevStep();
  }

  onNext(): void {
    this.stepperService.nextStep();
  }


  buildUpAreaValidator: ValidatorFn = (control: AbstractControl): { [key: string]: any } | null => {
    const plotSize = control.get('plotSize');
    // const plotSize = this.upiInfo.size;
    const buildUpArea = control.get('buildUpArea');
    const percentageSpaceUse: any = control.get('percentageSpaceUse');
    if (plotSize && buildUpArea && plotSize.value && buildUpArea.value) {
      if (buildUpArea.value > plotSize.value) {
        return { 'greaterThanPlotSize': true };
      }
    }
    return null;
  };






  onSubmit() {
    if (this.formAccess.isApplicationExists) {
      this.onNext();
      this.submitted = false;
    } else {
      if (this.userForm.invalid) {
        this.submitted = false;
        this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Fill all required', "bottom", "center")
        return;
      } else {
        this.submitted = true;
        this.applicationService.findAllWithPath(environment.applicationUrl + 'application/applicationStatus/code/search?search=' + 'PND')
          .subscribe(
            data => {
              this.userForm.controls['applicationStatusId'].setValue(data.items[0].id);
              this.confirmSubmit();
            }, error => {
              this.submitted = false;
              this.utilService.showNotification(NOTIFICATION_COLOR.error, "Application status not found", "bottom", "center");
            });
      }
    }

  }


  confirmSubmit() {

    let permitTypeCode = this.permitTypes.find((x: any) => x.id === this.userForm.value.permitTypeId).code;
    this.userForm.controls['permitTypeCode'].setValue(permitTypeCode);

    this.userForm.value.numberOfFloor = this.userForm.controls['numberOfFloor'].setValue(this.userForm.value.numberOfFloor);
    this.userForm.value.electricityConsumption = this.userForm.controls['electricityConsumption'].setValue(this.userForm.value.electricityConsumption);
    this.userForm.value.DistanceToTheNearestLandIn = this.userForm.controls['DistanceToTheNearestLandIn'].setValue(this.userForm.value.DistanceToTheNearestLandIn);
    this.userForm.value.ProjectCostInUSD = this.userForm.controls['ProjectCostInUSD'].setValue(this.userForm.value.ProjectCostInUSD);
    this.userForm.value.ProjectCostInRwf = this.userForm.controls['ProjectCostInRwf'].setValue(this.userForm.value.ProjectCostInRwf);
    this.userForm.value.buildUpArea = this.userForm.controls['buildUpArea'].setValue(this.userForm.value.buildUpArea);
    this.userForm.value.grossFloorArea = this.userForm.controls['grossFloorArea'].setValue(this.userForm.value.grossFloorArea);
    this.userForm.value.numberOfParkingSpace = this.userForm.controls['numberOfParkingSpace'].setValue(this.userForm.value.numberOfParkingSpace);
    this.userForm.value.priceOfDwellingUnitRwf = this.userForm.controls['priceOfDwellingUnitRwf'].setValue(this.userForm.value.priceOfDwellingUnitRwf);
    this.userForm.value.waterConsumption = this.userForm.controls['waterConsumption'].setValue(this.userForm.value.waterConsumption);


    this.applicationService.saveWithPath(this.userForm.value, environment.applicationUrl + 'application/application')
      .subscribe(
        data => {
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your application saved successfully", "bottom", "center");
          localStorage.setItem('application', JSON.stringify(data));
          this.onNext();
          this.submitted = false;
        }, error => { this.submitted = false; }
      )
  }




  callLookups() {
    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.BUILDING_TYPE)
      .subscribe(
        data => { this.buildingTypes = data; },
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => { this.categoryTypes = data; }
      )

    // this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
    this.applicationService.findAllWithPath(environment.applicationUrl + 'application/permitType/code/search?search=NCP')
      .subscribe(
        data => { this.permitTypes = data.items; },
      )

    this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.TECHNOLOGYSURVEY)
      .subscribe(
        data => { this.technologySurveys = data; }
      )

    // this.applicationService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
    //   .subscribe(
    //     data => { this.permitTypes = data.; },
    //   )
  }





  comeAndAllowToAssign(event: any) {
    if (event.found) {
      this.formAccess.isIAEverified = true;
      this.formAccess.isRetreived = 1;
      this.formAccess.isEIAVerified = event?.isEIAVerified;

      this.userForm.controls['certificateNumberEIA'].setValue(event.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event.isEIAVerified);

    } else {
      this.formAccess.isIAEverified = false;
      this.formAccess.isEIAVerified = event?.isEIAVerified;

      // this.formAccess.isRetreived = 0;
      this.formAccess.isRetreived = 1;
      this.userForm.controls['certificateNumberEIA'].setValue(event.certNumber);
      this.userForm.controls['isEIAVerified'].setValue(event.isEIAVerified);

    }
  }
}
