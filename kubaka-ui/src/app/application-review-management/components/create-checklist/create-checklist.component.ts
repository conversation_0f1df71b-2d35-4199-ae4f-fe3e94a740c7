import { Component, EventEmitter, Input, Output, HostListener } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ApplicationService } from 'src/app/application/services/application.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';

interface ChecklistCacheData {
  decision: string;
  conditionsOfApproval: string;
  structuralComment: string;
  civilEngineeringComment: string;
  architecturalComment: string;
  urbanPlanningComment: string;
  siteAnalysisComment: string;
  approvalStatusId: string;
  timestamp: number;
}

@Component({
  selector: 'app-create-checklist',
  templateUrl: './create-checklist.component.html',
  styleUrls: ['./create-checklist.component.scss']
})
export class CreateChecklistComponent {
  @Input() inputData: any;
  @Input() existingData: any = {};
  submitted!: boolean;
  checkListForm!: UntypedFormGroup;
  @Output() bankToParent = new EventEmitter();
  currentUser: any;
  approvalStatuses: any[] = [];
  fileData: any = {};
  private readonly CACHE_KEY_PREFIX = 'checklist_cache_';
  private readonly CACHE_EXPIRY_HOURS = 168;

  constructor(
    private modalService: NgbModal,
    private formBuilder: UntypedFormBuilder,
    private sessionService: SessionService,
    private utilService: UtilService,
    private applicationService: ApplicationService
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;



    if (this.currentUser.data.user.approvalLevelId) {
      this.applicationService.findAllWithPath(environment.applicationUrl + 'approval/approvalStatus/approvalLevel/' +
        this.currentUser.data.user.approvalLevelId)
        .subscribe(data => {
          if (data.length > 0) {
            this.approvalStatuses = data;
            if (this.currentUser.data?.user?.agency?.code !== 'RHA') {

              // const code = 'NORVW';
              // const index = this.approvalStatuses.findIndex((valueData: any) => valueData.code === code);
              // if (index !== -1) {
              //   this.approvalStatuses.splice(index, 1);
              // }

              let codesToRemove = ['0'];
              this.approvalStatuses = this.approvalStatuses.filter(
                (valueData: any) => codesToRemove.includes(valueData.additionalCode)
              );

            }


            if (this.currentUser.data?.user?.agency?.code === 'RHA') {


              // const codesArrayToRemove = ['NCPL', 'ACPD', 'NORCM']; // List of codes to remove

              // codesArrayToRemove.forEach((code) => {
              //   const index = this.approvalStatuses.findIndex((valueData: any) => valueData.code === code);
              //   if (index !== -1) {
              //     this.approvalStatuses.splice(index, 1);
              //   }
              // });


              let codesToRemove = ['1', '2', '3'];
              this.approvalStatuses = this.approvalStatuses.filter(
                (valueData: any) => codesToRemove.includes(valueData.additionalCode)
              );


            }
          } else {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, "No approval status found based on you approval level contact administrator to set approval status on your approval level", "bottom", "center");
          }
        });
    } else {
    }
  }



  getFileToSave(event: any) {
    this.fileData = event;
  }

  private saveToCache(applicationId: string, formData: ChecklistCacheData): void {
    try {
      const cacheKey = this.CACHE_KEY_PREFIX + applicationId;
      const cacheData = {
        ...formData,
        timestamp: Date.now()
      };
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error('Error saving checklist to cache:', error);
    }
  }

  private loadFromCache(applicationId: string): ChecklistCacheData | null {
    try {
      const cacheKey = this.CACHE_KEY_PREFIX + applicationId;
      const cachedData = localStorage.getItem(cacheKey);

      if (!cachedData) {
        return null;
      }

      const parsedData = JSON.parse(cachedData);

      const isExpired = Date.now() - parsedData.timestamp > (this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000);

      if (isExpired) {
        this.clearCache(applicationId);
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error('Error loading checklist from cache:', error);
      return null;
    }
  }

  private clearCache(applicationId: string): void {
    try {
      const cacheKey = this.CACHE_KEY_PREFIX + applicationId;
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.error('Error clearing checklist cache:', error);
    }
  }

  public clearCacheForApplication(applicationId: string): void {
    this.clearCache(applicationId);
  }

  public static clearChecklistCache(applicationId: string): void {
    try {
      const cacheKey = 'checklist_cache_' + applicationId;
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.error('Error clearing checklist cache:', error);
    }
  }

  private saveCurrentFormToCache(): void {
    if (this.checkListForm && this.inputData) {
      const formValues = this.checkListForm.value;
      const hasData = formValues.decision ||
                     formValues.conditionsOfApproval ||
                     formValues.structuralComment ||
                     formValues.civilEngineeringComment ||
                     formValues.architecturalComment ||
                     formValues.urbanPlanningComment ||
                     formValues.siteAnalysisComment ||
                     formValues.approvalStatusId;

      if (hasData) {
        const formData: ChecklistCacheData = {
          decision: formValues.decision || '',
          conditionsOfApproval: formValues.conditionsOfApproval || '',
          structuralComment: formValues.structuralComment || '',
          civilEngineeringComment: formValues.civilEngineeringComment || '',
          architecturalComment: formValues.architecturalComment || '',
          urbanPlanningComment: formValues.urbanPlanningComment || '',
          siteAnalysisComment: formValues.siteAnalysisComment || '',
          approvalStatusId: formValues.approvalStatusId || '',
          timestamp: Date.now()
        };

        this.saveToCache(this.inputData.toString(), formData);
      }
    }
  }

  onSubmitChecklist() {
    let formData = new FormData();

    formData.append('decision', this.checkListForm.value.decision);
    formData.append('conditionsOfApproval', this.checkListForm.value.conditionsOfApproval);
    formData.append('urbanPlanningComment', this.checkListForm.value.urbanPlanningComment);
    formData.append('siteAnalysisComment', this.checkListForm.value.siteAnalysisComment);
    formData.append('userId', this.currentUser.userId);
    formData.append('architecturalComment', this.checkListForm.value.architecturalComment);
    formData.append('civilEngineeringComment', this.checkListForm.value.civilEngineeringComment);
    formData.append('structuralComment', this.checkListForm.value.structuralComment);
    formData.append('approvalStatusId', this.checkListForm.value.approvalStatusId);
    formData.append('applicationId', this.inputData);
    formData.append('approvalLevelId', this.checkListForm.value.approvalLevelId);

    const fileList: FileList = this.fileData.file;
    if (fileList && fileList[0]) {
      const file: File = fileList[0];
      this.fileData.fileNameDisplay = file?.name
      formData.append('file', file, file?.name);
    }


    this.submitted = true;
    this.applicationService.saveAssetWithPathFormData(formData, environment.applicationUrl + 'approval/applicationApprovalCheckList')

      // this.applicationService.saveAssetWithPath(this.checkListForm.value, environment.applicationUrl + 'approval/applicationApprovalCheckList')
      .subscribe(
        data => {
          this.submitted = false;
          this.clearCache(this.inputData.toString());
          this.utilService.showNotification(NOTIFICATION_COLOR.success, "Your approval saved successfully", "bottom", "center");
          this.bankToParent.emit(true);
          this.submitted = false;
        }, err => {
          this.submitted = false;
          this.utilService.showNotification(NOTIFICATION_COLOR.error, err, "bottom", "center");
        }
      )
  }


  cancelCheckList() {
    this.saveCurrentFormToCache();
    this.bankToParent.emit(false);
  }


  ngOnInit(): void {

    if (this.existingData.id) {
      this.checkListForm = this.formBuilder.group({
        decision: [this.existingData.decision, [Validators.required]],
        conditionsOfApproval: [this.existingData.decision, [Validators.required]],
        structuralComment: [this.existingData.decision],
        civilEngineeringComment: [this.existingData.decision],
        architecturalComment: [this.existingData.architecturalComment],
        urbanPlanningComment: [this.existingData.urbanPlanningComment],
        siteAnalysisComment: [this.existingData.siteAnalysisComment],
        applicationId: [this.inputData.applicationId, [Validators.required]],
        approvalStatusId: [this.existingData.approvalStatus.id, [Validators.required]],
        approvalLevelId: [this.existingData.approvalLevels.id, [Validators.required]],
        userId: [this.existingData.userId],
      });
    } else {
      const cachedData = this.loadFromCache(this.inputData.toString());

      this.checkListForm = this.formBuilder.group({
        decision: [cachedData?.decision || '', [Validators.required]],
        conditionsOfApproval: [cachedData?.conditionsOfApproval || '', [Validators.required]],
        structuralComment: [cachedData?.structuralComment || ''],
        civilEngineeringComment: [cachedData?.civilEngineeringComment || ''],
        architecturalComment: [cachedData?.architecturalComment || ''],
        urbanPlanningComment: [cachedData?.urbanPlanningComment || ''],
        siteAnalysisComment: [cachedData?.siteAnalysisComment || ''],
        applicationId: [this.inputData, [Validators.required]],
        approvalStatusId: [cachedData?.approvalStatusId || '', [Validators.required]],
        approvalLevelId: [this.currentUser.data.user.approvalLevelId, [Validators.required]],
        userId: [this.currentUser.userId],
      });

      if (cachedData) {
        this.utilService.showNotification(
          NOTIFICATION_COLOR.success,
          "Previous unsaved checklist data has been restored",
          "bottom",
          "center"
        );
      }
    }
    this.setupAutoSave();
  }

  private setupAutoSave(): void {
    if (this.checkListForm && !this.existingData.id) {
      let debounceTimer: any;

      this.checkListForm.valueChanges.subscribe(() => {
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }

        debounceTimer = setTimeout(() => {
          this.saveCurrentFormToCache();
        }, 2000);
      });
    }
  }

  public hasCachedData(applicationId: string): boolean {
    const cachedData = this.loadFromCache(applicationId);
    return cachedData !== null;
  }

  public getCachedDataSummary(applicationId: string): string {
    const cachedData = this.loadFromCache(applicationId);
    if (!cachedData) {
      return 'No cached data found';
    }

    const fields = [];
    if (cachedData.decision) fields.push('decision');
    if (cachedData.conditionsOfApproval) fields.push('conditions');
    if (cachedData.structuralComment) fields.push('structural');
    if (cachedData.civilEngineeringComment) fields.push('civil');
    if (cachedData.architecturalComment) fields.push('architectural');
    if (cachedData.urbanPlanningComment) fields.push('urban planning');
    if (cachedData.siteAnalysisComment) fields.push('site analysis');
    if (cachedData.approvalStatusId) fields.push('approval status');

    const cacheAge = Math.round((Date.now() - cachedData.timestamp) / (1000 * 60)); // minutes
    return `Cached fields: ${fields.join(', ')} (${cacheAge} minutes ago)`;
  }

  @HostListener('window:beforeunload', ['$event'])
  beforeUnloadHandler(event: any) {
    if (this.checkListForm && !this.existingData.id) {
      this.saveCurrentFormToCache();
    }
  }

  get form() {
    return this.checkListForm.controls;
  }

}
