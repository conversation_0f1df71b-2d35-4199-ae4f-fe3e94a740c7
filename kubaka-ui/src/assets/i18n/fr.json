{"home": "Accueil", "topBarMenu": {"home": "Accueil", "service": "Service", "faq": "FAQ", "login": "Se connecter", "signup": "S'inscrire"}, "chatBotLabels": {"title": "Vous avez des questions? Discutez avec Kubaka!", "subTitle": "Obtenez une réponse instantanément", "Welcome": "Bienvenue", "Selectcategory": "Sélectionnez la catégorie", "Selectagency": "Sélectionnez l'agence(OSC)"}, "heroPage": {"title": "Système de permis de construire", "subTitle": "Suivez l'état de votre demande del' outorization", "subTitle2": "Ou CLIQUEZ ici pour l'ancienne application BPMIS", "search": "Rechercher UPI ici", "searchButton": "Recherche", "link": "Vérifiez ce qui est autorisé sur votre terrain", "constructionPermit": "Permis de construction", "constructionPermitApply": "Appliquer"}, "zoningCheck": {"title": "Chèque éligible", "subTitle": "Suivez ce qui est autorisé, interdit et conditionnel à construire sur votre terrain", "search": "Remplissez l'UPI", "searchButton": "Recherche", "checkRequirementProgress": "Vérification des exigences en cours", "taxPaymentReminder": "Pouvez-vous vous assurer que vos impôts sont payés ? Cette parcelle a des impôts impayés", "unpaidTaxesDetails": "Détails des impôts impayés", "declarationYear": "<PERSON><PERSON>", "taxTypeDescription": "Description du type de taxe", "totalTaxToPay": "Total des taxes à payer", "totalTaxPaid": "Total des taxes payées", "balance": "Solde", "isUnderRestriction": "Est sous restriction", "isUnderMortgage": "Est sous hypothèque", "yes": "O<PERSON>", "no": "Non", "location": "Emplacement", "district": "District", "sector": "<PERSON><PERSON><PERSON>", "cell": "Cellule", "village": "Village", "landUse": "Usage du terrain", "registeredUse": "Usage enregistré", "zoning": "Zonage", "area": "Surface", "thereIsA": "Il y a un(e)", "road": "Route", "passesThroughPlot": "qui traverse votre parcelle", "selectBuildMasterPlan": "Veuillez sélectionner ce que vous souhaitez construire selon le plan directeur", "permittedUse": "Usage autorisé", "apply": "Appliquer", "prohibitedUse": "Usage interdit", "conditionalUse": "Usage conditionnel", "representative": "Représentant", "doesPlotUnderRestriction": "La parcelle est-elle sous restriction", "doesPlotUnderMortgage": "La parcelle est-elle sous hypothèque", "owners": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "names": "Noms", "nationalId": "Numéro d'identification nationale", "maritalStatus": "État civil", "gender": "Genre", "male": "<PERSON><PERSON>", "female": "<PERSON>mme"}, "zoningCheckDetails": {"title": "Comment se déroule la demande de certificat", "subTitle": "Su<PERSON>z le statut de votre licence ici", "permit": "<PERSON><PERSON>", "permitNumber": "Numéro d'application", "date": "Date", "location": "Emplacement", "stageTitle": "Les étapes par lesquelles passe la candidature", "stage0": "ÉTAPE 0", "stage0Title": "Demande en attente", "stage0Description": "Il s'agit de la première étape si vous n'avez pas encore déposé de candidature.", "stage1": "ÉTAPE 1", "stage1Title": "Candidature soumise", "stage1Description": "Il s'agit du premier etape une fois que vous avez soumis votre candidature.", "stage2": "ÉTAPE 2", "stage2Title": "De retour pour demande de correction", "stage2Description": "C'est lorsque vous avez reçu une réponse de l'autorité indiquant que vous devez modifier quelque chose dans votre demande.", "stage3": "ÉTAPE 3", "stage3Title": "Examiner la demande", "stage3Description": "<PERSON><PERSON>, votre candidature sera examinée par le personnel chargé des candidatures.", "stage4": "ÉTAPE 4", "stage4Title": "Approuver la demande", "stage4Description": "C'est à ce moment-là que vous recevez une réponse de l'autorité qui a approuvé/rejeté votre demande.", "stage5": "ÉTAPE 5", "stage5Title": "<PERSON><PERSON>e rejetée/annulée", "stage5Description": " C'est lorsque vous avez reçu une réponse de l'autorité qui a approuvé/rejeté votre demande."}, "permitCategory": {"title": "Permis à demander", "subTitle": "Suivez l'état de votre demande de permis ici", "Category1": "Nouveau permis de construction", "Category1Description1Tile1": "À propos de ce service", "Category1Description1": "Ce service permet aux utilisateurs de demander un nouveau permis de construction. La demande est traitée par la Rwanda Housing Authority.", "Authorized": "Rwanda Housing Authority", "Period": "Valable seulement 12 heures", "Price": "<PERSON><PERSON><PERSON>", "authorized": "Autorisée", "period": "Période", "price": "prix", "Cancelbtn": "Annuler", "Applybtn": "Appliquer", "Category2": "Avis d'inspection", "Category2Description2Title2": "À propos de ce service", "Category2Description2": "Ce service permet aux utilisateurs de demander un avis d'inspection de leurs bâtiments. La demande est traitée par la Rwanda Housing Authority.", "Authorized2": "Rwanda Housing Authority", "Period2": "Valable seulement 12 heures", "Price2": "<PERSON><PERSON><PERSON>", "Cancelbtn2": "Annuler", "Applybtn2": "Appliquer", "Category3": "Renouvellement du permis", "Category3Description3Title3": "À propos de ce service", "Category3Description3": "Ce service permet aux utilisateurs de demander le renouvellement de leur permis de construire. La demande est traitée par la Rwanda Housing Authority.", "Authorized3": "Rwanda Housing Authority", "Period3": "Valable seulement 12 heures", "Price3": "<PERSON><PERSON><PERSON>", "Cancelbtn3": "Annuler", "Applybtn3": "Appliquer", "Category4": "Construction de clôture", "Category4Description4Title4": "À propos de ce service", "Category4Description4": "Ce service permet aux utilisateurs de demander le renouvellement de leur permis de construire. La demande est traitée par la Rwanda Housing Authority.", "Authorized4": "Rwanda Housing Authority", "Period4": "Valable seulement 12 heures", "Price4": "<PERSON><PERSON><PERSON>", "Cancelbtn4": "Annuler", "Applybtn4": "Appliquer", "Category5": "démolition totale/partielle", "Category5Description5Title5": "À propos de ce service", "Category5Description5": "Ce service permet aux utilisateurs de demander un permis de démolition, que ce soit pour une démolition totale ou partielle. La demande est traitée par la Rwanda Housing Authority.", "Authorized5": "Rwanda Housing Authority", "Period5": "Valable seulement 12 heures", "Price5": "<PERSON><PERSON><PERSON>", "Cancelbtn5": "Annuler", "Applybtn5": "Appliquer", "Category6": "Permis d'occupation", "Category6Description6Title6": "À propos de ce service", "Category6Description6": "Ce service permet aux utilisateurs de demander un permis d'occupation. La demande est traitée par la Rwanda Housing Authority.", "Authorized6": "Rwanda Housing Authority", "Period6": "Valable seulement 12 heures", "Price6": "<PERSON><PERSON><PERSON>", "Cancelbtn6": "Annuler", "Applybtn6": "Appliquer", "Category7": "Modification du projet", "Category7Description7Title7": "À propos de ce service", "Category7Description7": "Ce service permet aux utilisateurs de demander une modification de projet. La demande est traitée par la Rwanda Housing Authority.", "Authorized7": "Rwanda Housing Authority", "Period7": "Valable seulement 12 heures", "Price7": "<PERSON><PERSON><PERSON>", "Cancelbtn7": "Annuler", "Applybtn7": "AAppliquer", "Category8": "Demandes d'autorisation de structure temporaire", "Category8Description8Title8": "À propos de ce service", "Category8Description8": "Ce service permet aux utilisateurs de demander des demandes d'autorisation de structure temporaire. La demande est traitée par la Rwanda Housing Authority.", "Authorized8": "Rwanda Housing Authority", "Period8": "Valable seulement 12 heures", "Price8": "<PERSON><PERSON><PERSON>", "Cancelbtn8": "Annuler", "Applybtn8": "Appliquer", "Category9": "Rénovation d'un bâtiment existant avec modification structurelle", "Category9Description9Title9": "À propos de ce service", "Category9Description9": "Ce service permet aux utilisateurs de postuler pour la rénovation d'un bâtiment existant avec des modifications structurelles. La demande est traitée par la Rwanda Housing Authority.", "Authorized9": "Rwanda Housing Authority", "Period9": "Valable seulement 12 heures", "Price9": "<PERSON><PERSON><PERSON>", "Cancelbtn9": "Annuler", "Applybtn9": "Appliquer", "Category10": "Rénovation d'un bâtiment existant sans modification structurelle", "Category10Description10Title10": "À propos de ce service", "Category10Description10": "Ce service permet aux utilisateurs de postuler pour la rénovation d'un bâtiment existant sans modifications structurelles. La demande est traitée par la Rwanda Housing Authority.", "Authorized10": "Rwanda Housing Authority", "Period10": "Valable seulement 12 heures", "Price10": "<PERSON><PERSON><PERSON>", "Cancelbtn10": "Annuler", "Applybtn10": "Appliquer"}, "FAQs": {"Title": "Questions fréquemment posées", "subTitle": "Lisez ici les questions les plus fréquemment posées et comment les résoudre", "questionOwner": {"1": "Comment utiliser le système en tant que propriétaire foncier ?", "2": "Quelles catégories sont attribuées aux ingénieurs et aux architectes?", "3": "Co<PERSON>ien de temps faut-il pour traiter une demande de permis?", "4": "Quelle est la durée d'un permis et peut-il être renouvelé?"}, "descriptionOwner": {"1": "Pour utiliser le système en tant que propriétaire foncier, vous devez créer un compte et vous connecter. Vous devez avoir votre carte d'identité nationale ou votre passeport.", "2": "Toutes les applications dans n'importe quelle catégorie peuvent être soumises par des ingénieurs et des architectes.", "3": "La durée de la demande de permis est de 21 jours dans les COK OSC et de 30 jours dans les autres OSC.", "4": "La durée initiale du permis est deux ans et peut être renouvelée."}, "questionEngineer": {"1": "Comment utiliser le système en tant qu'ingénieur?", "2": "Comment accéder à une demande de permis en tant qu'ingénieur?", "3": "En tant qu'ingénieur, dois-je renouveler ma licence chaque année?"}, "descriptionEngineer": {"1": "En tant qu'ingénieur, vous devez créer un compte et vous connecter en utilisant votre numéro de licence.", "2": "Les propriétaires vous affecteront à des projets. Vous recevrez des notifications et pourrez accepter ou refuser la mission.", "3": "Les ingénieurs doivent vérifier auprès de l Association des ingénieurs pour chaque renouvellement."}, "questionArchitect": {"1": "Comment utiliser le système en tant qu'architecte?", "2": "Comment accéder à une demande de permis en tant qu'architecte?", "3": "En tant qu'architecte, dois-je renouveler ma licence chaque année?"}, "descriptionArchitect": {"1": "Les architectes doivent créer un compte et se connecter en utilisant leur numéro de licence.", "2": "Les propriétaires vous affecteront à des projets. Vous recevrez des notifications et pourrez accepter ou refuser la mission.", "3": "Les architectes doivent vérifier auprès de l'Association des architectes pour chaque renouvellement."}}, "footer": "Tous droits réservés.", "footer2": "Alimenté par Tech Avenue 137", "permitTypeInformation": {"applicationType1": "Nouveau permis de construction", "desc1": "Ce service permet aux Utilisateurs de demander un Nouveau Permis de Construction. La demande est traitée par la Rwanda Housing Authority.", "authorized1": "Autorité rwandaise du logement", "period1": "Valable seulement 2 ans", "price1": "<PERSON><PERSON><PERSON>", "applicationType2": "Avis d'inspection", "desc2": "Ce service permet aux utilisateurs de demander an Inspection Notice of their buildings.La demande est traitée par la Rwanda Housing Authority.", "authorized2": "Autorité rwandaise du logement", "period2": "Valable seulement 2 ans", "price2": "<PERSON><PERSON><PERSON>", "applicationType3": "Renouvellement du permis", "desc3": "Ce service permet aux utilisateurs de demander the renewal of their building permits.La demande est traitée par la Rwanda Housing Authority.", "authorized3": "Autorité rwandaise du logement", "period3": "Valable seulement 2 ans", "price3": "<PERSON><PERSON><PERSON>", "applicationType4": "Construction de clôture", "desc4": "Ce service permet aux utilisateurs de demander a construction of fence permit.La demande est traitée par la Rwanda Housing Authority.", "authorized4": "Autorité rwandaise du logement", "period4": "Valable seulement 2 ans", "price4": "<PERSON><PERSON><PERSON>", "applicationType5": "Démolition - Complète / Partielle", "desc5": "Ce service permet aux utilisateurs de demander a demolition permit, whether for full or partial demolition.La demande est traitée par la Rwanda Housing Authority.", "authorized5": "Autorité rwandaise du logement", "period5": "Valable seulement 2 ans", "price5": "<PERSON><PERSON><PERSON>", "applicationType6": "Permis d'occupation", "desc6": "Ce service permet aux utilisateurs de demander an occupancy permit.La demande est traitée par la Rwanda Housing Authority.", "authorized6": "Autorité rwandaise du logement", "period6": "Valable seulement 2 ans", "price6": "<PERSON><PERSON><PERSON>", "applicationType7": "Modification du projet", "desc7": "Ce service permet aux utilisateurs de demander project modification.La demande est traitée par la Rwanda Housing Authority.", "authorized7": "Autorité rwandaise du logement", "period7": "Valable seulement 2 ans", "price7": "<PERSON><PERSON><PERSON>", "applicationType8": "Demandes d'autorisation de structure temporaire", "desc8": "Ce service permet aux utilisateurs de demander d'autorisation de structure temporaire.La demande est traitée par la Rwanda Housing Authority.", "authorized8": "Autorité rwandaise du logement", "period8": "Valable seulement 2 ans", "price8": "<PERSON><PERSON><PERSON>", "applicationType9": "Kuvu<PERSON><PERSON>ra inyubako iriho hamwe no guhindura imiterere", "desc9": "Ce service permet aux utilisateurs de demander the refurbishment of existing building with structural alterations.La demande est traitée par la Rwanda Housing Authority.", "authorized9": "Autorité rwandaise du logement", "period9": "Valable seulement 2 ans", "price9": "<PERSON><PERSON><PERSON>", "applicationType10": "Rénovation d'un bâtiment existant sans modification structurelle", "desc10": "Ce service permet aux utilisateurs de demander the refurbishment of existing building without structural alterations.La demande est traitée par la Rwanda Housing Authority.", "authorized10": "Autorité rwandaise du logement", "period10": "Valable seulement 2 ans", "price10": "<PERSON><PERSON><PERSON>"}, "loginPage": {"title": "Connecter", "email": "E-mail", "username": "Nom d'utilisateur", "password": "Mot de passe", "loginButton": "Se connecter", "forgotPassword": "Mot de passe oublié ?", "createAccount": "<PERSON><PERSON><PERSON> un compte", "backHome": "Retour à l'accueil", "waitmsg": "Attends"}, "registerPage": {"title": "Inscription du demandeur", "accountType": "Type de compte", "accountTypeOptions": {"placeholder": "Choisir le type de compte", "owner": "Propri<PERSON><PERSON> foncier", "engineer": "Ingénieur / Entreprise", "architect": "Architecte / Entreprise"}, "proceedButton": "<PERSON><PERSON><PERSON>", "documentType": "Type de document", "documentTypeOptions": {"placeholder": "Choisir le type de document", "nationalId": "Carte d'identité nationale", "passport": "Passeport"}, "nationalIdLabel": "Numéro d'identification nationale", "passportLabel": "<PERSON>um<PERSON><PERSON>", "licenseLabel": "Numéro de licence", "savingtxt": "Enregistrement", "firstNameLabel": "Prénom", "lastNameLabel": "Nom de famille", "emailLabel": "E-mail", "phoneLabel": "Numéro de téléphone", "emailPlaceholder": "Veuillez entrer votre adresse e-mail", "phonePlaceholder": "Entrer le numéro de téléphone", "licensePlaceholder": "Entrer le numéro de licence", "passwordLabel": "Mot de passe", "confirmPasswordLabel": "Confirmer le mot de passe", "dobLabel": "Date de naissance", "dobPlaceholder": "Sélectionner la date de naissance", "genderLabel": "Genre", "genderOptions": {"placeholder": "Choi<PERSON>", "female": "<PERSON>mme", "male": "<PERSON><PERSON>"}, "consent": "Je consens que les informations fournies sont exactes au mieux de ma connaissance et de ma conviction.", "createAccountButton": "<PERSON><PERSON><PERSON> un compte", "haveAccount": "Vous avez un compte ?", "loginHere": "Connectez-vous ici", "orCancel": "<PERSON><PERSON>", "backTxt": "Retour", "backHomeTxt": "Retour à l'accueil", "waitTxt": "Attends"}, "resetPasswordPage": {"title": "Réinitialiser le mot de passe", "emailLabel": "E-mail", "emailPlaceholder": "Veuillez entrer votre adresse e-mail", "sendResetLinkButton": "Envoyer", "backToLogin": "Retour à la connexion", "waitTxt": "Attends", "rememberPassword": "Attends, je me souviens de mon mot de passe", "loginHere": "Cliquez ici"}, "errorNotification": {"successMsg": "Données enregistrées avec succès", "errorMsg": "Une erreur s'est produite lors de la requête"}, "guideLineHeader": "Guide de la procédure de candidature", "guideLineTitle_1": "Créer un projet", "guideLineContent_1": "Un projet ainsi que ses caractéristiques seront créés en fonction de votre UPI et en conformité avec la réglementation rwandaise relative à l'utilisation de votre terrain pour la construction.", "guideLineTitle_2": "Soumettez votre demande", "guideLineContent_2": "Votre demande doit être envoyée, et il est impératif de vous assurer que vous n'avez aucun impôt impayé ni certificat d'évaluation de l'impact environnemental.", "guideLineTitle_3": "Demande en cours d'examen", "guideLineContent_3": "Dès réception de votre demande, un membre de l'équipe OSC l'examinera et s'assurera que tout est conforme à la législation en vigueur.", "guideLineTitle_4": "Facture à payer", "guideLineContent_4": "Des factures seront émises après l'approbation de la demande, et vous devrez effectuer le paiement pour recevoir le permis.", "guideLineTitle_5": "<PERSON><PERSON>", "guideLineContent_5": "Votre permis sera disponible en téléchargement après que le paiement aura été effectué.", "accountHeaderLinksContainer": {"engineer": "Ingénieurs Professionnels", "architect": "Architectes Agréés"}, "accountHeaderMenu": {"dashboard": "Tableau de bord", "systemAdministrator": "Administrateur système", "myProjects": "Mes projets", "allApplications": "Toutes les demandes", "transferredPermits": "Permis transférés", "myApplications": "Me<PERSON> demandes", "myBox": "Mon espace", "projectRequests": "De<PERSON><PERSON> de projet", "invoices": "Factures", "allInvoices": "Toutes les factures", "permits": "<PERSON><PERSON>", "allPermits": "Tous les permis", "reports": "Rapports", "chat": "Messagerie"}, "accountHeaderUserDropDown": {"edit": "Modifier le profil", "change": "Changer le mot de passe", "logout": "Se déconnecter"}, "dashboardComponent": {"welcomeMsg": "Bienvenue", "loadingTxt": "Chargement", "plotsTitle": "<PERSON><PERSON> parcelles sous Kubaka", "viewMap": "Voir sur la carte", "plotLocation": "Emplacement de la parcelle", "plotUPI": "UPI", "plotUse": "Usage de la parcelle", "findApplication": "Rechercher une demande", "applyPermitInstruction": "Pour demander un permis, cliquez ici", "allApplication": "Toutes les demandes", "draftApplications": "Demandes en brouillon", "reviewedApplications": "<PERSON><PERSON><PERSON> examin<PERSON>", "submittedApplications": "Demandes soumises", "permitted": "Permis accord<PERSON>", "allPermitted": "Tous les permis accordés", "rejectedApplications": "<PERSON><PERSON><PERSON> re<PERSON>", "rejected": "Rejetées", "underReview": "En cours d'examen", "underCorrection": "En cours de correction", "resubmitted": "<PERSON><PERSON><PERSON><PERSON>", "preApproval": "Pré-approbation", "nonObjectionReturned": "Non-objection retournée", "certified": "Certifié", "returnedApplication": "<PERSON><PERSON><PERSON> re<PERSON>", "nonObjectionReviewed": "Non-objection examinée", "nonObjectionUnderReview": "Non-objection en cours d'examen", "nonObjectionApproved": "Non-objection approuvée", "nonObjectionRejected": "Non-objection rejetée", "nonObjectionBackForCorrection": "Non-objection renvoyée pour correction", "submitted": "<PERSON><PERSON><PERSON>", "tasksTitle": "<PERSON><PERSON> t<PERSON>", "taskTitle": "<PERSON> tâche", "search": {"general": "Recherche générale", "choose": "<PERSON><PERSON>-vous choisir ?"}, "rhaApplications": "<PERSON><PERSON><PERSON>", "agencyTxt": "Au sein de l'agence de"}, "countryLevelDashboard": {"agency": "Agence", "allAgency": "Toutes les agences", "allApplications": "Toutes les demandes", "preApproval": "Pré-approbation", "reviewedApplications": "<PERSON><PERSON><PERSON> examin<PERSON>", "submittedApplications": "Demandes soumises", "permitted": "Permis accord<PERSON>", "rejectedApplications": "<PERSON><PERSON><PERSON> re<PERSON>", "resubmitted": "<PERSON><PERSON><PERSON><PERSON>", "underReview": "En cours d'examen", "underCorrection": "En cours de correction", "nonObjectionReturned": "Non-objection retournée"}, "analyticsDashboard": {"title": "Tableau de bord", "filter_by_year": "Filtrer par année", "filter_by_agency": "Filtrer par agence", "filter": "<PERSON><PERSON><PERSON>"}, "chatMessageComponent": {"messageTxt": "Message", "users": "Utilisateurs", "applicants": "Demandeurs", "received": "<PERSON><PERSON><PERSON>", "sent": "Envoyés", "sPlaceholder": "Rechercher...", "sApplicantPlaceholder": "Rechercher un demandeur..."}, "projectComponent": {"welcomeMsg": "Bienvenue", "projects": "Projets", "searchForSomething": "Rechercher...", "newPermitApplication": "Nouvelle demande de permis", "plotSize": "<PERSON><PERSON>", "projectName": "Nom du projet", "projectDescription": "Description du projet", "selectedCategoryUse": "Catégorie d'utilisation sélectionnée", "selectedUse": "Utilisation sélectionnée", "province": "Province", "district": "District", "sector": "<PERSON><PERSON><PERSON>", "projectDetails": "Dé<PERSON> du projet", "removeEngineerArchitect": "Supprimer l'ingénieur/architecte", "noRecordsFound": "Aucun enregistrement trouvé", "verification": "Vérification", "verificationDetails": "Nous vérifierons si vous avez des impôts impayés, examinerons l'ancien BPMIS pour votre projet et récupérerons votre plan de zonage.", "confirmRemoveEngineerArchitect": "Vous êtes sur le point de supprimer l'ingénieur/architecte du projet", "removingEngineerArchitect": "Suppression de l'ingénieur/architecte du projet", "close": "<PERSON><PERSON><PERSON>", "yesDelete": "<PERSON><PERSON>, supprimer", "removing": "Suppression en cours...", "status": "Statut"}, "applicantApplication": {"welcomeMsg": "Bienvenue", "myApplications": "Me<PERSON> demandes", "searchForSomething": "Rechercher...", "applicationStatus": "Statut de la demande", "newPermitApplication": "Nouvelle demande de permis", "permitType": "Type de permis", "applicationNo": "<PERSON>um<PERSON><PERSON> de <PERSON>e", "projectName": "Nom du projet", "buildType": "Type de construction", "siteLocation": "Emplacement du site", "createdOn": "<PERSON><PERSON><PERSON>", "submittedDate": "Date de soumission", "applicationDetails": "<PERSON><PERSON><PERSON> de la demande", "relatedApplication": "<PERSON><PERSON><PERSON> liée", "pay": "Payer", "viewPermit": "Voir le permis", "viewComments": "Voir les commentaires", "delete": "<PERSON><PERSON><PERSON><PERSON>", "noRecordsFound": "Aucun enregistrement trouvé", "confirmDeleteApplication": "Vous êtes sur le point de supprimer une demande", "deleteWarning": "La suppression de votre demande effacera toutes vos informations de notre base de données.", "close": "<PERSON><PERSON><PERSON>", "yesDelete": "<PERSON><PERSON>, supprimer", "deletingApplication": "Suppression de la demande", "verification": "Vérification", "verificationDetails": "Nous vérifierons si vous avez des impôts impayés, consulterons l'ancien BPMIS pour votre projet et récupérerons votre plan de zonage.", "applicationComment": "Commentaire de la demande", "approvalLevel": "Niveau d'approbation", "status": "Statut", "date": "Date", "comment": "Commentaire"}, "applicantInvoices": {"welcomeMsg": "Bienvenue", "myInvoices": "Mes factures", "search": "Recher<PERSON> quelque chose...", "fromDate": "Date de début", "toDate": "Date de fin", "pageSize": "<PERSON><PERSON>", "invoiceItem": "Article de la facture", "invoiceNo": "Numéro de facture", "amount": "<PERSON><PERSON>", "transactionNumber": "Numéro de transaction", "createdOn": "<PERSON><PERSON><PERSON>", "dueDate": "Date d'échéance", "applicationName": "Nom de la demande", "more": "Plus", "viewAndPay": "Voir et payer", "view": "Voir", "cancel": "Annuler", "refund": "<PERSON><PERSON><PERSON><PERSON>", "update": "Mettre à jour", "invoiceExtension": "Extension de la facture", "confirmRefund": "Vous êtes sur le point de rembourser", "close": "<PERSON><PERSON><PERSON>", "yesRefund": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "confirmCancel": "Vous êtes sur le point d'annuler", "yesDelete": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "updateInvoice": "Mettre à jour la facture", "chooseInvoicePrices": "Choisir les prix de la facture", "getPrice": "Obt<PERSON>r le prix", "updating": "Mise à jour", "askForExtension": "Demander une extension", "expirationDate": "Date d'expiration", "submit": "So<PERSON><PERSON><PERSON>"}, "applicantPermits": {"welcomeMsg": "Bienvenue", "permits": "<PERSON><PERSON>", "searchForSomething": "Rechercher...", "fromDate": "Date de début", "toDate": "Date de fin", "pageSize": "<PERSON><PERSON>", "invoiceNo": "Numéro de facture", "permitNo": "Numéro de permis", "applicationNo": "<PERSON>um<PERSON><PERSON> de <PERSON>e", "permitType": "Type de permis", "agencyCode": "Code de l'agence", "issuedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON> le", "expiryDate": "Date d'expiration", "status": "Statut", "waitingForPayment": "En attente de paiement", "generated": "<PERSON><PERSON><PERSON><PERSON>", "permitStatus": "Statut du permis", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "transferOwnership": "Transférer la propriété", "documentType": "Type de document", "selectDocumentType": "Sélectionner le type de document", "documentNumber": "Numéro de document (Carte d'identité nationale / Passeport)", "firstName": "Prénom", "lastName": "Nom de famille", "phoneNumber": "Numéro de téléphone", "close": "<PERSON><PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "confirmCancelPermit": "Vous êtes sur le point d'annuler le permis avec ce numéro", "yesDelete": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>"}, "editProfile": {"header": "Informations personnelles", "title": "Modifier le profil", "firstName": "Prénom", "lastName": "Nom de famille", "email": "Email", "phoneNumber": "Numéro de téléphone", "cancel": "Annuler", "update": "Mettre à jour", "updating": "Mise à jour"}, "changePassword": {"title": "Changer le mot de passe", "currentPwd": "Mot de passe actuel", "newPwd": "Nouveau mot de passe", "pwdRequired": "Mot de passe requis", "pwdMinLength": "Le mdp doit avoir au moins 8 caractères", "pwdConditions": "Le mdp doit contenir au moins 1 minuscule, 1 majuscule & 1 chiffre", "pwdRules": "Le mdp doit suivre les conditions ci-dessous", "minUpper": "Min 1 Majuscule", "minLower": "Min 1 Minuscule", "minSpecial": "Min 1 Caractère spécial", "minNumber": "Min 1 Chiffre", "minChars": "Min 8 Caractères", "maxChars": "Max 16 Caractères", "confirmPwd": "Confirmer le mot de passe", "pwdMismatch": "Les mots de passe ne correspondent pas", "submit": "So<PERSON><PERSON><PERSON>"}, "newApplication": {"step1": {"step": "Étape", "plotInformation": "Informations sur la parcelle", "plotNumberUpi": "Numéro de parcelle / UPI", "upiDetails": "Détails UPI", "representative": "Représentant", "province": "Province", "district": "District", "sector": "<PERSON><PERSON><PERSON>", "cell": "Cellule", "village": "Village", "parcelDetails": "<PERSON><PERSON><PERSON> de <PERSON> parcelle", "applyFor": "<PERSON><PERSON><PERSON>", "parcelLocation": "Emplacement de la parcelle", "coordinates": "Coordonnées", "cancel": "Annuler", "next": "Suivant"}, "step2": {"step": "Étape", "projectFoundOldSystem": "Votre projet est déjà trouvé dans l'ancien système, complétez les détails manquants et continuez", "projectDetails": "Dé<PERSON> du projet", "projectName": "Nom du projet", "projectDescription": "Description du projet", "representativeFullName": "Nom complet du représentant", "representativeNationalId": "Numéro d'identification nationale du représentant", "plotSizeSquareMeters": "<PERSON><PERSON> <PERSON> (En mètres carrés)", "cannotContinueAssigned": "Vous ne pouvez pas continuer car le projet est déjà attribué à un ingénieur/architecte", "previous": "Précédent", "update": "Mettre à jour", "save": "Enregistrer", "andNext": "et Suivant", "next": "Suivant"}, "step3": {"step": "Étape", "projectDetails": "Dé<PERSON> du projet", "checkAddAssociatedUpi": "Cocher pour ajouter un UPI associé", "doItYourself": "V<PERSON><PERSON>z-vous le faire vous-même", "assignToEngineerArchitect": "Attribuer cette demande à l'ingénieur / architecte", "permitType": "Type de permis", "plotSize": "<PERSON><PERSON>", "combiningPlotSize": "Combinaison de la taille de la parcelle", "buildingType": "Type de construction", "projectCategory": "Catégorie de projet", "numberOfFloorGPlus": "Nombre d'étages / G+", "builtUpAreaSqMeters": "Surface construite (En mètres carrés)", "buildUpAreaRestriction": "La surface construite ne peut pas être supérieure à la taille de la parcelle.", "grossFloorArea": "Surface totale", "numberOfParkingSpaces": "Nombre de places de stationnement", "estimatedPriceDwellingRwf": "Prix estimé de l'unité d'habitation en RWF", "descriptionOperations": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "numberOfDwellingUnit": "Nombre d'unités d'habitation", "estimatedMonthlyWaterM3": "Consommation d'eau estimée par mois (m3)", "estimatedMonthlyElectricityWatts": "Consommation électrique estimée par mois (watts)", "distanceNearestLandLineFiberM": "Distance jusqu'à la ligne téléphonique / fibre optique la plus proche (m)", "estimatedProjectCostUsd": "Coût estimé du projet en USD", "estimatedProjectCostRwf": "Coût estimé du projet en RWF", "capacityInfoPeopleSeats": "Informations sur la capacité : Nombre de personnes / sièges", "technologySurveyOptional": "Enquête technologique (Optionnel)", "previous": "Précédent", "next": "Suivant", "cannotContinueAssigned": "Vous ne pouvez pas continuer car le projet est déjà attribué à un ingénieur / architecte", "associatedUpi": "UPI associé"}, "step4": {"associatedUpi": "UPI associé", "step": "Étape", "projectAttachments": "Pièces jointes du projet", "requiredDocuments": "Documents requis", "downloadBoqFile": "Cliquez ici pour télécharger le fichier BOQ", "boqFileInstruction": "Le fichier BOQ est au format Excel. Assurez-vous de le télécharger et de remplir vos informations. Pour les autres fichiers, seuls les PDF sont autorisés.", "uploadedDocuments": "Documents téléchargés", "allowedFileTypes": "Seuls les fichiers PDF sont autorisés. Sauf pour le BOQ.", "upload": "Télécharger", "view": "Voir", "wait": "<PERSON><PERSON><PERSON>", "noFile": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "previous": "Précédent", "resubmit": "Soumettre à nouveau", "submit": "So<PERSON><PERSON><PERSON>", "confirmDelete": "Vous êtes sur le point de supprimer", "deletingYour": "Suppression de votre", "deleteWarning": "supprimera toutes vos informations de notre base de données.", "close": "<PERSON><PERSON><PERSON>", "yesDelete": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "applicationDeletingFile": "L'application supprime le fichier", "viewDocument": "Voir le document", "confirm": "Confirmer", "certifyTruth": "Il est de mon devoir de certifier la véracité des informations soumises dans cette demande et j'accepte les termes et conditions.", "cancel": "Annuler", "uploadAllRequiredFiles": "documents requis"}, "associatedUpi": {"title": "Fusionner la taille des parcelles", "search": "Rechercher ...", "new": "Nouveau", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerId": "ID du propriétaire", "plotSize": "<PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "noRecordsFound": "Aucun enregistrement trouvé", "cancel": "Annuler", "saving": "Enregistrement", "save": "Enregistrer"}, "assignToEngineer": {"header": "En raison de sa catégorie, ce projet doit être attribué à un ingénieur/architecte. Veuillez sélectionner et rechercher", "userType": "Type d'utilisateur", "engineerArchitectLicenseNumber": "Numéro de licence de l'ingénieur/architecte", "names": "Noms", "daysNeededForSubmission": "Jours nécessaires pour la soumission", "wait": "<PERSON><PERSON><PERSON>", "assign": "Attribuer"}}, "viewPlot": {"title": "Emplacement de la parcelle", "back": "Retour", "parcelDetails": "<PERSON><PERSON><PERSON> de <PERSON> parcelle", "province": "Province", "district": "District", "sector": "<PERSON><PERSON><PERSON>", "cell": "Cellule", "village": "Village", "coordinates": "Coordonnées"}, "applicationDetails": {"title": "Applications", "applicationsDetails": "Détails des application", "clickToContinue": "Cliquez pour continuer", "back": "Retour", "owners": "<PERSON>p<PERSON><PERSON><PERSON><PERSON>", "parcelDetails": "<PERSON><PERSON><PERSON> de <PERSON> parcelle", "appliedFor": "De<PERSON>é pour", "province": "Province", "district": "District", "sector": "<PERSON><PERSON><PERSON>", "cell": "Cellule", "village": "Village", "projectDetails": "Dé<PERSON> du projet", "projectCategory": "Catégorie du projet", "buildingType": "Type de bâtiment", "permitType": "Type de permis", "projectBrief": "Résumé du projet", "projectEstimatesDetails": "Détails des estimations du projet", "estimatedMonthlyWaterM3": "Consommation mensuelle estimée d'eau (m3)", "distanceNearestLandLineFiberM": "Distance jusqu'à la ligne terrestre / câble à fibre optique le plus proche (m)", "estimatedMonthlyElectricityKwh": "Consommation mensuelle estimée d'électricité en Kwh", "estimatedProjectCostUsd": "Coût estimé du projet en USD", "estimatedProjectCostRwf": "Coût estimé du projet en Rwf", "developmentDetails": "Détails du développement", "plotSizeSquareMeters": "<PERSON><PERSON> <PERSON> (En mètres carrés)", "combinedPlotSizeSquareMeters": "Taille combinée de la parcelle (En mètres carrés)", "grossFloorArea": "Surface totale", "proposedNumberOfFloorsGPlus": "Nombre d'étages proposés / G+", "numberOfParkingSpaces": "Nombre de places de stationnement", "priceOfDwellingUnit": "Prix de l'unité d'habitation", "builtUpArea": "Surface construite", "capacityInfoPeopleSeats": "Informations sur la capacité : nombre de personnes / sièges", "submittedBy": "Soumis par", "names": "Noms", "phoneNumber": "Numéro de téléphone", "userType": "Type d'utilisateur", "projectAttachmentDetails": "Détails des pièces jointes du projet", "viewDocument": "Voir le document", "loading": "Chargement", "projectAttachmentAdditionalFiles": "Fichiers supplémentaires des pièces jointes du projet", "associatedUpi": "UPI associé"}, "otherApplicationInfo": {"title": "Autres Informations", "occupancyQuestion": "Avez-vous l'occupation ?", "firstAidBoxes": "Boîtes de premiers secours ?", "disabilityGrabBars": "Barres d'appui rabattables dans les toilettes pour personnes handicapées ?", "paraLightningSystem": "Paratonnerre", "equipmentCapacity": "Capacité de l'équipement", "constructionMethod": "Méthode de construction (matériaux)", "disasterPrevention": "Prévention des catastrophes", "fireAlarmSystem": "Système d'alarme incendie avec une cloche à chaque étage", "noFireAlarmSystem": "Pourquoi pas de système d'alarme incendie avec une cloche à chaque étage", "fireExtinguishers": "Extincteurs tous les 50m à chaque étage", "noFireExtinguishers": "Pourquoi pas d'extincteurs tous les 50m à chaque étage", "exitSigns": "Panneaux de sortie fonctionnels à chaque étage", "noExitSigns": "Pourquoi pas de panneaux de sortie fonctionnels à chaque étage", "emergencyExit": "Sortie de secours à chaque étage", "floorPlan": "Plan d'étage à chaque niveau", "noFloorPlan": "Pourquoi pas de plan d'étage à chaque niveau", "floorNumberSign": "Numéro d'étage affiché à chaque niveau", "noFloorNumberSign": "Pourquoi pas de numéro d'étage affiché à chaque niveau", "elevatorFireSign": "Panneau interdisant l'utilisation des ascenseurs en cas d'incendie", "noElevatorFireSign": "Pourquoi pas de panneau interdisant l'utilisation des ascenseurs en cas d'incendie", "helicopterLanding": "Espace d'atterrissage sur le toit pour évacuation par hélicoptère en cas de problème au sol : OUI / NON (sinon pourquoi)", "terrorAttacks": "Attaques terroristes", "cctvCameras": "Caméras de surveillance (CCTV)", "noCctvCameras": "Caméras de surveillance (CCTV) (sinon pourquoi)", "metalDetectors": "Portiques et détecteurs de métaux portatifs", "noMetalDetectors": "Portiques et détecteurs de métaux portatifs (sinon pourquoi)", "underSearchMirror": "Miroir de fouille sous véhicule", "noUnderSearchMirror": "Miroir de fouille sous véhicule (sinon pourquoi)", "luggageScanners": "Scanners de bagages", "noLuggageScanners": "Scanners de bagages (sinon pourquoi)", "emergencyContacts": "Plaques indiquant les unités d'intervention d'urgence et numéros de téléphone", "noEmergencyContacts": "Plaques indiquant les unités d'intervention d'urgence et numéros de téléphone (sinon pourquoi)", "evacuationPlan": "Plan d'évacuation d'urgence", "noEvacuationPlan": "Plan d'évacuation d'urgence (sinon pourquoi)", "securityManager": "Responsable de la sécurité et caméras du personnel", "noSecurityManager": "Responsable de la sécurité et caméras du personnel (sinon pourquoi)", "internalComms": "Système de communication interne", "noInternalComms": "Système de communication interne (sinon pourquoi)", "broadband": "Internet haut débit", "noBroadband": "Internet haut débit (sinon pourquoi)", "accessCards": "Cartes d'accès pour le personnel et les visiteurs", "noAccessCards": "Cartes d'accès pour le personnel et les visiteurs (sinon pourquoi)", "fixedLineApplication": "Demande de ligne téléphonique fixe", "disabilityFacilities": "Installations pour personnes handicapées", "disabilityQuestion": "Y a-t-il des installations pour les personnes handicapées dans ce bâtiment ?", "noDisabilityFacilities": "Y a-t-il des installations pour les personnes handicapées dans ce bâtiment ? (sinon pourquoi)", "inspectionDate": "Date de l'inspection demandée", "userType": "Ingénieur de chantier / Architecte (choisir le type d'utilisateur)", "licenseNumber": "Numéro de licence", "supervisingFirm": "Ingénieur/architecte du cabinet de supervision", "remarks": "<PERSON><PERSON><PERSON>", "constructionStage": "Étape de la construction"}, "otherApplicationWithoutNCP": {"projectDetails": "Dé<PERSON> du projet", "projectBrief": "Résumé du projet", "estimatedMonthlyWaterM3": "Consommation mensuelle estimée d'eau (m3)", "distanceNearestLandLineFiberM": "Distance jusqu'à la ligne fixe ou au câble en fibre optique le plus proche (m)", "estimatedProjectCostUsd": "Coût estimé du projet en USD", "estimatedProjectCostRwf": "Coût estimé du projet en RWF", "estimatedMonthlyElectricityKwh": "Consommation mensuelle estimée d'électricité (Kwh)", "developmentDetails": "Dé<PERSON> de développement", "plotSizeSquareMeters": "<PERSON><PERSON> <PERSON> (en mètres carrés)", "grossFloorArea": "Surface totale construite", "proposedNumberOfFloorsGPlus": "Nombre d'étages proposé / G+", "numberOfParkingSpaces": "Nombre de places de stationnement", "priceOfDwellingUnit": "Prix de l'unité de logement", "builtUpArea": "Surface bâtie", "capacityInfoPeopleSeats": "Capacité : Nombre de personnes / sièges", "doItYourself": "<PERSON><PERSON><PERSON><PERSON>-vous le faire vous-même", "assignToEngineerArchitect": "Attribuer cette demande à un ingénieur/architecte", "projectName": "Nom du projet", "projectDescription": "Description du projet", "permitType": "Type de permis", "plotSize": "<PERSON><PERSON>", "buildingType": "Type de bâtiment", "projectCategory": "Catégorie du projet", "numberOfFloorGPlus": "Nombre d'étages / G+", "builtUpAreaSqm": "Surface bâtie (en mètres carrés)", "buildUpAreaTooLarge": "La surface bâtie ne peut pas être supérieure à la taille de la parcelle", "grossFloorAreaRepeat": "Surface totale construite", "numberOfParkingSpacesRepeat": "Nombre de places de stationnement", "priceDwellingUnitRwf": "Prix estimé de l'unité de logement en RWF", "descriptionOperations": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "numberOfDwellingUnits": "Nombre d'unités de logement", "estimatedMonthlyWaterM3Repeat": "Consommation mensuelle estimée d'eau (m3)", "estimatedMonthlyElectricityWatts": "Consommation mensuelle estimée d'électricité (watts)", "distanceLandLineFiberRepeat": "Distance jusqu'à la ligne fixe ou fibre optique (m)", "estimatedCostUsdRepeat": "Coût estimé du projet en USD", "estimatedCostRwfRepeat": "Coût estimé du projet en RWF", "capacityInfoRepeat": "Capacité : Nombre de personnes / sièges", "technologySurveyOptional": "Étude technologique (optionnelle)", "dataNotFoundEIA": "Données non trouvées. Veuillez soumettre votre demande à RDB pour un certificat d'accès à l'impact environnemental !", "projectAlreadyAssigned": "Vous ne pouvez pas continuer car le projet a déjà été attribué à un ingénieur/architecte.", "cancel": "Annuler", "next": "Suivant", "messageTitle": "Message", "message": "Une demande existe déjà. Veuillez en choisir une autre ou vous assurer que tous les documents requis sont téléchargés. Vous pouvez également téléverser les documents manquants.", "chooseAnotherPermit": "Choisir un autre permis", "continueCheckingDocs": "Continuer la vérification des documents !"}, "otherApplicationPermitAnswer": {"otherInformation": "Autres informations", "doYouHaveTheOccupancy": "Avez-vous l'autorisation d'occupation ?", "firstAidBoxes": "Boîtes de premiers secours ?", "disabilityToilets": "Barres d'appui rabattables dans les toilettes pour personnes handicapées ?", "paraLighteningSystem": "Système Paratonnerre", "equipmentCapacity": "Capacité de l'équipement", "constructionMethod": "Méthode de construction (matériaux)", "disasterPrevention": "Prévention des catastrophes", "fireAlarmSystem": "Système d'alarme incendie avec une cloche à chaque étage", "whyNoFireAlarmSystem": "Pourquoi pas de système d'alarme incendie avec une cloche à chaque étage", "fireExtinguishers": "Extincteurs tous les 50m à chaque étage", "whyNoFireExtinguishers": "Pourquoi pas d'extincteurs tous les 50m à chaque étage", "exitSigns": "Panneaux de sortie fonctionnels à chaque étage", "whyNoExitSigns": "Pourquoi pas de panneaux de sortie fonctionnels à chaque étage", "emergencyExit": "Sortie de secours à chaque étage", "whyNoEmergencyExit": "Pourquoi pas de sortie de secours à chaque étage", "floorPlan": "Plan d'étage à chaque niveau", "whyNoFloorPlan": "Pourquoi pas de plan d'étage à chaque niveau", "numberSign": "Numéro d'étage affiché à chaque niveau", "whyNoNumberSign": "Pourquoi pas de numéro d'étage affiché à chaque niveau", "elevatorSign": "Panneau interdisant l'utilisation des ascenseurs en cas d'incendie", "whyNoElevatorSign": "Pourquoi pas de panneau interdisant l'utilisation des ascenseurs en cas d'incendie", "helicopterLanding": "Zone d'atterrissage sur le toit du bâtiment pour évacuation par hélicoptère en cas de problème : OUI / NON (sinon pourquoi)", "whyNoHelicopterLanding": "Zone d'atterrissage sur le toit pour évacuation par hélicoptère (sinon pourquoi)", "terrorAttacks": "Attaques terroristes", "cctvCameras": "Caméras de surveillance (CCTV)", "whyNoCctvCameras": "Caméras de surveillance (CCTV) (sinon pourquoi)", "metalDetector": "Détecteurs de métaux portatifs ou intégrés", "whyNoMetalDetector": "Détecteurs de métaux (sinon pourquoi)", "underSearchMirror": "Miroir de fouille sous véhicule", "whyNoUnderSearchMirror": "Miroir de fouille sous véhicule (sinon pourquoi)", "luggageScanners": "Scanners de bagages", "whyNoLuggageScanners": "Scanners de bagages (sinon pourquoi)", "emergencyContactPlates": "Plaques indiquant les unités d'intervention d'urgence et les numéros de téléphone", "whyNoEmergencyContactPlates": "Plaques indiquant les unités d'intervention d'urgence et les numéros (sinon pourquoi)", "evacuationPlan": "Plan d'évacuation d'urgence", "whyNoEvacuationPlan": "Plan d'évacuation d'urgence (sinon pourquoi)", "securityManagerCameras": "Responsable de sécurité et caméras du personnel", "whyNoSecurityManagerCameras": "Responsable de sécurité et caméras du personnel (sinon pourquoi)", "internalComms": "Système de communication interne", "whyNoInternalComms": "Système de communication interne (sinon pourquoi)", "broadband": "Internet haut débit", "whyNoBroadband": "Internet haut débit (sinon pourquoi)", "accessCards": "Cartes d'accès pour le personnel et les visiteurs", "whyNoAccessCards": "Cartes d'accès pour le personnel et les visiteurs (sinon pourquoi)", "fixedTelephone": "Demande de ligne téléphonique fixe", "facilitiesForPWD": "Installations pour personnes handicapées", "facilitiesForDisabled": "Y a-t-il des installations pour les personnes handicapées dans ce bâtiment ?", "whyNoFacilitiesForDisabled": "Y a-t-il des installations pour les personnes handicapées dans ce bâtiment ? (sinon pourquoi)", "inspectionDate": "Date de l'inspection demandée", "userType": "Ingénieur de chantier / Architecte (choisir le type d'utilisateur)", "licenseNumber": "Numéro de licence", "supervisingFirm": "Ingénieur/architecte du cabinet de supervision", "remarks": "<PERSON><PERSON><PERSON>", "stageOfConstruction": "Étape de la construction"}, "eiaFormChecker": {"checkingEIA": "Gusuzuma icyemezo cya EIA", "eiaApplyMessage": "Kanda hano usabe", "certificateNumber": "Nimero y'icyemezo", "upi": "UPI"}, "assignApplicationDetail": {"parcelDetails": "<PERSON><PERSON><PERSON> de <PERSON> parcelle", "plotNumberUPI": "Numéro de parcelle / UPI", "projectName": "Nom du projet", "plannedFor": "Prévu pour", "location": "Emplacement", "applicationDetails": "<PERSON><PERSON><PERSON> de la demande", "requestFrom": "De<PERSON>e provenant de", "plotSize": "<PERSON><PERSON>", "buildUpArea": "Surface construite", "applicationDate": "Date de la demande", "rejectRequest": "<PERSON><PERSON><PERSON> la demande", "acceptRequest": "Accepter la demande"}, "assignedApplications": {"welcome": "Bienvenue !", "projectRequests": "De<PERSON><PERSON> de projet", "searchPlaceholder": "Recher<PERSON> quelque chose...", "filterByStatusSize": "Filtrer par statut et taille", "requestFrom": "<PERSON><PERSON><PERSON> de", "projectName": "Nom du projet", "upi": "UPI", "plotSize": "<PERSON><PERSON>", "timelineForSubmission": "Calendrier de soumission", "appliedFor": "De<PERSON>é pour", "applicationDate": "Date de la demande", "status": "Statut", "viewOffer": "Voir l'offre", "applyForPermit": "Demander une autorisation", "request": "<PERSON><PERSON><PERSON>", "applicationRequest": "<PERSON><PERSON><PERSON> de dossier"}, "projectDetailContinue": {"complete": "Compléter <PERSON>", "application": "demande", "projectInformation": "Informations sur le projet", "checkToAddAssociatedUpi": "Cocher pour ajouter le UPI associé", "projectName": "Nom du projet", "projectDescription": "Description du projet", "permitType": "Type de permis", "buildingType": "Type de bâtiment", "projectCategory": "Catégorie de projet", "plotSize": "<PERSON><PERSON> <PERSON> (en mètres carrés)", "combiningPlotSize": "Fusion de la taille des parcelles", "numberOfFloor": "Nombre d'étages / G+", "buildUpArea": "Surface construite (en mètres carrés)", "buildUpAreaError": "La surface construite ne peut pas être supérieure à la taille de la parcelle.", "grossFloorArea": "Surface totale construite", "numberOfParkingSpaces": "Nombre de places de stationnement", "estimatedPriceOfDwellingUnit": "Prix estimé de l'unité de logement en RWF", "numberOfDwellingUnits": "Nombre d'unités de logement", "descriptionOfOperations": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "estimatedWaterConsumption": "Consommation mensuelle estimée d'eau (m3)", "estimatedElectricityConsumption": "Consommation mensuelle estimée d'électricité (watts)", "distanceToLandline": "Distance jusqu'à la ligne fixe / câble fibre optique le plus proche (m)", "estimatedProjectCostUSD": "Coût estimé du projet en USD", "estimatedProjectCostRWF": "Coût estimé du projet en RWF", "capacityInformation": "Informations de capacité : nombre de personnes / sièges", "selectedCategoryUse": "Catégorie d'utilisation sélectionnée", "selectedUse": "Utilisation sélectionnée", "isFromOldSystem": "La demande provient-elle de l'ancien système", "isUnderMortgage": "Soumis à une hypothèque", "isUnderRestriction": "Soumis à une restriction", "cancel": "Annuler", "next": "Suivant", "associatedUpi": "UPI associé"}, "continuePendingApplication": {"step": "Étape", "developmentDetails": "Détails du développement", "oldSystemHeaderNote": "Votre projet existe déjà dans l'ancien système, veuille<PERSON> compléter les détails manquants pour continuer", "checkToAddAssociatedUpi": "Cochez pour ajouter un UPI associé", "projectName": "Nom du projet", "projectDescription": "Description du projet", "permitType": "Type de permis", "buildingType": "Type de bâtiment", "plotSize": "<PERSON><PERSON> <PERSON> (en mètres carrés)", "buildUpArea": "Surface construite (en mètres carrés)", "buildUpAreaError": "La surface construite ne peut pas être supérieure à la taille de la parcelle.", "numberOfFloor": "Nombre d'étages / G+", "grossFloorArea": "Surface totale construite", "numberOfParkingSpaces": "Nombre de places de stationnement", "estimatedPriceOfDwellingUnitRWF": "Prix estimé de l'unité de logement en RWF", "numberOfDwellingUnits": "Nombre d'unités de logement", "descriptionOfOperations": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "estimatedWaterConsumption": "Consommation mensuelle estimée d'eau (m³)", "estimatedElectricityConsumption": "Consommation mensuelle estimée d'électricité (watts)", "distanceToLandline": "Distance jusqu'à la ligne fixe ou câble fibre optique le plus proche (m)", "estimatedProjectCostUSD": "Coût estimé du projet en USD", "estimatedProjectCostRWF": "Coût estimé du projet en RWF", "capacityInformation": "Capacité : nombre de personnes / sièges", "selectedCategoryUse": "Catégorie d'utilisation sélectionnée", "selectedUse": "Utilisation sélectionnée", "isFromOldSystem": "La demande provient-elle de l'ancien système", "isUnderMortgage": "Sous hypothèque", "isUnderRestriction": "Sous restriction", "cancel": "Annuler", "update": "Mettre à jour", "next": "Suivant", "associatedUpi": "UPI associé"}, "projectModification": {"title": "Modification du projet", "projectName": "Nom du projet", "projectDescription": "Description du projet", "plotSize": "<PERSON><PERSON> <PERSON> (en mètres carrés)", "permitType": "Type de permis", "buildingType": "Type de bâtiment", "projectCategory": "Catégorie du projet", "builtUpArea": "Surface construite (en mètres carrés)", "builtUpAreaError": "La surface construite ne peut pas être supérieure à la taille de la parcelle.", "numberOfFloor": "Nombre d'étages / G+", "grossFloorArea": "Surface totale construite", "numberOfParkingSpaces": "Nombre de places de stationnement", "estimatedPriceDwellingRwf": "Prix estimé de l'unité de logement en RWF", "descriptionOfOperations": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "numberOfDwellingUnits": "Nombre d'unités de logement", "estimatedWaterConsumption": "Consommation mensuelle estimée d'eau (m³)", "estimatedElectricityConsumption": "Consommation mensuelle estimée d'électricité (watts)", "distanceToLandline": "Distance jusqu'à la ligne fixe ou fibre optique la plus proche (m)", "estimatedProjectCostUsd": "Coût estimé du projet en USD", "estimatedProjectCostRwf": "Coût estimé du projet en RWF", "capacityInformation": "Capacité : nombre de personnes / sièges", "technologySurvey": "Étude technologique (optionnelle)", "cancel": "Annuler", "next": "Suivant"}, "relatedPermitType": {"step": "Étape", "projectDetails": "Dé<PERSON> du projet", "addAssociatedUpi": "Cochez pour ajouter un UPI associé", "doItYourself": "Souhaitez-vous le faire vous-même ?", "assignToEngineer": "Attribuer cette demande à un ingénieur / architecte", "permitType": "Type de permis", "plotSize": "<PERSON><PERSON>", "buildingType": "Type de bâtiment", "projectCategory": "Catégorie du projet", "numberOfFloor": "Nombre d'étages / G+", "builtUpArea": "Surface construite (en mètres carrés)", "builtUpAreaError": "La surface construite ne peut pas être supérieure à la taille de la parcelle.", "grossFloorArea": "Surface totale construite", "parkingSpaces": "Nombre de places de stationnement", "dwellingPriceRwf": "Prix estimé de l'unité de logement en RWF", "descriptionOfOperation": "Description des opérations", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "numberOfDwellingUnits": "Nombre d'unités de logement", "waterConsumption": "Consommation mensuelle estimée d'eau (m³)", "electricityConsumption": "Consommation mensuelle estimée d'électricité (watts)", "distanceToLandline": "Distance jusqu'à la ligne fixe ou câble fibre optique le plus proche (m)", "projectCostUsd": "Coût estimé du projet en USD", "projectCostRwf": "Coût estimé du projet en RWF", "capacityInformation": "Informations sur la capacité : nombre de personnes / sièges", "technologySurvey": "Étude technologique (optionnelle)", "assignConditionNote": "Sélectionnez cette option si vous souhaitez attribuer ce projet à un ingénieur/architecte ou continuer vous-même.", "submitEIARequestNote": "Données non trouvées. Veuillez soumettre votre demande à RDB pour un certificat d'accès environnemental !", "notAllowedToProceed": "Vous ne pouvez pas continuer car le projet est déjà attribué à un ingénieur/architecte.", "previous": "Précédent", "next": "Suivant"}, "applicationRoadMap": {"step": "Étape"}, "otherApplicationDocument": {"associatedUpiTitle": "UPI associé", "projectAttachmentTitle": "Pièce jointe du projet", "requiredTitle": "Documents requis", "requiredBoqLink": "Cliquez ici pour télécharger le fichier BOQ", "requiredWarning": "Le fichier BOQ est au format Excel. Assurez-vous de le télécharger et de remplir vos informations. Pour les autres fichiers, seuls les PDF sont autorisés", "uploadedTitle": "Documents téléversés", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "wait": "Veuillez patienter...", "nofile": "aucun fichier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "previous": "Précédent", "saveDocument": "Enregistrer le document", "submit": "So<PERSON><PERSON><PERSON>", "resubmit": "Soumettre à nouveau", "confirm": "Confirmer", "certificationText": "Il est de mon devoir solennel de certifier la véracité des informations soumises dans cette demande et j'accepte les termes et conditions.", "cancel": "Annuler", "deleteFileTitle": "Vous êtes sur le point de supprimer {{fileName}} ?", "deleteFileBodyA": "La suppression de", "deleteFileBodyB": "entraînera la suppression de toutes vos informations de notre base de données.", "confirmDelete": "Oui, supprimer !", "close": "<PERSON><PERSON><PERSON>"}, "additionalUploadFile": {"title": "Documents supplémentaires", "uploadedDocuments": "Documents téléversés", "additionalFile": "Fichier supplémentaire", "onlyPdfAllowed": "Seuls les fichiers PDF sont autorisés, sauf pour le BOQ", "noFile": "aucun fichier", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "remove": "<PERSON><PERSON><PERSON>", "viewDocumentTitle": "Voir le document", "youAreAboutToDelete": "Vous êtes sur le point de supprimer ", "descriptionA": "La suppression de ", "descriptionB": "entraînera la suppression de toutes vos informations de notre base de données.", "close": "<PERSON><PERSON><PERSON>", "confirm": "Oui, supprimer !", "inProgress": "L'application supprime le fichier...", "wait": "<PERSON><PERSON><PERSON>"}, "otherApplication": {"projectDetails": "Dé<PERSON> du projet", "doItYourself": "<PERSON><PERSON><PERSON><PERSON>-vous le faire vous-même", "assignToEngineerArchitect": "Attribuer cette demande à un ingénieur / architecte", "projectBrief": "Résumé du projet", "estimatedMonthlyWaterM3": "Consommation mensuelle estimée d'eau (m³)", "distanceToLandline": "Distance jusqu'à la ligne fixe / câble fibre optique le plus proche (m)", "estimatedProjectCostUsd": "Coût estimé du projet en USD", "estimatedProjectCostRwf": "Coût estimé du projet en RWF", "estimatedMonthlyElectricityKwh": "Consommation mensuelle estimée d'électricité (Kwh)", "developmentDetails": "Détails du développement", "plotSizeSquareMeters": "<PERSON><PERSON> <PERSON> (en mètres carrés)", "proposedNumberOfFloorsGPlus": "Nombre d'étages proposé / G+", "numberOfParkingSpaces": "Nombre de places de stationnement", "priceOfDwellingUnit": "Prix de l'unité de logement", "builtUpArea": "Surface construite", "grossFloorArea": "Surface totale construite", "percentageSpaceUse": "Pourcentage d'utilisation de l'espace", "numberOfDwellingUnits": "Nombre d'unités de logement", "descriptionOperations": "Description des opérations", "capacityInfoPeopleSeats": "Informations sur la capacité : nombre de personnes / sièges", "permitType": "Type de permis", "buildingType": "Type de bâtiment", "category": "Catégorie du projet", "upi": "UPI", "dataNotFoundEIA": "Données non trouvées. Veuillez soumettre votre demande à RDB pour un certificat d'impact environnemental !", "submitRdb": "Soumettez votre demande à RDB pour l'EIA", "projectAlreadyAssigned": "Vous ne pouvez pas continuer car le projet est déjà attribué à un ingénieur/architecte", "prev": "Précédent", "next": "Suivant", "cancel": "Annuler", "chooseAnotherPermit": "Choisir un autre permis", "continueCheckingDocs": "Continuer la vérification des documents !", "message": "Message", "applicationExists": "Une demande existe déjà. Veuillez envisager une autre ou assurez-vous que tous les documents requis sont téléchargés. Vous pouvez aussi téléverser les documents manquants.", "loading": "Chargement des données ....."}, "projectAttachment": {"title": "Pièce jointe du projet", "requiredTitle": "Document requis", "requiredDocuments": "Documents requis", "choose": "Choi<PERSON>", "chooseUploadDocument": "Choisir un document à téléverser", "uploadDocumentPdf": "Téléverser document.pdf", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "uploadedTitle": "Documents téléversés", "deleteFileTitle": "Vous êtes sur le point de supprimer ", "deleteFileBodyA": "La suppression de ", "deleteFileBodyB": " entraînera la suppression de toutes vos informations de notre base de données.", "wait": "Veuillez patienter...", "previous": "Précédent", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "certificationText": "C'est mon devoir solennel de certifier que les informations soumises dans cette demande sont exactes et j'accepte les termes et conditions.", "cancel": "Annuler", "confirmDelete": "Oui, supprimer !", "nofile": "aucun fichier", "requiredDocumentsList": "Documents requis", "uploadedDocumentsList": "Documents téléversés", "requiredWarning": "Le fichier BOQ est au format Excel. Assurez-vous de le télécharger et de le remplir. Seuls les fichiers PDF sont autorisés pour les autres documents.", "requiredBoqLink": "Cliquez ici pour télécharger le fichier BOQ"}, "factorLogin": {"verify": "Vérifier", "confirm": "Confirmer", "login": "Se connecter"}}