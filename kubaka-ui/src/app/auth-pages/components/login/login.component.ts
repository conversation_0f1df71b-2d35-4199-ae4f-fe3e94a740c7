import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AppConfig } from 'src/app/app.config';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { AuthService } from '../../services/auth.service';
import { TokenService } from '../../services/token.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  saveForm!: UntypedFormGroup;
  submitted!: boolean
  secretKey = 'your-secret-key';
  constructor(
    private formBuilder: UntypedFormBuilder,
    private router: Router,
    private authService: AuthService,
    private tokenService: TokenService,
    private utilService: UtilService,
    private appConfig: AppConfig
  ) { }

  ngOnInit(): void {
    this.saveForm = this.formBuilder.group({
      username: ["", [Validators.required]],
      password: ["", [Validators.required]],
    });
  }





  onSubmit() {

    if (this.saveForm.invalid) {
      this.submitted = false;
      this.utilService.showNotification(NOTIFICATION_COLOR.error, 'Please fill username and password', "bottom", "center")
      return;
    } else if (this.saveForm.value.username.startsWith(' ') || this.saveForm.value.username.startsWith(' ')) {
      this.submitted = false;
      return;
    } else {

      this.saveForm.value.email = this.saveForm.value.username;
      this.submitted = true;
      this.authService.saveAssetWithPath({
        "email": this.saveForm.value.username,
        "password": this.saveForm.value.password
      }, environment.authUrl + 'auth/login')
        .subscribe(data => {
          if (data.data.status === 404) {
            this.submitted = false;
            this.utilService.showNotification(NOTIFICATION_COLOR.error, 'User not found', 'center', 'bottom');
          } else if (data.data.status === 403) {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, data.data.response, 'center', 'bottom');
            this.submitted = false;
          } else if (data.data.status === 401) {
            this.utilService.showNotification(NOTIFICATION_COLOR.error, data.data.response, 'center', 'bottom');
            this.submitted = false;
          } else {
            if (localStorage.getItem(this.appConfig.JWT_Token)) {
              this.submitted = false;
              this.utilService.showNotification(NOTIFICATION_COLOR.error, 'You have another session running in another tab please logout please first', 'center', 'bottom');
            } else {
              // this.submitted = false;
              // this.tokenService.setToken(data);
              // this.router.navigate(['/account/dashboard']);
              this.utilService.showNotification(NOTIFICATION_COLOR.success, data.data.message, 'center', 'bottom');
              // const encryptedString: any = this.utilService.encryptString(this.saveForm.value.username, '123456');
              // this.router.navigate(['/2-factor-auth/', encryptedString]);
              // this.router.navigate(['/2-factor-auth'], { queryParams: { encryptedString } });

              this.utilService.encryptString(this.saveForm.value.username, '123456').then((email) => {
                this.router.navigate(['/2-factor-auth'], { queryParams: { email } });
              })
            }
          }
        }, (error) => {
          this.submitted = false;
        });
    }
  }
}
