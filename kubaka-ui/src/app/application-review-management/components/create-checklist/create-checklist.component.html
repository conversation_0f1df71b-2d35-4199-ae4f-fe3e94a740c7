<div class="step-panel_body">
    <form [formGroup]="checkListForm">
        <div class="form-set">
            <div class="form-input">
                <label>Recommendation</label>
                <div>
                    <textarea name="decision" formControlName="decision" id="decision" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="form-input">
                <label>Conditions of Approval</label>
                <div>
                    <textarea name="conditionsOfApproval" formControlName="conditionsOfApproval"
                        id="conditionsOfApproval" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="form-input">
                <label>Urban Planning comments</label>
                <div>
                    <textarea name="urbanPlanningComment" formControlName="urbanPlanningComment"
                        id="urbanPlanningComment" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="form-input">
                <label>Site Analysis Planning comments</label>
                <div>
                    <textarea name="siteAnalysisComment" formControlName="siteAnalysisComment" id="siteAnalysisComment"
                        cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="form-input">
                <label>Architectural Comments</label>
                <div>
                    <textarea name="architecturalComment" formControlName="architecturalComment"
                        id="architecturalComment" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="form-input">
                <!-- <label>Civil Engineering Comment</label> -->
                <label>Electrical and mechanical conditional</label>
                <div>
                    <textarea name="civilEngineeringComment" formControlName="civilEngineeringComment"
                        id="civilEngineeringComment" cols="5" rows="10"></textarea>
                </div>
            </div>

            <div class="form-input">
              <label>Structural Comment</label>
              <div>
                  <textarea name="structuralComment" formControlName="structuralComment" id="structuralComment"
                      cols="5" rows="10"></textarea>
              </div>
          </div>

            <div class="form-input">
                <label>Recommended decision</label>
                <div>
                    <select name="approvalStatusId" id="approvalStatusId" formControlName="approvalStatusId" required>
                        <option *ngFor="let op of approvalStatuses" [value]="op.id"> {{op.name}} </option>
                    </select>
                </div>
            </div>
            <div class="form-input">
                <app-input-file-upload (fileUpload)="getFileToSave($event)"></app-input-file-upload>
            </div>

            <!-- <br>
            <div class="form-input">
                <app-input-file-upload (fileUpload)="getFileToSave($event)"></app-input-file-upload>
            </div> -->
        </div>
    </form>
</div>
<div class="modol-content" *ngIf="!this.existingData.id">
    <div class="kbk-x-c sp-sm mt-md">
        <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancelCheckList()"> Cancel </button>
        <button class="kbk-btn kbk-btn-main" type="button" (click)="onSubmitChecklist()"> Submit </button>
    </div>
</div>
