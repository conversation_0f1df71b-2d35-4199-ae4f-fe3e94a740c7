<div class="pg-chatroom">
  <div class="chatlist">
    <div class="chatlist-hder">
      <div class="mb-3">
        <span class="hder" aria-label="header tittle">{{"chatMessageComponent.messageTxt" | translate }}</span>
        <div class="tabs">
          <!-- Tabs for Users and Applicants -->
          <button class="kbk-btn kbk-btn-sm kbk-btn-sec" [class.active]="activeTab === 'Users'" (click)="setActiveTab('Users')">{{"chatMessageComponent.users" | translate }}</button>
          <button class="kbk-btn kbk-btn-sm kbk-btn-sec" *ngIf="currentUser?.data?.user?.role.code !== 'APP' && currentUser?.data?.user?.role.code !== 'STF'"  [class.active]="activeTab === 'Applicants'" (click)="setActiveTab('Applicants')">{{"chatMessageComponent.applicants" | translate }}</button>
        </div>
      </div>
      <div class="form-input w-aut clear-m">
        <div class="form-input_search" *ngIf="activeTab === 'Users'" >
          <input type="text" name="searchUser" [placeholder]="'chatMessageComponent.sPlaceholder' | translate"
          [(ngModel)]="searchData.searchUser" />
          <button type="button" class="btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <g data-name="Layer 2">
                <g data-name="search">
                  <rect width="24" height="24" opacity="0" />
                  <path
                    d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                </g>
              </g>
            </svg>
          </button>
        </div>
        <div class="form-input_search" *ngIf="activeTab !== 'Users'" >
          <input type="text" name="searchApplicant"
          [(ngModel)]="searchData.searchApplicant" [placeholder]="'chatMessageComponent.sApplicantPlaceholder' | translate" />
          <button type="button" class="btn">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <g data-name="Layer 2">
                <g data-name="search">
                  <rect width="24" height="24" opacity="0" />
                  <path
                    d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z" />
                </g>
              </g>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div class="chatlist-bdy">
      <ul *ngIf="activeTab === 'Users'" class="contlist">
        <li class="contlist-item" *ngFor="let us of users | genericfilter: searchData.searchUser" (click)="getUser(us)">
          <a class="contlist-link" routerLinkActive="active">
            <div class="user-avtar">
              <img src="assets/imgs/profile1.svg" alt="profile picture" />
            </div>
            <div class="user-txt">
              <h6>{{us.lastName | titlecase}} {{us.firstName | titlecase}}</h6>
              <p>{{ us?.agency?.name }} </p>
            </div>
            <div class="user-xn">
              <!-- <span class="xn-dt">1 min</span> -->
              <span class="xn-cnt">{{us.unreadMessageCount}}</span>
              <!-- <span class="xn-cnt"></span> -->
              
            </div>
          </a>
        </li>
        <!-- <li class="contlist-item">
          <a class="contlist-link">
            <div class="user-avtar">
              <img src="assets/imgs/profile1.svg" alt="profile picture" />
            </div>
            <div class="user-txt">
              <h6>Kabera Jerome</h6>
              <p>Hello</p>
            </div>
            <div class="user-xn">
              <span class="xn-dt">10 min</span>
              <span class="xn-cnt">3</span>
            </div>
          </a>
        </li>
        <li class="contlist-item">
          <a class="contlist-link ">
            <div class="user-avtar">
              <img src="assets/imgs/profile1.svg" alt="profile picture" />
            </div>
            <div class="user-txt">
              <h6>Bugingo Jean Baptitse</h6>
              <p>Salut!</p>
            </div>
            <div class="user-xn">
              <span class="xn-dt">Yesterday</span>
              <span class="xn-cnt">2</span>
            </div>
          </a>
        </li> -->
      </ul>
      <ul   *ngIf="activeTab === 'Applicants'" class="contlist">
        <li class="contlist-item" *ngFor="let us of applicants | genericfilter:  searchData.searchApplicant" (click)="getUser(us)">
          <a class="contlist-link active" routerLinkActive="active">
            <div class="user-avtar">
              <img src="assets/imgs/profile1.svg" alt="profile picture" />
            </div>
            <div class="user-txt">
              <h6>{{us.lastName | titlecase}} {{us.firstName | titlecase}}
                {{us.unreadMessageCount}}
              </h6>
              <!-- <p>Mwiriwe?, Mwadufasha</p> -->
            </div>
            <div class="user-xn">
              <!-- <span class="xn-dt">1 min</span> -->
              <span class="xn-cnt">{{us.unreadMessageCount}}</span>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </div>
  <div class="chatcontent">
    <form [formGroup]="messageForm" (ngSubmit)="onSubmit()">
      <div class="chatwrap">
        <div class="chathder">
          <div class="user-avtar">
            <img src="assets/imgs/profile1.svg" alt="profile picture" />
          </div>
          <div class="user-txt">
            <h6>{{userData.lastName | titlecase}} {{userData.firstName | titlecase}}</h6>
            <span class="status offline"></span>
          </div>
        </div>
        <div class="chatbdy">
          <div class="chatbox">
            <div class="chatbox-bdy">
              <!-- Loop through all messages -->
              <ng-container *ngFor="let message of messages">
                <!-- Incoming messages (where current user is the receiver) -->
                <div class="chatbox_msg" *ngIf="message.senderId !== this.currentUser.userId">
                  <div class="msgbox">
                    <div class="msgbox-txt">
                      <p>{{ message.content }}</p>
                    </div>
                    <div class="msgbox-tm">
                      <span class="tm">{{ message.createdAt }}</span>
                      <span class="typ">{{"chatMessageComponent.received" | translate }}</span>
                    </div>
                  </div>
                </div>

                <!-- Outgoing messages (where current user is the sender) -->
                <div class="chatbox_msg outmsg" *ngIf="message.senderId === this.currentUser.userId">
                  <div class="msgbox">
                    <div class="msgbox-txt">
                      <p>{{ message.content }}</p>
                    </div>
                    <div class="msgbox-tm">
                      <span class="tm">{{ message.createdAt }}</span>
                      <span class="typ">{{"chatMessageComponent.sent" | translate }}</span>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
            <!-- <div class="chatbox-bdy">
              <div class="chatbox_msg">
                <div class="msgbox">
                  <div class="msgbox-txt">
                    <p>Mwiriwe?, Mwadufasha</p>
                    <p>Twasabye ibyangobwa arko</p>
                    <p>Ntagisubizo turahabwa</p>
                  </div>
                  <div class="msgbox-tm">
                    <span class="tm">10:50 AM</span>
                    <span class="typ">Sent</span>
                  </div>
                </div>
              </div>
              <div class="chatbox_msg outmsg">
                <div class="msgbox">
                  <div class="msgbox-txt">
                    <p>Muraho!?</p>
                    <p>Ubutumwa byanyu twabubonye</p>
                    <p>Muraza guhabwa igisubizo vuba</p>
                  </div>
                  <div class="msgbox-tm">
                    <span class="tm">10:52 AM</span>
                    <span class="typ">Sent</span>
                  </div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
        <div class="chatfter">
          <div class="form-input">
            <div>
              <textarea name="content" id="content" formControlName="content"></textarea>
            </div>
          </div>
          <button type="submit" class="kbk-btn kbk-btn-sec" [disabled]="messageForm.invalid">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <g id="Layer_2" data-name="Layer 2">
                <g id="Layer_1-2" data-name="Layer 1">
                  <g id="ikon-send">
                    <rect class="cls-1" width="24" height="24" />
                    <path class="cls-2"
                      d="M21.49,3.44a1.19,1.19,0,0,0,0-.26.36.36,0,0,0,0-.09,1.17,1.17,0,0,0-.19-.3,1.17,1.17,0,0,0-.3-.19l-.09,0a1.09,1.09,0,0,0-.26-.05h-.08a1.07,1.07,0,0,0-.31.06l-17,5.95a1,1,0,0,0-.08,1.85l7.3,3.25,3.25,7.3a1,1,0,0,0,.91.59h0a1,1,0,0,0,.9-.67l6-17a1.07,1.07,0,0,0,.06-.31S21.49,3.47,21.49,3.44ZM6.21,9.56,16.69,5.89l-5.76,5.77Zm8.23,8.23-2.1-4.72,5.77-5.76Z" />
                  </g>
                </g>
              </g>
            </svg>
          </button>
          <!-- <button type="submit" class="kbk-btn kbk-btn-sec">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <g id="Layer_2" data-name="Layer 2">
                <g id="Layer_1-2" data-name="Layer 1">
                  <g id="ikon-at">
                    <rect class="cls-1" width="24" height="24" />
                    <path class="cls-2"
                      d="M9.29,21a6.26,6.26,0,0,1-4.43-1.88,6,6,0,0,1-.23-8.48h0L12,3.2A4.1,4.1,0,0,1,15,2a4.46,4.46,0,0,1,3.19,1.35,4.36,4.36,0,0,1,.15,6.13l-7.4,7.43a2.56,2.56,0,0,1-1.81.75,2.71,2.71,0,0,1-2-.82,2.68,2.68,0,0,1-.08-3.77l6.83-6.86a1,1,0,0,1,1.39.28,1,1,0,0,1,0,1.13L8.47,14.48a.68.68,0,0,0,.*********,0,0,0,.***********,0,0,0,.4-.16l7.39-7.43a2.36,2.36,0,0,0-.15-3.31,2.38,2.38,0,0,0-3.27-.15L6.06,12a4,4,0,0,0,.2,5.65l0,0a4.21,4.21,0,0,0,3,1.29,3.63,3.63,0,0,0,2.61-1.06l7.39-7.43a1,1,0,1,1,1.42,1.41l-7.39,7.43A5.64,5.64,0,0,1,9.29,21Z" />
                  </g>
                </g>
              </g>
            </svg>
          </button> -->
        </div>
      </div>
    </form>
  </div>
</div>
<script>
  function openTab(evt, tabName) {
    // Hide all tab content
    var tabcontent = document.getElementsByClassName("tabcontent");
    for (var i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }

    // Remove the active class from all tab links
    var tablinks = document.getElementsByClassName("tablink");
    for (var i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }

    // Show the current tab content and add an active class to the clicked tab link
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
  }
</script>
