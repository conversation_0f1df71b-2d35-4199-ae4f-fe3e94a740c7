import { Component } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { jwtDecode } from 'jwt-decode';
import { AuthService } from 'src/app/auth-pages/services/auth.service';
import { SessionService } from 'src/app/authentication-services/session.service';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import {
  NOTIFICATION_COLOR,
  UtilService,
} from 'src/app/shared/services/util.service';
import { environment } from 'src/environments/environment';
import { interval, Subscription, takeWhile } from 'rxjs';
import { ApplicationService } from 'src/app/application/services/application.service';

@Component({
  selector: 'app-chat-bot',
  templateUrl: './chat-bot.component.html',
  styleUrls: ['./chat-bot.component.scss'],
})
export class ChatBotComponent {
  messages: any[] = ['How can we help with <PERSON><PERSON><PERSON>?'];
  userMessage: string = '';
  currentUser: any;
  userForm!: UntypedFormGroup;
  // private subscription!: Subscription;
  issueCategories: any[] = [];
  agencies: any[] = [];
  selectedFiles: File[] = [];
  isOpenConversation: boolean = false;
  attachementsServerUrl = environment?.uploadedDocumentUrl || 'https://api-gatway.kubaka.gov.rw/attachment/path/';
  private subscription!: Subscription;

  constructor(
    private sessionService: SessionService,
    private authService: AuthService,
    private formBuilder: UntypedFormBuilder,
    private utilsService: UtilService,
    private applicationService: ApplicationService
  ) {
    if (this.sessionService.getSession()) {
      this.currentUser = this.sessionService.getSession();
      this.currentUser.userId = (
        jwtDecode(this.currentUser.data.token.access_token) as any
      ).UserId;
      this.startBackgroundFetch();
    }

    this.authService
      .findAllWithPath(environment.chatUrl + 'issueCategory')
      .subscribe((data) => {
        this.issueCategories = data;
      });

    this.authService
      .findAllWithPath(environment.authUrl + APIURLPATH.AGENCIES)
      .subscribe((data) => {
        this.agencies = data;
      });
  }

  findThePhoneConversation() {
    if (this.userForm.value.phoneNumber) {
      this.authService
        .findAllWithPath(
          environment.chatUrl +
            'chatBoard/phone/' +
            this.userForm.value.phoneNumber
        )
        .subscribe(
          (data) => {
            this.messages = data;
            if (data.length > 0) {
              this.getChatAttachments();
            }
          },
          (error) => {
            // this.onSubmit();
          }
        );
    }
  }

  startBackgroundFetch(): void {
    //  setInterval(() => {
    //     // this.loadMessages();
    //     this.findThePhoneConversation();
    //   }, 10000); // Fetch every 10 seconds
    this.subscription = interval(20000).subscribe(() => {
      // if (!this.isDestroyed) {
      this.findThePhoneConversation();
      // }
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    // throw new Error('Method not implemented.');

    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  ngOnInit(): void {
    this.userForm = this.formBuilder.group({
      senderNames: [''],
      agencyId: [''],
      content: [''],
      issueCategoryId: [''],
      phoneNumber: [''],
      isReply: ['0', Validators.required],
      documentIds: [[]],
    });

    if (this.currentUser) {
      this.userForm.controls['senderNames'].setValue(
        this.currentUser.data.user.firstName +
          ' ' +
          this.currentUser.data.user.lastName
      );
      this.userForm.controls['phoneNumber'].setValue(
        this.currentUser.data.user.phoneNumber
      );
      this.findThePhoneConversation();
    }

    if (
      this.currentUser &&
      this.currentUser.data &&
      this.currentUser.data.user &&
      this.currentUser.data.user.agency
    ) {
      this.userForm.controls['agencyId'].setValue(
        this.currentUser.data.user.agency.id
      );
    }
  }

  sendMessage() {
    // if (this.userMessage.trim()) {
    //   this.messages.push(this.userMessage);
    //   this.userMessage = ''; // Clear the input
    // }
    this.onSubmit();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      const selected = Array.from(input.files);
      this.selectedFiles = selected;
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  getFileIcon(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf':
        return 'assets/ikons/SVG/ikon-pdf-file.svg';
      case 'xls':
      case 'xlsx':
        return 'assets/ikons/SVG/ikon-xlsx-file.svg';
      case 'ppt':
      case 'pptx':
        return 'assets/ikons/SVG/ikon-pptx-file.svg';
      case 'zip':
      case 'rar':
        return 'assets/ikons/SVG/ikon-zip-file.svg';
      default:
        return 'assets/ikons/SVG/ikon-filec.svg';
    }
  }

  isImageFile(fileName: string): boolean {
    const ext = fileName?.toLowerCase().split('.').pop();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext || '');
  }
  
  onSubmit() {
    this.userForm.controls['content'].setValue(this.userMessage);

    if (
      !this.userForm.value.phoneNumber ||
      this.userForm.value.phoneNumber.length < 10
    ) {
      this.utilsService.showNotification(
        NOTIFICATION_COLOR.error,
        'Phone number should contains 10 digits',
        'bottom',
        'center'
      );
    } else if (
      this.userForm.value.agencyId &&
      this.userForm.value.issueCategoryId &&
      this.userForm.value.phoneNumber &&
      this.userForm.value.content
    ) {
      this.saveMessage();
    } else {
      this.utilsService.showNotification(
        NOTIFICATION_COLOR.error,
        'Please fill phone number, category, agency and message to proceed',
        'bottom',
        'center'
      );
    }
  }

 async saveMessage() {
    if (this.selectedFiles && this.selectedFiles.length > 0) {
      try {
        await this.uploadChatAttachment();
      } catch (error) {
        this.utilsService.showNotification(
          NOTIFICATION_COLOR.error,
          'Error uploading attachments',
          'bottom',
          'center'
        );
        return;
      }
    }

    this.authService
      .saveAssetWithPath(this.userForm.value, environment.chatUrl + 'chatBoard')
      .subscribe((data) => {
        this.userMessage = '';
        this.selectedFiles = [];
        this.findThePhoneConversation();
      });
  }

  uploadChatAttachment(): Promise<any> {
    return new Promise((resolve, reject) => {
      const formData = new FormData();

      if (this.selectedFiles && this.selectedFiles.length > 0) {
        for (const file of this.selectedFiles) {
          formData.append('files', file, file.name);
        }
      }

      this.applicationService
        .saveAssetWithPathFormData(
          formData,
          environment.documentUrl + 'DocMgt/chatBoard/uploadAttachment'
        )
        .subscribe(
          (data) => {
            this.userForm.controls['documentIds'].setValue(data);
            resolve(data);
          },
          (err) => {
            reject(err);
          }
        );
    });
  }

  getChatAttachments() {
    this.applicationService
      .saveAssetWithPath(
        this.messages,
        environment.documentUrl + 'DocMgt/chatBoard/getAttachments'
      )
      .subscribe((data) => {
        this.messages = data;
      });
  }

  downloadDocument(doc: { fileUrl: string; fileName?: string }) {
  const link = document.createElement('a');
  link.href = this.attachementsServerUrl + doc.fileName || doc.fileUrl;
  link.download = doc.fileName || 'download';
  link.target = '_blank';
  link.rel = 'noopener noreferrer';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

previewDocument(doc: { fileUrl: string; fileName?: string }) {
  const safeUrl = this.attachementsServerUrl + doc.fileName || doc.fileUrl;
  const isPreviewable = /\.(pdf|jpg|jpeg|png|gif|webp|bmp|svg|mp4|webm)$/i.test(safeUrl);
  // preview with the right format
  if (isPreviewable) {
    window.open(safeUrl, '_blank', 'noopener');
  } else {
    // download it
    this.downloadDocument(doc);
  }
}
}
