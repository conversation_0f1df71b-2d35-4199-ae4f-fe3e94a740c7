import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AllApplicationsComponent } from '../components/all-applications/all-applications.component';
import { SharedModule } from 'src/app/shared/modules/shared/shared.module';
import { ApplicationReviewDetailComponent } from '../components/application-review-detail/application-review-detail.component';
import { ViewApprovalProcessCommentComponent } from '../components/view-approval-process-comment/view-approval-process-comment.component';
import { ViewProcessHistoryComponent } from '../components/view-process-history/view-process-history.component';
import { VerificationReviewerComponent } from '../components/verification-reviewer/verification-reviewer.component';
import { CreateChecklistComponent } from '../components/create-checklist/create-checklist.component';
import { FoundationInspectionComponent } from '../components/foundation-inspection/foundation-inspection.component';
import { GeneralInspectionComponent } from '../components/general-inspection/general-inspection.component';
import { AllApplicationByAgencyComponent } from '../components/all-application-by-agency/all-application-by-agency.component';
import { SplitByCapitalPipe } from 'src/app/shared/pipes/generic-filter.pipe';

export const reviewRoutes: Routes = [
  { path: 'lists', component: AllApplicationsComponent },
  { path: 'lists/:status', component: AllApplicationsComponent },
  { path: 'lists-agency/:status/:agency', component: AllApplicationByAgencyComponent },
  { path: 'application-detail/:id', component: ApplicationReviewDetailComponent },
  { path: 'view-approval-process/:id', component: ViewApprovalProcessCommentComponent },
  { path: 'view-process-history/:id', component: ViewProcessHistoryComponent },
  { path: 'foundation-inspection/:id', component: FoundationInspectionComponent },
  { path: 'general-inspection/:id', component: GeneralInspectionComponent },




]


@NgModule({
  declarations: [
    AllApplicationsComponent,
    ApplicationReviewDetailComponent,
    ViewApprovalProcessCommentComponent,
    ViewProcessHistoryComponent,
    VerificationReviewerComponent,
    CreateChecklistComponent,
    AllApplicationByAgencyComponent,
    SplitByCapitalPipe
  ],
  imports: [
    SharedModule,
    CommonModule,
    RouterModule.forChild(reviewRoutes),
  ],
  exports: [
    RouterModule,
    SplitByCapitalPipe
  ]
})
export class ApplicationReviewModule { }
