# compiled output
/dist
/node_modules
**/node_modules
/pgdata
# .env


# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

<<<<<<< HEAD
# Docker Compose
docker-compose.yaml
=======
>>>>>>> 3a2d91ed29f2c77a38c59dfefe544bfabbee30b3



# Ignore environment files in both projects
.env
upload/
apps/applications/.env
apps/applications/config.ts
apps/auth/.env
apps/auth/config.ts

apps/documents/.env
apps/documents/config.ts

apps/integrations/.env
apps/integrations/config.ts

apps/notifications/.env
apps/notifications/config.ts

apps/payments/.env
apps/payments/config.ts