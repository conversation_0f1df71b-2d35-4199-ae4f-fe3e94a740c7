<div class="login-container">
  <div class="login-content">
    <div class="illust">
      <img class="logo" src="assets/ikons/logo/NavLogo.svg" alt="">
      <img class="ill" src="assets/imgs/SVG/illustration.svg" alt="">
    </div>
    <div class="dtials">
      <form class="form" [formGroup]="passresetForm" (ngSubmit)="onSubmit()">
        <div class="form-header">
          <h2 class="display-md">{{"resetPasswordPage.title" | translate}}</h2>
        </div>
        <div class="form-body">
          <div class="form-input">
            <label>{{"resetPasswordPage.emailLabel" | translate}}</label>
            <div>
              <input type="email" class="flg" id="email" [placeholder]="'resetPasswordPage.emailPlaceholder' | translate"
                formControlName="email" [class.is-invalid]="
              passresetForm.get('email')?.invalid &&
              passresetForm.get('email')?.touched
            ">
              <div *ngIf="
              (passresetForm.get('email')?.invalid &&
              passresetForm.get('email')?.touched) ||
              passresetForm.get('email')?.dirty
            ">
                <small *ngIf="passresetForm.get('email')?.hasError('required')" class="text-danger">Email is
                  required</small>
                <small *ngIf="passresetForm.get('email')?.hasError('pattern')" class="text-danger">Please provide a
                  valid email address</small>
              </div>
            </div>
          </div>
        </div>
        <div class="form-footer">
          <button class="kbk-btn kbk-btn-lg kbk-btn-main" [disabled]="passresetForm.invalid" type="submit">{{"resetPasswordPage.sendResetLinkButton" | translate}}</button>
          <p class="mb-0">{{"resetPasswordPage.rememberPassword" | translate}}... <a routerLink="/login" class="kbk-link"> {{"resetPasswordPage.loginHere" | translate}} </a>
          </p>
        </div>
      </form>
    </div>
  </div>
</div>