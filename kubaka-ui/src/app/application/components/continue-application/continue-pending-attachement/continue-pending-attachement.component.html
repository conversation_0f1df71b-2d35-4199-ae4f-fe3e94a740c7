<ng-template #associatedUpiContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"newApplication.step4.associatedUpi" | translate }}</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-associated-upi-application [inputData]="outputData"
            (backToParent)="closeAssociatedPopup()"></app-associated-upi-application>
    </div>
</ng-template>
<div class="stepper-content">
    <div class="stepper-header">
        <div class="upper-counter"> {{"newApplication.step4.step" | translate }} <span>4</span> / <span>4</span> </div>
    </div>
    <div class="stepper-body">
        <div>
            <div class="step-panel">
                <div class="step-panel_header">
                    <h2>{{"newApplication.step4.projectAttachments" | translate }}</h2>
                    <!-- <div class="form-incheckbox">
                        <label class="checkbox">
                            <input style="display: none;" type="checkbox" id="check" [checked]="isAssociated"
                                (click)="checkAssociated(associatedUpiContent)" data-bs-toggle="modal" type="button"
                                data-bs-target="#showModal" />
                            <span class="checkbox_box"></span>
                            <span class="checkbox_txt">Check to add associated UPI</span>
                        </label>
                    </div> -->

                </div>
                <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
                    <div class="kbk-updloadtable">
                        <ul class="updload-list">
                            <div class="updload-list_header">
                                <li class="updload-list_item">
                                    <div class="updload-list_item-dt hsv">
                                        <span class="text">{{"newApplication.step4.requiredDocuments" | translate }} <a
                                                href="assets/files/BOQ-OF-REVIEW.xlsx">{{"newApplication.step4.downloadBoqFile" | translate }}</a></span>
                                        <span class="kbk-warn"><img src="assets/ikons/colored/ikon-warn-o.svg">{{"newApplication.step4.boqFileInstruction" | translate }}</span>
                                    </div>
                                    <div class="updload-list_item-dt">
                                        <span class="text">{{"newApplication.step4.uploadedDocuments" | translate }}</span>
                                    </div>
                                </li>
                            </div>
                            <div class="updload-list_body">
                                <li class="updload-list_item" *ngFor="let dr of pageController.documentRequired">
                                    <div class="updload-list_item-dt">
                                        <div class="hsv">
                                            <span class="text">{{dr.name}}</span>
                                            <span class="kbk-warn"><img src="assets/ikons/colored/ikon-warn-o.svg">{{"newApplication.step4.allowedFileTypes" | translate }}</span>
                                        </div>
                                        <label *ngIf="!pageController.fileSubmit && !dr.uploaded"
                                            class="kbk-btn kbk-btn-sm kbk-btn-sec">
                                            <input type="file" multiple (change)="getFileDetails($event, dr)"
                                                id="document" name="document" formControlName="document"
                                                style="display: none;"> {{"newApplication.step4.upload" | translate }} </label>
                                        <label *ngIf="!pageController.fileSubmit && dr.uploaded"
                                            class="kbk-btn kbk-btn-sm kbk-btn-sec"
                                            (click)="viewDocument(viewContent, dr, 'ap-stas')"> {{"newApplication.step4.view" | translate }} </label>
                                        <label
                                            *ngIf="pageController.fileSubmit && pageController.fileDataName === dr.name"
                                            class="kbk-btn kbk-btn-sm kbk-btn-sec">
                                            <input type="file" style="display: none;"> {{"newApplication.step4.wait" | translate }}... </label>
                                    </div>
                                    <div class="updload-list_item-dt">
                                        <div *ngIf="dr.uploaded">
                                            <span class="icon">
                                                <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                                            </span>
                                            <span class="text">{{dr.uploaded?.fileName | titlecase}}</span>
                                        </div>
                                        <div class="nofile" *ngIf="!dr.uploaded">
                                            <span class="icon">
                                                <img src="assets/ikons/colored/ikon-file-orange.svg" alt="" />
                                            </span>
                                            <span class=" text">{{"newApplication.step4.noFile" | translate }}</span>
                                        </div>
                                        <button *ngIf="dr.uploaded && this.pageController.applicationDetail.applicationStatus.code !== 'CTFD'
                                        && this.pageController.applicationDetail.applicationStatus.code !== 'CTFD'
                                        && this.pageController.applicationDetail.applicationStatus.code !== 'SUB'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'CXL'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'UNRV'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RSMB'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NORHA'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RTNNO'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RVW'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NORVW'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NOUNRV'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'PAPRV'" class="kbk-btn kbk-btn-sm kbk-btn-error"
                                            type="button" data-bs-target="#showModal"
                                            (click)="openDeleteModal(deleteModel, 'md', dr.uploaded)">{{"newApplication.step4.remove" | translate }}</button>
                                    </div>
                                </li>


                                <app-additional-upload-file *ngIf="pageController.applicationDetail" [inputData]="pageController.applicationDetail">
                                </app-additional-upload-file>
                            </div>
                        </ul>
                    </div>
                    <div class="step-panel_footer">
                        <button type="button" (click)="onPrev()">{{"newApplication.step4.previous" | translate }}</button>
                        <button type="button" *ngIf="pageController.submitted"> {{"newApplication.step4.wait" | translate }}... </button>

                        <!-- Submit button that validates all attachments are uploaded -->
                        <button type="button"
                            *ngIf="!pageController.submitted
                            && this.pageController.applicationDetail.applicationStatus.code !== 'CTFD'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'SUB'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'CXL'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'UNRV'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RSMB'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NORHA'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RTNNO'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'RVW'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NORVW'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'NOUNRV'
                            && this.pageController.applicationDetail.applicationStatus.code !== 'PAPRV'"
                            class="kbk-btn kbk-btn-main"
                            [class.kbk-btn-disabled]="!pageController.isReadyToSubmit"
                            [disabled]="!pageController.isReadyToSubmit"
                            (click)="onSubmitApplication(isAcceptThatTheInformationProvided)"> {{resubmitData.isResubmit ?
                            ('newApplication.step4.resubmit' | translate) : ('newApplication.step4.submitApplication' | translate) }} </button>

                        <div *ngIf="!pageController.isReadyToSubmit" class="alert alert-warning" role="alert">
                            {{"newApplication.step4.uploadAllRequiredFiles" | translate }}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Delete file modal -->
<ng-template #deleteModel let-modal>
    <div class="modal-content">
        <div class="modol-header">
            <h2 id="exampleModalLabel">{{"newApplication.step4.confirmDelete" | translate }} {{pageController?.deleteData?.fileName | titlecase}} ?
            </h2>
            <p>{{"newApplication.step4.deletingYour" | translate }} {{pageController?.deleteData.name | titlecase}} {{"newApplication.step4.deleteWarning" | translate }}. </p>
            <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
                id="btn-close" (click)="modal.dismiss('Cross click')"></button>
        </div>
        <div class="modal-body">
            <div class="mt-md">
                <!-- <h4>{{"newApplication.step4.confirmDelete" | translate }} {{deleteData.fileName}} ?</h4> -->
            </div>
            <div class="kbk-x-c sp-sm mt-md">
                <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal"
                    (click)="modal.close('Close click')" id="deleteRecord-close">{{"newApplication.step4.close" | translate }}</button>
                <button *ngIf="!pageController.isDeleting" type="button" class="kbk-btn kbk-btn-error"
                    id="delete-product" (click)="deleteFile()">{{"newApplication.step4.yesDelete" | translate }}!</button>
                <button *ngIf="pageController.isDeleting" type="button" class="kbk-btn kbk-btn-error"
                    id="delete-product">{{"newApplication.step4.applicationDeletingFile" | translate }}...</button>
            </div>
        </div>
    </div>
</ng-template>
<!-- Delete file modal -->
<ng-template #viewContent role="document" let-modal>
    <div class="modol-header">
        <h2 class="exampleModalLabel">{{"newApplication.step4.view" | translate }} Document</h2>
        <!-- <span class="caption">Fill required input to create new user</span> -->
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modo-contain" *ngIf="outputData">
        <app-view-document [inputData]="outputData"></app-view-document>
    </div>
</ng-template>
<!-- Submit application modal -->
<ng-template #isAcceptThatTheInformationProvided role="document" let-modal>
    <div class="modol-header">
        <h2 id="exampleModalLabel">{{"newApplication.step4.confirm" | translate }} </h2>
        <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
            id="close-modal" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="step-panel_body">
        <div class="form-incheckbox">
            <label class="checkbox">
                <input type="checkbox" id="check" [checked]="pageController.isChecked " (click)="allowToSubmit()" />
                <span class="checkbox_box"></span>
                <span class="checkbox_txt">{{"newApplication.step4.certifyTruth" | translate }}.</span>
            </label>
        </div>
    </div>
    <div class="modol-content">
        <div class="kbk-x-c sp-sm mt-md">
            <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> {{"newApplication.step4.cancel" | translate }} </button>
            <button *ngIf="!resubmitData.isResubmit" class="kbk-btn kbk-btn-main" type="button"
                [disabled]="!pageController.isChecked" (click)="onNext()"> {{"newApplication.step4.submit" | translate }} </button>
            <button *ngIf="resubmitData.isResubmit" class="kbk-btn kbk-btn-main" type="button"
                [disabled]="!pageController.isChecked" (click)="onNext()"> {{"newApplication.step4.resubmit" | translate }} </button>
        </div>
    </div>
</ng-template>
<!-- Submit application modal -->
