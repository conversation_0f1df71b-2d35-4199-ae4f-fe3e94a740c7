<div class="app-gen appl-details">
  <div class="app-gen-header genvline">
    <div>
      <span class="hder" aria-label="header tittle">applications </span>
      <span class="hder-sm" aria-label="header tittle">application details: {{ applicationDetail.applicationName
        }}</span>
    </div>
    <section class="genline">
      <!-- <div>
        <span class="hder" aria-label="header tittle">applications</span>
        <span class="hder-sm" aria-label="header tittle">application details:
          {{applicationDetail.applicationName}}</span>
      </div> -->
      <div class="btns">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="verify(verifyContent, 'NID', 'lg')"> Verify National ID </button>
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="verify(verifyContent, 'EIA', 'lg')"> Verify EIA </button>
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="verify(verifyContent, 'RRA', 'lg')"> Verify RRA </button>
        <!-- <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
        data-bs-target="#showModal" (click)="verify(verifyContent, 'LIS', 'lg')"> Verify LIS </button> -->
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="verify(upiFromLandContent, 'LIS', 'lg')"> Verify LIS </button>
        <button *ngIf="applicationDetail.permitTypes?.code === 'OCP'" type="button" class="kbk-btn kbk-btn-sec"
          data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="inspectionForm('3')"> Occupancy
          Inspection </button>
        <button *ngIf="applicationDetail.permitTypes?.code === 'FINS'" type="button" class="kbk-btn kbk-btn-sec"
          data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal" (click)="inspectionForm('2')"> Foundation
          Inspection </button>
        <!-- <button *ngIf="applicationDetail.permitTypes.code !== 'OCP' &&
      applicationDetail.permitTypes.code !== 'FINS'" type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal"
        id="create-btn" data-bs-target="#showModal" (click)="inspectionForm('1')"> Inspection </button> -->
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="viewReviewers(viewTheReviewers, 'lg')"> View the reviewers </button>
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="viewApplicationProcessMethod(viewApplicationProcess, 'lg')"> Application
          process </button>
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-toggle="modal" id="create-btn"
          data-bs-target="#showModal" (click)="viewProcessHistoryComponent(viewProcessHistory, 'lg')"> Process History </button>
        <!-- <button type="button" class="kbk-btn kbk-btn-sec" [routerLink]="['/account/all-applications/lists']"> Back
      </button> -->
      </div>
      <div class="btns">
        <button type="button" class="kbk-btn kbk-btn-sec" [routerLink]="
            currentUser.data?.user?.role?.code === 'STF' &&
            currentUser.data?.user?.userType?.code === 'STF'
              ? '/account/application/my-box'
              : '/account/all-applications/lists'
          "> Back </button>
      </div>
      <!-- <ul class="kbk-tabs">
                <li class="kbk-tabs-item">
                    <a class="kbk-tabs-link" routerLinkActive="active">User</a>
                </li>
                <li class="kbk-tabs-item">
                    <a class="kbk-tabs-link" routerLinkActive="active">Role</a>
                </li>
            </ul> -->
    </section>
    <hr />
    <section class="kbk-x-s kbk-aend sp-2">
      <div>
        <div class="track-info">
          <div class="track-profile">
            <img src="assets/imgs/profile1.svg" alt="" />
          </div>
          <div class="track-dtail">
            <label>Owners </label>
            <span class="track-user" *ngFor="let name of applicationDetail?.parcelOwners">
              {{name}}</span>
            <!-- <span class="track-user">{{ applicationDetail.userDetails?.firstName }} {{
              applicationDetail.userDetails?.lastName }}</span> -->
            <span class="track-usersub">{{ applicationDetail.projects?.upi }}</span>
          </div>
        </div>
      </div>
      <div class="land-info">
        <div class="land-info-item">
          <h3>Parcel Details</h3>
          <div class="aline">
            <div class="form-out">
              <label>Planned for</label>
              <span>{{ applicationDetail.projects?.selectedUse }} </span>
            </div>
            <div class="form-out">
              <label>Provice</label>
              <span>{{ applicationDetail.projects?.provinceName }}</span>
            </div>
            <div class="form-out">
              <label>District</label>
              <span>{{ applicationDetail.projects?.districtName }}</span>
            </div>
            <div class="form-out">
              <label>Sector</label>
              <span>{{ applicationDetail.projects?.sectorName }}</span>
            </div>
            <div class="form-out">
              <label>Cell</label>
              <span>{{ applicationDetail.projects?.cellName }}</span>
            </div>
            <div class="form-out">
              <label>Village</label>
              <span>{{ applicationDetail.projects?.villageName }}</span>
            </div>
          </div>
        </div>
        <div class="land-info-item kbk-x-e kbk-aend">
          <div *ngIf="
              (currentUser.data?.user?.role?.code === 'DRCT' ||
                currentUser.data?.user?.role?.code === 'OFCMNG' ||
                currentUser.data?.user?.role?.code === 'TMLD') &&
              applicationDetail.applicationStatus?.code !== 'CTFD' &&
              applicationDetail.applicationStatus?.code !== 'PND'
            " class="form-out" data-bs-toggle="modal" id="create-btn" style="cursor: pointer"
            data-bs-target="#showModal" (click)="assignToUser(openToAssign, 'lg')">
            <span class="bdg bdg-pend">Assign</span>
          </div>
          <div class="form-out">
            <span class="bdg bdg-pend">{{ applicationDetail?.applicationStatus?.name }}</span>
          </div>
          <!-- <h3>Application Status</h3> -->
          <!-- <div>
                        <div class="aline">

                            <div class="form-out">
                                <label>Progress</label>
                                <div class="progr-bar">
                                    <div class="progr-bar_item" role="progressbar">50%</div>
                                </div>
                            </div>
                            <div class="form-out">

                            </div>
                        </div>
                    </div> -->
        </div>
      </div>
    </section>
  </div>
  <div class="app-gen-content" *ngIf="
      inspectionRight.isRandomInspection ||
      inspectionRight.isFoundationInspection ||
      inspectionRight.isOccupancyInspection
    ">
    <app-general-inspection *ngIf="inspectionRight.isRandomInspection" [inputData]="inspectionRight"
      (backToParent)="closeInspection()"></app-general-inspection>
    <app-foundation-inspection *ngIf="inspectionRight.isFoundationInspection" [inputData]="inspectionRight"
      (backToParent)="closeInspection()"></app-foundation-inspection>
  </div>
  <app-occupancy-inspection *ngIf="inspectionRight.isOccupancyInspection" [inputData]="inspectionRight"
    (backToParent)="closeInspection()"></app-occupancy-inspection>
  <div class="app-gen-content">
    <div class="appl-info">
      <section>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card">
            <h3>Project Details</h3>
            <div>
              <div class="form-out">
                <label>Project Category</label>
                <span>{{ applicationDetail.categoryTypes?.name }}</span>
              </div>
              <div class="form-out">
                <label>Applied For</label>
                <span>{{ applicationDetail.projects?.selectedUse }}</span>
              </div>
              <div class="form-out">
                <label>Building Type</label>
                <span>{{ applicationDetail.buildTypes?.name }}</span>
              </div>
              <div class="form-out">
                <label>Permit Type</label>
                <span>{{ applicationDetail.permitTypes?.name }}</span>
              </div>
              <div class="form-out">
                <label>Project Brief</label>
                <span class="form-out_txtarea">{{ applicationDetail?.projects?.projectDescription }}</span>
              </div>
              <div class="form-out" *ngIf="
                  applicationDetail.categoryTypes?.code === 'CAT5' ||
                  applicationDetail.categoryTypes?.code === 'CAT4'
                ">
                <label>Certificate Number EIA</label>
                <span>{{ applicationDetail.certificateNumberEIA }}</span>
              </div>
            </div>
          </div>
          <div class="appl-info-card">
            <h3>Project Estimates Details </h3>
            <div>
              <div class="form-out">
                <label>Estimated monthly water consumption(m3)</label>
                <span>{{ applicationDetail?.waterConsumption }}</span>
              </div>
              <div class="form-out">
                <label> Distance to the nearest Land Line/ optic fiber cable(m)</label>
                <span>{{ applicationDetail?.DistanceToTheNearestLandIn }}</span>
              </div>
              <div class="form-out">
                <label>Estimated monthly electricity consumption in Kwh</label>
                <span>{{ applicationDetail?.electricityConsumption }}</span>
              </div>
              <div class="form-out">
                <label>Estiamted Estimated project cost in USD</label>
                <span>${{ applicationDetail?.ProjectCostInUSD }}</span>
              </div>
              <div class="form-out">
                <label>Estiamted project cost in Rwf</label>
                <span>{{ applicationDetail?.ProjectCostInRwf }} Rwf</span>
              </div>
              <!-- <div class="form-out">
                                <label>Any other comment</label>
                                <span>--</span>
                            </div> -->
            </div>
          </div>
        </div>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card fx-2">
            <h3>Development Details</h3>
            <div class="kbk-x kbk-wrap-4">
              <div class="form-out">
                <label>Project Category</label>
                <span>{{ applicationDetail.categoryTypes?.name }}</span>
              </div>
              <div class="form-out">
                <label>Plot Size (In Square Meters)</label>
                <span>{{ applicationDetail.projects?.plotSize }} m²</span>
              </div>
              <div class="form-out">
                <label>Combined Plot size (In Square Meters)</label>
                <span>{{applicationDetail?.combiningPlotSize}} m²</span>
            </div>
              <!-- <div class="form-out">
                                <label>Building Coverage</label>
                                <span> {{applicationDetail.projects?.percentageSpaceUse}}%</span>
                            </div> -->
              <!-- <div class="form-out">
                                <label>Proposed Number of floors / G+</label>
                                <span>3</span>
                            </div> -->
              <div class="form-out">
                <label>Gross Floor Area</label>
                <span>{{ applicationDetail?.grossFloorArea }}</span>
              </div>
              <div class="form-out">
                <label>Proposed Number of floors / G+</label>
                <span>G+{{ applicationDetail?.numberOfFloor }}</span>
              </div>
              <div class="form-out">
                <label>Number of parking spaces</label>
                <span>{{ applicationDetail?.numberOfParkingSpace }}</span>
              </div>
              <div class="form-out">
                <label>Price of Dwelling Unit</label>
                <span>{{ applicationDetail?.priceOfDwellingUnitRwf }}</span>
              </div>
              <div class="form-out">
                <label>Built-up Area</label>
                <span>{{ applicationDetail?.buildUpArea }} </span>
              </div>
              <div class="form-out">
                <label>Gross Floor Area</label>
                <span>{{ applicationDetail?.grossFloorArea }}</span>
              </div>
              <!-- <div class="form-out">
                                <label>Estimate % of space</label>
                                <span>60%</span>
                            </div>
                            <div class="form-out">
                                <label>Price of Dwelling Unit</label>
                                <span>$60</span>
                            </div> -->
              <div class="form-out">
                <label>Capacity Information: Number of people / seats</label>
                <span>{{ applicationDetail?.capacityInformation }}</span>
              </div>
              <!-- <div class="form-out">
                                <label>For Industrial projects</label>
                                <span>Lorem ipsum dolor sit amet consectetur adipisicing elit. Aut excepturi hic, eaque
                                    velit a quod adipisci accusamus exercitationem? Qui, libero.</span>
                            </div> -->
            </div>
          </div>
        </div>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card fx-2">
            <h3>Created by</h3>
            <div class="kbk-x kbk-wrap-4">
              <div class="form-out">
                <label>Names</label>
                <span> {{ applicationDetail.userDetails?.firstName | titlecase }} {{
                  applicationDetail.userDetails?.lastName | titlecase }}</span>
              </div>
              <div class="form-out">
                <label>Phone number</label>
                <span>{{ applicationDetail.userDetails?.phoneNumber }}</span>
              </div>
              <!-- <div class="form-out">
                                <label>Building Coverage</label>
                                <span> {{applicationDetail.projects?.percentageSpaceUse}}%</span>
                            </div> -->
              <!-- <div class="form-out">
                                <label>Proposed Number of floors / G+</label>
                                <span>3</span>
                            </div> -->
              <div class="form-out">
                <label>User Type</label>
                <span>{{ applicationDetail?.userDetails?.userType?.name }}</span>
              </div>
            </div>
          </div>
          <div class="appl-info-card fx-2">
            <h3>Submitted By</h3>
            <div class="kbk-x kbk-wrap-4">
              <div class="form-out">
                <label>Names</label>
                <span> {{ applicationDetail.senderDetails?.firstName | titlecase }} {{
                  applicationDetail.senderDetails?.lastName | titlecase }}</span>
              </div>
              <div class="form-out">
                <label>Phone number</label>
                <span>{{ applicationDetail.senderDetails?.phoneNumber }}</span>
              </div>
              <!-- <div class="form-out">
                                <label>Building Coverage</label>
                                <span> {{applicationDetail.projects?.percentageSpaceUse}}%</span>
                            </div> -->
              <!-- <div class="form-out">
                                <label>Proposed Number of floors / G+</label>
                                <span>3</span>
                            </div> -->
              <div class="form-out">
                <label>User Type</label>
                <span>{{ applicationDetail?.senderDetails?.userType?.name }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card">
            <h3>Project Attachment Details</h3>
            <div>
              <ul class="uploaded-list">
                <li class="uploaded-file" *ngFor="let dc of documents">
                  <div class="kbk-x-s kbk-ac">
                    <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                    <div>
                      <p>{{ dc.requiredDocumentName?.name }}</p>
                      <span class="bdg bdg-pend">{{dc.fileName}}</span>

                      <!-- <span>{{ dc.documentStatusId === "1" ? "Approved" : "Pending" }}</span> -->
                    </div>
                  </div>
                  <div class="kbk-vd-btn">
                    <!-- <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                      (click)="approveDocument(approveContent, dc)"> Approve document </a> -->
                    <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                      (click)="viewDocument(viewContent, dc, 'ap-stas')"> View Document </a>
                    <a *ngIf="loading" class="kbk-link hs-tp"> Loading ... </a>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div class="appl-info-card">
            <h3>Project Attachment Additional files</h3>
            <div>
              <ul class="uploaded-list">
                <li class="uploaded-file" *ngFor="let dc of additionalDocuments">
                  <div class="kbk-x-s kbk-ac">
                    <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                    <div>
                      <p>{{ dc.additionalDescription }}</p>
                      <span class="bdg bdg-pend">{{dc.fileName}}</span>
                    </div>
                  </div>
                  <div class="kbk-vd-btn">
                    <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                      (click)="viewDocument(viewContent, dc, 'ap-stas')"> View Document </a>
                    <a *ngIf="loading" class="kbk-link hs-tp"> Loading ... </a>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card" *ngIf="associatedUpis.length > 0">
            <h3>Associated UPI</h3>
            <div>
              <ul class="uploaded-list">
                <li class="uploaded-file" *ngFor="let dc of associatedUpis">
                  <div class="kbk-x-s kbk-ac">
                    <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                    <div>
                      <p>{{ dc.upi }}</p>
                    </div>
                  </div>
                  <div class="">
                    <a *ngIf="!loading" class="kbk-link hs-tp" data-kbk-tooltip="view document"
                      (click)="viewUpiDetails(upiFromLandContent, dc, 'lg')"> View Details </a>
                    <!-- <a *ngIf="loading" class="kbk-link hs-tp"> Loding.... </a> -->
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div class="appl-info-card">
            <h3>Comments</h3>
            <div class="app-dash">
              <div class="app-main">
                <div class="app-lists">
                  <div class="app-tblist">
                    <ul class="tblist">
                      <li class="tblist-item" *ngFor="let dc of approvalComments?.approvals">
                        <div class="tblist-item_dt txt-l">
                          <span>
                            <!-- <span class="ttl">Done By</span> {{ dc.userDataApproval }} </span> -->
                            <span class="ttl">Done By</span> {{ dc.user.firstName }} {{ dc.user.lastName }}</span>
                          <!-- <span>
                            <span class="ttl">Comment</span> {{ dc.comment | titlecase }} </span> -->

                            <div class="tblist-item_dt">
                              <span class="ttl">Comment</span>
                              <div class="comment-toggle">
                                <!-- <span >
                                  {{ dc.comments }}
                                  <i class="ri-pencil-fill fs-16" (click)="dc.showFullComment = false"></i>
                                </span> -->
                                <span>
                                  {{ dc.showFullComment ? dc.comments : getShortComment(dc.comments) }}
                                  <a href="#" (click)="dc.showFullComment = !dc.showFullComment; $event.preventDefault()">
                                    {{ dc.showFullComment ? 'Read less' : 'Read more' }}
                                  </a>
                                  <i class="ri-pencil-fill fs-16" (click)="dc.showFullComment = false"></i>
                                </span>
                              </div>
                            </div>

                        </div>
                        <div class="tblist-item_dt txt-l">
                          <span>
                            <span class="ttl">Approval Level</span> {{ dc.approvalLevel.name }} </span>
                          <span>
                            <span class="ttl">Approval Status</span> {{ dc.approvalStatus.name | titlecase }} </span>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div>
              <ul class="uploaded-list">
                <li class="uploaded-file" *ngFor="let dc of approvalComments?.approvals">
                  <div class="kbk-x-s kbk-ac">
                    <img src="assets/ikons/colored/ikon-file-blue.svg" alt="" />
                    <div>
                      <p>{{ dc.comment }}</p>
                    </div>
                  </div>
                </li>
              </ul>
            </div> -->
          </div>
        </div>
        <div class="form-incheckbox" *ngIf="
            currentUser.data.user.role.code === 'DRCT' ||
            (currentUser.data.user.role.code != 'DRCT' &&
              !this.applicationDetail.isLocked)
          ">
          <label class="checkbox">
            <input type="checkbox" id="checkAll" value="option" [checked]="isViewingCheckList != isViewingCheckList"
              (change)="switchReviews()" />
            <span class="checkbox_box"></span>
            <span class="checkbox_txt">Check if you want to see the reviews  </span>
          </label>
        </div>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card" *ngIf="isViewingCheckList">
            <div class="app-dash">
              <div class="app-main">
                <div class="app-lists" style="box-shadow: none">
                  <div class="app-tblist_title" style="padding: 0 0 1rem">
                    <h3 style="margin: 0">Permit check list</h3>
                    <div class="btns" *ngIf="
                      (
                        currentUser?.data?.user?.role?.code === 'DRCT' ||
                        currentUser?.data?.user?.role?.code === 'STF' ||
                        currentUser?.data?.user?.role?.code === 'TMLD' ||
                        currentUser?.data?.user?.role?.code === 'OFCMNG' ||
                        currentUser?.data?.user?.role?.code === 'INSP'
                      )
                      &&
                      !(
                        currentUser?.data?.user?.role?.code === 'SINSP' ||
                        currentUser?.data?.user?.role?.code === 'DINSP' ||
                        applicationDetail.applicationStatus?.code === 'CTFD' ||
                        applicationDetail.permitTypes?.code === 'OCP' ||
                        applicationDetail.permitTypes?.code === 'FINS'
                      )
                    ">
                      <a class="kbk-btn kbk-btn-sm kbk-btn-main" data-bs-toggle="modal" id="create-btn"
                        data-bs-target="#showModal" (click)="openModal(checkListPopup, 'lg')">
                        New check list
                      </a>
                    </div>
                  </div>
                  <!-- <div class="app-tblist_title" style="padding: 0 0 1rem;">
                    <h3 style="margin: 0;">Permit check list</h3>
                    <div class="btns"
                    *ngIf="
                    currentUser?.data?.user?.role?.code === 'DRCT'||
                    currentUser?.data?.user?.role?.code === 'STF'||
                    currentUser?.data?.user?.role?.code === 'TMLD'||
                    currentUser?.data?.user?.role?.code === 'OFCMNG'||
                    currentUser?.data?.user?.role?.code === 'INSP'

                    ">
                      <a *ngIf="
                      (currentUser?.data?.user?.role?.code !== 'SINSP' &&
                      currentUser?.data?.user?.role?.code !== 'DINSP') &&
                      applicationDetail.applicationStatus?.code !== 'CTFD' &&
                      applicationDetail.applicationStatus?.code !== 'CTFD' &&
                      applicationDetail.permitTypes?.code !== 'OCP' &&
                      applicationDetail.permitTypes?.code !== 'FINS'" class="kbk-btn kbk-btn-sm kbk-btn-main"
                        data-bs-toggle="modal" id="create-btn" data-bs-target="#showModal"
                        (click)="openModal(checkListPopup, 'lg')">New check list</a>
                    </div>
                  </div> -->
                  <div class="app-tblist">
                    <!-- <div class="app-tblist_title">
                                                          <span class="hder" aria-label="header tittle">List of
                                                              Checklists</span>
                                                      </div> -->
                    <ul class="tblist">
                      <li class="tblist-item valign" *ngFor="let li of checkListsAnswers">
                        <!-- <div class="tblist-item_icon bg-l-o">
                                                                  <img src="assets/ikons/colored/ikon-calendar.svg" alt="" />
                                                              </div> -->
                        <div class="tblist-item_ansl">
                          <div class="tblist-item_dt">
                            <span class="ttl">Recommendation</span> {{ li.decision }}
                          </div>
                          <div class="tblist-item_dt">
                            <span class="ttl">Condition of Approval</span> {{ li.conditionsOfApproval }}
                          </div>
                          <div class="tblist-item_dt">
                            <span class="ttl">Created By</span> {{ li.userDetails?.firstName }} {{
                            li.userDetails?.lastName }}
                          </div>
                          <div class="tblist-item_dt">
                            <span class="ttl">Recommendation Decision </span> {{ li.approvalStatus.name }}
                          </div>
                          <div class="tblist-item_dt">
                            <span class="ttl">Created On</span> {{ li.created_at | date }}
                          </div>
                          <div class="tblist-item_xcn">
                            <!-- <a class="kbk-link-btn hs-tp" data-kbk-tooltip="View Certificate"
                              (click)="viewCheckList(checkListPopup, li, 'lg')" data-bs-toggle="modal" id="create-btn"
                              data-bs-target="#showModal">
                              <img src="assets/ikons/colored/ikon-eye.svg" alt="" />
                            </a> -->
                          </div>
                        </div>
                        <div class="tblist-item_commnt">
                          <label>
                            <input type="checkbox" name="ansList" id="ansLst" /> View comments </label>
                          <div class="tblist-item_commntlist">
                            <div class="tblist-item_dt">
                              <span class="ttl">Structural Comment</span> {{ li.structuralComment }}
                            </div>
                            <div class="tblist-item_dt">
                              <span class="ttl">Electrical and mechanical conditional Comment</span> {{
                              li.civilEngineeringComment }}
                            </div>
                            <div class="tblist-item_dt">
                              <span class="ttl">Architectural Comment</span> {{ li.architecturalComment }}
                            </div>
                            <div class="tblist-item_dt">
                              <span class="ttl">Urban Planning Comment</span> {{ li.urbanPlanningComment }}
                            </div>
                            <div class="tblist-item_dt">
                              <span class="ttl">Site Analysis Comment</span> {{ li.siteAnalysisComment }}
                            </div>
                            <div class="tblist-item_dt">
                              <div class="">
                                <a *ngIf="!loading && li.fileBase64" class="kbk-link hs-tp"
                                  data-kbk-tooltip="view document" (click)="
                                    viewDirectDocument(
                                      viewContent,
                                      li,
                                      'ap-stas'
                                    )
                                  "> View supporting Document </a>
                                <a *ngIf="loading" class="kbk-link hs-tp"> Loding image.... </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="container" style="display: none">
            <div class="content">
              <div class="chlst-st"></div>
              <div class="app-dash">
                <div class="app-main"></div>
              </div>
              <!-- <p>Track the status of your permit application here</p> -->
              <!-- <div class="content-body">
                                <ul class="accordion-list">
                                    <li class="accordion-item" *ngFor="let ch of checkLists">
                                        <label class="accordion-btn">
                                            <input type="checkbox" style="display: none" /> {{ch.name}} <span
                                                class="more-btn"></span>
                                        </label>
                                        <div class="accordion-content">
                                            <form (ngSubmit)="saveCheckList(ch)" [formGroup]="checkForm"
                                                autocomplete="off">
                                                <div class="">
                                                    <div class="form-input">
                                                        <label>Comment</label>
                                                        <div>
                                                            <textarea name="comment" formControlName="comment"
                                                                id="comment" required cols="30" rows="10"></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="step-panel_footer">
                                                    <button type="submit">Save</button>
                                                </div>
                                            </form>
                                        </div>
                                    </li>
                                </ul>
                            </div> -->
            </div>
          </div>
        </div>
        <!-- Enable below form when application is pending -->
        <div class="form-incheckbox">
          <label class="checkbox" *ngIf="
              currentUser?.data?.user?.role?.code === 'DRCT' ||
              currentUser?.data?.user?.role?.code === 'TMLD' ||
              currentUser?.data?.user?.role?.code === 'DINSP'||
              currentUser?.data?.user?.role?.code === 'SINSP' ||
               currentUser?.data?.user?.role?.code === 'OFCMNG'
            ">
            <input type="checkbox" id="checkAll" value="option" [checked]="isChecked != isChecked"
              (change)="switch()" />
            <span class="checkbox_box"></span>
            <span *ngIf="
                (applicationDetail.permitTypes?.code === 'OCP' ||
                  applicationDetail.permitTypes?.code === 'FINS') &&
                (currentUser?.data?.user?.role?.code !== 'SINSP' ||
                  currentUser?.data?.user?.role?.code !== 'DINSP' ||
                  currentUser?.data?.user?.role?.code === 'TMLD' )
                  && currentUser?.data?.user?.role?.code !== 'DRCT'
              ">Check If you want to approve  </span>
            <span *ngIf="
                currentUser?.data?.user?.role?.code === 'DRCT' ||
                currentUser?.data?.user?.role?.code === 'TLD' ||
                currentUser?.data?.user?.role?.code === 'TMLD' ||
                currentUser?.data?.user?.role?.code === 'OFCMNG'

              "> Check If you want to approve  </span>
          </label>
        </div>
        <!-- <span *ngIf="currentUser?.data?.user?.role?.code === 'DRCT' ||
        currentUser?.data?.user?.role?.code === 'TMLD' ||
        currentUser?.data?.user?.role?.code === 'SINSP'">Check If you want to approve</span> -->
        <!-- <span *ngIf="

        (currentUser?.data?.user?.role?.code === 'DRCT' ||
        currentUser?.data?.user?.role?.code === 'TLD' ||
        currentUser?.data?.user?.role?.code === 'SINSP' &&
        )">Do you want to approve</span> -->
        <div class="kbk-x-s sp-2 appl-info-fx" *ngIf="
            (currentUser?.data?.user?.role?.code === 'DRCT' ||
              currentUser?.data?.user?.role?.code === 'DINSP' ||
              currentUser?.data?.user?.role?.code === 'TMLD' ||
              currentUser?.data?.user?.role?.code === 'SINSP' ||
              currentUser?.data?.user?.role?.code === 'OFCMNG') &&
            applicationDetail?.applicationStatus?.code !== 'CTFD' &&
            isChecked
          ">
          <div class="appl-info-card">
            <div class="container">
              <div class="content">
                <h3>Application approval</h3>
                <!-- <form (ngSubmit)="approvalStatusApprovalSubmit()" [formGroup]="userForm" autocomplete="off"> -->
                <form (ngSubmit)="confirmToSubmitMethod()" [formGroup]="userForm" autocomplete="off">
                  <div class="">
                    <div class="form-input">
                      <label>Approval Status</label>
                      <div>
                        <select name="approvalStatusId" id="approvalStatusId" formControlName="approvalStatusId"
                          (change)="validateApproval()" required>
                          <option *ngFor="let op of approvalStatuses" [value]="op.id"> {{ op.name }} </option>
                        </select>
                      </div>
                    </div>
                    <div class="form-input">
                      <label>Application Status</label>
                      <div>
                        <select name="applicationStatusId" id="applicationStatusId"
                          [class.disabled-select]="isSelectDisabled" formControlName="applicationStatusId" required>
                          <option *ngFor="let op of applicationStatuses" [value]="op.id"> {{ op.name }} </option>
                        </select>
                      </div>
                    </div>
                    <!-- <div class="form-input">
                      <app-input-file-upload (fileUpload)="getFileToSave($event)"></app-input-file-upload>
                    </div> -->
                    <div class="form-input">
                      <label>Comment<span>*</span></label>
                      <div>
                        <textarea name="comment" formControlName="comment" id="comment" required cols="30"
                          rows="10"></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="step-panel_footer">
                    <button type="buttom" (click)="close()">Cancel</button>
                    <button *ngIf="
                        !submitted &&
                        !formAccess.isNotAllowedToSubmitFinalApproval &&
                        permissions.isAllowToApproveApplication
                      " type="submit"> Submit </button>
                    <button *ngIf="
                        applicationDetail?.applicationStatus?.code === 'CTFD' &&
                        currentUser?.data?.user?.role?.code === 'DRCT' &&
                        permissions.isAllowToApproveApplication && submitted
                      " type="button"> Regenerate Invoice </button>
                    <button *ngIf="submitted" type="button">Saving....</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <div class="appl-info-card" *ngIf="isAllowedToGenerateInvoice">
            <div class="container">
              <div class="content">
                <h3>Invoice</h3>
                <form (ngSubmit)="approvalStatusApprovalSubmit()" [formGroup]="userForm" autocomplete="off">
                  <div class="">
                    <div class="form-input">
                      <label>Invoice Item</label>
                      <div>
                        <select name="invoiceItemId" id="invoiceItemId" formControlName="invoiceItemId" required>
                          <option *ngFor="let op of invoiceItems" [value]="op.id"> {{ op.name }} </option>
                        </select>
                      </div>
                    </div>
                    <!-- <div class="form-input">
                      <label>Range</label>
                      <div>
                        <input
                          type="number"
                          name="range"
                          noNegative
                          formControlName="range"
                          (focusout)="getAmount()"
                          required
                        />
                      </div>
                    </div> -->
                    <div class="form-input">
                      <label>Amount <span style="cursor: pointer; color: #155724" data-bs-toggle="modal" id="create-btn"
                          data-bs-target="#showModal" (click)="openPricePopup(contentPrice, 'lg')">(Get
                          price)</span></label>
                      <div>
                        <input type="number" name="amount" formControlName="amount" readonly required />
                      </div>
                    </div>
                  </div>
                  <!-- <div class="step-panel_footer">
                                        <button>Cancel</button>
                                        <button type="submit">Submit</button>
                                    </div> -->
                </form>
              </div>
              <div class="content">
                <h3>Certificate</h3>
                <form (ngSubmit)="approvalStatusApprovalSubmit()" [formGroup]="userForm" autocomplete="off">
                  <div class="">
                    <div class="form-input">
                      <label>Number of Months (Permit duration)</label>
                      <div>
                        <input type="number" name="month" formControlName="month" noNegative required
                          (keyup)="updateExpiryDate()" />
                      </div>
                    </div>
                    <div class="form-input">
                      <label>Expiry Date</label>
                      <div>
                        <input type="date" name="expiredDate" formControlName="expiredDate"
                          [value]="expiryDate | date : 'yyyy-MM-dd'" readonly />
                      </div>
                    </div>
                  </div>
                  <!-- <div class="step-panel_footer">
                                        <button>Cancel</button>
                                        <button type="submit">Submit</button>
                                    </div> -->
                </form>
              </div>
            </div>
          </div>
          <div *ngIf="checkingData.isSearching" class="alert alert-success" role="alert"> {{ checkingData.label }}
          </div>
        </div>
        <!-- regenerate new invoice -->
        <div class="kbk-x-s sp-2 appl-info-fx" *ngIf="
            currentUser?.data?.user?.role?.code === 'DRCT' &&
            applicationDetail?.applicationStatus?.code === 'CTFD' &&
            permissions.isAllowToApproveApplication
          ">
          <div class="appl-info-card">
            <div class="container">
              <div class="content">
                <h3>Regenerate Invoice</h3>
                <form (ngSubmit)="regenerateInvoice()" [formGroup]="userForm" autocomplete="off">
                  <div class="">
                    <div class="form-input">
                      <label>Invoice Item</label>
                      <div>
                        <select name="invoiceItemId" id="invoiceItemId" formControlName="invoiceItemId" required>
                          <option *ngFor="let op of invoiceItems" [value]="op.id"> {{ op.name }} </option>
                        </select>
                      </div>
                    </div>
                    <div class="form-input">
                      <label>Amount <span style="cursor: pointer; color: #155724" data-bs-toggle="modal" id="create-btn"
                          data-bs-target="#showModal" (click)="openPricePopup(contentPrice, 'lg')">(Get price)</span>
                      </label>
                      <div>
                        <input type="number" name="amount" formControlName="amount" readonly required />
                      </div>
                    </div>
                  </div>
                  <div class="step-panel_footer">
                    <button *ngIf="
                        applicationDetail?.applicationStatus?.code === 'CTFD' &&
                        currentUser?.data?.user?.role?.code === 'DRCT'
                      " type="submit"> Regenerate Invoice </button>
                    <button *ngIf="submitted" type="button">Saving....</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <div *ngIf="checkingData.isSearching" class="alert alert-success" role="alert"> {{ checkingData.label }}
          </div>
        </div>
        <!-- regenerate new invoice -->
        <!-- <div class="kbk-x-c sp-sm">
                    <button class="kbk-btn kbk-btn-main">Process Application</button>
                    <button class="kbk-btn kbk-btn-main">Add Comment</button>

                </div> -->
      </section>
    </div>
  </div>
</div>
<ng-template #viewApplicationProcess role="document" let-modal>
  <div class="modol-header">
    <h2 class="exampleModalLabel">View Process</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modo-contain" *ngIf="outputData.isApplicationProcess">
    <app-view-approval-process-comment [inputData]="outputData"></app-view-approval-process-comment>
  </div>
</ng-template>
<ng-template #viewContent role="document" let-modal>
  <div class="modol-header">
    <h2 class="exampleModalLabel">View Document</h2>
    <!-- <span class="caption">Fill required input to create new user</span> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modo-contain" *ngIf="outputData">
    <app-view-document [inputData]="outputData"></app-view-document>
  </div>
</ng-template>
<ng-template #approveContent role="document" let-modal>
  <div class="modol-header">
    <h2 class="exampleModalLabel"></h2>
    <!-- <span class="caption">Fill required input to create new user</span> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modo-contain" *ngIf="outputData.applicationId">
    <app-generic-approval [inputData]="outputData" (backToParent)="closePopup($event)"></app-generic-approval>
  </div>
</ng-template>
<ng-template #checkListPopup role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">New Checklist</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="dismissCheckListModal()"></button>
  </div>
  <app-create-checklist *ngIf="this.isApplicationDetailReady" [inputData]="this.paramsId"
    [existingData]="outputCheckList" (bankToParent)="cancelCheckList()"></app-create-checklist>
</ng-template>
<ng-template #verifyPopup let-modal>
  <div class="modal-content">
    <div class="modol-header">
      <h2 id="exampleModalLabel">{{ verificationData.title }}</h2>
      <p>{{ verificationData.message }}</p>
      <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
        id="btn-close" (click)="modal.dismiss('Cross click')"></button>
    </div>
    <div class="modal-body">
      <div class="mt-md">
        <h4>Do you want to proceed?</h4>
      </div>
      <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec" data-bs-dismiss="modal" (click)="modal.close('Close click')"
          id="deleteRecord-close"> No </button>
        <button type="button" class="kbk-btn kbk-btn-error" id="delete-product"
          (click)="approvalStatusApprovalSubmit()"> Yes </button>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #viewDocumentContent role="document" let-modal>
  <div class="modol-header">
    <h2 class="exampleModalLabel">View Document</h2>
    <!-- <span class="caption">Fill required input to create new user</span> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="app-gen-content">
    <div class="appl-info">
      <section>
        <div class="kbk-x-s sp-2 appl-info-fx">
          <div class="appl-info-card">
            <div>
              <div class="form-out">
                <label>Permit No</label>
                <span>{{ outputData.certificate_number }}</span>
              </div>
            </div>
            <div>
              <div class="form-out">
                <label>Status</label>
                <span>{{ outputData.document_status }}</span>
              </div>
            </div>
          </div>
          <div class="appl-info-card">
            <div>
              <div class="form-out">
                <label>Valid From</label>
                <span>{{ outputData.valid_from | date }}</span>
              </div>
            </div>
            <div>
              <div class="form-out">
                <label>Valid Until</label>
                <span>{{ outputData.valid_until }}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <div class="modol-content">
      <div class="kbk-x-c sp-sm mt-md">
        <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancelDocumentView()"> Cancel </button>
        <button class="kbk-btn kbk-btn-main" type="button" (click)="continue()"> Continue </button>
      </div>
    </div>
  </div>
  <div class="modo-contain" *ngIf="outputData">
    <app-view-document [inputData]="outputData"></app-view-document>
  </div>
</ng-template>
<ng-template #openToAssign role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Assign Reviewers</h2>
    <!-- <p> Users </p> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content" *ngIf="outputData.isAssigningData">
    <app-assign-application-to-user [inputData]="outputData"
      (backToParent)="closePopup($event)"></app-assign-application-to-user>
  </div>
</ng-template>
<ng-template #viewTheReviewers role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Reviewers</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div>
    <app-reassign-reviewers *ngIf="outputData.isReassignReviewer" [inputData]="outputData"
      (backToParent)="closeReAssignPopup($event)"></app-reassign-reviewers>
  </div>
</ng-template>
<ng-template #verifyContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Verify {{ outputData.verifyType }}</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <app-verification-reviewer *ngIf="outputData.isVerifying" [inputData]="outputData"></app-verification-reviewer>
</ng-template>
<ng-template #upiFromLandContent role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Verification</h2>
    <!-- <p> We will check if you have unpaid taxes, look in the old BPMIS for your project, and pull your zoning plan.
      </p> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content">
    <!-- <app-upi-info [info]="'2'" [upiData]="applicationDetail.projects.upi"></app-upi-info> -->
    <app-upi-info [info]="'2'" [upiData]="applicationDetail.projects.upi"></app-upi-info>
    <!-- <app-upi-info [info]="'2'" [upiData]="'5/07/01/05/1662'"></app-upi-info> -->
  </div>
</ng-template>
<ng-template #contentPrice role="document" let-modal>
  <div class="modol-header">
    <h2 id="exampleModalLabel">Get prices</h2>
    <!-- <p>Create new {{outPutData.name}}</p> -->
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modol-content">
    <form autocomplete="off">
      <div class="form-set">
        <div class="form-input">
          <label>Invoice Prices</label>
          <div>
            <select name="invoicePriceId" id="invoicePriceId" [(ngModel)]="invoicePriceId" (change)="getPrice()"
              required>
              <option *ngFor="let op of invoicePrices" [value]="op.id"> Min Sqm: {{ op.rangeInSqmMin }} - Max Sqm: {{
                op.rangeInSqmMax }} | Price {{ op.amount }} </option>
            </select>
          </div>
        </div>
      </div>
      <!-- <div class="kbk-x-c sp-sm mt-md">
        <button type="button" class="kbk-btn kbk-btn-sec">Cancel</button>
        <button *ngIf="submitted" type="button" class="kbk-btn kbk-btn-sec">Wait...</button>
        <button *ngIf="!submitted" type="submit" [disabled]="userForm.invalid"
          class="kbk-btn kbk-btn-main">{{this.userForm.value.id? 'Update' : 'Save'}}</button>
      </div> -->
    </form>
  </div>
</ng-template>
<ng-template #confirmToSubmit role="document" let-modal>
  <div class="modol-header" *ngIf="permissions.isAllowToApproveApplication">
    <h2 id="exampleModalLabel">Are you sure you want to submit</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <!-- <div class="step-panel_body">
    <div class="form-incheckbox">
      <label class="checkbox">
        <input type="checkbox" id="check" [checked]="isChecked" (click)="allowToSubmit()" />
        <span class="checkbox_box"></span>
        <span class="checkbox_txt">It is my solemn duty to certify the truth of the information submitted on this
          application and I agree to the terms and conditions.</span>
      </label>
    </div>
  </div> -->
  <div class="modol-content" *ngIf="permissions.isAllowToApproveApplication">
    <div class="kbk-x-c sp-sm mt-md">
      <button class="kbk-btn kbk-btn-sec" type="button" (click)="cancel()"> Cancel </button>
      <button class="kbk-btn kbk-btn-main" type="button" [disabled]="!isChecked" (click)="submitAndVerifyEverything()">
        Submit </button>

      <button *ngIf="!applicationDetail.certificateNumberEIA &&
        (this.applicationDetail.categoryTypes.code === 'CAT4' ||
        this.applicationDetail.categoryTypes.code === 'CAT5') &&
        outputData.isEIACertificateNumberChecker" class="kbk-btn kbk-btn-main" type="button" [disabled]="!isChecked"
        (click)="continueWithoutCerticateEIAChecker()">
        Submit without check EIA certicate </button>

    </div>
  </div>
  <div class="modol-header" *ngIf="!permissions.isAllowToApproveApplication">
    <h2 id="exampleModalLabel">You have no permission to approve</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
</ng-template>

<!-- Adding the View Process History Modal Revamp-->
<ng-template #viewProcessHistory role="document" let-modal>
  <div class="modol-header">
    <h2 class="exampleModalLabel">View Process</h2>
    <button type="button" class="kbk-btn kbk-btn-sec kbk-btn-close" data-bs-dismiss="modal" aria-label="Close"
      id="close-modal" (click)="modal.dismiss('Cross click')"></button>
  </div>
  <div class="modo-contain" *ngIf="outputData.isApplicationProcess">
    <app-view-process-history [inputData]="outputData"></app-view-process-history>
  </div>
</ng-template>