import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  ApplicationApprovalCheckListDto,
  ApplicationApprovalDto,
  // ApplicationApprovalCheckListDto,
  ApprovalDocumentDto,
  ApprovalLevelDto,
  ApprovalStatusDto,
  PermitCheckListDto,
  ReviewerReportDto,
} from '../dto/approval.dto';
import {
  ApplicationApproval,
  ApplicationApprovalCheckList,
  // ApplicationApprovalCheckList,
  ApprovalDocument,
  ApprovalLevel,
  ApprovalStatus,
  PermitCheckList,
} from '../entities/approval.entity';
import {
  ApplicationApprovalCheckListRepository,
  ApplicationApprovalRepository,
  ApprovalDocumentRepository,
  ApprovalLevelRepository,
  ApprovalStatusRepository,
  PermitCheckListRepository,
} from './approval.repository';
import { AUTH_SERVICE, DOCUMENTS_SERVICE } from '@app/common/constants';
import { ClientProxy } from '@nestjs/microservices';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
// import { PermitTypesRepository } from '../application.repository';
import axios from 'axios';
import { ApplicationService } from '../application/application.service';

import config from '../../config';

@Injectable()
export class ApprovalService {
  constructor(
    // Approval Level
    private readonly approvalStatusRepository: ApprovalStatusRepository,
    private readonly approvalLevelRepository: ApprovalLevelRepository,
    private readonly applicationApprovalRepository: ApplicationApprovalRepository,
    private readonly permitCheckListRepository: PermitCheckListRepository,
    private readonly applicationApprovalCheckListRepository: ApplicationApprovalCheckListRepository,
    private readonly approvalDocumentRepository: ApprovalDocumentRepository,

    @Inject(AUTH_SERVICE)
    private readonly authService: ClientProxy,

    @Inject(DOCUMENTS_SERVICE)
    private readonly documentService: ClientProxy,

    @InjectRepository(ApprovalStatus)
    private approvalStatusEntityManagerRepository: Repository<ApprovalStatus>,

    @InjectRepository(ApplicationApproval)
    private applicationApprovalEntityManagerRepository: Repository<ApplicationApproval>,

    @InjectRepository(PermitCheckList)
    private permitCheckListEntityManagerRepository: Repository<PermitCheckList>,

    @InjectRepository(ApplicationApprovalCheckList)
    private approvalCheckListEntityManagerRepository: Repository<ApplicationApprovalCheckList>,

    // readonly permitTypesRepository: PermitTypesRepository,
    private readonly applicationService: ApplicationService,
  ) {}

  async checkUser(userId: string) {
    return this.authService.send<any>({ cmd: 'userData' }, userId).toPromise();
  }

  async updateDocumentStatusEvent(documentId: string) {
    return this.documentService
      .send<any>({ cmd: 'updateDocumentStatusEvent' }, documentId)
      .toPromise();
  }

  // async checkApplication(applicationId: string) {
  //   return this.applicationService
  //     .send<any>({ cmd: 'applicationData' }, applicationId)
  //     .toPromise();
  // }

  // ApprovalLevel
  async createApprovalLevel(approvalLevelDto: ApprovalLevelDto) {
    const approvalLevel = new ApprovalLevel({
      ...approvalLevelDto,
    });
    return this.approvalLevelRepository.create(approvalLevel);
  }

  async findAllApprovalLevels() {
    return this.approvalLevelRepository.find({});
  }

  async findOneApprovalLevel(id: string) {
    return this.approvalLevelRepository.findOne({ id });
  }

  async updateApprovalLevel(id: string, approvalLevelDto: ApprovalLevelDto) {
    return this.approvalLevelRepository.findOneAndUpdate(
      { id },
      approvalLevelDto,
    );
  }

  async removeApprovalLevel(id: string) {
    return this.approvalLevelRepository.findOneAndDelete({ id });
  }

  // approval Status
  async createApprovalStatus(approvalStatusDto: ApprovalStatusDto) {
    const dataFromDb = await this.approvalLevelRepository.findOne({
      id: approvalStatusDto.approvalLevelId,
    });
    if (!dataFromDb)
      throw new HttpException(
        'Referenced Data Not Found',
        HttpStatus.NOT_FOUND,
      );
    const approvalStatus = new ApprovalStatus({
      ...approvalStatusDto,
      approvalLevels: (approvalStatusDto.approvalLevelId = {
        id: dataFromDb.id,
      } as any),
    });
    return this.approvalStatusRepository.create(approvalStatus);
  }

  async findAllApprovalStatus() {
    return this.approvalStatusRepository.findAll({
      relations: { approvalLevels: true },
    });
  }

  async findOneApprovalStatus(id: string) {
    return this.approvalStatusRepository.findOne({ id });
  }

  // async updateApprovalStatus(
  //   id: string,
  //   approvalStatusDto: ApprovalStatusDto,
  // ): Promise<void> {
  //   const dataFromDb = await this.approvalLevelRepository.findOne({
  //     id: approvalStatusDto.approvalLevelId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );

  //   // Update application entity
  //   const approvalStatusUpdate =
  //     await this.approvalStatusEntityManagerRepository
  //       .createQueryBuilder()
  //       .update(ApprovalStatus)
  //       .set({ approvalLevels: dataFromDb })
  //       .where('id = :id', { id })
  //       .execute();

  //   console.log(approvalStatusUpdate);
  // }

  async updateApprovalStatus(id: string, approvalStatusDto: ApprovalStatusDto) {
    const approvalLevelData = await this.approvalLevelRepository.findOne({
      id: approvalStatusDto.approvalLevelId,
    });
    if (!approvalLevelData) {
      throw new HttpException(
        'Approval Level Data Not Found',
        HttpStatus.NOT_FOUND,
      );
    }

    // Update approvalStatus entity
    const approvalStatusUpdate =
      await this.approvalStatusEntityManagerRepository
        .createQueryBuilder()
        .update(ApprovalStatus)
        .set({
          approvalLevels: approvalLevelData,
          name: approvalStatusDto.name,
          code: approvalStatusDto.code,
          additionalCode: approvalStatusDto.additionalCode,
        })
        .where('id = :id', { id: id })
        .execute();
    console.log(approvalStatusUpdate);
    return {
      message: 'Approval status updated successfully',
      status: HttpStatus.OK,
    };
  }

  // async updateApprovalStatus(id: string, approvalStatusDto: ApprovalStatusDto) {
  //   return this.approvalStatusRepository.findOneAndUpdate(
  //     { id },
  //     approvalStatusDto,
  //   );
  // }

  async removeApprovalStatus(id: string) {
    return this.approvalStatusRepository.findOneAndDelete({ id });
  }

  async findApprovalStatusByApprovalLevelId(approvalLevelId: string) {
    return this.approvalStatusEntityManagerRepository
      .createQueryBuilder('approvalstatus')
      .where('approvalstatus.approvalLevelsId = :approvalLevelId', {
        approvalLevelId,
      })
      .leftJoinAndSelect('approvalstatus.approvalLevels', 'approvalLevels')
      .getMany();
  }

  // //   // ApplicationApproval
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   const applicationStatusFromDb =
  //     await this.applicationService.findOneApplicationStatus(
  //       applicationApprovalDto.applicationStatusId,
  //     );

  //   console.log(applicationStatusFromDb.name);

  //   const dataFromDb = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalDto.approvalStatusId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);

  //   const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalDto.approvalLevelId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);

  //   const applicationApproval = new ApplicationApproval({
  //     ...applicationApprovalDto,
  //     approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //     applications: (applicationApprovalDto.applicationId = {
  //       id: applicationApprovalDto.applicationId,
  //     } as any),
  //   });

  //   const savedData =
  //     await this.applicationApprovalRepository.create(applicationApproval);

  //   // // get application service for update
  //   const sendUpdateApplicationAStatus =
  //     this.applicationService.updateApplicationAfterApproval(
  //       applicationApprovalDto.applicationId,
  //       applicationApprovalDto.applicationStatusId,
  //     );
  //   console.log(sendUpdateApplicationAStatus);

  //   // get user information from auth service
  //   const userDetails = await this.checkUser(
  //     applicationApprovalDto.applicantUserId,
  //   );

  //   if (
  //     dataFromDb.code === 'APPD' ||
  //     dataFromDb.code === 'REJT' ||
  //     dataFromDb.code === 'RFAC' ||
  //     dataFromDb.code === 'CTFD' ||
  //     dataFromDb.code === 'DCRTF' ||
  //     dataFromDb.code === 'CRTFD'
  //   ) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetails.email,
  //       subject: 'Kubaka Approval Notification',
  //       message: `
  //       Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //       Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //       ${savedData.comment}<br><br>
  //       Best regards, <br>
  //       KUBAKA Team
  //       `,
  //     };

  //     try {
  //       const response = await axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);

  //       const responseSMS = await axios.post(
  //         'https://notification.kubaka.gov.rw/sms/send',
  //         {
  //           msisdn: `${userDetails.phoneNumber}`,
  //           message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //         },
  //       );
  //       console.log('SMS sent successfully:', responseSMS.data);

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     console.log(dataFromDb.code);
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  //   // ApplicationApproval
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   const applicationStatusFromDb =
  //     await this.applicationService.findOneApplicationStatus(
  //       applicationApprovalDto.applicationStatusId,
  //     );

  //   console.log(applicationStatusFromDb.name);

  //   const dataFromDb = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalDto.approvalStatusId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);

  //   const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalDto.approvalLevelId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);

  //   const applicationApproval = new ApplicationApproval({
  //     ...applicationApprovalDto,
  //     approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //     applications: (applicationApprovalDto.applicationId = {
  //       id: applicationApprovalDto.applicationId,
  //     } as any),
  //   });

  //   // const applicationId = 'd91be1d4-85c5-45cf-9e72-5f546a999d30';
  //   const applicationId = applicationApprovalDto.applicationId;
  //   console.log(applicationId);

  //   const inputObject = applicationId;
  //   const uuidApplication = this.extractUUID(inputObject);
  //   console.log(uuidApplication);

  //   const applicationFromDb =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       uuidApplication,
  //     );
  //   console.log(applicationFromDb);

  //   const savedData =
  //     await this.applicationApprovalRepository.create(applicationApproval);

  //   // // get application service for update
  //   const sendUpdateApplicationAStatus =
  //     this.applicationService.updateApplicationAfterApproval(
  //       applicationApprovalDto.applicationId,
  //       applicationApprovalDto.applicationStatusId,
  //     );
  //   console.log(sendUpdateApplicationAStatus);

  //   // get user information from auth service
  //   const userDetails = await this.checkUser(
  //     applicationApprovalDto.applicantUserId,
  //   );

  //   // get owner information from auth service
  //   const ownerUserId = applicationFromDb.projects.userId;
  //   const ownerDetails = await this.checkUser(ownerUserId);

  //   // if (
  //   //   dataFromDb.code === 'APPD' ||
  //   //   dataFromDb.code === 'REJT' ||
  //   //   dataFromDb.code === 'RFAC' ||
  //   //   dataFromDb.code === 'CTFD' ||
  //   //   dataFromDb.code === 'DCRTF' ||
  //   //   dataFromDb.code === 'CRTFD'
  //   // ) {
  //   if (
  //     dataFromDb.code === 'SUB' ||
  //     dataFromDb.code === 'CXL' ||
  //     dataFromDb.code === 'CTFD' ||
  //     dataFromDb.code === 'UNRV' ||
  //     dataFromDb.code === 'RSMB' ||
  //     dataFromDb.code === 'RVWD'
  //   ) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetails.email,
  //       subject: 'Kubaka Approval Notification',
  //       message: `
  //       Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //       Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //       ${savedData.comment}<br><br>
  //       Best regards, <br>
  //       KUBAKA Team
  //       `,
  //     };

  //     try {
  //       const response = await axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);

  //       const responseSMS = await axios.post(
  //         'https://notification.kubaka.gov.rw/sms/send',
  //         {
  //           msisdn: `${userDetails.phoneNumber}`,
  //           message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //         },
  //       );
  //       console.log('SMS sent successfully:', responseSMS.data);

  //       const responseSMSOwner = await axios.post(
  //         'https://notification.kubaka.gov.rw/sms/send',
  //         {
  //           msisdn: `${ownerDetails.phoneNumber}`,
  //           message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //         },
  //       );
  //       console.log(
  //         'SMS sent successfully to the owner:',
  //         responseSMSOwner.data,
  //       );

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     console.log(dataFromDb.code);
  //     throw new HttpException(
  //       'Email not sent',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // ApplicationApproval
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   const applicationStatusFromDb =
  //     await this.applicationService.findOneApplicationStatus(
  //       applicationApprovalDto.applicationStatusId,
  //     );

  //   console.log(applicationStatusFromDb.name);

  //   const dataFromDb = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalDto.approvalStatusId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);

  //   const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalDto.approvalLevelId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);

  //   const applicationApproval = new ApplicationApproval({
  //     ...applicationApprovalDto,
  //     approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //     applications: (applicationApprovalDto.applicationId = {
  //       id: applicationApprovalDto.applicationId,
  //     } as any),
  //   });

  //   const applicationId = applicationApprovalDto.applicationId;
  //   console.log(applicationId);

  //   const inputObject = applicationId;
  //   const uuidApplication = this.extractUUID(inputObject);
  //   console.log(uuidApplication);

  //   const applicationFromDb =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       uuidApplication,
  //     );
  //   console.log(applicationFromDb);

  //   const savedData =
  //     await this.applicationApprovalRepository.create(applicationApproval);

  //   if (applicationStatusFromDb.name === 'NORHA') {
  //     // Update application status here for RHA
  //     const updatedApplication =
  //       await this.applicationService.updateApplicationAfterApprovalNORHA(
  //         applicationApprovalDto.applicationId,
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log('Updated application status of RRA:', updatedApplication);
  //   } else {
  //     // // get application service for update
  //     const sendUpdateApplicationAStatus =
  //       this.applicationService.updateApplicationAfterApproval(
  //         applicationApprovalDto.applicationId,
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log(
  //       'Updated application status from the user:',
  //       sendUpdateApplicationAStatus,
  //     );
  //   }

  //   const userDetails = await this.checkUser(
  //     applicationApprovalDto.applicantUserId,
  //   );

  //   const ownerUserId = applicationFromDb.projects.userId;
  //   const ownerDetails = await this.checkUser(ownerUserId);

  //   if (
  //     dataFromDb.code === 'SUB' ||
  //     dataFromDb.code === 'REJT' ||
  //     dataFromDb.code === 'CXL' ||
  //     dataFromDb.code === 'CTFD' ||
  //     dataFromDb.code === 'RFAC' ||
  //     dataFromDb.code === 'UNRV' ||
  //     dataFromDb.code === 'RSMB'
  //   ) {
  //     const requestData = {
  //       sender_name: 'KUBAKA MIS',
  //       sender_email: '<EMAIL>',
  //       receiver_name: 'KUBAKA User',
  //       receiver_email: userDetails.email,
  //       subject: 'Kubaka Approval Notification',
  //       message: `
  //       Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //       Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //       ${savedData.comment}<br><br>
  //       Best regards, <br>
  //       KUBAKA Team
  //     `,
  //     };

  //     try {
  //       const response = await axios.post(
  //         'https://notification.kubaka.gov.rw/email/send/',
  //         requestData,
  //       );
  //       console.log('Email sent successfully:', response.data);

  //       const responseSMS = await axios.post(
  //         'https://notification.kubaka.gov.rw/sms/send',
  //         {
  //           msisdn: userDetails.phoneNumber,
  //           message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //         },
  //       );
  //       console.log('SMS sent successfully:', responseSMS.data);

  //       const responseSMSOwner = await axios.post(
  //         'https://notification.kubaka.gov.rw/sms/send',
  //         {
  //           msisdn: ownerDetails.phoneNumber,
  //           message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //         },
  //       );
  //       console.log(
  //         'SMS sent successfully to the owner:',
  //         responseSMSOwner.data,
  //       );

  //       return savedData;
  //     } catch (error) {
  //       console.error('Error sending email:', error);
  //       throw new HttpException(
  //         'Email not sent',
  //         HttpStatus.INTERNAL_SERVER_ERROR,
  //       );
  //     }
  //   } else {
  //     console.log(dataFromDb.code);
  //     throw new HttpException(
  //       'Error occur on approval',
  //       HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // ApplicationApproval
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   try {
  //     const applicationStatusFromDb =
  //       await this.applicationService.findOneApplicationStatus(
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log(applicationStatusFromDb.name);

  //     const dataFromDb = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalDto.approvalStatusId,
  //     });
  //     if (!dataFromDb) {
  //       console.error('Status Not Found');
  //       throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalDto.approvalLevelId,
  //     });
  //     if (!dataFromDb2) {
  //       console.error('Level Not Found');
  //       throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationApproval = new ApplicationApproval({
  //       ...applicationApprovalDto,
  //       // approvalStatus: { id: dataFromDb.id },
  //       // approvalLevels: { id: dataFromDb2.id },
  //       // applications: { id: applicationApprovalDto.applicationId },
  //       approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //         id: dataFromDb.id,
  //       } as any),
  //       approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //         id: dataFromDb2.id,
  //       } as any),
  //       applications: (applicationApprovalDto.applicationId = {
  //         id: applicationApprovalDto.applicationId,
  //       } as any),
  //     });

  //     const applicationId = applicationApprovalDto.applicationId;
  //     console.log(applicationId);

  //     const uuidApplication = this.extractUUID(applicationId);
  //     console.log(uuidApplication);

  //     const applicationFromDb =
  //       await this.applicationService.findOneApplicationWithAllDetails(
  //         uuidApplication,
  //       );
  //     console.log(applicationFromDb);

  //     const savedData =
  //       await this.applicationApprovalRepository.create(applicationApproval);

  //     if (applicationStatusFromDb.name === 'NORHA') {
  //       // Update application status here for RHA
  //       const updatedApplication =
  //         await this.applicationService.updateApplicationAfterApprovalNORHA(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log('Updated application status of RHA:', updatedApplication);
  //     } else {
  //       // get application service for update
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterApproval(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const userDetails = await this.checkUser(
  //       applicationApprovalDto.applicantUserId,
  //     );

  //     const ownerUserId = applicationFromDb.projects.userId;
  //     const ownerDetails = await this.checkUser(ownerUserId);

  //     if (
  //       ['SUB', 'REJT', 'CXL', 'CTFD', 'RFAC', 'UNRV', 'RSMB'].includes(
  //         dataFromDb.code,
  //       )
  //     ) {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetails.email,
  //         subject: 'Kubaka Approval Notification',
  //         message: `
  //           Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //           Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //           ${savedData.comment}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const response = await axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         );
  //         console.log('Email sent successfully:', response.data);

  //         const responseSMS = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: userDetails.phoneNumber,
  //             message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log('SMS sent successfully:', responseSMS.data);

  //         const responseSMSOwner = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: ownerDetails.phoneNumber,
  //             message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log(
  //           'SMS sent successfully to the owner:',
  //           responseSMSOwner.data,
  //         );

  //         return savedData;
  //       } catch (error) {
  //         console.error('Error sending notification:', error);
  //         throw new HttpException(
  //           'Notification not sent',
  //           HttpStatus.BAD_GATEWAY,
  //         );
  //       }
  //     } else {
  //       console.log(dataFromDb.code);
  //       throw new HttpException(
  //         'Error occur on approval',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // // approve with file upload in base64
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const applicationStatusFromDb =
  //       await this.applicationService.findOneApplicationStatus(
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log(applicationStatusFromDb.name);

  //     const dataFromDb = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalDto.approvalStatusId,
  //     });
  //     if (!dataFromDb) {
  //       console.error('Status Not Found');
  //       throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalDto.approvalLevelId,
  //     });
  //     if (!dataFromDb2) {
  //       console.error('Level Not Found');
  //       throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationApproval = new ApplicationApproval({
  //       ...applicationApprovalDto,
  //       approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //         id: dataFromDb.id,
  //       } as any),
  //       approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //         id: dataFromDb2.id,
  //       } as any),
  //       applications: (applicationApprovalDto.applicationId = {
  //         id: applicationApprovalDto.applicationId,
  //       } as any),

  //       fileBase64,
  //     });

  //     const applicationId = applicationApprovalDto.applicationId;
  //     console.log(applicationId);

  //     const uuidApplication = this.extractUUID(applicationId);
  //     console.log(uuidApplication);

  //     const applicationFromDb =
  //       await this.applicationService.findOneApplicationWithAllDetails(
  //         uuidApplication,
  //       );
  //     console.log(applicationFromDb);

  //     const savedData =
  //       await this.applicationApprovalRepository.create(applicationApproval);

  //     if (applicationStatusFromDb.code === 'NORHA') {
  //       const updatedApplication =
  //         await this.applicationService.updateApplicationAfterApprovalNORHA(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log('Updated application status of RRA:', updatedApplication);
  //       // return 'Application sent to RHA';
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application sent to RHA',
  //       };
  //     } else {
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterApproval(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const userDetails = await this.checkUser(
  //       applicationApprovalDto.applicantUserId,
  //     );

  //     const ownerUserId = applicationFromDb.projects.userId;
  //     const ownerDetails = await this.checkUser(ownerUserId);

  //     if (
  //       ['SUB', 'REJT', 'CXL', 'CTFD', 'RFAC', 'UNRV', 'RSMB'].includes(
  //         dataFromDb.code,
  //       )
  //     ) {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetails.email,
  //         subject: 'Kubaka Approval Notification',
  //         message: `
  //           Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //           Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //           ${savedData.comment}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const response = await axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         );
  //         console.log('Email sent successfully:', response.data);

  //         const responseSMS = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: userDetails.phoneNumber,
  //             message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log('SMS sent successfully:', responseSMS.data);

  //         const responseSMSOwner = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: ownerDetails.phoneNumber,
  //             message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log(
  //           'SMS sent successfully to the owner:',
  //           responseSMSOwner.data,
  //         );

  //         console.log('Application approved successfully');
  //         // return 'successfully approved';
  //         return {
  //           statusCode: HttpStatus.OK,
  //           message: 'Application approval approved',
  //         };
  //       } catch (error) {
  //         console.error('Error sending notification:', error);
  //         throw new HttpException(
  //           'Notification not sent',
  //           HttpStatus.BAD_GATEWAY,
  //         );
  //       }
  //     } else {
  //       console.error('Unexpected approval status code:', dataFromDb.code);
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application approval run successfully without NORHA',
  //       };
  //     }
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // without file with IP address and browser
  async createApplicationApproval(
    applicationApprovalDto: ApplicationApprovalDto,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      // Parse user agent for browser and OS
      const clientInfo = this.parseUserAgent(userAgent);

      const applicationStatusFromDb =
        await this.applicationService.findOneApplicationStatus(
          applicationApprovalDto.applicationStatusId,
        );
      console.log(applicationStatusFromDb.name);

      const dataFromDb = await this.approvalStatusRepository.findOne({
        id: applicationApprovalDto.approvalStatusId,
      });
      if (!dataFromDb) {
        console.error('Status Not Found');
        throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
      }

      const dataFromDb2 = await this.approvalLevelRepository.findOne({
        id: applicationApprovalDto.approvalLevelId,
      });
      if (!dataFromDb2) {
        console.error('Level Not Found');
        throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);
      }

      const applicationApproval = new ApplicationApproval({
        ...applicationApprovalDto,

        // save IP address and browser
        ipAddress: clientIp,
        browser: clientInfo.browser,
        operatingSystem: clientInfo.operatingSystem,

        approvalStatus: (applicationApprovalDto.approvalStatusId = {
          id: dataFromDb.id,
        } as any),
        approvalLevels: (applicationApprovalDto.approvalLevelId = {
          id: dataFromDb2.id,
        } as any),
        applications: (applicationApprovalDto.applicationId = {
          id: applicationApprovalDto.applicationId,
        } as any),
      });

      const applicationId = applicationApprovalDto.applicationId;
      console.log(applicationId);

      const uuidApplication = this.extractUUID(applicationId);
      console.log(uuidApplication);

      const applicationFromDb =
        await this.applicationService.findOneApplicationWithAllDetails(
          uuidApplication,
        );
      console.log(applicationFromDb);

      const savedData =
        await this.applicationApprovalRepository.create(applicationApproval);

      if (applicationStatusFromDb.code === 'NORHA') {
        const updatedApplication =
          await this.applicationService.updateApplicationAfterApprovalNORHA(
            applicationApprovalDto.applicationId,
            applicationApprovalDto.applicationStatusId,
          );
        console.log('Updated application status of RRA:', updatedApplication);
        // return 'Application sent to RHA';
        return {
          statusCode: HttpStatus.OK,
          message: 'Application sent to RHA',
        };
      }
      // Special case: status is RTNNO to update the returned date
      else if (applicationStatusFromDb.code === 'RTNNO') {
        const updatedApplication =
          await this.applicationService.updateApplicationAfterApprovalRTNNO(
            applicationApprovalDto.applicationId,
            applicationApprovalDto.applicationStatusId,
          );
        console.log('Updated application status of RRA:', updatedApplication);
        return {
          statusCode: HttpStatus.OK,
          message: 'Application marked as returned without objection',
        };
      } else {
        const sendUpdateApplicationAStatus =
          await this.applicationService.updateApplicationAfterApproval(
            applicationApprovalDto.applicationId,
            applicationApprovalDto.applicationStatusId,
          );
        console.log(
          'Updated application status from the user:',
          sendUpdateApplicationAStatus,
        );
      }

      const userDetails = await this.checkUser(
        applicationFromDb.submittedByUserId,
      );

      // const userDetails = await this.checkUser(
      //   applicationApprovalDto.applicantUserId,
      // );

      const ownerUserId = applicationFromDb.projects.userId;
      const ownerDetails = await this.checkUser(ownerUserId);

      if (['UNCRN', 'CTFD', 'CXL'].includes(applicationStatusFromDb.code)) {
        const updatedApplication =
          await this.applicationService.updateApplicationNumberOfDaysItTakes(
            applicationApprovalDto.applicationId,
          );
        console.log('Updated application status of RRA:', updatedApplication);
      }
      if (
        ['SUB', 'REJT', 'CXL', 'CTFD', 'RFAC', 'UNRV', 'RSMB'].includes(
          dataFromDb.code,
        )
      ) {
        const requestData = {
          sender_name: 'KUBAKA MIS',
          sender_email: `${config.notification.senderEmail}`,
          // sender_email: '<EMAIL>',
          receiver_name: 'KUBAKA User',
          receiver_email: userDetails.email,
          subject: 'Kubaka Approval Notification',
          message: `
            Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
            Your application in OSC via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
            ${savedData.comment}<br><br>
            Best regards, <br>
            KUBAKA Team
          `,
        };

        try {
          const response = await axios.post(
            'https://notification.kubaka.gov.rw/email/send/',
            requestData,
          );
          console.log('Email sent successfully:', response.data);

          const responseSMS = await axios.post(
            'https://notification.kubaka.gov.rw/sms/send',
            {
              msisdn: userDetails.phoneNumber,
              message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in OSC via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
            },
          );
          console.log('SMS sent successfully:', responseSMS.data);

          const responseSMSOwner = await axios.post(
            'https://notification.kubaka.gov.rw/sms/send',
            {
              msisdn: ownerDetails.phoneNumber,
              message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in OSC via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
            },
          );
          console.log(
            'SMS sent successfully to the owner:',
            responseSMSOwner.data,
          );

          console.log('Application approved successfully');
          // return 'successfully approved';
          return {
            statusCode: HttpStatus.OK,
            message: 'Application approval approved',
          };
        } catch (error) {
          console.error('Error sending notification:', error);
          throw new HttpException(
            'Notification not sent',
            HttpStatus.BAD_GATEWAY,
          );
        }
      } else {
        console.error('Unexpected approval status code:', dataFromDb.code);
        return {
          statusCode: HttpStatus.OK,
          message: 'Application approval run successfully without NORHA',
        };
      }
    } catch (error) {
      console.error('Error in createApplicationApproval:', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // application approval with irembo
  async createApplicationApprovalWithIrembo(
    applicationApprovalDto: ApplicationApprovalDto,
    clientIp: string,
    userAgent: string,
  ) {
    console.log('approval with irembo is working well');
    console.log('======================================');
    console.log(applicationApprovalDto);
    console.log(clientIp);
    console.log(userAgent);
    return {
      statusCode: HttpStatus.OK,
      message: 'Application approval approved',
    };
  }

  // // without file
  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   try {
  //     const applicationStatusFromDb =
  //       await this.applicationService.findOneApplicationStatus(
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log(applicationStatusFromDb.name);

  //     const dataFromDb = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalDto.approvalStatusId,
  //     });
  //     if (!dataFromDb) {
  //       console.error('Status Not Found');
  //       throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalDto.approvalLevelId,
  //     });
  //     if (!dataFromDb2) {
  //       console.error('Level Not Found');
  //       throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationApproval = new ApplicationApproval({
  //       ...applicationApprovalDto,
  //       approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //         id: dataFromDb.id,
  //       } as any),
  //       approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //         id: dataFromDb2.id,
  //       } as any),
  //       applications: (applicationApprovalDto.applicationId = {
  //         id: applicationApprovalDto.applicationId,
  //       } as any),
  //     });

  //     const applicationId = applicationApprovalDto.applicationId;
  //     console.log(applicationId);

  //     const uuidApplication = this.extractUUID(applicationId);
  //     console.log(uuidApplication);

  //     const applicationFromDb =
  //       await this.applicationService.findOneApplicationWithAllDetails(
  //         uuidApplication,
  //       );
  //     console.log(applicationFromDb);

  //     const savedData =
  //       await this.applicationApprovalRepository.create(applicationApproval);

  //     if (applicationStatusFromDb.code === 'NORHA') {
  //       const updatedApplication =
  //         await this.applicationService.updateApplicationAfterApprovalNORHA(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log('Updated application status of RRA:', updatedApplication);
  //       // return 'Application sent to RHA';
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application sent to RHA',
  //       };
  //     } else {
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterApproval(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const userDetails = await this.checkUser(
  //       applicationApprovalDto.applicantUserId,
  //     );

  //     const ownerUserId = applicationFromDb.projects.userId;
  //     const ownerDetails = await this.checkUser(ownerUserId);

  //     if (
  //       ['SUB', 'REJT', 'CXL', 'CTFD', 'RFAC', 'UNRV', 'RSMB'].includes(
  //         dataFromDb.code,
  //       )
  //     ) {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetails.email,
  //         subject: 'Kubaka Approval Notification',
  //         message: `
  //           Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //           Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //           ${savedData.comment}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const response = await axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         );
  //         console.log('Email sent successfully:', response.data);

  //         const responseSMS = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: userDetails.phoneNumber,
  //             message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log('SMS sent successfully:', responseSMS.data);

  //         const responseSMSOwner = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: ownerDetails.phoneNumber,
  //             message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log(
  //           'SMS sent successfully to the owner:',
  //           responseSMSOwner.data,
  //         );

  //         console.log('Application approved successfully');
  //         // return 'successfully approved';
  //         return {
  //           statusCode: HttpStatus.OK,
  //           message: 'Application approval approved',
  //         };
  //       } catch (error) {
  //         console.error('Error sending notification:', error);
  //         throw new HttpException(
  //           'Notification not sent',
  //           HttpStatus.BAD_GATEWAY,
  //         );
  //       }
  //     } else {
  //       console.error('Unexpected approval status code:', dataFromDb.code);
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application approval run successfully without NORHA',
  //       };
  //     }
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // Get the operating system
  private parseUserAgent(userAgent: string) {
    let operatingSystem = 'Unknown OS';

    if (/windows/i.test(userAgent)) {
      operatingSystem = 'Windows';
    } else if (/macintosh|mac os x/i.test(userAgent)) {
      operatingSystem = 'macOS';
    } else if (/linux/i.test(userAgent)) {
      operatingSystem = 'Linux';
    } else if (/android/i.test(userAgent)) {
      operatingSystem = 'Android';
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
      operatingSystem = 'iOS';
    }

    let browser = 'Unknown Browser';
    if (/chrome/i.test(userAgent)) {
      browser = 'Chrome';
    } else if (/safari/i.test(userAgent) && !/chrome/i.test(userAgent)) {
      browser = 'Safari';
    } else if (/firefox/i.test(userAgent)) {
      browser = 'Firefox';
    } else if (/edge/i.test(userAgent)) {
      browser = 'Edge';
    } else if (/msie|trident/i.test(userAgent)) {
      browser = 'Internet Explorer';
    }

    return { operatingSystem, browser };
  }

  // async createApplicationApproval(
  //   applicationApprovalDto: ApplicationApprovalDto,
  // ) {
  //   try {
  //     const applicationStatusFromDb =
  //       await this.applicationService.findOneApplicationStatus(
  //         applicationApprovalDto.applicationStatusId,
  //       );
  //     console.log(applicationStatusFromDb.name);

  //     const dataFromDb = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalDto.approvalStatusId,
  //     });
  //     if (!dataFromDb) {
  //       console.error('Status Not Found');
  //       throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const dataFromDb2 = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalDto.approvalLevelId,
  //     });
  //     if (!dataFromDb2) {
  //       console.error('Level Not Found');
  //       throw new HttpException('Level Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationApproval = new ApplicationApproval({
  //       ...applicationApprovalDto,
  //       approvalStatus: (applicationApprovalDto.approvalStatusId = {
  //         id: dataFromDb.id,
  //       } as any),
  //       approvalLevels: (applicationApprovalDto.approvalLevelId = {
  //         id: dataFromDb2.id,
  //       } as any),
  //       applications: (applicationApprovalDto.applicationId = {
  //         id: applicationApprovalDto.applicationId,
  //       } as any),
  //     });

  //     const applicationId = applicationApprovalDto.applicationId;
  //     console.log(applicationId);

  //     const uuidApplication = this.extractUUID(applicationId);
  //     console.log(uuidApplication);

  //     const applicationFromDb =
  //       await this.applicationService.findOneApplicationWithAllDetails(
  //         uuidApplication,
  //       );
  //     console.log(applicationFromDb);

  //     const savedData =
  //       await this.applicationApprovalRepository.create(applicationApproval);

  //     if (applicationStatusFromDb.code === 'NORHA') {
  //       const updatedApplication =
  //         await this.applicationService.updateApplicationAfterApprovalNORHA(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log('Updated application status of RRA:', updatedApplication);
  //       // return 'Application sent to RHA';
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application sent to RHA',
  //       };
  //     } else {
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterApproval(
  //           applicationApprovalDto.applicationId,
  //           applicationApprovalDto.applicationStatusId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const userDetails = await this.checkUser(
  //       applicationApprovalDto.applicantUserId,
  //     );

  //     const ownerUserId = applicationFromDb.projects.userId;
  //     const ownerDetails = await this.checkUser(ownerUserId);

  //     if (
  //       ['SUB', 'REJT', 'CXL', 'CTFD', 'RFAC', 'UNRV', 'RSMB'].includes(
  //         dataFromDb.code,
  //       )
  //     ) {
  //       const requestData = {
  //         sender_name: 'KUBAKA MIS',
  //         sender_email: '<EMAIL>',
  //         receiver_name: 'KUBAKA User',
  //         receiver_email: userDetails.email,
  //         subject: 'Kubaka Approval Notification',
  //         message: `
  //           Dear ${userDetails.firstName} ${userDetails.lastName}, <br>
  //           Your application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name}<br><br>
  //           ${savedData.comment}<br><br>
  //           Best regards, <br>
  //           KUBAKA Team
  //         `,
  //       };

  //       try {
  //         const response = await axios.post(
  //           'https://notification.kubaka.gov.rw/email/send/',
  //           requestData,
  //         );
  //         console.log('Email sent successfully:', response.data);

  //         const responseSMS = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: userDetails.phoneNumber,
  //             message: `Dear ${userDetails.firstName} ${userDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nPlease check details in the system \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log('SMS sent successfully:', responseSMS.data);

  //         const responseSMSOwner = await axios.post(
  //           'https://notification.kubaka.gov.rw/sms/send',
  //           {
  //             msisdn: ownerDetails.phoneNumber,
  //             message: `Dear ${ownerDetails.firstName} ${ownerDetails.lastName},\n\nYour application in RHA via KUBAKA MIS has been ${applicationStatusFromDb.name} \n\nBest regards,\nKUBAKA Team`,
  //           },
  //         );
  //         console.log(
  //           'SMS sent successfully to the owner:',
  //           responseSMSOwner.data,
  //         );

  //         console.log('Application approved successfully');
  //         // return 'successfully approved';
  //         return {
  //           statusCode: HttpStatus.OK,
  //           message: 'Application approval approved',
  //         };
  //       } catch (error) {
  //         console.error('Error sending notification:', error);
  //         throw new HttpException(
  //           'Notification not sent',
  //           HttpStatus.BAD_GATEWAY,
  //         );
  //       }
  //     } else {
  //       console.error('Unexpected approval status code:', dataFromDb.code);
  //       return {
  //         statusCode: HttpStatus.OK,
  //         message: 'Application approval run successfully without NORHA',
  //       };
  //     }
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  extractUUID(input: { id: string } | string): string {
    if (typeof input === 'string') {
      return input;
    }
    return input.id;
  }

  async findAllApplicationApprovals() {
    return this.applicationApprovalRepository.findAll({
      relations: {
        applications: true,
        approvalStatus: true,
        approvalLevels: true,
      },
    });
  }

  async findOneApplicationApproval(id: string) {
    return this.applicationApprovalRepository.findOne({ id });
  }

  // application process
  // async findOneApplicationApprovalByApplicationId(applicationId: string) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       applicationId,
  //     );

  //   const applicationApprovalsQuery =
  //     await this.applicationApprovalEntityManagerRepository
  //       .createQueryBuilder('applicationApproval')
  //       .where('applicationApproval.applicationsId = :applicationId', {
  //         applicationId,
  //       })
  //       .leftJoinAndSelect('applicationApproval.applications', 'applications')
  //       .leftJoinAndSelect('applications.projects', 'projects')
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalLevels',
  //         'approvalLevels',
  //       )
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .getMany();

  //   if (applicationApprovalsQuery.length === 0) {
  //     // Fetch user names and reviewer names even if no application approvals are found
  //     const userData = application.userId
  //       ? await this.checkUser(application.userId)
  //       : null;

  //     const reviewerPromises = application.assignUsersFoReview.map(
  //       async (reviewerId) => {
  //         return this.checkUser(reviewerId);
  //       },
  //     );

  //     const reviewers = await Promise.all(reviewerPromises);

  //     return {
  //       ...application,
  //       user: userData ? `${userData.firstName} ${userData.lastName}` : null,
  //       reviewers: reviewers.map((reviewer) =>
  //         reviewer ? `${reviewer.firstName} ${reviewer.lastName}` : null,
  //       ),
  //     };
  //   }

  //   const promises = applicationApprovalsQuery.map(async (approval) => {
  //     const userData = application.userId
  //       ? await this.checkUser(application.userId)
  //       : null;

  //     const reviewerPromises = application.assignUsersFoReview.map(
  //       async (reviewerId) => {
  //         return this.checkUser(reviewerId);
  //       },
  //     );

  //     const reviewers = await Promise.all(reviewerPromises);

  //     if (reviewers.length === 0) {
  //       // Return only the application details if no reviewers are found
  //       return application;
  //     }

  //     return {
  //       ...approval,
  //       user: userData ? `${userData.firstName} ${userData.lastName}` : null,
  //       reviewers: reviewers.map((reviewer) =>
  //         reviewer ? `${reviewer.firstName} ${reviewer.lastName}` : null,
  //       ),
  //       application,
  //     };
  //   });

  //   const results = await Promise.all(promises);

  //   // If all promises result in just the application, return the application
  //   if (results.every((result) => result === application)) {
  //     return application;
  //   }

  //   return results;
  // }

  // // first one with the apllicaiton process
  // async findOneApplicationApprovalByApplicationId(applicationId: string) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       applicationId,
  //     );

  //   if (!application) {
  //     // If application is not found, return a consistent structure with null values.
  //     return {
  //       application: null,
  //       approvals: null,
  //       reviewers: null,
  //     };
  //   }

  //   // Fetch application approvals
  //   const applicationApprovals =
  //     await this.applicationApprovalEntityManagerRepository
  //       .createQueryBuilder('applicationApproval')
  //       .where('applicationApproval.applicationsId = :applicationId', {
  //         applicationId,
  //       })
  //       .leftJoinAndSelect('applicationApproval.applications', 'applications')
  //       .leftJoinAndSelect('applications.projects', 'projects')
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalLevels',
  //         'approvalLevels',
  //       )
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .getMany();

  //   // Fetch user data for the application owner
  //   const userData = application.userId
  //     ? await this.checkUser(application.userId)
  //     : null;
  //   const userFullName = userData
  //     ? `${userData.firstName} ${userData.lastName}`
  //     : null;

  //   // Fetch reviewer data
  //   const reviewerPromises = application.assignUsersFoReview
  //     ? application.assignUsersFoReview.map((reviewerId) =>
  //         this.checkUser(reviewerId),
  //       )
  //     : [];
  //   const reviewers = await Promise.all(reviewerPromises);
  //   const formattedReviewers = reviewers.map((reviewer) =>
  //     reviewer
  //       ? {
  //           id: reviewer.id,
  //           name: `${reviewer.firstName} ${reviewer.lastName}`,
  //         }
  //       : null,
  //   );

  //   // If no approvals, return application data with null approvals
  //   if (applicationApprovals.length === 0) {
  //     return {
  //       application,
  //       approvals: null,
  //       user: userFullName,
  //       reviewers: formattedReviewers,
  //     };
  //   }

  //   // Map through approvals to structure the data
  //   const formattedApprovals = applicationApprovals.map((approval) => ({
  //     id: approval.id,
  //     comment: approval.comment,
  //     applicationStatusId: approval.applicationStatusId,
  //     approvalStatus: approval.approvalStatus || null,
  //     approvalLevel: approval.approvalLevels || null,
  //     user: userFullName,
  //     ipAddress: approval.ipAddress || null,
  //     browser: approval.browser || null,
  //     operatingSystem: approval.operatingSystem || null,
  //     createdAt: approval.created_at,
  //     updatedAt: approval.updated_at,
  //   }));

  //   // Return the consolidated data
  //   return {
  //     application,
  //     approvals: formattedApprovals,
  //     user: userFullName,
  //     reviewers: formattedReviewers,
  //   };
  // }

  // fithe last one with the apllicaiton process
  // async findOneApplicationApprovalByApplicationId(applicationId: string) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       applicationId,
  //     );

  //   if (!application) {
  //     // If application is not found, return a consistent structure with null values.
  //     return {
  //       application: null,
  //       approvals: null,
  //       reviewers: null,
  //       submissionLogs: null,
  //       approvalCheckList: null,
  //       reviewersOnApplications: null,
  //     };
  //   }

  //   // Fetch application approvals
  //   const applicationApprovals =
  //     await this.applicationApprovalEntityManagerRepository
  //       .createQueryBuilder('applicationApproval')
  //       .where('applicationApproval.applicationsId = :applicationId', {
  //         applicationId,
  //       })
  //       .leftJoinAndSelect('applicationApproval.applications', 'applications')
  //       .leftJoinAndSelect('applications.projects', 'projects')
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalLevels',
  //         'approvalLevels',
  //       )
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .getMany();

  //   // Fetch submission logs
  //   const submissionLogs = await this.applicationService.findAllSubmissionLog(
  //     application.id,
  //   );

  //   // Fetch approval checklist
  //   const approvalCheckList =
  //     await this.approvalCheckListEntityManagerRepository
  //       .createQueryBuilder('applicationApprovalCheckList')
  //       .where('applicationApprovalCheckList.applicationsId = :applicationId', {
  //         applicationId,
  //       })
  //       .leftJoinAndSelect(
  //         'applicationApprovalCheckList.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .leftJoinAndSelect(
  //         'applicationApprovalCheckList.approvalLevels',
  //         'approvalLevels',
  //       )
  //       .getMany();

  //   // Fetch reviewers on application
  //   const reviewersOnApplications =
  //     await this.applicationService.findReviewersOnApplication(application.id);

  //   // Fetch user data for the application owner
  //   const userData = application.userId
  //     ? await this.checkUser(application.userId)
  //     : null;
  //   const userFullName = userData
  //     ? `${userData.firstName} ${userData.lastName}`
  //     : null;

  //   // Fetch reviewer data
  //   const reviewerPromises = application.assignUsersFoReview
  //     ? application.assignUsersFoReview.map((reviewerId) =>
  //         this.checkUser(reviewerId),
  //       )
  //     : [];
  //   const reviewers = await Promise.all(reviewerPromises);
  //   const formattedReviewers = reviewers.map((reviewer) =>
  //     reviewer
  //       ? {
  //           id: reviewer.id,
  //           name: `${reviewer.firstName} ${reviewer.lastName}`,
  //         }
  //       : null,
  //   );

  //   // Map through approvals to structure the data
  //   const formattedApprovals = applicationApprovals.map((approval) => ({
  //     id: approval.id,
  //     comment: approval.comment,
  //     applicationStatusId: approval.applicationStatusId,
  //     approvalStatus: approval.approvalStatus || null,
  //     approvalLevel: approval.approvalLevels || null,
  //     user: userFullName,
  //     ipAddress: approval.ipAddress || null,
  //     browser: approval.browser || null,
  //     operatingSystem: approval.operatingSystem || null,
  //     createdAt: approval.created_at,
  //     updatedAt: approval.updated_at,
  //   }));

  //   // Format submission logs with user name
  //   const formattedSubmissionLogs = await Promise.all(
  //     submissionLogs.map(async (log) => {
  //       const user = log.userId ? await this.checkUser(log.userId) : null;
  //       const userName = user ? `${user.firstName} ${user.lastName}` : null;
  //       return {
  //         userId: log.userId,
  //         name: userName,
  //         applicationStatusId: log.applicationStatusId,
  //         ipAddress: log.ipAddress,
  //         browser: log.browser,
  //         operatingSystem: log.operatingSystem,
  //         createdAt: log.created_at,
  //         updatedAt: log.updated_at,
  //       };
  //     }),
  //   );

  //   // Format approval checklist with user name
  //   const formattedApprovalCheckList = await Promise.all(
  //     approvalCheckList.map(async (checkList) => {
  //       const user = checkList.userId
  //         ? await this.checkUser(checkList.userId)
  //         : null;
  //       const userName = user ? `${user.firstName} ${user.lastName}` : null;
  //       return {
  //         decision: checkList.decision,
  //         conditionsOfApproval: checkList.conditionsOfApproval,
  //         structuralComment: checkList.structuralComment,
  //         civilEngineeringComment: checkList.civilEngineeringComment,
  //         architecturalComment: checkList.architecturalComment,
  //         urbanPlanningComment: checkList.urbanPlanningComment,
  //         siteAnalysisComment: checkList.siteAnalysisComment,
  //         userId: checkList.userId,
  //         name: userName,
  //         approvalStatus: checkList.approvalStatus || null,
  //         approvalLevel: checkList.approvalLevels || null,
  //         ipAddress: checkList.ipAddress || null,
  //         browser: checkList.browser || null,
  //         operatingSystem: checkList.operatingSystem || null,
  //         createdAt: checkList.created_at,
  //       };
  //     }),
  //   );

  //   // Format reviewers on application
  //   const formattedReviewersOnApplications = await Promise.all(
  //     reviewersOnApplications.map(async (reviewer) => {
  //       const reviewerData = await this.checkUser(reviewer.userId);
  //       return {
  //         userId: reviewer.userId,
  //         status: reviewer.status,
  //         name: reviewerData
  //           ? `${reviewerData.firstName} ${reviewerData.lastName}`
  //           : null,
  //         ipAddress: reviewer.ipAddress || null,
  //         browser: reviewer.browser || null,
  //         operatingSystem: reviewer.operatingSystem || null,
  //       };
  //     }),
  //   );

  //   // Return the consolidated data
  //   return {
  //     application,
  //     approvals: formattedApprovals,
  //     user: userFullName,
  //     reviewers: formattedReviewers,
  //     submissionLogs: formattedSubmissionLogs,
  //     approvalCheckList: formattedApprovalCheckList,
  //     reviewersOnApplications: formattedReviewersOnApplications,
  //   };
  // }
  async findOneApplicationApprovalByApplicationId(applicationId: string) {
    const application =
      await this.applicationService.findOneApplicationWithAllDetails(
        applicationId,
      );

    if (!application) {
      // If application is not found, return a consistent structure with null values.
      return {
        application: null,
        approvals: null,
        reviewers: null,
        submissionLogs: null,
        approvalCheckList: null,
        reviewersOnApplications: null,
      };
    }

    // Fetch all application statuses
    const applicationStatuses =
      await this.applicationService.findAllApplicationStatus();

    // Fetch application approvals
    const applicationApprovals =
      await this.applicationApprovalEntityManagerRepository
        .createQueryBuilder('applicationApproval')
        .where('applicationApproval.applicationsId = :applicationId', {
          applicationId,
        })
        .leftJoinAndSelect('applicationApproval.applications', 'applications')
        .leftJoinAndSelect('applications.projects', 'projects')
        .leftJoinAndSelect(
          'applicationApproval.approvalLevels',
          'approvalLevels',
        )
        .leftJoinAndSelect(
          'applicationApproval.approvalStatus',
          'approvalStatus',
        )
        .getMany();

    // Fetch submission logs
    const submissionLogs = await this.applicationService.findAllSubmissionLog(
      application.id,
    );

    // Fetch approval checklist
    const approvalCheckList =
      await this.approvalCheckListEntityManagerRepository
        .createQueryBuilder('applicationApprovalCheckList')
        .where('applicationApprovalCheckList.applicationsId = :applicationId', {
          applicationId,
        })
        .leftJoinAndSelect(
          'applicationApprovalCheckList.approvalStatus',
          'approvalStatus',
        )
        .leftJoinAndSelect(
          'applicationApprovalCheckList.approvalLevels',
          'approvalLevels',
        )
        .getMany();

    // Fetch reviewers on application
    const reviewersOnApplications =
      await this.applicationService.findReviewersOnApplication(application.id);

    // Fetch user data for the application owner
    const userData = application.userId
      ? await this.checkUser(application.userId)
      : null;
    const userFullName = userData
      ? `${userData.firstName} ${userData.lastName}`
      : null;

    // Fetch reviewer data
    const reviewerPromises = application.assignUsersFoReview
      ? application.assignUsersFoReview.map((reviewerId) =>
          this.checkUser(reviewerId),
        )
      : [];
    const reviewers = await Promise.all(reviewerPromises);
    const formattedReviewers = reviewers.map((reviewer) =>
      reviewer
        ? {
            id: reviewer.id,
            name: `${reviewer.firstName} ${reviewer.lastName}`,
          }
        : null,
    );

    // Map through approvals to structure the data
    // Add application status name to approvals
    // const formattedApprovals = applicationApprovals.map((approval) => {
    //   const statusName = applicationStatuses.find(
    //     (status) => status.id === approval.applicationStatusId,
    //   )?.name;

    //   return {
    //     id: approval.id,
    //     comment: approval.comment,
    //     applicationStatusId: approval.applicationStatusId,
    //     applicationStatusName: statusName || null,
    //     approvalStatus: approval.approvalStatus || null,
    //     approvalLevel: approval.approvalLevels || null,
    //     user: userFullName,
    //     ipAddress: approval.ipAddress || null,
    //     browser: approval.browser || null,
    //     operatingSystem: approval.operatingSystem || null,
    //     createdAt: approval.created_at,
    //     updatedAt: approval.updated_at,
    //   };
    // });
    const formattedApprovals = await Promise.all(
      applicationApprovals.map(async (approval) => {
        const statusName = applicationStatuses.find(
          (status) => status.id === approval.applicationStatusId,
        )?.name;

        // Fetch user data per approval (if needed)
        const userDataApproval = await this.checkUser(approval.userId);

        return {
          id: approval.id,
          comment: approval.comment,
          applicationStatusId: approval.applicationStatusId,
          applicationStatusName: statusName || null,
          approvalStatus: approval.approvalStatus || null,
          approvalLevel: approval.approvalLevels || null,
          user: userDataApproval,
          ipAddress: approval.ipAddress || null,
          browser: approval.browser || null,
          operatingSystem: approval.operatingSystem || null,
          createdAt: approval.created_at,
          updatedAt: approval.updated_at,
        };
      }),
    );

    // Format submission logs with application status name and user name
    const formattedSubmissionLogs = await Promise.all(
      submissionLogs.map(async (log) => {
        const user = log.userId ? await this.checkUser(log.userId) : null;
        const userName = user ? `${user.firstName} ${user.lastName}` : null;
        const statusName = applicationStatuses.find(
          (status) => status.id === log.applicationStatusId,
        )?.name;

        return {
          userId: log.userId,
          name: userName,
          applicationStatusId: log.applicationStatusId,
          applicationStatusName: statusName || null,
          ipAddress: log.ipAddress || null,
          browser: log.browser || null,
          operatingSystem: log.operatingSystem || null,
          createdAt: log.created_at,
          updatedAt: log.updated_at,
        };
      }),
    );

    // Format approval checklist with user name
    const formattedApprovalCheckList = await Promise.all(
      approvalCheckList.map(async (checkList) => {
        const user = checkList.userId
          ? await this.checkUser(checkList.userId)
          : null;
        const userName = user ? `${user.firstName} ${user.lastName}` : null;
        return {
          decision: checkList.decision,
          conditionsOfApproval: checkList.conditionsOfApproval,
          structuralComment: checkList.structuralComment,
          civilEngineeringComment: checkList.civilEngineeringComment,
          architecturalComment: checkList.architecturalComment,
          urbanPlanningComment: checkList.urbanPlanningComment,
          siteAnalysisComment: checkList.siteAnalysisComment,
          userId: checkList.userId,
          name: userName,
          approvalStatus: checkList.approvalStatus || null,
          approvalLevel: checkList.approvalLevels || null,
          ipAddress: checkList.ipAddress || null,
          browser: checkList.browser || null,
          operatingSystem: checkList.operatingSystem || null,
          createdAt: checkList.created_at,
        };
      }),
    );

    // Format reviewers on application
    const formattedReviewersOnApplications = await Promise.all(
      reviewersOnApplications.map(async (reviewer) => {
        const reviewerData = await this.checkUser(reviewer.userId);
        return {
          userId: reviewer.userId,
          status: reviewer.status,
          name: reviewerData
            ? `${reviewerData.firstName} ${reviewerData.lastName}`
            : null,
          ipAddress: reviewer.ipAddress || null,
          browser: reviewer.browser || null,
          operatingSystem: reviewer.operatingSystem || null,
          createdAt: reviewer.created_at || null,
        };
      }),
    );

    // Return the consolidated data
    return {
      application,
      approvals: formattedApprovals,
      user: userFullName,
      reviewers: formattedReviewers,
      submissionLogs: formattedSubmissionLogs,
      approvalCheckList: formattedApprovalCheckList,
      reviewersOnApplications: formattedReviewersOnApplications,
    };
  }

  // async findOneApplicationApprovalByApplicationId(applicationId: string) {
  //   const application =
  //     await this.applicationService.findOneApplicationWithAllDetails(
  //       applicationId,
  //     );

  //   const applicationApprovalsQuery =
  //     await this.applicationApprovalEntityManagerRepository
  //       .createQueryBuilder('applicationApproval')
  //       .where('applicationApproval.applicationsId = :applicationId', {
  //         applicationId,
  //       })
  //       .leftJoinAndSelect('applicationApproval.applications', 'applications')
  //       .leftJoinAndSelect('applications.projects', 'projects')
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalLevels',
  //         'approvalLevels',
  //       )
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .getMany();

  //   if (applicationApprovalsQuery.length === 0) {
  //     // Fetch user names and reviewer names even if no application approvals are found
  //     const userData = application.userId
  //       ? await this.checkUser(application.userId)
  //       : null;

  //     const reviewerPromises = application.assignUsersFoReview
  //       ? application.assignUsersFoReview.map(async (reviewerId) => {
  //           return this.checkUser(reviewerId);
  //         })
  //       : [];

  //     const reviewers = await Promise.all(reviewerPromises);

  //     return {
  //       ...application,
  //       user: userData ? `${userData.firstName} ${userData.lastName}` : null,
  //       reviewers: reviewers.map((reviewer) =>
  //         reviewer ? `${reviewer.firstName} ${reviewer.lastName}` : null,
  //       ),
  //     };
  //   }

  //   const promises = applicationApprovalsQuery.map(async (approval) => {
  //     const userData = application.userId
  //       ? await this.checkUser(application.userId)
  //       : null;

  //     const reviewerPromises = application.assignUsersFoReview
  //       ? application.assignUsersFoReview.map(async (reviewerId) => {
  //           return this.checkUser(reviewerId);
  //         })
  //       : [];

  //     const reviewers = await Promise.all(reviewerPromises);

  //     if (reviewers.length === 0) {
  //       // Return only the application details if no reviewers are found
  //       return application;
  //     }

  //     return {
  //       ...approval,
  //       user: userData ? `${userData.firstName} ${userData.lastName}` : null,
  //       reviewers: reviewers.map((reviewer) =>
  //         reviewer ? `${reviewer.firstName} ${reviewer.lastName}` : null,
  //       ),
  //       application,
  //     };
  //   });

  //   const results = await Promise.all(promises);

  //   // If all promises result in just the application, return the application
  //   if (results.every((result) => result === application)) {
  //     return application;
  //   }

  //   return results;
  // }
  async findApplicationApprovalByApplicationIdAndApprovalStatus(
    applicationId: string,
    applicationStatusId,
  ) {
    const applicationApprovalsQuery =
      await this.applicationApprovalEntityManagerRepository
        .createQueryBuilder('applicationApproval')
        .where('applicationApproval.applicationsId = :applicationId', {
          applicationId,
        })
        .andWhere(
          'applicationApproval.applicationStatusId = :applicationStatusId',
          {
            applicationStatusId,
          },
        )
        .leftJoinAndSelect('applicationApproval.applications', 'applications')
        .leftJoinAndSelect(
          'applicationApproval.approvalLevels',
          'approvalLevels',
        )
        .leftJoinAndSelect(
          'applicationApproval.approvalStatus',
          'approvalStatus',
        )
        .getMany();
    return applicationApprovalsQuery;
  }

  async updateApplicationApproval(
    id: string,
    applicationApprovalDto: ApplicationApprovalDto,
  ) {
    return this.applicationApprovalRepository.findOneAndUpdate(
      { id },
      applicationApprovalDto,
    );
  }

  async removeApplicationApproval(id: string) {
    return this.applicationApprovalRepository.findOneAndDelete({ id });
  }

  // PermitCheckList
  async createPermitCheckList(permitCheckListDto: PermitCheckListDto) {
    const dataFromDb =
      await this.applicationService.permitTypesRepository.findOne({
        id: permitCheckListDto.permitTypeId,
      });

    if (!dataFromDb)
      throw new HttpException('Permit Type Not Found', HttpStatus.NOT_FOUND);

    const permitCheckList = new PermitCheckList({
      ...permitCheckListDto,
      permitTypes: (permitCheckListDto.permitTypeId = {
        id: dataFromDb.id,
      } as any),
    });
    return this.permitCheckListRepository.create(permitCheckList);
  }

  async findAllPermitCheckLists() {
    return this.permitCheckListRepository.findAll({
      relations: { permitTypes: true },
    });
  }

  async findOnePermitCheckList(id: string) {
    return this.permitCheckListRepository.findOne({ id });
  }

  // async updatePermitCheckList(
  //   id: string,
  //   permitCheckListDto: PermitCheckListDto,
  // ) {
  //   return this.permitCheckListRepository.findOneAndUpdate(
  //     { id },
  //     permitCheckListDto,
  //   );
  // }

  async updatePermitCheckList(
    id: string,
    permitCheckListDto: PermitCheckListDto,
  ) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { permitTypeId, ...updateData } = permitCheckListDto;
    // Update the permit using QueryBuilder
    const updateResult = await this.permitCheckListEntityManagerRepository
      .createQueryBuilder()
      .update(PermitCheckList)
      .set(updateData)
      .where('id = :id', { id })
      .execute();

    console.log(updateResult);

    // Retrieve the updated entity
    const updatedPermitCheckList = await this.permitCheckListRepository.findOne(
      { id },
    );

    return updatedPermitCheckList;
  }

  async removePermitCheckList(id: string) {
    return this.permitCheckListRepository.findOneAndDelete({ id });
  }

  // ApplicationApprovalCheckList
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //     ...applicationApprovalCheckListDto,
  //   });
  //   return this.applicationApprovalCheckListRepository.create(
  //     applicationApprovalCheckList,
  //   );
  // }

  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   const approvalLevel = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalLevelId,
  //   });
  //   if (!approvalLevel) {
  //     throw new HttpException('Approval Level Not Found', HttpStatus.NOT_FOUND);
  //   }

  //   const approvalStatus = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalStatusId,
  //   });
  //   if (!approvalStatus) {
  //     throw new HttpException(
  //       'Approval Status Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const application = await this.applicationService.findOneApplication(
  //     applicationApprovalCheckListDto.applicationId,
  //   );
  //   if (!application) {
  //     throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //   }

  //   const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //     decision: applicationApprovalCheckListDto.decision,
  //     conditionsOfApproval:
  //       applicationApprovalCheckListDto.conditionsOfApproval,
  //     structuralComment: applicationApprovalCheckListDto.structuralComment,
  //     civilEngineeringComment:
  //       applicationApprovalCheckListDto.civilEngineeringComment,
  //     architecturalComment:
  //       applicationApprovalCheckListDto.architecturalComment,
  //     urbanPlanningComment:
  //       applicationApprovalCheckListDto.urbanPlanningComment,
  //     siteAnalysisComment: applicationApprovalCheckListDto.siteAnalysisComment,
  //     userId: applicationApprovalCheckListDto.userId,
  //     approvalLevels: { id: approvalLevel.id } as any,
  //     approvalStatus: { id: approvalStatus.id } as any,
  //     applications: { id: application.id } as any,
  //   });

  //   const savedEntity =
  //     await this.approvalCheckListEntityManagerRepository.save(
  //       applicationApprovalCheckList,
  //     );

  //   // Update ReviewersOnApplication status to 1 if approvalStatus code is 'RVW'
  //   console.log(approvalStatus.code);
  //   if (approvalStatus.code === 'ACPD') {
  //     const reviewerToUpdate =
  //       await this.applicationService.findOneReviewersOnApplication(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //     if (reviewerToUpdate) {
  //       reviewerToUpdate.status = '1';
  //       await this.applicationService.updateReviewersOnApplicationStatus(
  //         applicationApprovalCheckListDto.applicationId,
  //       );
  //     }

  //     // Continue with the rest of your code here if needed
  //     console.log('hello');
  //     // // get application service for update the status as a reviewed application
  //     const sendUpdateApplicationAStatus =
  //       this.applicationService.updateApplicationAfterReviewerComment(
  //         applicationApprovalCheckListDto.applicationId,
  //       );
  //     console.log(
  //       'Updated application status from the user:',
  //       sendUpdateApplicationAStatus,
  //     );
  //   }

  //   return savedEntity;
  // }

  // with a file
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const approvalLevel = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalLevelId,
  //     });
  //     if (!approvalLevel) {
  //       throw new HttpException(
  //         'Approval Level Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const approvalStatus = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalStatusId,
  //     });
  //     if (!approvalStatus) {
  //       throw new HttpException(
  //         'Approval Status Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const application = await this.applicationService.findOneApplication(
  //       applicationApprovalCheckListDto.applicationId,
  //     );
  //     if (!application) {
  //       throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     // Check if the reviewer has already approved the application
  //     const reviewerToUpdate =
  //       await this.applicationService.findOneReviewersOnApplication(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //     if (reviewerToUpdate?.status === '1') {
  //       throw new HttpException(
  //         'You have already approved this application',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //       decision: applicationApprovalCheckListDto.decision,
  //       conditionsOfApproval:
  //         applicationApprovalCheckListDto.conditionsOfApproval,
  //       structuralComment: applicationApprovalCheckListDto.structuralComment,
  //       civilEngineeringComment:
  //         applicationApprovalCheckListDto.civilEngineeringComment,
  //       architecturalComment:
  //         applicationApprovalCheckListDto.architecturalComment,
  //       urbanPlanningComment:
  //         applicationApprovalCheckListDto.urbanPlanningComment,
  //       siteAnalysisComment:
  //         applicationApprovalCheckListDto.siteAnalysisComment,
  //       userId: applicationApprovalCheckListDto.userId,
  //       approvalLevels: { id: approvalLevel.id } as any,
  //       approvalStatus: { id: approvalStatus.id } as any,
  //       applications: { id: application.id } as any,

  //       fileBase64,
  //     });

  //     // Update ReviewersOnApplication status to 1 if approvalStatus code is 'RVW'
  //     console.log(approvalStatus.code);
  //     if (
  //       approvalStatus.code === 'ACPD' ||
  //       approvalStatus.code === 'NCPL' ||
  //       approvalStatus.code === 'NORCM'
  //     ) {
  //       const reviewerToUpdate =
  //         await this.applicationService.findOneReviewersOnApplication(
  //           applicationApprovalCheckListDto.applicationId,
  //         );

  //       if (reviewerToUpdate) {
  //         reviewerToUpdate.status = '1';
  //         await this.applicationService.updateReviewersOnApplicationStatus(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       }

  //       // get application service for update the status as a reviewed application
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterReviewerComment(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     } else if (
  //       approvalStatus.code === 'NORVW' ||
  //       approvalStatus.code === 'NORVW' ||
  //       approvalStatus.code === 'NORVW'
  //     ) {
  //       const reviewerToUpdate =
  //         await this.applicationService.findOneReviewersOnApplication(
  //           applicationApprovalCheckListDto.applicationId,
  //         );

  //       if (reviewerToUpdate) {
  //         reviewerToUpdate.status = '1';
  //         await this.applicationService.updateReviewersOnApplicationStatus(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       }

  //       // get application service for update the status as a reviewed application
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterReviewerCommentRHA(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const savedEntity =
  //       await this.approvalCheckListEntityManagerRepository.save(
  //         applicationApprovalCheckList,
  //       );

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // // Updated function with a file
  // // Updated function with a file
  // // Updated function with a file
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const approvalLevel = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalLevelId,
  //     });
  //     if (!approvalLevel) {
  //       throw new HttpException(
  //         'Approval Level Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const approvalStatus = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalStatusId,
  //     });
  //     if (!approvalStatus) {
  //       throw new HttpException(
  //         'Approval Status Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const application = await this.applicationService.findOneApplication(
  //       applicationApprovalCheckListDto.applicationId,
  //     );
  //     if (!application) {
  //       throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     // Check if the reviewer has already approved the application
  //     const reviewerToUpdate =
  //       await this.applicationService.findOneReviewersOnApplicationByApplicationId(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //     if (reviewerToUpdate?.status === '1') {
  //       throw new HttpException(
  //         'You have already reviewed this application',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     // Proceed to create ApplicationApprovalCheckList if not already approved
  //     const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //       decision: applicationApprovalCheckListDto.decision,
  //       conditionsOfApproval:
  //         applicationApprovalCheckListDto.conditionsOfApproval,
  //       structuralComment: applicationApprovalCheckListDto.structuralComment,
  //       civilEngineeringComment:
  //         applicationApprovalCheckListDto.civilEngineeringComment,
  //       architecturalComment:
  //         applicationApprovalCheckListDto.architecturalComment,
  //       urbanPlanningComment:
  //         applicationApprovalCheckListDto.urbanPlanningComment,
  //       siteAnalysisComment:
  //         applicationApprovalCheckListDto.siteAnalysisComment,
  //       userId: applicationApprovalCheckListDto.userId,
  //       approvalLevels: { id: approvalLevel.id } as any,
  //       approvalStatus: { id: approvalStatus.id } as any,
  //       applications: { id: application.id } as any,
  //       fileBase64,
  //     });

  //     console.log(reviewerToUpdate);

  //     // Update ReviewersOnApplication status to 1 if the approvalStatus code is as specified
  //     if (['ACPD', 'NCPL', 'NORCM'].includes(approvalStatus.code)) {
  //       reviewerToUpdate.status = '1';
  //       await this.applicationService.updateReviewersOnApplicationStatus(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //       const updatedApplicationStatus =
  //         await this.applicationService.updateApplicationAfterReviewerComment(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         updatedApplicationStatus,
  //       );
  //     } else if (['NORVW'].includes(approvalStatus.code)) {
  //       reviewerToUpdate.status = '1';
  //       await this.applicationService.updateReviewersOnApplicationStatus(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //       const updatedApplicationStatus =
  //         await this.applicationService.updateApplicationAfterReviewerCommentRHA(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         updatedApplicationStatus,
  //       );
  //     }

  //     console.log(reviewerToUpdate);

  //     const savedEntity =
  //       await this.approvalCheckListEntityManagerRepository.save(
  //         applicationApprovalCheckList,
  //       );

  //     console.log(reviewerToUpdate);

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // Updated function with a file and submisison logs and RHA reviewers updates
  async createApplicationApprovalCheckList(
    applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
    file: Express.Multer.File,
    clientIp: string,
    userAgent: string,
  ) {
    try {
      let fileBase64: string = null;

      if (file) {
        fileBase64 = file.buffer.toString('base64');
      }

      // Parse user agent for browser and OS
      const clientInfo = this.parseUserAgent(userAgent);

      const approvalLevel = await this.approvalLevelRepository.findOne({
        id: applicationApprovalCheckListDto.approvalLevelId,
      });
      if (!approvalLevel) {
        throw new HttpException(
          'Approval Level Not Found',
          HttpStatus.NOT_FOUND,
        );
      }

      const approvalStatus = await this.approvalStatusRepository.findOne({
        id: applicationApprovalCheckListDto.approvalStatusId,
      });
      if (!approvalStatus) {
        throw new HttpException(
          'Approval Status Not Found',
          HttpStatus.NOT_FOUND,
        );
      }

      const application = await this.applicationService.findOneApplication(
        applicationApprovalCheckListDto.applicationId,
      );
      if (!application) {
        throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
      }

      // const reviewerToUpdate =
      //   await this.applicationService.findOneReviewersOnApplicationByApplicationId(
      //     applicationApprovalCheckListDto.applicationId,
      //   );

      // // if (reviewerToUpdate?.status === '1') {
      // //   throw new HttpException(
      // //     'You have already reviewed this application',
      // //     HttpStatus.BAD_REQUEST,
      // //   );
      // // }
      // if (
      //   reviewerToUpdate &&
      //   reviewerToUpdate.userId === applicationApprovalCheckListDto.userId &&
      //   reviewerToUpdate.status === '1'
      // ) {
      //   throw new HttpException(
      //     'You have already reviewed this application',
      //     HttpStatus.BAD_REQUEST,
      //   );
      // }
      // Check if reviewer has already reviewed the application
      const reviewersToUpdate =
        await this.applicationService.findReviewersOnApplication2(
          applicationApprovalCheckListDto.applicationId,
        );

      // Check if the current user has already reviewed
      const hasReviewed = reviewersToUpdate.some(
        (reviewer) =>
          reviewer.userId === applicationApprovalCheckListDto.userId &&
          reviewer.status === '1',
      );

      if (hasReviewed) {
        throw new HttpException(
          'You have already reviewed this application',
          HttpStatus.BAD_REQUEST,
        );
      }

      const applicationApprovalCheckList = new ApplicationApprovalCheckList({
        decision: applicationApprovalCheckListDto.decision,
        conditionsOfApproval:
          applicationApprovalCheckListDto.conditionsOfApproval,
        structuralComment: applicationApprovalCheckListDto.structuralComment,
        civilEngineeringComment:
          applicationApprovalCheckListDto.civilEngineeringComment,
        architecturalComment:
          applicationApprovalCheckListDto.architecturalComment,
        urbanPlanningComment:
          applicationApprovalCheckListDto.urbanPlanningComment,
        siteAnalysisComment:
          applicationApprovalCheckListDto.siteAnalysisComment,
        userId: applicationApprovalCheckListDto.userId,
        ipAddress: clientIp, // Save IP
        browser: clientInfo.browser, // Save browser info
        operatingSystem: clientInfo.operatingSystem, // Save OS info
        approvalLevels: { id: approvalLevel.id } as any,
        approvalStatus: { id: approvalStatus.id } as any,
        applications: { id: application.id } as any,
        fileBase64,
      });

      if (['ACPD', 'NCPL', 'NORCM'].includes(approvalStatus.code)) {
        // // Update the status for all reviewers in the array
        // for (const reviewer of reviewersToUpdate) {
        //   reviewer.status = '1';
        //   await this.applicationService.updateReviewersOnApplicationStatus(
        //     applicationApprovalCheckListDto.applicationId,
        //   );
        // }
        // Update status for the specific reviewer
        // Find the reviewer that made the action
        const reviewerToUpdate = reviewersToUpdate.find(
          (reviewer) =>
            reviewer.userId === applicationApprovalCheckListDto.userId,
        );
        // Update the status for that specid reviewer
        if (reviewerToUpdate) {
          reviewerToUpdate.status = '1';
          await this.applicationService.updateReviewersOnApplicationStatusSpecificUser(
            applicationApprovalCheckListDto.applicationId,
            applicationApprovalCheckListDto.userId,
          );
        }
        // // Update the status for that specid reviewer
        // if (reviewerToUpdate) {
        //   reviewerToUpdate.status = '1';
        //   await this.applicationService.updateReviewersOnApplicationStatus(
        //     applicationApprovalCheckListDto.applicationId,
        //   );
        // }

        const updatedApplicationStatus =
          await this.applicationService.updateApplicationAfterReviewerComment(
            applicationApprovalCheckListDto.applicationId,
          );
        console.log(
          'Updated application status from the user:',
          updatedApplicationStatus,
        );
        // } else if (['NORVW'].includes(approvalStatus.code)) {
        //   // Fetch reviewers for this application
        //   // Check if all reviewers have status not equal to '0'
        //   const allReviewed = reviewersToUpdate.every(
        //     (reviewer) => reviewer.status !== '0',
        //   );

        //   if (allReviewed) {
        //     const updatedApplicationStatus =
        //       await this.applicationService.updateApplicationAfterReviewerCommentRHA(
        //         applicationApprovalCheckListDto.applicationId,
        //       );

        //     console.log(
        //       'Updated application status from the user:',
        //       updatedApplicationStatus,
        //     );
        //   } else {
        //     console.log(
        //       'Some reviewers still have status "0", application status will not be updated.',
        //     );
        //   }
        // }
      } else if (approvalStatus.code === 'NORVW') {
        // Prevent the current reviewer from reviewing again
        const alreadyReviewed = reviewersToUpdate.find(
          (reviewer) =>
            reviewer.userId === applicationApprovalCheckListDto.userId &&
            reviewer.status === '1',
        );

        if (alreadyReviewed) {
          throw new HttpException(
            'You have already reviewed this application',
            HttpStatus.BAD_REQUEST,
          );
        }

        // Update the current reviewer status to '1'
        await this.applicationService.updateReviewersOnApplicationStatusSpecificUser(
          applicationApprovalCheckListDto.applicationId,
          applicationApprovalCheckListDto.userId,
        );

        // Check if all reviewers now have status '1'
        const refreshedReviewers =
          await this.applicationService.findReviewersOnApplication2(
            applicationApprovalCheckListDto.applicationId,
          );

        const allHaveApproved = refreshedReviewers.every(
          (reviewer) => reviewer.status === '1',
        );

        if (allHaveApproved) {
          const updatedApplicationStatus =
            await this.applicationService.updateApplicationAfterReviewerCommentRHA(
              applicationApprovalCheckListDto.applicationId,
            );

          console.log(
            'Updated application status from the user:',
            updatedApplicationStatus,
          );
        } else {
          console.log(
            'Not all reviewers have approved yet. Skipping RHA update.',
          );
        }
      }

      const savedEntity =
        await this.approvalCheckListEntityManagerRepository.save(
          applicationApprovalCheckList,
        );

      console.log('Saved entity:', savedEntity);

      return savedEntity;
    } catch (error) {
      console.error('Error in createApplicationApprovalCheckList:', error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // // Updated function with a file and submisison logs
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  //   file: Express.Multer.File,
  //   clientIp: string,
  //   userAgent: string,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     // Parse user agent for browser and OS
  //     const clientInfo = this.parseUserAgent(userAgent);

  //     const approvalLevel = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalLevelId,
  //     });
  //     if (!approvalLevel) {
  //       throw new HttpException(
  //         'Approval Level Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const approvalStatus = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalStatusId,
  //     });
  //     if (!approvalStatus) {
  //       throw new HttpException(
  //         'Approval Status Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const application = await this.applicationService.findOneApplication(
  //       applicationApprovalCheckListDto.applicationId,
  //     );
  //     if (!application) {
  //       throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     // const reviewerToUpdate =
  //     //   await this.applicationService.findOneReviewersOnApplicationByApplicationId(
  //     //     applicationApprovalCheckListDto.applicationId,
  //     //   );

  //     // // if (reviewerToUpdate?.status === '1') {
  //     // //   throw new HttpException(
  //     // //     'You have already reviewed this application',
  //     // //     HttpStatus.BAD_REQUEST,
  //     // //   );
  //     // // }
  //     // if (
  //     //   reviewerToUpdate &&
  //     //   reviewerToUpdate.userId === applicationApprovalCheckListDto.userId &&
  //     //   reviewerToUpdate.status === '1'
  //     // ) {
  //     //   throw new HttpException(
  //     //     'You have already reviewed this application',
  //     //     HttpStatus.BAD_REQUEST,
  //     //   );
  //     // }
  //     // Check if reviewer has already reviewed the application
  //     const reviewersToUpdate =
  //       await this.applicationService.findReviewersOnApplication2(
  //         applicationApprovalCheckListDto.applicationId,
  //       );

  //     // Check if the current user has already reviewed
  //     const hasReviewed = reviewersToUpdate.some(
  //       (reviewer) =>
  //         reviewer.userId === applicationApprovalCheckListDto.userId &&
  //         reviewer.status === '1',
  //     );

  //     if (hasReviewed) {
  //       throw new HttpException(
  //         'You have already reviewed this application',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //       decision: applicationApprovalCheckListDto.decision,
  //       conditionsOfApproval:
  //         applicationApprovalCheckListDto.conditionsOfApproval,
  //       structuralComment: applicationApprovalCheckListDto.structuralComment,
  //       civilEngineeringComment:
  //         applicationApprovalCheckListDto.civilEngineeringComment,
  //       architecturalComment:
  //         applicationApprovalCheckListDto.architecturalComment,
  //       urbanPlanningComment:
  //         applicationApprovalCheckListDto.urbanPlanningComment,
  //       siteAnalysisComment:
  //         applicationApprovalCheckListDto.siteAnalysisComment,
  //       userId: applicationApprovalCheckListDto.userId,
  //       ipAddress: clientIp, // Save IP
  //       browser: clientInfo.browser, // Save browser info
  //       operatingSystem: clientInfo.operatingSystem, // Save OS info
  //       approvalLevels: { id: approvalLevel.id } as any,
  //       approvalStatus: { id: approvalStatus.id } as any,
  //       applications: { id: application.id } as any,
  //       fileBase64,
  //     });

  //     if (['ACPD', 'NCPL', 'NORCM'].includes(approvalStatus.code)) {
  //       // // Update the status for all reviewers in the array
  //       // for (const reviewer of reviewersToUpdate) {
  //       //   reviewer.status = '1';
  //       //   await this.applicationService.updateReviewersOnApplicationStatus(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );
  //       // }
  //       // Update status for the specific reviewer
  //       // Find the reviewer that made the action
  //       const reviewerToUpdate = reviewersToUpdate.find(
  //         (reviewer) =>
  //           reviewer.userId === applicationApprovalCheckListDto.userId,
  //       );
  //       // Update the status for that specid reviewer
  //       if (reviewerToUpdate) {
  //         reviewerToUpdate.status = '1';
  //         await this.applicationService.updateReviewersOnApplicationStatusSpecificUser(
  //           applicationApprovalCheckListDto.applicationId,
  //           applicationApprovalCheckListDto.userId,
  //         );
  //       }
  //       // // Update the status for that specid reviewer
  //       // if (reviewerToUpdate) {
  //       //   reviewerToUpdate.status = '1';
  //       //   await this.applicationService.updateReviewersOnApplicationStatus(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );
  //       // }

  //       const updatedApplicationStatus =
  //         await this.applicationService.updateApplicationAfterReviewerComment(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         updatedApplicationStatus,
  //       );
  //     } else if (['NORVW'].includes(approvalStatus.code)) {
  //       // Update the status for all reviewers in the array
  //       for (const reviewer of reviewersToUpdate) {
  //         reviewer.status = '1';
  //         await this.applicationService.updateReviewersOnApplicationStatus(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       }

  //       const updatedApplicationStatus =
  //         await this.applicationService.updateApplicationAfterReviewerCommentRHA(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         updatedApplicationStatus,
  //       );
  //     }

  //     const savedEntity =
  //       await this.approvalCheckListEntityManagerRepository.save(
  //         applicationApprovalCheckList,
  //       );

  //     console.log('Saved entity:', savedEntity);

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in createApplicationApprovalCheckList:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // // with a file
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  //   file: Express.Multer.File,
  // ) {
  //   try {
  //     let fileBase64: string = null;

  //     if (file) {
  //       fileBase64 = file.buffer.toString('base64');
  //     }

  //     const approvalLevel = await this.approvalLevelRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalLevelId,
  //     });
  //     if (!approvalLevel) {
  //       throw new HttpException(
  //         'Approval Level Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const approvalStatus = await this.approvalStatusRepository.findOne({
  //       id: applicationApprovalCheckListDto.approvalStatusId,
  //     });
  //     if (!approvalStatus) {
  //       throw new HttpException(
  //         'Approval Status Not Found',
  //         HttpStatus.NOT_FOUND,
  //       );
  //     }

  //     const application = await this.applicationService.findOneApplication(
  //       applicationApprovalCheckListDto.applicationId,
  //     );
  //     if (!application) {
  //       throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //     }

  //     const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //       decision: applicationApprovalCheckListDto.decision,
  //       conditionsOfApproval:
  //         applicationApprovalCheckListDto.conditionsOfApproval,
  //       structuralComment: applicationApprovalCheckListDto.structuralComment,
  //       civilEngineeringComment:
  //         applicationApprovalCheckListDto.civilEngineeringComment,
  //       architecturalComment:
  //         applicationApprovalCheckListDto.architecturalComment,
  //       urbanPlanningComment:
  //         applicationApprovalCheckListDto.urbanPlanningComment,
  //       siteAnalysisComment:
  //         applicationApprovalCheckListDto.siteAnalysisComment,
  //       userId: applicationApprovalCheckListDto.userId,
  //       approvalLevels: { id: approvalLevel.id } as any,
  //       approvalStatus: { id: approvalStatus.id } as any,
  //       applications: { id: application.id } as any,

  //       fileBase64,
  //     });

  //     // Update ReviewersOnApplication status to 1 if approvalStatus code is 'RVW'
  //     console.log(approvalStatus.code);
  //     if (
  //       approvalStatus.code === 'ACPD' ||
  //       approvalStatus.code === 'NCPL' ||
  //       approvalStatus.code === 'NORCM'
  //     ) {
  //       // const reviewerToUpdate =
  //       //   await this.applicationService.findOneReviewersOnApplication(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );

  //       // if (reviewerToUpdate) {
  //       //   reviewerToUpdate.status = '1';
  //       //   await this.applicationService.updateReviewersOnApplicationStatus(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );
  //       // }

  //       // get application service for update the status as a reviewed application
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterReviewerComment(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     } else if (
  //       approvalStatus.code === 'NORVW' ||
  //       approvalStatus.code === 'NORVW' ||
  //       approvalStatus.code === 'NORVW'
  //     ) {
  //       // const reviewerToUpdate =
  //       //   await this.applicationService.findOneReviewersOnApplication(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );

  //       // if (reviewerToUpdate) {
  //       //   reviewerToUpdate.status = '1';
  //       //   await this.applicationService.updateReviewersOnApplicationStatus(
  //       //     applicationApprovalCheckListDto.applicationId,
  //       //   );
  //       // }

  //       // get application service for update the status as a reviewed application
  //       const sendUpdateApplicationAStatus =
  //         await this.applicationService.updateApplicationAfterReviewerCommentRHA(
  //           applicationApprovalCheckListDto.applicationId,
  //         );
  //       console.log(
  //         'Updated application status from the user:',
  //         sendUpdateApplicationAStatus,
  //       );
  //     }

  //     const savedEntity =
  //       await this.approvalCheckListEntityManagerRepository.save(
  //         applicationApprovalCheckList,
  //       );

  //     return savedEntity;
  //   } catch (error) {
  //     console.error('Error in createApplicationApproval:', error);
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  // // without a file
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   const approvalLevel = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalLevelId,
  //   });
  //   if (!approvalLevel) {
  //     throw new HttpException('Approval Level Not Found', HttpStatus.NOT_FOUND);
  //   }

  //   const approvalStatus = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalStatusId,
  //   });
  //   if (!approvalStatus) {
  //     throw new HttpException(
  //       'Approval Status Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const application = await this.applicationService.findOneApplication(
  //     applicationApprovalCheckListDto.applicationId,
  //   );
  //   if (!application) {
  //     throw new HttpException('Application Not Found', HttpStatus.NOT_FOUND);
  //   }

  //   const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //     decision: applicationApprovalCheckListDto.decision,
  //     conditionsOfApproval:
  //       applicationApprovalCheckListDto.conditionsOfApproval,
  //     structuralComment: applicationApprovalCheckListDto.structuralComment,
  //     civilEngineeringComment:
  //       applicationApprovalCheckListDto.civilEngineeringComment,
  //     architecturalComment:
  //       applicationApprovalCheckListDto.architecturalComment,
  //     urbanPlanningComment:
  //       applicationApprovalCheckListDto.urbanPlanningComment,
  //     siteAnalysisComment: applicationApprovalCheckListDto.siteAnalysisComment,
  //     userId: applicationApprovalCheckListDto.userId,
  //     approvalLevels: { id: approvalLevel.id } as any,
  //     approvalStatus: { id: approvalStatus.id } as any,
  //     applications: { id: application.id } as any,
  //   });

  //   // Update ReviewersOnApplication status to 1 if approvalStatus code is 'RVW'
  //   console.log(approvalStatus.code);
  //   if (
  //     approvalStatus.code === 'ACPD' ||
  //     approvalStatus.code === 'NCPL' ||
  //     approvalStatus.code === 'NORCM'
  //   ) {
  //     // const reviewerToUpdate =
  //     //   await this.applicationService.findOneReviewersOnApplication(
  //     //     applicationApprovalCheckListDto.applicationId,
  //     //   );

  //     // if (reviewerToUpdate) {
  //     //   reviewerToUpdate.status = '1';
  //     //   await this.applicationService.updateReviewersOnApplicationStatus(
  //     //     applicationApprovalCheckListDto.applicationId,
  //     //   );
  //     // }

  //     // Continue with the rest of your code here if needed
  //     console.log('hello');

  //     // get application service for update the status as a reviewed application
  //     const sendUpdateApplicationAStatus =
  //       await this.applicationService.updateApplicationAfterReviewerComment(
  //         applicationApprovalCheckListDto.applicationId,
  //       );
  //     console.log(
  //       'Updated application status from the user:',
  //       sendUpdateApplicationAStatus,
  //     );
  //   }

  //   const savedEntity =
  //     await this.approvalCheckListEntityManagerRepository.save(
  //       applicationApprovalCheckList,
  //     );

  //   return savedEntity;
  // }

  //the edit one before status
  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   const dataFromDb = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalLevelId,
  //   });
  //   if (!dataFromDb) {
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const dataFromDb2 = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalStatusId,
  //   });
  //   if (!dataFromDb2) {
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const dataFromDb3 = await this.applicationService.findOneApplication(
  //     applicationApprovalCheckListDto.applicationId,
  //   );
  //   if (!dataFromDb3) {
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //     decision: applicationApprovalCheckListDto.decision,
  //     conditionsOfApproval:
  //       applicationApprovalCheckListDto.conditionsOfApproval,
  //     structuralComment: applicationApprovalCheckListDto.structuralComment,
  //     civilEngineeringComment:
  //       applicationApprovalCheckListDto.civilEngineeringComment,
  //     architecturalComment:
  //       applicationApprovalCheckListDto.architecturalComment,
  //     urbanPlanningComment:
  //       applicationApprovalCheckListDto.urbanPlanningComment,
  //     siteAnalysisComment: applicationApprovalCheckListDto.siteAnalysisComment,
  //     userId: applicationApprovalCheckListDto.userId,
  //     approvalLevels: { id: dataFromDb.id } as any,
  //     approvalStatus: { id: dataFromDb2.id } as any,
  //     applications: { id: dataFromDb3.id } as any,
  //   });

  //   // console.log(applicationApprovalCheckList);

  //   const savedEntity =
  //     await this.approvalCheckListEntityManagerRepository.save(
  //       applicationApprovalCheckList,
  //     );

  //   return savedEntity;
  // }

  // async createApplicationApprovalCheckList(
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   const dataFromDb = await this.approvalLevelRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalLevelId,
  //   });
  //   if (!dataFromDb)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const dataFromDb2 = await this.approvalStatusRepository.findOne({
  //     id: applicationApprovalCheckListDto.approvalStatusId,
  //   });
  //   if (!dataFromDb2)
  //     throw new HttpException(
  //       'Referenced Data Not Found',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   const applicationApprovalCheckList = new ApplicationApprovalCheckList({
  //     ...applicationApprovalCheckListDto,
  //     approvalLevels: (applicationApprovalCheckListDto.approvalLevelId = {
  //       id: dataFromDb.id,
  //     } as any),
  //     approvalStatus: (applicationApprovalCheckListDto.approvalStatusId = {
  //       id: dataFromDb2.id,
  //     } as any),
  //   return this.applicationApprovalCheckListRepository.create(
  //     applicationApprovalCheckList,
  //   );
  // }

  // async findAllApplicationApprovalCheckLists() {
  //   return this.applicationApprovalCheckListRepository.findAll({
  //     relations: {
  //       approvalLevels: true,
  //       approvalStatus: true,
  //       applications: true,
  //     },
  //   });
  // }
  async findAllApplicationApprovalCheckLists() {
    const applicationApprovalCheckLists =
      await this.applicationApprovalCheckListRepository.findAll({
        relations: {
          approvalLevels: true,
          approvalStatus: true,
          applications: true,
        },
      });

    const filteredCheckLists = applicationApprovalCheckLists.filter(
      (checkList) => checkList.userId,
    );

    const combinedData = await Promise.all(
      filteredCheckLists.map(async (checkList) => {
        const userDetails = await this.checkUser(checkList.userId);
        return {
          ...checkList,
          userDetails,
        };
      }),
    );

    return combinedData;
  }

  async findOneApplicationApprovalCheckList(id: string) {
    return this.applicationApprovalCheckListRepository.findOne({ id });
  }

  async findOneApplicationApprovalCheckListAllDetails(id: string) {
    try {
      const checklist =
        await this.approvalCheckListEntityManagerRepository.findOne({
          where: { id },
          relations: {
            approvalLevels: true,
            approvalStatus: true,
            applications: true,
          },
        });

      if (!checklist) {
        throw new HttpException('Checklist not found', HttpStatus.NOT_FOUND);
      }

      return {
        id: checklist.id,
        decision: checklist.decision,
        conditionsOfApproval: checklist.conditionsOfApproval,
        structuralComment: checklist.structuralComment,
        civilEngineeringComment: checklist.civilEngineeringComment,
        architecturalComment: checklist.architecturalComment,
        urbanPlanningComment: checklist.urbanPlanningComment,
        siteAnalysisComment: checklist.siteAnalysisComment,
        userId: checklist.userId,
        ipAddress: checklist.ipAddress,
        browser: checklist.browser,
        operatingSystem: checklist.operatingSystem,
        approvalLevel: {
          id: checklist.approvalLevels.id,
          name: checklist.approvalLevels.name,
          // Add other fields if needed
        },
        approvalStatus: {
          id: checklist.approvalStatus.id,
          name: checklist.approvalStatus.name,
          code: checklist.approvalStatus.code,
          // Add other fields if needed
        },
        application: {
          id: checklist.applications.id,
          applicationName: checklist.applications.applicationName,
          // Add other fields if needed
        },
        fileBase64: checklist.fileBase64, // base64 string for frontend to download or preview
        createdAt: checklist.created_at,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to retrieve checklist',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAllApplicationApprovalCheckListByUserId(userId: string) {
    const approvalCheckListQuery =
      await this.approvalCheckListEntityManagerRepository
        .createQueryBuilder('applicationApprovalCheckList')
        .leftJoinAndSelect(
          'applicationApprovalCheckList.approvalStatus',
          'approvalStatus',
        )
        .where('applicationApprovalCheckList.userId = :userId', {
          userId,
        })
        .leftJoinAndSelect(
          'applicationApprovalCheckList.applications',
          'applications',
        )
        .getMany();

    return approvalCheckListQuery;
  }

  // async findAllApplicationApprovalCheckListByApplicationId(
  //   applicationId: string,
  // ) {
  //   const approvalCheckListQuery =
  //     await this.approvalCheckListEntityManagerRepository
  //       .createQueryBuilder('applicationApprovalCheckList')
  //       .where('applicationApprovalCheckList.applications = :applicationId', {
  //         applicationId,
  //       })
  //       .getMany();

  //   return approvalCheckListQuery;
  // }

  // async findAllApplicationApprovalCheckListByApplicationId(
  //   applicationId: string,
  // ) {
  //   if (!applicationId) {
  //     return [];
  //   }

  //   const approvalCheckListQuery =
  //     await this.approvalCheckListEntityManagerRepository
  //       .createQueryBuilder('applicationApprovalCheckList')
  //       .where('applicationApprovalCheckList.applications = :applicationId', {
  //         applicationId,
  //       })
  //       .getMany();

  //   return approvalCheckListQuery || [];
  // }
  async findAllApplicationApprovalCheckListByApplicationId(
    applicationId: string,
  ) {
    if (!applicationId) {
      return [];
    }

    const approvalCheckListQuery =
      await this.approvalCheckListEntityManagerRepository
        .createQueryBuilder('applicationApprovalCheckList')
        .leftJoinAndSelect(
          'applicationApprovalCheckList.approvalStatus',
          'approvalStatus',
        )
        .where('applicationApprovalCheckList.applications = :applicationId', {
          applicationId,
        })
        .getMany();

    const filteredCheckLists = approvalCheckListQuery.filter(
      (checkList) => checkList.userId,
    );

    const combinedData = await Promise.all(
      filteredCheckLists.map(async (checkList) => {
        const userDetails = await this.checkUser(checkList.userId);
        return {
          ...checkList,
          userDetails,
        };
      }),
    );

    return combinedData || [];
  }

  // async updateApplicationApprovalCheckList(
  //   id: string,
  //   applicationApprovalCheckListDto: ApplicationApprovalCheckListDto,
  // ) {
  //   return this.applicationApprovalCheckListRepository.findOneAndUpdate(
  //     { id },
  //     applicationApprovalCheckListDto,
  //   );
  // }

  async updateApplicationApprovalCheckList(
    id: string,
    approvalCheckListDto: ApplicationApprovalCheckListDto,
  ) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { applicationId, ...updateData } = approvalCheckListDto;
    // Update the permit using QueryBuilder
    const updateResult = await this.approvalCheckListEntityManagerRepository
      .createQueryBuilder()
      .update(ApplicationApprovalCheckList)
      .set(updateData)
      .where('id = :id', { id })
      .execute();

    console.log(updateResult);

    // Retrieve the updated entity
    const updatedApprovalPermitCheckList =
      await this.applicationApprovalCheckListRepository.findOne({ id });

    return updatedApprovalPermitCheckList;
  }

  async removeApplicationApprovalCheckList(id: string) {
    return this.applicationApprovalCheckListRepository.findOneAndDelete({ id });
  }

  //   // ApprovalDocument
  async createApprovalDocument(approvalDocumentDto: ApprovalDocumentDto) {
    const dataFromDb = await this.approvalStatusRepository.findOne({
      id: approvalDocumentDto.approvalStatusId,
    });
    if (!dataFromDb)
      throw new HttpException('Status Not Found', HttpStatus.NOT_FOUND);

    const approvalDocument = new ApprovalDocument({
      ...approvalDocumentDto,
      approvalStatus: (approvalDocumentDto.approvalStatusId = {
        id: dataFromDb.id,
      } as any),
      applications: (approvalDocumentDto.applicationId = {
        id: approvalDocumentDto.applicationId,
      } as any),
    });

    // // // get application service for update
    // if (dataFromDb.code === 'APPD') {
    //   const updateDocumentStatus = await this.updateDocumentStatusEvent(
    //     approvalDocumentDto.documentId,
    //   );
    //   console.log(updateDocumentStatus);
    // }

    return this.approvalDocumentRepository.create(approvalDocument);
  }

  async findAllApprovalDocuments() {
    return this.approvalDocumentRepository.findAll({
      relations: { applications: true, approvalStatus: true },
    });
  }

  async findOneApprovalDocument(id: string) {
    return this.approvalDocumentRepository.findOne({ id });
  }

  async updateApprovalDocument(
    id: string,
    approvalDocumentDto: ApprovalDocumentDto,
  ) {
    return this.approvalDocumentRepository.findOneAndUpdate(
      { id },
      approvalDocumentDto,
    );
  }

  async removeApprovalDocument(id: string) {
    return this.approvalDocumentRepository.findOneAndDelete({ id });
  }

  async findApplicationsApprovalByUserId(userId: string) {
    const applicationApprovalsQuery =
      await this.applicationApprovalEntityManagerRepository
        .createQueryBuilder('applicationApproval')
        .where('applicationApproval.userId = :userId', {
          userId,
        })
        .leftJoinAndSelect('applicationApproval.applications', 'applications')
        .leftJoinAndSelect('applications.projects', 'projects')
        .leftJoinAndSelect(
          'applicationApproval.approvalLevels',
          'approvalLevels',
        )
        .leftJoinAndSelect(
          'applicationApproval.approvalStatus',
          'approvalStatus',
        )
        .getMany();

    return applicationApprovalsQuery;
  }

  async findApprovalsByUserIdInDateRange(reviewerReportDto: ReviewerReportDto) {
    const userId = reviewerReportDto.userId;
    const startDate = reviewerReportDto.startDate;
    const endDate = reviewerReportDto.endDate;
    const approvals = await this.applicationApprovalEntityManagerRepository
      .createQueryBuilder('approval')
      .where('approval.userId = :userId', { userId })
      .andWhere('approval.created_at BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .leftJoinAndSelect('approval.applications', 'applications')
      .leftJoinAndSelect('approval.approvalStatus', 'approvalStatus')
      .getMany();

    if (approvals.length === 0) {
      throw new NotFoundException(
        `No data found for the given user and date range`,
      );
    }

    return approvals;
  }
  // async findApprovalsByUserIdInDateRange(
  //   userId: string,
  //   startDate: string,
  //   endDate: string,
  // ) {
  //   const approvals = await this.applicationApprovalEntityManagerRepository
  //     .createQueryBuilder('approval')
  //     .where('approval.userId = :userId', { userId })
  //     .andWhere('approval.created_at BETWEEN :startDate AND :endDate', {
  //       startDate,
  //       endDate,
  //     })
  //     .leftJoinAndSelect('approval.applications', 'applications')
  //     .leftJoinAndSelect('approval.applicationStatus', 'applicationStatus')
  //     .getMany();

  //   if (approvals.length === 0) {
  //     throw new NotFoundException(
  //       `No data found for the given user and date range`,
  //     );
  //   }

  //   return approvals;
  // }

  // RHA agency dashboard
  // async countApplicationsByStatusByAgencyRHA() {
  //   const applicationsData2 =
  //     await this.applicationService.findAllApplicationsWithNonObjectionsRHA();

  //   // Initialize the counting board with zero values
  //   const countingBoard2: { [status: string]: number } = {
  //     all: 0,
  //     rha: 0,
  //     rhaReturned: 0,
  //     nonObjectionReviewed: 0,
  //     nonObjectionUnderReview: 0,
  //     nonObjectionApproved: 0,
  //     nonObjectionRejected: 0,
  //     nonObjectionBackForCorrection: 0,
  //     certified: 0,
  //   };

  //   if (applicationsData2.length === 0) {
  //     // Return zeros if no applications are found
  //     return countingBoard2;
  //   }

  //   countingBoard2.all = applicationsData2.length;

  //   for (const application of applicationsData2) {
  //     switch (application.applicationStatus.code) {
  //       case 'NORHA':
  //         countingBoard2.rha++;
  //         break;
  //       case 'RTNNO':
  //         countingBoard2.rhaReturned++;
  //         break;
  //       case 'NORVW':
  //         countingBoard2.nonObjectionReviewed++;
  //         break;
  //       case 'NOUNRV':
  //         countingBoard2.nonObjectionUnderReview++;
  //         break;
  //       case 'CTFD':
  //         countingBoard2.certified++;
  //         break;
  //       default:
  //         break;
  //     }

  //     // Check approvals for specific statuses and additional codes
  //     const approvals = await this.applicationApprovalEntityManagerRepository
  //       .createQueryBuilder('applicationApproval')
  //       .where('applicationApproval.applicationsId = :applicationId', {
  //         applicationId: application.id,
  //       })
  //       .leftJoinAndSelect(
  //         'applicationApproval.approvalStatus',
  //         'approvalStatus',
  //       )
  //       .getMany();

  //     approvals.forEach((approval) => {
  //       if (
  //         approval.approvalStatus.code === 'RHAAPRVD' &&
  //         approval.approvalStatus.additionalCode === '4'
  //       ) {
  //         countingBoard2.nonObjectionApproved++;
  //       } else if (
  //         approval.approvalStatus.code === 'RHARJCT' &&
  //         approval.approvalStatus.additionalCode === '5'
  //       ) {
  //         countingBoard2.nonObjectionRejected++;
  //       } else if (
  //         approval.approvalStatus.code === 'RHABFCR' &&
  //         approval.approvalStatus.additionalCode === '6'
  //       ) {
  //         countingBoard2.nonObjectionBackForCorrection++;
  //       }
  //     });
  //   }

  //   return countingBoard2;
  // }

  async countApplicationsByStatusByAgencyRHA() {
    const applicationsData2 =
      await this.applicationService.findAllApplicationsWithNonObjectionsRHA();

    const countingBoard2: { [status: string]: number } = {
      all: 0,
      rha: 0,
      rhaReturned: 0,
      nonObjectionReviewed: 0,
      nonObjectionUnderReview: 0,
      nonObjectionApproved: 0,
      nonObjectionRejected: 0,
      nonObjectionBackForCorrection: 0,
      certified: 0,
    };

    if (!applicationsData2 || applicationsData2.length === 0) {
      return countingBoard2;
    }

    countingBoard2.all = applicationsData2.length;

    for (const application of applicationsData2) {
      // Check based on status flags or codes
      if (application.applicationStatus?.code === 'NORHA') {
        countingBoard2.rha++;
      }

      if (application.isNonObjectionReturned === '1') {
        countingBoard2.rhaReturned++;
      }

      if (
        application.applicationStatus?.code === 'NORVW' // Reviewed
      ) {
        countingBoard2.nonObjectionReviewed++;
      }

      if (
        application.applicationStatus?.code === 'NOUNRV' // Under review
      ) {
        countingBoard2.nonObjectionUnderReview++;
      }

      if (application.applicationStatus?.code === 'CTFD') {
        countingBoard2.certified++;
      }

      // Now check approvals
      // const approvals = await this.applicationApprovalEntityManagerRepository
      //   .createQueryBuilder('applicationApproval')
      //   .leftJoinAndSelect(
      //     'applicationApproval.approvalStatus',
      //     'approvalStatus',
      //   )
      //   .getMany();

      const isNonObjection = '1';
      const approvals = await this.applicationApprovalEntityManagerRepository
        .createQueryBuilder('applicationApproval')
        .leftJoinAndSelect(
          'applicationApproval.approvalStatus',
          'approvalStatus',
        )
        .leftJoinAndSelect('applicationApproval.applications', 'application')
        .where('application.isNonObjection = :isNonObjection', {
          isNonObjection,
        })
        .distinctOn(['application.id'])
        .orderBy('application.id', 'ASC')
        .addOrderBy('applicationApproval.updated_at', 'DESC')
        .getMany();

      for (const approval of approvals) {
        const { code, additionalCode } = approval.approvalStatus;

        if (code === 'RHAAPRVD' && additionalCode === '4') {
          countingBoard2.nonObjectionApproved++;
        } else if (code === 'RHARJCT' && additionalCode === '5') {
          countingBoard2.nonObjectionRejected++;
        } else if (code === 'RHABFCR' && additionalCode === '6') {
          countingBoard2.nonObjectionBackForCorrection++;
        }
      }
    }

    return countingBoard2;
  }

  // get All Applications With Details By Approval Status ==> return everthing
  async findAllApplicationsWithDetailsByApprovalStatus(approvalStatusId: any) {
    const applications = await this.applicationApprovalEntityManagerRepository
      .createQueryBuilder('approval')
      .where('approval.approvalStatusId = :approvalStatusId', {
        approvalStatusId,
      })
      .leftJoinAndSelect('approval.applications', 'application')
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .getMany();

    // if (applications.length === 0) {
    //   throw new NotFoundException(
    //     `No applications found with status id ${applicationStatusId}`,
    //   );
    // }

    return applications;
  }
  // get All Applications With Details By Approval Status ==> return only applications with details
  async findAllApplicationsWithDetailsByApprovalStatus2(approvalStatusId: any) {
    const approvals = await this.applicationApprovalEntityManagerRepository
      .createQueryBuilder('approval')
      .where('approval.approvalStatusId = :approvalStatusId', {
        approvalStatusId,
      })
      .leftJoinAndSelect('approval.applications', 'application')
      // .select(
      //   'DISTINCT ON (application.applicationNumber) application.id',
      //   'id',
      // )
      .leftJoinAndSelect('application.applicationStatus', 'applicationStatus')
      .leftJoinAndSelect('application.permitTypes', 'permitTypes')
      .leftJoinAndSelect('application.categoryTypes', 'categoryTypes')
      .leftJoinAndSelect('application.buildTypes', 'buildTypes')
      .leftJoinAndSelect('application.invoices', 'invoices')
      .leftJoinAndSelect('invoices.invoiceStatus', 'invoiceStatus')
      .leftJoinAndSelect('application.certificates', 'certificates')
      .leftJoinAndSelect('application.projects', 'projects')
      .leftJoinAndSelect('application.technologySurveys', 'technologySurveys')
      .addOrderBy('application.created_at', 'DESC')
      .getMany();

    const applications = approvals.flatMap((approval) => approval.applications);

    return applications.map((application) => ({
      id: application.id,
      upi: application.upi,
      isLocked: application.isLocked,
      combiningPlotSize: application.combiningPlotSize,
      isNonObjection: application.isNonObjection,
      isInspected: application.isInspected,
      isAssociatedUpi: application.isAssociatedUpi,
      isEIAVerified: application.isEIAVerified,
      buildUpArea: application.buildUpArea,
      numberOfFloor: application.numberOfFloor,
      grossFloorArea: application.grossFloorArea,
      numberOfParkingSpace: application.numberOfParkingSpace,
      priceOfDwellingUnitRwf: application.priceOfDwellingUnitRwf,
      capacityInformation: application.capacityInformation,
      numberOfDwellingUnits: application.numberOfDwellingUnits,
      DescriptionOfOperation: application.DescriptionOfOperation,
      percentageSpaceUse: application.percentageSpaceUse,
      waterConsumption: application.waterConsumption,
      electricityConsumption: application.electricityConsumption,
      DistanceToTheNearestLandIn: application.DistanceToTheNearestLandIn,
      ProjectCostInUSD: application.ProjectCostInUSD,
      ProjectCostInRwf: application.ProjectCostInRwf,
      agencyId: application.agencyId,
      userId: application.userId,
      submittedByUserId: application.submittedByUserId,
      certificateNumberEIA: application.certificateNumberEIA,
      applicationName: application.applicationName,
      permitTypeCode: application.permitTypeCode,
      agencyCode: application.agencyCode,
      created_at: application.created_at,
      updated_at: application.updated_at,
      submittedDate: application.submittedDate,
      isCountingActive: application.isCountingActive,
      assignUsersFoReview: application.assignUsersFoReview,
      other: application.other || {},
      applicationStatus: application.applicationStatus || {},
      permitTypes: application.permitTypes || {},
      categoryTypes: application.categoryTypes || {},
      buildTypes: application.buildTypes || {},
      invoices: application.invoices || [],
      certificates: application.certificates || [],
      projects: application.projects || {},
      technologySurveys: application.technologySurveys || {},
    }));
  }
}
