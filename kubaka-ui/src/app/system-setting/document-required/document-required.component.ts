import { Component, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { jwtDecode } from 'jwt-decode';
import { ngxCsv } from 'ngx-csv';
import { SessionService } from 'src/app/authentication-services/session.service';
import { NgbdAdvancedSortableHeader } from 'src/app/shared/directives/sortable.directive';
import { APIURLPATH } from 'src/app/shared/services/url-path';
import { NOTIFICATION_COLOR, UtilService } from 'src/app/shared/services/util.service';
import { UserMgtService } from 'src/app/user-management/services/user-mgt.service';
import { environment } from 'src/environments/environment';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-document-required',
  templateUrl: './document-required.component.html',
  styleUrls: ['./document-required.component.scss']
})
export class DocumentRequiredComponent {
  userForm!: UntypedFormGroup;
  submitted = false;
  lists: any[] = [];
  total!: number;
  @ViewChildren(NgbdAdvancedSortableHeader)
  headers!: QueryList<NgbdAdvancedSortableHeader>;
  page = 1;
  pageSize = 10;
  startIndex = 0;
  endIndex = 9;
  totalRecords = 0;
  collectionSize = 0;
  content?: any;
  econtent: any = {};
  masterSelected!: boolean;
  outPutData: any = {};
  permitTypes: any[] = [];
  documentTypes: any[] = [];
  categoryTypes: any[] = [];
  permitTypeFilter: any = '';

  fullList: any[] = [];
  filteredList: any[] = [];
  deleteId: any = '';

  documentCodes = [
    {
      id: '0',
    },
    {
      id: '1',
    }
  ];
  currentUser: any = {};

  constructor(
    private modalService: NgbModal,
    public userService: UserMgtService,
    private formBuilder: UntypedFormBuilder,
    private utilService: UtilService,
    private sessionService: SessionService,
  ) {
    this.currentUser = this.sessionService.getSession();
    this.currentUser.userId = (jwtDecode(this.currentUser.data.token.access_token) as any).UserId;


    this.initiliazeForm();
    this.loadList();
    this.lookups();

  }

  onSearchTermChange(term: string): void {
    this.userService.searchTerm = term;
    this.filterAndPaginate();
  }

  filterByPermitType() {
    // this.fullList = this.content.filter((x: any) => x.permitTypes.id === this.permitTypeFilter);
    // this.filterAndPaginate();
    this.page = 1; // Reset to first page when filtering
    this.filterAndPaginate();
  }

  ngOnDestroy() {
    this.userService.searchTerm = '';
  }

  lookups() {
    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.PERMITYPES)
      .subscribe(
        data => { this.permitTypes = data }
      )

    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.DOCUMENT_TYPE)
      .subscribe(
        data => { this.documentTypes = data }
      )
    this.userService.findAllWithPath(environment.applicationUrl + APIURLPATH.CATEGORYTYPE)
      .subscribe(
        data => { this.categoryTypes = data }
      )
  }

  loadList() {
    this.userService
      .findAllWithPath(environment.applicationUrl + APIURLPATH.DOCUMENTS_REQUIRED)
      .subscribe(
        (data) => {
          // this.lists = data;
          data.forEach((element: any) => {
            element.categoryName = element.categoryTypes ? element.categoryTypes.name : '';
            element.documentTypeName = element.documentTypes ? element.documentTypes.name : '';
            element.permitTypeName = element.permitTypes ? element.permitTypes.name : ''
          });



          // this.content = data;
          // this.fullList = data;
          // this.totalRecords = this.fullList.length;
          // this.filterAndPaginate();

          this.fullList = data; // Store full data for search
          this.totalRecords = this.fullList.length;
          this.filterAndPaginate(); // Apply filter and pagination

          if (this.permitTypeFilter) {
            this.filterByPermitType();
          }
          setTimeout(() => {
            document.getElementById("elmLoader")?.classList.add("d-none");
          }, 1200);
        });
  }


  filterAndPaginate() {
    const filtered = this.fullList.filter(item => this.applyFilter(item));
    this.filteredList = filtered;
    this.totalRecords = filtered.length;


    // this.startIndex = (this.page - 1) * this.pageSize + 1;
    // this.endIndex = (this.page - 1) * this.pageSize + this.pageSize;
    // if (this.endIndex > this.totalRecords) {
    //   this.endIndex = this.totalRecords;
    // }
    this.startIndex = (this.page - 1) * this.pageSize;
    this.endIndex = this.startIndex + this.pageSize;

    if (this.endIndex > this.totalRecords) {
      this.endIndex = this.totalRecords;
    }
    // this.lists = filtered.slice(this.startIndex - 1, this.endIndex);
    this.lists = filtered.slice(this.startIndex, this.endIndex);
  }

  applyFilter(item: any): boolean {

    const term = this.userService.searchTerm ? this.userService.searchTerm.toLowerCase() : '';
    const dateFilter = this.userService.filterByDate ? new Date(this.userService.filterByDate).toISOString().split('T')[0] : '';
    const selectedPermitTypeFilter = this.permitTypeFilter && this.permitTypeFilter != 'all' ? this.permitTypeFilter : null;


    const matchesTerm = term ? (
      Object.values(item).some(val => String(val).toLowerCase().includes(term)) ||
      (item.permitTypes?.name && item.permitTypes.name.toLowerCase().includes(term))
    ) : true;



    const permitTypeFilter = selectedPermitTypeFilter ? item.permitTypes.id === selectedPermitTypeFilter : true;
    // const matchesTerm = term ? Object.values(item).some(val => String(val).toLowerCase().includes(term)) : true;
    const matchesDate = dateFilter ? new Date(item.projects.created_at).toISOString().split('T')[0] === dateFilter : true;

    return matchesTerm && permitTypeFilter && matchesDate;
  }

  getPremiumData() {
    this.filterAndPaginate();
  }

  ngOnInit(): void {
    this.outPutData.name = 'Required Document';
  }


  initiliazeForm() {
    this.userForm = this.formBuilder.group({
      id: [""],
      name: ['', [Validators.required]],
      code: [""],
      permitTypeId: ['', Validators.required],
      documentTypeId: ['', Validators.required],
      categoryTypeId: ['', Validators.required],
    });
  }

  get form() {
    return this.userForm.controls;
  }



  openModal(actionType: any, content: any) {
    // if (actionType === '1') {

    // }
    this.submitted = false;
    this.modalService.open(content, { size: "md", centered: true });
  }

  checkedValGet: any[] = [];
  onCheckboxChange(e: any) {
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }




  editDataGet(id: any, content: any) {
    this.submitted = false;

    this.modalService.open(content, { size: "md", centered: true });
    var modelTitle = document.querySelector(".modal-title") as HTMLAreaElement;
    modelTitle.innerHTML = "Edit Document Required";
    var updateBtn = document.getElementById("add-btn") as HTMLAreaElement;
    updateBtn.innerHTML = "Update";

    this.userService.findOneWithPath(id, environment.applicationUrl + APIURLPATH.DOCUMENTS_REQUIRED).subscribe({
      next: (data: any) => {
        const users = data;
        this.econtent = users;
        this.userForm.controls['documentTypeId'].setValue(this.econtent.documentTypes.id);
        this.userForm.controls['permitTypeId'].setValue(this.econtent.permitTypes.id);
        this.userForm.controls['categoryTypeId'].setValue(this.econtent.categoryTypes.id);
        this.userForm.controls['code'].setValue(this.econtent.code);
        this.userForm.controls['name'].setValue(this.econtent.name);
        this.userForm.controls["id"].setValue(this.econtent.id);
        this.outPutData.type = 'edit';
        this.outPutData.name = 'Document Required';
        // this.outputData.isOpen = true;

      },
    });
  }


  saveUser() {
    if (this.userForm.invalid) {
    } else {
      if (this.userForm.get("id")?.value) {
        let dataToSave = this.userForm.value;
        dataToSave.updatedBy = this.currentUser.userId;
        this.submitted = true;
        this.userService.patchAssetWithPath(this.userForm.value.id, dataToSave, environment.applicationUrl + APIURLPATH.DOCUMENTS_REQUIRED)
          .subscribe(
            (data: any) => {
              this.utilService.showNotification(NOTIFICATION_COLOR.success, "Document required saved successfully", "bottom", "center");
              this.submitted = false;
              this.modalService.dismissAll();
              this.loadList();
            }, error => {
            })
      } else {
        let dataToSave = {

          "name": this.userForm.value.name,
          "permitTypeId": this.userForm.value.permitTypeId,
          "documentTypeId": this.userForm.value.documentTypeId,
          "categoryTypeId": this.userForm.value.categoryTypeId,
          "code": this.userForm.value.code,
          "createdBy": this.currentUser.userId
        }
        this.userService.saveAssetWithPath(dataToSave, environment.applicationUrl + APIURLPATH.DOCUMENTS_REQUIRED)
          .subscribe(
            (data: any) => {
              this.utilService.showNotification(NOTIFICATION_COLOR.success, "Document required saved successfully", "bottom", "center");
              this.loadList();
              this.modalService.dismissAll();
            },
          )
      }
    }
  }


  deleteData(id: any, content: any) {
    this.modalService.open(content, { size: "md", centered: true });
    this.deleteId = id

  }


  confirmToDelete() {
    if (this.deleteId) {
      this.userService.deleteWithPath(this.deleteId, environment.applicationUrl + APIURLPATH.DOCUMENTS_REQUIRED).subscribe({
        next: data => { this.loadList(); },
        error: err => {
        }
      });
    } else {

    }
  }


  deleteMultiple(content: any) {
    var checkboxes: any = document.getElementsByName("checkAll");
    var result;
    var checkedVal: any[] = [];
    for (var i = 0; i < checkboxes.length; i++) {
      if (checkboxes[i].checked) {
        result = checkboxes[i].value;
        checkedVal.push(result);
      }
    }
    if (checkedVal.length > 0) {
      this.modalService.open(content, { centered: true });
    } else {
      Swal.fire({
        text: "Please select at least one checkbox",
        confirmButtonColor: "#239eba",
      });
    }
    this.checkedValGet = checkedVal;
  }

  checkUncheckAll(ev: any) {
    this.lists.forEach((x: { state: any }) => (x.state = ev.target.checked));
    var checkedVal: any[] = [];
    var result;
    for (var i = 0; i < this.lists.length; i++) {
      if (this.lists[i].state == true) {
        result = this.lists[i];
        checkedVal.push(result);
      }
    }
    this.checkedValGet = checkedVal;
    checkedVal.length > 0
      ? ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "block")
      : ((
        document.getElementById("remove-actions") as HTMLElement
      ).style.display = "none");
  }


  saveValue(event: any) {
    this.loadList();
    this.modalService.dismissAll();
  }



  // Csv File Export
  csvFileExport() {
    var users = {
      fieldSeparator: ",",
      quoteStrings: '"',
      decimalseparator: ".",
      showLabels: true,
      showTitle: true,
      title: "Check List",
      useBom: true,
      noDownload: false,
      headers: [
        "ID",
        "name",
        "code",
        "documentType"
      ],
    };
    new ngxCsv(this.content, "checklist", users);
  }





}
