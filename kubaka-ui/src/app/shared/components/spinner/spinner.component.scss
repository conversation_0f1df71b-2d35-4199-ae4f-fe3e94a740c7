.spinner-container {
    text-align: center;
    /* Center align the content */
}

.spinner-paragraph {
    display: inline-block;
    /* Ensure the paragraph wraps around the spinner */
}

.spinner {
    position: relative;
    width: 500px;
    height: 40px;
    margin: 0 auto;
    /* Center the spinner horizontally */
}

.spinner-line {
    position: absolute;
    width: 100%;
    height: 4px;
    background-color: rgba(0, 0, 0, 0.2);
    /* Transparent color */
    animation: blink 1s infinite alternate;
}

@keyframes blink {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.spinner-line:nth-child(1) {
    top: 0;
}

.spinner-line:nth-child(2) {
    top: 25%;
    /* Adjusted to accommodate four lines */
}

.spinner-line:nth-child(3) {
    top: 50%;
    /* Adjusted to accommodate four lines */
}

.spinner-line:nth-child(4) {
    bottom: 0;
}