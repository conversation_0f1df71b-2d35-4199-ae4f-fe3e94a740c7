import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import {
  NOTIFICATION_COLOR,
  UtilService,
} from '../shared/services/util.service';

@Injectable({
  providedIn: 'root',
})
export class ExportExcelService {
  constructor(private utilService: UtilService) {}
  /**
   * Exports process history data to a CSV file.
   * @param data - The process history data to export.
   * @param fileName - The name of the file to save (default is 'export.csv').
   * @param sheetName - The name of the sheet in the Excel file (default is 'Data').
   */
  exportProcessHistoryToCSV(
    workSheetData: XLSX.WorkSheet,
    fileName: string = 'export.csv',
    sheetName: string = 'Data'
  ): void {
    if (!workSheetData) {
      this.utilService.showNotification(
        NOTIFICATION_COLOR.warning,
        'No data available to export.',
        'bottom',
        'right'
      );
      console.warn('No data available to export.');
      return;
    }

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, workSheetData, sheetName);

    const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([buffer], { type: 'text/csv;charset=utf-8' });
    saveAs(blob, fileName);
  }
}
